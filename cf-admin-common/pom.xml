<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
	<packaging>jar</packaging>
	<artifactId>cf-admin-common</artifactId>

	<parent>
		<groupId>com.shuidihuzhu.cf</groupId>
		<artifactId>cf-admin-parent</artifactId>
		<version>3.5.526-SNAPSHOT</version>
	</parent>

	<dependencies>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>3.17</version>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
		</dependency>
		<dependency>
			<groupId>com.shuidihuzhu.common</groupId>
			<artifactId>web-core-v2</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.shuidihuzhu.msg</groupId>
					<artifactId>msg-rpc-common</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.shuidihuzhu.account</groupId>
			<artifactId>account-grpc-client</artifactId>
		</dependency>
		<!--<dependency>-->
		<!--<groupId>com.shuidihuzhu.common</groupId>-->
		<!--<artifactId>web-util</artifactId>-->
		<!--</dependency>-->
    </dependencies>

</project>
