<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
	<packaging>jar</packaging>
	<artifactId>cf-admin-dal</artifactId>

	<parent>
		<groupId>com.shuidihuzhu.cf</groupId>
		<artifactId>cf-admin-parent</artifactId>
		<version>3.5.526-SNAPSHOT</version>
	</parent>

	<dependencies>
		<dependency>
			<groupId>com.shuidihuzhu.cf</groupId>
			<artifactId>cf-admin-common</artifactId>
		</dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.4.6</version>
        </dependency>
		<dependency>
			<groupId>com.shuidihuzhu.cf</groupId>
			<artifactId>cf-admin-model</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>xstream</artifactId>
					<groupId>com.thoughtworks.xstream</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.shuidihuzhu.cf</groupId>
			<artifactId>cf-admin-api-pure-client</artifactId>
		</dependency>
	</dependencies>

</project>
