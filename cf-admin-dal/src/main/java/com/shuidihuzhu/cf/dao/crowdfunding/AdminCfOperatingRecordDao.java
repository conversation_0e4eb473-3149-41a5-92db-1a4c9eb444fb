package com.shuidihuzhu.cf.dao.crowdfunding;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import org.apache.ibatis.annotations.Param;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;

@DataSource(AdminDS.CF_RW)
public interface AdminCfOperatingRecordDao {


	CfOperatingRecord getByInfoUuid(@Param("infoUuid") String infoUuid, @Param("role") int role);

	@DataSource(DS.CF_SLAVE_2)
	List<CfOperatingRecord> getByCreateTime(@Param("beginTime") Timestamp beginTime,
			@Param("endTime") Timestamp endTime, @Param("types") List<Integer> types);


	List<CfOperatingRecord> getCapitalListByOperationIds(@Param("operationIds")List<Long> operationIds);


	List<CfOperatingRecord> getListByInfoUuid(@Param("infoUuid")String infoUuid);


	List<CfOperatingRecord> getLastOneByInfoUUidList(@Param("infoUuids") List<String> infoUuids,
													 @Param("type") int type);


	CfOperatingRecord getByInfoUuidAndType(@Param("infoUuid") String infoUuid, @Param("type") int type);


	List<CfOperatingRecord> getAllOperateByInfoUuidAndType(@Param("infoUuid") String infoUuid, @Param("type") int type);


	List<CfOperatingRecord> getAllOperateByInfoUuidAndTypes(@Param("infoUuid")String infoUuid, @Param("types") List<Integer> types);

	int deleteById (@Param("infoUuid") String infoUuid, @Param("id") long id);




	List<String> selectCaseLastOperateByCreateTime(@Param("userSubmitBeginTime")long userSubmitBeginTime,
												   @Param("userSubmitEndTime")long userSubmitEndTime,
												   @Param("type") int type);


	List<String> selectOperateByCreateTimeAndInfoUuid(@Param("userSubmitEndTime")long userSubmitEndTime,
												   @Param("infoIds") Collection<String> infoIds,
												   @Param("type") int type);
}
