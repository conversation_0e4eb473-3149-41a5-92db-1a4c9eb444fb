package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.model.admin.AdminCreditSupplement;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Created by ahrievil on 2017/2/26.
 */
@DataSource(DS.CF)
public interface AdminCrowdfundingInfoStatusDao {
    int add(CrowdfundingInfoStatus crowdfundingInfoStatus);
    int updateByType(@Param("infoUuid") String infoUuid, @Param("type") int type, @Param("status") int status);
    int update(@Param("infoUuid") String infoUuid, @Param("status") int status);
    List<CrowdfundingInfoStatus> getByInfoUuid(@Param("infoUuid") String infoUuid);
    List<CrowdfundingInfoStatus> getByCaseId(@Param("caseId") int caseId);
    CrowdfundingInfoStatus getByInfoUuidAndType(@Param("infoUuid") String infoUuid, @Param("type") int type);
    int changeTypeAndInfoUuid(CrowdfundingInfoStatus crowdfundingInfoStatus);
    int updateByTypes(@Param("infoUuid") String infoUuid, @Param("status") int status, @Param("set") Set<Integer> set);

    int updateByInfoUuidsAndType(@Param("type") int type, @Param("set") Set<String> set,
                                 @Param("originalStatus") int originalStatus, @Param("targetStatus") int targetStatus);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingInfoStatus> getInfoUuidList(@Param("status") int  status, @Param("type") int type, @Param("offset") int offset,
                                 @Param("size") int size);

    @DataSource(DS.CF_SLAVE)
    int countByTypeAndStatus(@Param("status") int  status, @Param("type") int type);

    @DataSource(DS.CF_SLAVE_2)
    List<String> selectByAttributes(@Param("houseCredit") AdminCreditSupplement houseCredit,
                                    @Param("carCredit") AdminCreditSupplement carCredit,
                                    @Param("status") int  status, @Param("startTime") Date startTime,
                                    @Param("endTime") Date endTime, @Param("offset") int offset,
                                    @Param("size") int size);

    @DataSource(DS.CF_SLAVE)
    int getCountByAttributes(@Param("houseCredit") AdminCreditSupplement houseCredit,
                             @Param("carCredit") AdminCreditSupplement carCredit, @Param("status") int  status,
                             @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    int updateByInfoIdSet(@Param("infoUuIdSet") Set<String> infoUuIdSet, @Param("type") int type, @Param("status") int status);

    List<CrowdfundingInfoStatus> getByInfoUuidsAndType(@Param("infoUuidList")List<String> infoUuidList, @Param("type")int type);

    List<CrowdfundingInfoStatus> getByInfoUuidsAndTypes(@Param("infoUuidList")List<String> infoUuidList, @Param("types")List<Integer> types);

    List<CrowdfundingInfoStatus> getByInfoUuids(@Param("infoUuidList")List<String> infoUuidList);
}