package com.shuidihuzhu.cf.dao.approve;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.dto.CfMultipleCaseRiskDto;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(AdminDS.CF_ADMIN_RW)
public interface CfMultipleCaseRiskDao {
    CfMultipleCaseRiskDto selectByWorkOrderId(@Param("workOrderId") long workOrderId);
    CfMultipleCaseRiskDto selectLatestByCaseId(@Param("caseId") int workOrderId);
    int insertOne(@Param("data") CfMultipleCaseRiskDto cfMultipleCaseRiskDto);
}
