<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingInfoStatusDao">
    <sql id="tableName">
        crowdfunding_info_status
    </sql>

    <sql id="queryFields">
        `id`,
        `case_id` as caseId,
        `info_uuid` as infoUuid,
        `type`,
        `status`,
        `date_created` as dateCreated,
        `last_modified` as lastModified
    </sql>
    <insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus" keyProperty="id"
            useGeneratedKeys="true">
        INSERT INTO
        <include refid="tableName"/>
        (`case_id`,`info_uuid`,`type`,`status`)
        VALUES
        (#{caseId},#{infoUuid},#{type},#{status})
    </insert>
    <update id="updateByType">
        UPDATE
        <include refid="tableName"/>
        SET
        `status`=#{status}
        WHERE
        `info_uuid`=#{infoUuid}
        AND
        `type`=#{type}
    </update>
    <update id="update">
        UPDATE
        <include refid="tableName"/>
        SET
        `status`=#{status}
        WHERE
        `info_uuid`=#{infoUuid}
        LIMIT 4
    </update>
    <select id="getByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus">
        SELECT
        <include refid="queryFields"/>
        FROM
        <include refid="tableName"/>
        WHERE info_uuid=#{infoUuid}
    </select>
    <select id="getByInfoUuidAndType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus">
        SELECT
        <include refid="queryFields"/>
        FROM
        <include refid="tableName"/>
        WHERE `info_uuid`=#{infoUuid}
        AND
        `type`=#{type}
        LIMIT 1
    </select>
    <update id="changeTypeAndInfoUuid" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus">
        UPDATE
        <include refid="tableName"/>
        SET
        `type`=#{type},
        `info_uuid`=#{infoUuid}
        WHERE
        `id`=#{id}
    </update>

    <update id="updateByTypes">
        UPDATE
        <include refid="tableName"/>
        SET
        `status`=#{status}
        WHERE
        `info_uuid`=#{infoUuid}
        AND
        `type` IN
        <foreach collection="set" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateByInfoUuidsAndType">
        UPDATE
        <include refid="tableName"/>
        SET
        `status`=#{targetStatus}
        WHERE
        `status`=#{originalStatus}
        AND
        `type` = #{type}
        AND
        `info_uuid` IN
        <foreach collection="set" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="getInfoUuidList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus">
        SELECT
        <include refid="queryFields"/>
        FROM
        <include refid="tableName"/>
        WHERE `status`=#{status}
        AND
        `type`=#{type}
        ORDER BY `last_modified` desc
        LIMIT #{offset},#{size}
    </select>

    <select id="countByTypeAndStatus" resultType="java.lang.Integer">
        SELECT count(*)
        FROM
        <include refid="tableName"/>
        WHERE `status`=#{status}
        AND
        `type`=#{type}
    </select>

    <select id="selectByAttributes" resultType="java.lang.String">
          SELECT DISTINCT (info_status.info_uuid)
          FROM crowdfunding_info_status info_status
        <if test="houseCredit!=null">
            INNER JOIN cf_credit_supplement credit1 on credit1.info_uuid = info_status.info_uuid
        </if>
        <if test="carCredit!=null">
            INNER JOIN cf_credit_supplement credit2  on credit2.info_uuid = info_status.info_uuid
        </if>
        <if test="startTime!=null and endTime!=null">
            INNER JOIN cf_operating_record record  on record.info_uuid = info_status.info_uuid
        </if>
        LEFT JOIN crowdfunding_operation operation ON info_status.info_uuid = operation.info_id

        LEFT JOIN cf_info_ext ext ON info_status.info_uuid = ext.info_uuid

        WHERE info_status.type = 5
          AND ext.cf_version != 2000
          AND info_status.`status` = #{status}
        <if test="houseCredit!=null">
          and credit1.property_type = 1
          and credit1.convention = 0
          and credit1.count BETWEEN  #{houseCredit.startCount}  and  #{houseCredit.endCount}
          and credit1.total_value BETWEEN #{houseCredit.startTotalValue}  and #{houseCredit.endTotalValue}
          <if test="houseCredit.status=1 or houseCredit.status=0">
          and credit1.status =  #{houseCredit.status}
          </if>
          AND credit1.is_delete = 0
        </if>
        <if test="carCredit!=null">
          and credit2.property_type = 2
          and credit2.convention = 0
          and credit2.count BETWEEN  #{carCredit.startCount}  and  #{carCredit.endCount}
          and credit2.total_value BETWEEN #{carCredit.startTotalValue}  and #{carCredit.endTotalValue}
           <if test="carCredit.status=1 or carCredit.status=0">
          and credit2.status =  #{carCredit.status}
           </if>
          AND credit2.is_delete = 0
        </if>
        <if test="startTime!=null and endTime!=null">
          and record.date_created BETWEEN #{startTime} and #{endTime}
          and record.role = 2
        </if>
        AND operation.operation != 4
        ORDER BY info_status.last_modified DESC
        limit #{offset},#{size}
    </select>

    <select id="getCountByAttributes" resultType="java.lang.Integer">
        SELECT count(DISTINCT info_status.info_uuid)
        FROM crowdfunding_info_status info_status
        LEFT JOIN crowdfunding_operation operation ON info_status.info_uuid = operation.info_id
        <if test="houseCredit!=null">
            INNER JOIN cf_credit_supplement credit1 on credit1.info_uuid = info_status.info_uuid
        </if>
        <if test="carCredit!=null">
            INNER JOIN cf_credit_supplement credit2  on credit2.info_uuid = info_status.info_uuid
        </if>
        <if test="startTime!=null and endTime!=null">
            INNER JOIN cf_operating_record record  on record.info_uuid = info_status.info_uuid
        </if>

        LEFT JOIN cf_info_ext ext ON info_status.info_uuid = ext.info_uuid

        WHERE info_status.type = 5

        AND ext.cf_version != 2000

        AND info_status.`status` = #{status}
        AND operation.operation != 4
        <if test="houseCredit!=null">
            and credit1.property_type = 1
            and credit1.count BETWEEN  #{houseCredit.startCount}  and  #{houseCredit.endCount}
            and credit1.total_value BETWEEN #{houseCredit.startTotalValue}  and #{houseCredit.endTotalValue}
            <if test="houseCredit.status=1 or houseCredit.status=0">
            and credit1.status =  #{houseCredit.status}
            </if>
            AND credit1.is_delete = 0
        </if>
        <if test="carCredit!=null">
            and credit2.property_type = 2
            and credit2.count BETWEEN  #{carCredit.startCount}  and  #{carCredit.endCount}
            and credit2.total_value BETWEEN #{carCredit.startTotalValue}  and #{carCredit.endTotalValue}
            <if test="carCredit.status=1 or carCredit.status=0">
                and credit2.status =  #{carCredit.status}
            </if>
            AND credit2.is_delete = 0
        </if>
        <if test="startTime!=null and endTime!=null">
            and record.date_created BETWEEN #{startTime} and #{endTime}
            and record.role = 2
        </if>
    </select>

    <update id="updateByInfoIdSet">
        UPDATE
        <include refid="tableName"/>
        SET
        `status`=#{status}
        WHERE
        `type` = #{type}
        AND
        `info_uuid` IN
        <foreach collection="infoUuIdSet" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="getByInfoUuidsAndType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus">
        SELECT
        <include refid="queryFields"/>
        FROM
        <include refid="tableName"/>
        WHERE `info_uuid`in
        <foreach collection="infoUuidList" open="(" close=")" item="infoUuid" separator=",">
            #{infoUuid}
        </foreach>
        AND
        `type`=#{type}
    </select>

    <select id="getByInfoUuidsAndTypes" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus">
        SELECT
        <include refid="queryFields"/>
        FROM
        <include refid="tableName"/>
        WHERE `info_uuid`in
        <foreach collection="infoUuidList" open="(" close=")" item="infoUuid" separator=",">
            #{infoUuid}
        </foreach>
        AND
        `type` in
        <foreach collection="types" open="(" close=")" item="type" separator=",">
            #{type}
        </foreach>
    </select>

    <select id="getByInfoUuids" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus">
        SELECT
        <include refid="queryFields"/>
        FROM
        <include refid="tableName"/>
        WHERE `info_uuid`in
        <foreach collection="infoUuidList" open="(" close=")" item="infoUuid" separator=",">
            #{infoUuid}
        </foreach>
    </select>

    <select id="getByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus">
        SELECT
        <include refid="queryFields"/>
        FROM
        <include refid="tableName"/>
        WHERE case_id=#{caseId}
    </select>
</mapper>