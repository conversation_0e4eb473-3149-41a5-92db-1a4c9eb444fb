<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfOperatingRecordDao">

	<sql id="TABLE">
        cf_operating_record
    </sql>
    <sql id="insert">
	  (`info_uuid`,`type`,`role`,`user_id`,`user_name`,`comment`,`refuse_count`)
	</sql>

	<select id="getByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord">
		SELECT *
		FROM <include refid="TABLE" />
		WHERE info_uuid=#{infoUuid} AND role=#{role}
		AND `is_delete` = 0
		ORDER BY id DESC
		LIMIT 1
	</select>
	
	<select id="getByCreateTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord">
		SELECT *
		FROM <include refid="TABLE" />
		WHERE <![CDATA[ date_created>=#{beginTime} AND date_created<#{endTime} ]]>
		AND `is_delete` = 0
		<if test="types!=null">
			AND type in 
			<foreach collection="types" item="type" open="(" separator="," close=")">#{type}</foreach>
		</if>
	</select>

	<select id="getCapitalListByOperationIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord">
		SELECT *
		FROM <include refid="TABLE"/>
		WHERE `id` IN
		<foreach collection="operationIds" item="operationId" close=")" open="(" separator=",">
			#{operationId}
		</foreach>
		AND `is_delete` = 0
		ORDER BY  `date_created`
	</select>


	<select id="getListByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord">
		SELECT *
	    FROM <include refid="TABLE"/>
		WHERE `info_uuid` = #{infoUuid}
		AND `is_delete` = 0
	</select>

	<select id="getLastOneByInfoUUidList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord">
		select *
		FROM (SELECT *
		FROM <include refid="TABLE"/>
		WHERE `type` = #{type}
		AND  `info_uuid` IN
		<foreach collection="infoUuids" item="infoUuid" close=")" open="(" separator=",">
			#{infoUuid}
		</foreach>
		AND `is_delete` = 0
		order by `id` DESC ) o
		GROUP BY o.`info_uuid`
	</select>

	<select id="getByInfoUuidAndType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord">
		SELECT *
		FROM <include refid="TABLE" />
		WHERE info_uuid=#{infoUuid} AND `type`=#{type}
		AND `is_delete` = 0
		ORDER BY id DESC
		LIMIT 1
	</select>

	<select id="getAllOperateByInfoUuidAndType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord">
		SELECT *
		FROM <include refid="TABLE" />
		WHERE info_uuid=#{infoUuid} AND `type`=#{type}
		AND `is_delete` = 0
		ORDER BY date_created DESC
	</select>

	<select id="getAllOperateByInfoUuidAndTypes" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord">
		SELECT *
		FROM <include refid="TABLE" />
		WHERE info_uuid=#{infoUuid}
		AND `is_delete` = 0
		AND type in
		<foreach collection="types" item="type" open="(" separator="," close=")">#{type}</foreach>
		ORDER BY date_created DESC
	</select>

	<update id="deleteById" >
		update <include refid="TABLE" />
		set `is_delete` = 1
		WHERE info_uuid=#{infoUuid}
		AND id = #{id}
	</update>

	<select id="selectCaseLastOperateByCreateTime" resultType="java.lang.String">

		SELECT info_uuid
		FROM  cf_operating_record
		WHERE date_created >=  FROM_UNIXTIME(#{userSubmitBeginTime})
		AND date_created <![CDATA[ <= ]]> FROM_UNIXTIME(#{userSubmitEndTime})
		AND `type` = #{type}
		AND `is_delete` = 0
	</select>


	<select id="selectOperateByCreateTimeAndInfoUuid" resultType="java.lang.String">
		SELECT info_uuid
		FROM  cf_operating_record
		WHERE info_uuid IN
		<foreach collection="infoIds" item="item" open="(" close=")" separator=",">
			#{item}
		</foreach>
		AND date_created >=  FROM_UNIXTIME(#{userSubmitEndTime})
		AND `type` = #{type}
		AND `is_delete` = 0
	</select>
</mapper>