<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.approve.CfMultipleCaseRiskDao">
    <sql id="table">
        cf_multiple_case_risk
    </sql>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.dto.CfMultipleCaseRiskDto">
        insert into
        <include refid="table"/>
        (`case_id`, `have_amount_risk`, `amount`, `similar_case`, `first_approve_time`, `work_order_id`)
        values(#{data.caseId}, #{data.haveAmountRisk}, #{data.amount}, #{data.similarCase}, #{data.firstApproveTime}, #{data.workOrderId})
    </insert>

    <select id="selectByWorkOrderId" resultType="com.shuidihuzhu.cf.dto.CfMultipleCaseRiskDto">
        select * from
        <include refid="table"/>
        where `is_delete` = 0 and `work_order_id` = #{workOrderId}
        order by id desc
        limit 1;
    </select>

    <select id="selectLatestByCaseId" resultType="com.shuidihuzhu.cf.dto.CfMultipleCaseRiskDto">
        select * from
        <include refid="table"/>
        where `is_delete` = 0 and `case_id` = #{caseId}
        order by id desc
        limit 1;
    </select>

</mapper>