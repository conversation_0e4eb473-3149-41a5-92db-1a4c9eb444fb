package com.shuidihuzhu.cf.biz.crowdfunding;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.delegate.PreposeMaterialDelegate;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClient;
import com.shuidihuzhu.cf.risk.model.risk.DiseaseAmountResultRecord;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditSearchService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditTargetAmountReasonableService;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolFeginClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCoreFeignClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCreateParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static java.util.stream.Collectors.toList;

/**
 * Description:
 *
 * <AUTHOR>
 * @Create 2025/7/7 15:12
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class InitialAuditTargetAmountReasonableServiceTest {

    @Resource
    private InitialAuditTargetAmountReasonableService initialAuditTargetAmountReasonableService;

    @Resource
    private WorkOrderCoreFeignClient workOrderCoreFeignClient;

    @Resource
    private InitialAuditSearchService initialAuditSearchService;

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    /**
     * 测试 evaluatingDiseaseSpendingStrategies
     */
    @Test
    public void test1() {
        CrowdfundingInfo crowdfundingInfo = new CrowdfundingInfo();
        // 绑定了代录入的案例
        crowdfundingInfo.setId(3173865);
        initialAuditTargetAmountReasonableService.evaluatingDiseaseSpendingStrategies(crowdfundingInfo);
    }

    /**
     * 测试添加额外字段
     */
    @Test
    public void test2() {
        WorkOrderCreateParam workOrderCreateParam = new WorkOrderCreateParam();
        workOrderCreateParam.setOrderType(WorkOrderType.target_amount_reasonable_audit.getType());
        workOrderCreateParam.setCaseId(3173865);
        workOrderCreateParam.setOrderlevel(OrderLevel.D.getType());
        workOrderCreateParam.addExt(OrderExtName.targetAmountAuditWorkOrderScene, 1);

        // 添加额外字段 评估疾病花费策略，这个额外字段插入到了 sea-stat1 的 work_order_ext 表
        // 查询通过 com.shuidihuzhu.workorder.dao.WorkOrderDaoExt#listOrderExtByIdsAndExtNames 传入work_order_id以及ext_name
        workOrderCreateParam.addExt(OrderExtName.evaluatingTheCostOfIllness, 1);
        // 分配到von-type:target_amount_medical
        workOrderCreateParam.setAssignGroupPermission("von-type:target_amount_medical");

        workOrderCoreFeignClient.create(workOrderCreateParam);
    }

    @Test
    public void test3() {
        List<Integer> evaluatingTheCostOfIllnessCode = initialAuditSearchService.getEvaluatingTheCostOfIllnessCode(2156814);
        log.info("evaluatingTheCostOfIllnessCode = " + evaluatingTheCostOfIllnessCode); // 1
    }

    @Resource
    private DiseaseClient diseaseClient;

    @Test
    public void test4() {
        Response<List<DiseaseAmountResultRecord>> amountResultRecordByCaseId = diseaseClient.getAmountResultRecordByCaseId(3174326);
        List<DiseaseAmountResultRecord> diseaseAmountResultRecords = Optional.ofNullable(amountResultRecordByCaseId)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Lists.newArrayList());
        // 计算疾病花费，单位是分
        long sicknessExpenses = 0;
        long adviseMaxAmount = 0;
        if (!diseaseAmountResultRecords.isEmpty()) {
            DiseaseAmountResultRecord newest = diseaseAmountResultRecords.get(diseaseAmountResultRecords.size() - 1);
            log.info("疾病小工具建议最大花费取DiseaseAmountResultRecord下标为{}的，值为{}元", diseaseAmountResultRecords.size() - 1, newest.getAdviseMaxAmount());
            adviseMaxAmount = newest.getAdviseMaxAmount();
        }
    }

    @Resource
    private PreposeMaterialDelegate preposeMaterialDelegate;
    @Resource
    private CfGrowthtoolFeginClient cfGrowthtoolFeginClient;

    @Test
    public void test5() {
        PreposeMaterialModel.MaterialInfoVo materialInfoVo = preposeMaterialDelegate.queryByCaseId(3174523);
        // 是否命中试点，默认为false
        boolean highRiskGreyResult = false;

        if (materialInfoVo != null) {
            log.info("materialInfoVo.getVolunteerUniqueCode()：{}", materialInfoVo.getVolunteerUniqueCode());
            // 查询是否命中试点
            Response<Boolean> highRiskGrey = cfGrowthtoolFeginClient.isHighRiskGrey(materialInfoVo.getVolunteerUniqueCode());
            highRiskGreyResult = Optional.ofNullable(highRiskGrey)
                    .filter(Response::ok)
                    .map(Response::getData)
                    .orElse(false);
            log.info("highRiskGreyResult:{}", highRiskGreyResult);
        }
    }

    @Test
    public void test6() {
        AlarmBotService.sentMarkDown("6cfa5440-3d8d-489a-86c5-d787158ff4c1",
                new StringBuilder()
                        .append("<at id=all></at>，案例ID ")
                        .append(1111)
                        .append(" 收款人系统提示异常请复审，即刻执行。")
                        .toString());
    }

    @Resource
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Test
    public void test7() {
        // 根据info_uuid查询一个CrowdfundingInfo
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo("3bd2ce7a-32ed-4608-a7a4-8b3149500d2d");
        // passTypeIds需要有3
        Set<Integer> passTypeIds = new HashSet<>();
        passTypeIds.add(3);
        feishuGroupNotification(passTypeIds, crowdfundingInfo);
    }

    @Resource
    private AdminCrowdfundingAuthorBiz adminCrowdfundingAuthorBiz;

    /**
     * 收款人模块审核通过时通知飞书群
     */
    private void feishuGroupNotification(Set<Integer> passTypeIds, CrowdfundingInfo crowdfundingInfo) {
        if (crowdfundingInfo == null) {
            return;
        }

        Integer caseId = crowdfundingInfo.getId();
        String payeeIdCardEncrypt = crowdfundingInfo.getPayeeIdCard();

        CrowdfundingAuthor crowdfundingAuthor = adminCrowdfundingAuthorBiz.get(caseId);

        String patientIdCardEnc = Optional.ofNullable(crowdfundingAuthor)
                .map(CrowdfundingAuthor::getCryptoIdCard)
                .orElse(StringUtils.EMPTY);

        // 防止 passTypeIds 为空
        if (CollectionUtils.isEmpty(passTypeIds)) {
            return;
        }

        boolean payeeRepeat = getPayeeRepeat(payeeIdCardEncrypt, caseId, patientIdCardEnc);

        // 如果是多收款人，并且收款人模块审核通过了，则发送飞书群通知
        if (payeeRepeat && passTypeIds.contains(CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode())) {
            String content = new StringBuilder()
                    .append("测试消息")
                    .append("<at id=all></at>，案例ID ")
                    .append(caseId)
                    .append(" 收款人系统提示异常请复审，即刻执行。")
                    .toString();

            AlarmBotService.sentMarkDown("ea969363-0465-4e49-b20a-b6a3d1608344", content);
        }
    }

    @Resource
    private AdminCrowdfundingInfoPayeeBiz adminCrowdfundingInfoPayeeBiz;

    /**
     * 判断是否是多收款人
     */
    private boolean getPayeeRepeat(String currentPayeeIdCardEncrypt, int currentCaseId, String currentPatientIdCardEncrypt) {
        // 当前案例是否存在收款人信息
        if (StringUtils.isAnyBlank(currentPayeeIdCardEncrypt, currentPatientIdCardEncrypt) || currentCaseId <= 0) {
            return false;
        }
        // 查询收款人卡号相同的案例
        List<Integer> otherCaseIds = adminCrowdfundingInfoPayeeBiz.selectCaseIdsByPayeeIdCard(currentPayeeIdCardEncrypt)
                .stream()
                .filter(r -> r != currentCaseId)
                .collect(toList());
        if (CollectionUtils.isEmpty(otherCaseIds)) {
            return false;
        }
        // 如果存在，再判断这些案例中是否存在患者身份证号与当前案例不一致的数据
        for (List<Integer> item : Lists.partition(otherCaseIds, 200)) {
            List<CrowdfundingAuthor> crowdfundingAuthors = adminCrowdfundingAuthorBiz.selectByCaseIdList(item);
            if (CollectionUtils.isNotEmpty(crowdfundingAuthors) &&
                    crowdfundingAuthors.stream().anyMatch(r -> !StringUtils.equals(currentPatientIdCardEncrypt, r.getCryptoIdCard()))) {
                return true;
            }
        }
        return false;
    }
}