package com.shuidihuzhu.cf.biz.crowdfunding.hr;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.admin.mq.ImageAIRecognitionConsumer;
import com.shuidihuzhu.cf.admin.util.MaskUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminTwModifyService;
import com.shuidihuzhu.cf.biz.crowdfunding.impl.CaseEndReasonServiceImpl;
import com.shuidihuzhu.cf.client.adminpure.enums.AmountReasonableTaskType;
import com.shuidihuzhu.cf.client.feign.CrowdfundingAttachmentFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.dao.admin.IPromoteWebCallDao;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminTwModifyDao;
import com.shuidihuzhu.cf.dao.photoai.CfPhotoAiDao;
import com.shuidihuzhu.cf.delegate.crowdfunding.impl.CrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.finance.client.model.CaseInteractionVo;
import com.shuidihuzhu.cf.model.aiphoto.PhotoAiInfoModel;
import com.shuidihuzhu.cf.model.crowdfunding.CfAttachmentVo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachmentDTO;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.risk.client.risk.BrokenPromisesClientV2;
import com.shuidihuzhu.cf.risk.client.risk.RiskStrategyBizClient;
import com.shuidihuzhu.cf.service.admin.search.impl.CaseSearchServiceImpl;
import com.shuidihuzhu.cf.service.ai.*;
import com.shuidihuzhu.cf.service.approve.impl.CaseInteractionServiceImpl;
import com.shuidihuzhu.cf.service.credit.impl.CfIncreaseCreditServiceImpl;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cf.service.tag.impl.CfCaseLabelServiceImpl;
import com.shuidihuzhu.cf.service.tag.impl.CfThreeBodyServiceImpl;
import com.shuidihuzhu.cf.service.workorder.initialAudit.CfInitialAuditHandleV2ConsumerService;
import com.shuidihuzhu.cf.util.crowdfunding.AdminAppPushTemplateUtil;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationItem;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationMqVO;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.client.cf.admin.model.AIGenerateParam;
import com.shuidihuzhu.client.cf.admin.model.AiGenerateBaseInfo;
import com.shuidihuzhu.client.cf.admin.model.QueryParam;
import com.shuidihuzhu.client.cf.admin.model.SubsidyConst;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackJudgeConformThreeBodyClient;
import com.shuidihuzhu.client.cf.growthtool.model.CaseLabelParam;
import com.shuidihuzhu.client.cf.growthtool.model.RuleJudge;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCoreFeignClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCreateParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.dataservice.datautil.v1.DataUtilApiClient;

import com.shuidihuzhu.client.model.ChatChunk;
import com.shuidihuzhu.client.model.ChatCompletionChunk;
import com.shuidihuzhu.client.model.ChatStreamResult;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.msg.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertEquals;


import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description: 海瑞测试
 * @Author: panghairui
 * @Date: 2021/12/10 3:45 下午
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class AdminCfHaiRuiBizTest {

    static {
        System.setProperty("spring.cloud.consul.host","k8s-discovery-bedin.shuidi.io:80");
    }

    @Resource
    private IPromoteWebCallDao promoteWebCallDao;

    @Resource
    private AdminTwModifyDao adminTwModifyDao;

    @Resource(name = "cf2RedissonHandler")
    private RedissonHandler redissonHandler;

    @Resource
    private AdminTwModifyService adminTwModifyService;

    @Resource
    private Producer producer;

    @Resource
    private CfThreeBodyServiceImpl cfThreeBodyService;
    @Resource
    private BrokenPromisesClientV2 brokenPromisesClientV2;

    @Resource
    private CaseEndReasonServiceImpl caseEndReasonService;

    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Resource
    private CrowdfundingDelegate crowdfundingDelegate;
    @Autowired
    private IRiskDelegate riskDelegate;
    @Resource
    private CfClewtrackJudgeConformThreeBodyClient cfClewtrackJudgeConformThreeBodyClient;
    @Resource
    private ShuidiCipher shuidiCipher;

    @Resource
    private OldShuidiCipher oldShuidiCipher;
    @Resource
    private CfIncreaseCreditServiceImpl cfIncreaseCreditService;
    @Resource
    private CfInitialAuditHandleV2ConsumerService cfInitialAuditHandleV2ConsumerService;
    @Resource
    private RiskStrategyBizClient riskStrategyBizClient;
    @Resource
    private CfCaseLabelServiceImpl cfCaseLabelService;
    @Resource
    private CfAddressDataQueryService cfAddressDataQueryService;
    @Resource
    private DataUtilApiClient dataUtilApiClient;
    @Resource
    private WorkOrderCoreFeignClient workOrderCoreFeignClient;

    @Resource
    private AiGenerateServiceImpl aiGenerateService;
    @Resource
    private WenXinAiModel wenXinAiModel;
    @Resource
    private ImageAIRecognitionConsumer aiRecognitionConsumer;

    @Resource
    private MsgClientV2Service msgClientV2Service;
    @Resource
    private CaseInteractionServiceImpl caseInteractionService;
    @Resource
    private CaseSearchServiceImpl caseSearchService;
    @Resource
    private DeepSeekR1Model deepSeekR1Model;
    @Resource
    private AlarmClient alarmClient;

    @Resource
    private CrowdfundingAttachmentFeignClient crowdfundingAttachmentFeignClient;

    @Resource
    private CfRiskAiJudgeServiceImpl cfRiskAiJudgeService;

    @Resource
    private AdGenerationService adGenerationService;

    @Test
    public void testAttachment() {

        adGenerationService.generateAllAdsForCase("eff02798-b477-43f4-b212-b5159e60f23a");

        // cfRiskAiJudgeService.aiJudgeRisk("eff02798-b477-43f4-b212-b5159e60f23a");

//
//
//        // 测试 getAttachmentsByParentIdAndType 与 getAttachmentsByTypeToSea ！
//        List<CrowdfundingAttachment> crowdfundingAttachmentListA = crowdfundingAttachmentDao.getAttachmentsByParentIdAndType(3172150, 0);
//        FeignResponse<List<CrowdfundingAttachmentDTO>> response = crowdfundingAttachmentFeignClient.getAttachmentsByTypeToSea(3172150, 0);
//        List<CrowdfundingAttachment> crowdfundingAttachmentListB = CrowdfundingAttachmentConverterUtil.toEntityList(Optional.ofNullable(response)
//                .filter(FeignResponse::ok)
//                .map(FeignResponse::getData)
//                .orElse(null));
//
//        // 验证结果
//        assertNotNull(crowdfundingAttachmentListA);
//        assertNotNull(crowdfundingAttachmentListB);
//        assertEquals(crowdfundingAttachmentListA.size(), crowdfundingAttachmentListB.size());
//
//        crowdfundingAttachmentListA = crowdfundingAttachmentDao.getAttachmentsByParentIdAndType(9172152, 0);
//        response = crowdfundingAttachmentFeignClient.getAttachmentsByTypeToSea(9172152, 0);
//        crowdfundingAttachmentListB = CrowdfundingAttachmentConverterUtil.toEntityList(Optional.ofNullable(response)
//                .filter(FeignResponse::ok)
//                .map(FeignResponse::getData)
//                .orElse(null));
//
//        // --------------------------------------------------------------------------------
//
//        // 测试 deleteByIds
//        List<Integer> idsToDelete = Arrays.asList(1885274, 1885273);
//        int deleteCount = crowdfundingAttachmentDao.deleteByIds(idsToDelete);
//        FeignResponse<Integer> deleteResponse = crowdfundingAttachmentFeignClient.deleteByIds(3172072, Arrays.asList(1885264, 1885263));
//        Integer deleteCountFromFeign = Optional.ofNullable(deleteResponse)
//                .filter(FeignResponse::ok)
//                .map(FeignResponse::getData)
//                .orElse(null);
//
//        assertNotNull(deleteCountFromFeign);
//        assertEquals(deleteCount, deleteCountFromFeign.intValue());
//
//        // 测试 getAttachmentById ！
//        Integer testId = 1885259;
//        CrowdfundingAttachment attachment = crowdfundingAttachmentDao.getAttachmentById(testId);
//        FeignResponse<CrowdfundingAttachmentDTO> attachmentResponse = crowdfundingAttachmentFeignClient.getAttachmentById(3172071, testId);
//        CrowdfundingAttachment attachmentFromFeign = CrowdfundingAttachmentConverterUtil.toEntity(Optional.ofNullable(attachmentResponse)
//                .filter(FeignResponse::ok)
//                .map(FeignResponse::getData)
//                .orElse(null));
//
//        if (attachment != null) {
//            assertNotNull(attachmentFromFeign);
//            assertEquals(attachment.getId(), attachmentFromFeign.getId());
//        }
//
//        testId = 9885259;
//        attachment = crowdfundingAttachmentDao.getAttachmentById(testId);
//        attachmentResponse = crowdfundingAttachmentFeignClient.getAttachmentById(3172071, testId);
//        attachmentFromFeign = CrowdfundingAttachmentConverterUtil.toEntity(Optional.ofNullable(attachmentResponse)
//                .filter(FeignResponse::ok)
//                .map(FeignResponse::getData)
//                .orElse(null));
//
//        if (attachment != null) {
//            assertNotNull(attachmentFromFeign);
//            assertEquals(attachment.getId(), attachmentFromFeign.getId());
//        }
//
//        // 测试 getFundingAttachment 与 getFundingAttachmentToSea ！
//        int parentId = 3172070;
//        List<CrowdfundingAttachment> fundingAttachments = crowdfundingAttachmentDao.getFundingAttachment(parentId);
//        FeignResponse<List<CrowdfundingAttachmentDTO>> fundingAttachmentsResponse = crowdfundingAttachmentFeignClient.getFundingAttachmentToSea(parentId);
//        List<CrowdfundingAttachment> fundingAttachmentsFromFeign = CrowdfundingAttachmentConverterUtil.toEntityList(Optional.ofNullable(fundingAttachmentsResponse)
//                .filter(FeignResponse::ok)
//                .map(FeignResponse::getData)
//                .orElse(null));
//
//        assertNotNull(fundingAttachments);
//        assertNotNull(fundingAttachmentsFromFeign);
//        assertEquals(fundingAttachments.size(), fundingAttachmentsFromFeign.size());
//
//        fundingAttachments = crowdfundingAttachmentDao.getFundingAttachment(9172070);
//        fundingAttachmentsResponse = crowdfundingAttachmentFeignClient.getFundingAttachmentToSea(9172070);
//        fundingAttachmentsFromFeign = CrowdfundingAttachmentConverterUtil.toEntityList(Optional.ofNullable(fundingAttachmentsResponse)
//                .filter(FeignResponse::ok)
//                .map(FeignResponse::getData)
//                .orElse(null));
//
//        assertNotNull(fundingAttachments);
//        assertNotNull(fundingAttachmentsFromFeign);
//        assertEquals(fundingAttachments.size(), fundingAttachmentsFromFeign.size());
//
//        // 测试 queryAttachment 与 getFundingAttachmentAllFieldToSea ！
//        List<CrowdfundingAttachment> queryAttachments = crowdfundingAttachmentDao.queryAttachment(parentId);
//        FeignResponse<List<CrowdfundingAttachmentDTO>> queryAttachmentsResponse = crowdfundingAttachmentFeignClient.getFundingAttachmentAllFieldToSea(parentId);
//        List<CrowdfundingAttachment> queryAttachmentsFromFeign = CrowdfundingAttachmentConverterUtil.toEntityList(Optional.ofNullable(queryAttachmentsResponse)
//                .filter(FeignResponse::ok)
//                .map(FeignResponse::getData)
//                .orElse(null));
//
//        assertNotNull(queryAttachments);
//        assertNotNull(queryAttachmentsFromFeign);
//        assertEquals(queryAttachments.size(), queryAttachmentsFromFeign.size());
//
//        queryAttachments = crowdfundingAttachmentDao.queryAttachment(9172070);
//        queryAttachmentsResponse = crowdfundingAttachmentFeignClient.getFundingAttachmentAllFieldToSea(9172070);
//        queryAttachmentsFromFeign = CrowdfundingAttachmentConverterUtil.toEntityList(Optional.ofNullable(queryAttachmentsResponse)
//                .filter(FeignResponse::ok)
//                .map(FeignResponse::getData)
//                .orElse(null));
//
//        assertEquals(queryAttachments.size(), queryAttachmentsFromFeign.size());
//
//        // 测试 getAttachmentsByType 与 getAttachmentsByTypeToSea (使用枚举类型)
//        AttachmentTypeEnum typeEnum = AttachmentTypeEnum.ATTACH_CF; // 替换为实际的枚举值
//        List<CrowdfundingAttachment> typeAttachments = crowdfundingAttachmentDao.getAttachmentsByType(parentId, typeEnum);
//        FeignResponse<List<CrowdfundingAttachmentDTO>> typeAttachmentsResponse = crowdfundingAttachmentFeignClient.getAttachmentsByTypeToSea(parentId, typeEnum.value());
//        List<CrowdfundingAttachment> typeAttachmentsFromFeign = CrowdfundingAttachmentConverterUtil.toEntityList(Optional.ofNullable(typeAttachmentsResponse)
//                .filter(FeignResponse::ok)
//                .map(FeignResponse::getData)
//                .orElse(null));
//
//        assertNotNull(typeAttachments);
//        assertNotNull(typeAttachmentsFromFeign);
//        assertEquals(typeAttachments.size(), typeAttachmentsFromFeign.size());
//
//        typeAttachments = crowdfundingAttachmentDao.getAttachmentsByType(9172070, typeEnum);
//        typeAttachmentsResponse = crowdfundingAttachmentFeignClient.getAttachmentsByTypeToSea(9172070, typeEnum.value());
//        typeAttachmentsFromFeign = CrowdfundingAttachmentConverterUtil.toEntityList(Optional.ofNullable(typeAttachmentsResponse)
//                .filter(FeignResponse::ok)
//                .map(FeignResponse::getData)
//                .orElse(null));
//
//        // 测试 getListByInfoIdListAndType
//        List<Integer> parentIdList = Arrays.asList(3172068, 3172069, 3172070);
//        List<CrowdfundingAttachment> listByInfoId = crowdfundingAttachmentDao.getListByInfoIdListAndType(parentIdList, typeEnum);
//        FeignResponse<List<CrowdfundingAttachmentDTO>> listByInfoIdResponse = crowdfundingAttachmentFeignClient.getListByInfoIdListAndType(parentIdList, typeEnum);
//        List<CrowdfundingAttachment> listByInfoIdFromFeign = CrowdfundingAttachmentConverterUtil.toEntityList(Optional.ofNullable(listByInfoIdResponse)
//                .filter(FeignResponse::ok)
//                .map(FeignResponse::getData)
//                .orElse(null));
//
//        assertNotNull(listByInfoId);
//        assertNotNull(listByInfoIdFromFeign);
//        assertEquals(listByInfoId.size(), listByInfoIdFromFeign.size());
//
//        // 测试 getAttachmentsByTypes 与 getAttachmentsByTypesToSea
//        List<Integer> types = Arrays.asList(0);
//        List<CrowdfundingAttachment> attachmentsByTypes = crowdfundingAttachmentDao.getAttachmentsByTypes(parentId, types);
//        FeignResponse<List<CrowdfundingAttachmentDTO>> attachmentsByTypesResponse = crowdfundingAttachmentFeignClient.getAttachmentsByTypesToSea(parentId, types);
//        List<CrowdfundingAttachment> attachmentsByTypesFromFeign = CrowdfundingAttachmentConverterUtil.toEntityList(Optional.ofNullable(attachmentsByTypesResponse)
//                .filter(FeignResponse::ok)
//                .map(FeignResponse::getData)
//                .orElse(null));
//
//        assertNotNull(attachmentsByTypes);
//        assertNotNull(attachmentsByTypesFromFeign);
//        assertEquals(attachmentsByTypes.size(), attachmentsByTypesFromFeign.size());
//
//        // 测试 getAttachmentsByCaseIdsWithDelete
//        List<CrowdfundingAttachment> attachmentsByCaseIds = crowdfundingAttachmentDao.getAttachmentsByCaseIdsWithDelete(parentIdList, types);
//        FeignResponse<List<CrowdfundingAttachmentDTO>> attachmentsByCaseIdsResponse = crowdfundingAttachmentFeignClient.getAttachmentsByCaseIdsWithDelete(parentIdList, types);
//        List<CrowdfundingAttachment> attachmentsByCaseIdsFromFeign = CrowdfundingAttachmentConverterUtil.toEntityList(Optional.ofNullable(attachmentsByCaseIdsResponse)
//                .filter(FeignResponse::ok)
//                .map(FeignResponse::getData)
//                .orElse(null));
//
//        assertNotNull(attachmentsByCaseIds);
//        assertNotNull(attachmentsByCaseIdsFromFeign);
//        assertEquals(attachmentsByCaseIds.size(), attachmentsByCaseIdsFromFeign.size());
//
//        // 测试 getAttachmentsByIdList 与 getAttachmentsByIdListToSea
//        List<Integer> idList = Arrays.asList(1885811, 1885810);
//        List<CrowdfundingAttachment> attachmentsByIdList = crowdfundingAttachmentDao.getAttachmentsByIdList(idList);
//        FeignResponse<List<CrowdfundingAttachmentDTO>> attachmentsByIdListResponse = crowdfundingAttachmentFeignClient.getAttachmentsByIdListToSea(3172154, idList);
//        List<CrowdfundingAttachment> attachmentsByIdListFromFeign = CrowdfundingAttachmentConverterUtil.toEntityList(Optional.ofNullable(attachmentsByIdListResponse)
//                .filter(FeignResponse::ok)
//                .map(FeignResponse::getData)
//                .orElse(null));
//
//        assertNotNull(attachmentsByIdList);
//        assertNotNull(attachmentsByIdListFromFeign);
//        assertEquals(attachmentsByIdList.size(), attachmentsByIdListFromFeign.size());
//        System.out.println(1);
    }


    @Test
    public void testModel() {

        alarmClient.sendTextByBot("da0bfea9-9aad-46db-8ac3-3e174a4432a4", "2G-测试");

        deepSeekR1Model.callModelApi(null, "你好，帮我生成一个筹款标题");
        // caseSearchService.similarCaseSearch("胃癌", "220105199905070679");


    }

    @Test
    public void test123() {

        List<CaseInteractionVo> caseInteractionVoList = caseInteractionService.getCaseInteractionList(3162453);

        CaseLabelParam caseLabelParam = new CaseLabelParam();
        caseLabelParam.setCaseId(3147601);
        caseLabelParam.setPatientProfession("教师");
        cfCaseLabelService.judgeMajorCase(caseLabelParam);

        Map<Integer, String> params = Maps.newHashMap();
        params.put(1, "100");
        params.put(2, "事故案例");
        params.put(3, MaskUtil.limitComment("海瑞测试海瑞测试海瑞测试海瑞测试海瑞测试海瑞测试海瑞海瑞测试海瑞测试海瑞测试海瑞测试海瑞测试海瑞测试海瑞测试海瑞测试海瑞测试海瑞测试海瑞测试"));
        Map<Long, Map<Integer, String>> mapMap = Maps.newHashMap();
        mapMap.put(407482544L, params);
        String wxNum = "RBT2218";
        msgClientV2Service.sendWxParamsMsg(wxNum, mapMap);


        params = Maps.newConcurrentMap();
        params.put(1, "111");
        mapMap.put(407482544L, params);
        String modelNum0 = "template905";
        msgClientV2Service.sendWxParamsMsg(modelNum0, mapMap);

    }

    @Test
    public void test() {

        PhotoAiInfoModel photoAiInfoModel = new PhotoAiInfoModel();
        photoAiInfoModel.setIdNumber("");
        photoAiInfoModel.setInfoId(999999);

        QueryParam queryParam = new QueryParam();
        queryParam.setUuid("11");
        // List<AiGenerateRecordVO> aiGenerateRecordVOS = aiGenerateService.queryGenerateRecord(queryParam);

        AiGenerateBaseInfo info = AiGenerateBaseInfo.builder()
                .raisePatientRelation("女儿")
                .raiseName("李王飞")
                .raiserAge(30)
                .raiserStanding("普通")
                .patientName("李玉琼")
                .patientAge(4)
                .patientGender("女")
                .patientIdentity("学生")
                .patientAddress("北京市朝阳区")
                .medicalDisease("肺癌")
                .targetAmount(100000)
                .diseaseProcess("已进行两次手术")
                .familySituation("单亲家庭")
                .otherSituation("无")
                .build();

        AIGenerateParam aiGenerateParam = new AIGenerateParam();
        aiGenerateParam.setGenerateType(1);
        aiGenerateParam.setAiGenerateBaseInfo(info);
        aiGenerateParam.setClewId(1);
        aiGenerateParam.setUuid("zhangan5");
        aiGenerateParam.setOperatorId(1L);

        aiGenerateService.generate(aiGenerateParam);
        System.out.println(1);

    }

    @Test
    public void testV2() {

        RuleJudge ruleJudge = new RuleJudge();

        // 测试目标金额
        ruleJudge.setRuleId(1);
        ruleJudge.setInputContent(Map.of("targetAmount", "600000"));
        Boolean result = cfThreeBodyService.judgeThreeBodyRule(ruleJudge);
        ruleJudge.setInputContent(Map.of("targetAmount", "50000"));
        result = cfThreeBodyService.judgeThreeBodyRule(ruleJudge);
        ruleJudge.setInputContent(Map.of("targetAmount", "50000", "patientIdCard", "220105201000570679"));
        result = cfThreeBodyService.judgeThreeBodyRule(ruleJudge);
        ruleJudge.setRuleId(6);
        ruleJudge.setInputContent(Map.of("targetAmount", "600000"));
        result = cfThreeBodyService.judgeThreeBodyRule(ruleJudge);
        ruleJudge.setInputContent(Map.of("targetAmount", "400000"));
        result = cfThreeBodyService.judgeThreeBodyRule(ruleJudge);

        // 测试年龄
        ruleJudge.setRuleId(1);
        ruleJudge.setInputContent(Map.of("targetAmount", "110000", "patientIdCard", "4418718"));
        result = cfThreeBodyService.judgeThreeBodyRule(ruleJudge);
        ruleJudge.setInputContent(Map.of("targetAmount", "110000", "patientIdCard", "220105200000570679"));
        result = cfThreeBodyService.judgeThreeBodyRule(ruleJudge);

        // 测试疾病规则
        ruleJudge.setRuleId(2);
        ruleJudge.setInputContent(Map.of("targetAmount", "50000", "classifyDiseases", "急性白血病"));
        result = cfThreeBodyService.judgeThreeBodyRule(ruleJudge);
        ruleJudge.setInputContent(Map.of("targetAmount", "110000", "classifyDiseases", "急性白血病"));
        result = cfThreeBodyService.judgeThreeBodyRule(ruleJudge);
        ruleJudge.setInputContent(Map.of("targetAmount", "110000", "classifyDiseases", "我没病"));
        result = cfThreeBodyService.judgeThreeBodyRule(ruleJudge);

        // 测试案例类型规则
        ruleJudge.setRuleId(3);
        ruleJudge.setInputContent(Map.of("targetAmount", "50000", "accidentType", "车祸"));
        result = cfThreeBodyService.judgeThreeBodyRule(ruleJudge);
        ruleJudge.setInputContent(Map.of("targetAmount", "110000", "accidentType", "车祸"));
        result = cfThreeBodyService.judgeThreeBodyRule(ruleJudge);
        ruleJudge.setInputContent(Map.of("targetAmount", "110000", "accidentType", "不是车祸"));
        result = cfThreeBodyService.judgeThreeBodyRule(ruleJudge);

        // 测试患者身份规则
        ruleJudge.setRuleId(4);
        ruleJudge.setInputContent(Map.of("targetAmount", "50000", "patientIdentity", "教师"));
        result = cfThreeBodyService.judgeThreeBodyRule(ruleJudge);
        ruleJudge.setInputContent(Map.of("targetAmount", "110000", "patientIdentity", "教师"));
        result = cfThreeBodyService.judgeThreeBodyRule(ruleJudge);
        ruleJudge.setInputContent(Map.of("targetAmount", "110000", "patientIdentity", "不是教师"));
        result = cfThreeBodyService.judgeThreeBodyRule(ruleJudge);

        // 测试所在地规则
        ruleJudge.setRuleId(5);
        ruleJudge.setInputContent(Map.of("targetAmount", "50000", "raiseMobile", "17822833299",
                "selfCryptoIdcard", "", "patientIdCard", "650105198405270020"));
        result = cfThreeBodyService.judgeThreeBodyRule(ruleJudge);
        ruleJudge.setInputContent(Map.of("targetAmount", "110000", "raiseMobile", "13244323206",
                "selfCryptoIdcard", "", "patientIdCard", ""));
        result = cfThreeBodyService.judgeThreeBodyRule(ruleJudge);
        String mobile = "Sh8B7Rqp4Q4C7ItC6lMQSw$0";
        String idCard = "hg9z1H1BJbs2n6Q5QLgKbPOmGqdouEPDX-gJnSEXtn0$0";
        ruleJudge.setInputContent(Map.of("targetAmount", "110000", "raiseMobile", shuidiCipher.decrypt(mobile),
                "selfCryptoIdcard", shuidiCipher.decrypt(idCard), "patientIdCard", shuidiCipher.decrypt(idCard)));
        result = cfThreeBodyService.judgeThreeBodyRule(ruleJudge);

        System.out.println(1);
    }

    private String timeConvertToString(long time) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return formatter.format(time);
    }

}
