package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.model.admin.vo.CfDiseaseManagerVo;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.pf.common.v2.model.pagehelper.PaginationListVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;


/**
 * @author: fengxuan
 * @create 2019-11-11 18:09
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class DiseaseManagerServiceTest {

    static {
        System.setProperty("spring.cloud.consul.host","consul.zelda.shuiditech.com:80");
    }

    @Autowired
    SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    DiseaseManagerService diseaseManagerService;

    @Test
    public void create() {
    }

    @Test
    public void edit() {
    }

    @Test
    public void delete() {
    }

    @Test
    public void list() {
        CfDiseaseManagerDO cfDiseaseManagerDO = new CfDiseaseManagerDO();
        cfDiseaseManagerDO.setNormalName("冒");
        Response<PaginationListVO<CfDiseaseManagerVo>> list = diseaseManagerService.list(cfDiseaseManagerDO, 1, 3);
        log.info("list:{}", JSON.toJSONString(list));
    }

    @Test
    public void get() {
        CfDiseaseManagerRecordDO recordDO = diseaseManagerService.buildRecord(10, 327, 1);
        log.info("recordDO:{}", JSON.toJSONString(recordDO));
    }

    @Test
    public void listRecord() {
    }

    @Test
    public void listAllClassifyInfo() {
    }

    @Test
    public void testAddDiseaseClassify() {
    }

    @Test
    public void testGetUserInfo() {
        AuthRpcResponse<List<AdminUserAccountModel>> userAccountsByIds = seaAccountClientV1.getUserAccountsByIds(Lists.newArrayList(*********));
        if (userAccountsByIds.isSuccess()) {
            log.info("userAccountsByIds:{}", JSON.toJSONString(userAccountsByIds.getResult()));
        }
    }

    @Test
    public void testPrint() {
        CfDiseaseManagerDO managerDO = new CfDiseaseManagerDO();
        managerDO.setDiseaseClassifyId(1);
        managerDO.setRaiseType(1);
        managerDO.setMinTreatmentFee(10);
        managerDO.setMaxTreatmentFee(100);
        managerDO.setMedicalName("感冒");
        managerDO.setStandardName("流行性感冒");
        managerDO.setNormalName("感冒");
        managerDO.setTreatmentProject(1);
        log.info(JSON.toJSONString(managerDO));
    }
}