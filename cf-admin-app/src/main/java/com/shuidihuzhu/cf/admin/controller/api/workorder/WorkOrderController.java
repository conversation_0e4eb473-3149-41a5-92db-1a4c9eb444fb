package com.shuidihuzhu.cf.admin.controller.api.workorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Ordering;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditCreateOrder;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.admin.cache.WorkOrderDoneListCache;
import com.shuidihuzhu.cf.admin.util.UserUtil;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.client.feign.CfUserInfoFeignClient;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrderSecType;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrderType;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.model.crowdfunding.vo.QueryListResultVo;
import com.shuidihuzhu.cf.param.workorder.WorkOrderListQueryOldParam;
import com.shuidihuzhu.cf.service.CfCaseWorkOrderService;
import com.shuidihuzhu.cf.service.markfollowuptime.CfMarkFollowService;
import com.shuidihuzhu.cf.service.workorder.read.WorkOrderReadService;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderStaffClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderTypeFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.*;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2018/12/29
 */
@Slf4j
@RestController
@RefreshScope
@RequestMapping(path="/admin/workorder")
public class WorkOrderController {


    @Autowired
    private CfWorkOrderClient workOrderClient;

    @Autowired
    private CfWorkOrderStaffClient staffClient;

    @Autowired
    private UserCommentBiz commentBiz;

    @Autowired
    private CfCaseWorkOrderService workOrderService;

    @Autowired
    private WorkOrderReadService workOrderReadService;

    @Resource
    private CfUserInfoFeignClient cfUserInfoFeignClient;

    @Autowired
    private WorkOrderDoneListCache workOrderDoneListCache;

    @Autowired
    private CfMarkFollowService cfMarkFollowService;

    @Autowired
    private CfWorkOrderTypeFeignClient cfWorkOrderTypeFeignClient;

    @Autowired
    private InitialAuditCreateOrder initialAuditCreateOrder;
    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    Map<String, Object> createErCiWorkOrderByFeiShuMap = new HashMap<>();

    @Value("${er-ci-work-order-by-fei-shu:{}}")
    public void createErCiWorkOrderByFeiShuConfig(String createErCiWorkOrderByFeiShuConfig) {
        if(StringUtils.isBlank(createErCiWorkOrderByFeiShuConfig)) {
            return;
        }
        createErCiWorkOrderByFeiShuMap = JSONObject.parseObject(createErCiWorkOrderByFeiShuConfig);
    }

    @RequiresPermission("work-order:classify-type")
    @RequestMapping(path = "classify-type", method = RequestMethod.POST)
    Response classifyType(@RequestParam("classifyType")int classifyType){
      return workOrderClient.classifyType(classifyType);
    }

    @RequiresPermission("work-order:classify")
    @RequestMapping(path = "workorder-classify", method = RequestMethod.POST)
    Response workOrderClassify(@RequestParam("userId")long userId) {

        List<AdminWorkOrderType> result = Lists.newArrayList();

        Map<Integer, List<Integer>> map = workOrderClient.userClassify(userId).getData();

        if (map == null || map.size() == 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_NO_PCODE_PRIVILEGE);
        }
        map.remove(OneTypeEnum.common.getType());

        for (Integer i : map.keySet()){

            AdminWorkOrderType adminWorkOrderType = new AdminWorkOrderType();
            adminWorkOrderType.setType(i);

            List<Integer> orderTypes = map.get(i);

            List<AdminWorkOrderSecType> l = orderTypes.stream().map(r->{
                AdminWorkOrderSecType secType = new AdminWorkOrderSecType();
                secType.setOrderType(r);
                return secType;
            }).collect(Collectors.toList());
            adminWorkOrderType.setSecTypes(l);
            result.add(adminWorkOrderType);
        }

        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("work-order:user-classify")
    @RequestMapping(path = "user-classify", method = RequestMethod.POST)
    Response userClassify(@RequestParam("userId")long userId){
        final Boolean allEnable = ConfigService.getAppConfig()
                .getBooleanProperty("apollo.user-classify-all.enable", false);
        if (allEnable) {
            return workOrderClient.userClassifyAll(userId);
        }

        List<AdminWorkOrderType> result = Lists.newArrayList();

        Map<Integer,List<Integer>> map = workOrderClient.userClassify(userId).getData();

        if (map == null || map.size()==0){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_NO_PCODE_PRIVILEGE);
        }

        for (Integer i : map.keySet()){

            AdminWorkOrderType adminWorkOrderType = new AdminWorkOrderType();
            adminWorkOrderType.setType(i);

            List<Integer> orderTypes = map.get(i);

            final Map<Integer, StaffRealTimeWorkData> immutableOrderTypeTData = getPersonUnHandleWork(i, userId);

            List<AdminWorkOrderSecType> l = orderTypes.stream().map(r->{
                AdminWorkOrderSecType secType = getCount(userId,r);
                secType.setOrderType(r);
                Response<StaffStatus>  response = staffClient.getStaffStatus(r,userId);
                if (response == null || response.getData() == null){
                    secType.setStaffStatus(StaffStatusEnum.stop.getType());
                }else {
                    StaffStatus ss = response.getData();
                    secType.setStaffStatus(ss.getStaffStatus());
                    secType.setStaffType(ss.getOperType());
                }
                StaffRealTimeWorkData staffRealTimeWorkData = immutableOrderTypeTData.get(r);
                if (staffRealTimeWorkData != null) {
                    secType.setPersonUndoCount(staffRealTimeWorkData.getPersonalUnHandle());
                }
                return secType;
            }).collect(Collectors.toList());

            if (i == OneTypeEnum.juanzhuan.getType()) {
                Response<List<Integer>> listResponse = cfWorkOrderTypeFeignClient.getByOneLevel(OneTypeEnum.juanzhuan.getType());
                if (listResponse.notOk()) {
                    log.info("cfWorkOrderTypeFeignClient getByOneLevel fail, oneType:{}", OneTypeEnum.juanzhuan.getType());
                }
                List<Integer> juanzhuanTypes = Lists.newArrayList();
                juanzhuanTypes = listResponse.getData();
                juanzhuanTypes.remove((Integer) WorkOrderType.d0_1v1_1.getType());
                juanzhuanTypes.add(1, (Integer) WorkOrderType.d0_1v1_1.getType());
                //l 重排序
                Comparator<AdminWorkOrderSecType> sort = Ordering.explicit(juanzhuanTypes).onResultOf(AdminWorkOrderSecType::getOrderType);
                l.sort(sort);
            }

            if (i == OneTypeEnum.delay_finance.getType()) {
                l = l.stream().sorted(Comparator.comparingInt(AdminWorkOrderSecType::getOrderType)).collect(Collectors.toList());
            }
            adminWorkOrderType.setSecTypes(l);

            result.add(adminWorkOrderType);
        }

        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("work-order:get-personal-unHandleCount")
    @RequestMapping(path = "get-personal-unHandleCount", method = RequestMethod.POST)
    Response<List<StaffRealTimeWorkData>> getPersonalUnHandleCount(@RequestParam("userId") long userId,
                                               @RequestParam("orderTypes") List<Integer> orderTypes) {
        Response<List<StaffRealTimeWorkData>> realTimeWorkData = workOrderClient.listStaffRealTimeWorkData(userId);
        if (realTimeWorkData.notOk() || realTimeWorkData.getData() == null) {
            log.info("调用远程接口失败:{}", JSON.toJSONString(realTimeWorkData));
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
        }
        return realTimeWorkData;

    }


    @RequiresPermission("work-order:get-user-count")
    @RequestMapping(path = "get-user-count", method = RequestMethod.POST)
    Response<Integer> getUserCount(@RequestParam("userId") long userId,
                                   @RequestParam("orderType") int orderType,
                                   @RequestParam("handleResult") String handleResult) {

        return workOrderClient.getUserCount(userId, orderType, handleResult);
    }

    @RequiresPermission("work-order:get-all-count")
    @RequestMapping(path = "get-all-count")
    Response<Integer> getAllCount(@RequestParam("orderType")int orderType,
                                  @RequestParam("handleResult")int handleResult,
                                  @RequestParam("userId")int userId){
        return workOrderClient.getAllCountV1(orderType,handleResult,userId);
    }

    @RequiresPermission("work-order:get-workorder-count")
    @RequestMapping(path = "get-workorder-count")
    Response<AdminWorkOrderSecType> getWorkOrderCount(@RequestParam("userId") long userId,
                                  @RequestParam("orderType")int orderType){

        AdminWorkOrderSecType result= getCount(userId,orderType);

        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("work-order:get-staff-status-stat")
    @RequestMapping(path = "get-staff-status-stat",method = RequestMethod.POST)
    public Response<List<StaffStat>> getStaffStatusStat(long userId){
       return workOrderClient.getStaffStatusStat(userId);
    }

    @RequiresPermission("work-order:get-count-time")
    @RequestMapping(path = "get-count-time",method = RequestMethod.POST)
    public Response<Integer> getWorkOrderCount(@RequestParam("orderTypes") String orderTypes,
                                               @RequestParam("handleResult") String handleResult,
                                               @RequestParam("time") String time){

        Response<Integer> response = workOrderClient.getWorkOrderCount(orderTypes,handleResult,time);

        return response;
    }

    @RequiresPermission("work-order:get-workorder-by-id")
    @RequestMapping(path = "get-workorder-by-id",method = RequestMethod.POST)
    public Response<WorkOrderVO> getWorkOrderById(@RequestParam("workOrderId")  long workOrderId){
      return workOrderClient.getWorkOrderById(workOrderId);
    }

    @RequiresPermission("work-order:get-workorder-list")
    @RequestMapping(path = "get-workorder-list")
    public Response<PageResult<QueryListResultVo>> getWorkOrderList(@RequestParam("param") String param,
                                                                    @RequestParam("userId") int userId) {

        QueryListParam queryListParam = JSON.parseObject(param,QueryListParam.class);//已检查过
        workOrderService.fillWorkOrderStartAndEndTime(queryListParam);
        queryListParam.setOuterUser(checkOuterUser(queryListParam,userId));

        Response<PageResult<QueryListResult>> response = workOrderClient.getWorkOrderList(queryListParam);

        if (response == null || response.notOk()){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_OPERATION_FAILED);
        }
        PageResult<QueryListResult> pageResult = response.getData();

        List<QueryListResultVo> resultList = workOrderReadService.getQueryListResultVos(queryListParam, pageResult.getPageList());

        PageResult<QueryListResultVo> result = new PageResult<>();
        result.setHasNext(pageResult.isHasNext());
        result.setPageList(resultList);

        return NewResponseUtil.makeSuccess(result);
    }

    private boolean checkOuterUser(QueryListParam queryListParam,int userId){

        if (UserUtil.isInternalStaff(userId)){
            return false;
        }

        if (queryListParam.getWorkOrderId() > 0 || queryListParam.getCaseId() > 0 || StringUtils.isNotBlank(queryListParam.getMobile())){
            return false;
        }

        return true;

    }


    @RequiresPermission("work-order:get-workorder-list-count")
    @RequestMapping(path = "get-workorder-list-count")
    public Response<Integer> getWorkOrderListCount(@RequestParam("param") String param,
                                                   @RequestParam("userId") int userId) {

        QueryListParam queryListParam = JSON.parseObject(param,QueryListParam.class);//已检查过
        workOrderService.fillWorkOrderStartAndEndTime(queryListParam);
        queryListParam.setOuterUser(checkOuterUser(queryListParam,userId));

        return workOrderClient.getWorkOrderListCount(queryListParam);
    }


    @RequiresPermission("work-order:change-urgent-level")
    @RequestMapping(path = "change-urgentlevel",method = RequestMethod.POST)
    public Response<Integer> changeOrderLevel(@RequestParam("workOrderId") long workOrderId,
                                              @RequestParam("userId") long userId,
                                              @RequestParam("caseId") int caseId,
                                              @RequestParam("userComment") String userComment){
        Response response = workOrderClient.changeOrderLevel(workOrderId, OrderLevel.urgent.getType(),userId, userComment);

        insertUserCommentLog(userId,caseId,workOrderId,userComment);

        return response;
    }

    @RequiresPermission("work-order:save-timeline")
    @RequestMapping(path = "save-timeline",method = RequestMethod.POST)
    public Response saveTimeLine(@RequestParam("param") String param){

        if (StringUtils.isEmpty(param)){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        WorkOrderTimeline workOrderTimeline = JSON.parseObject(param,WorkOrderTimeline.class);//已检查过
        //进入详情页
        workOrderTimeline.setOperateType(OrdetTimelineEnum.entry_page.getCode());
        workOrderTimeline.setComment("点击记录");

        return workOrderClient.saveTimeLine(workOrderTimeline);
    }



    public void insertUserCommentLog(long userId,int caseId,long workOrderId,String userComment) {
        UserComment comment = new UserComment();
        comment.setOperatorId(userId);
        comment.setCaseId(caseId);
        comment.setCommentSource(UserCommentSourceEnum.INITIAL_AUDIT.getCode());
        comment.setCommentType(UserCommentSourceEnum.CommentType.CHANGE_ORDER_LEVEL.getCode());
        comment.setOperateMode(UserCommentSourceEnum.CommentType.CHANGE_ORDER_LEVEL.getDesc());
        comment.setWorkOrderId(workOrderId);
        comment.setComment(userComment);
        comment.setOperateDesc("催单");

        commentBiz.insert(comment);
    }

    @RequiresPermission("work-order:get-handle-results")
    @RequestMapping(path = "get-handle-results",method = RequestMethod.POST)
    public Response getHandleResults(){
        return NewResponseUtil.makeSuccess(HandleResultEnum.getShowTypeMsg());
    }

    @RequiresPermission("work-order:get-donetype-list")
    @RequestMapping(path = "get-donetype-list",method = RequestMethod.POST)
    public Response getDoneTypeList(int orderType){

        List<Integer> types = getDoneList(orderType);
        if (CollectionUtils.isNotEmpty(types)){
            return NewResponseUtil.makeSuccess(types);
        }
        return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
    }

    @ApiOperation("分配工单")
    @RequiresPermission("work-order:assign-work-order")
    @PostMapping("assign-work-order")
    public Response<Long> assignWorkOrder(
            @ApiParam("工单id") @RequestParam long workOrderId,
            @ApiParam("领取人id") @RequestParam long operatorId,
            @ApiParam("分配人id") @RequestParam(required = false, defaultValue = "0") long userId){
        return workOrderClient.assignWorkOrder(workOrderId, operatorId, userId);
    }

    @ApiOperation("回收工单")
    @RequiresPermission("work-order:callback-work-order")
    @PostMapping("callback-work-order")
    public Response<Long> callbackWorkOrder(
            @ApiParam("工单id") @RequestParam long workOrderId,
            @ApiParam("领取人id") @RequestParam long operatorId,
            @ApiParam("分配人id") @RequestParam(required = false, defaultValue = "0") long userId){

        try {
            cfMarkFollowService.updateOperatorIdByBizId(workOrderId, operatorId);
        } catch (Exception e) {
            log.info("主动服务库跟进表修改操作人 operatorId:{} workOrderId:{}", operatorId, workOrderId);
        }
        return workOrderClient.callbackAndAssignOrder(workOrderId, operatorId, userId);
    }

    @RequiresPermission("work-order:handle-workorder-by-workId")
    @ApiOperation("后门工具,处理工单")
    @PostMapping("handle-workorder-by-workId")
    public Response<Boolean> handleWorkOrderByWorkId(@ApiParam("工单id") @RequestParam(name = "workId") long workOrderId,
            @RequestParam(name = "userId") long userId, @RequestParam int handleResult) {
        return workOrderClient.handleWorkOrderByWorkId(workOrderId, userId, handleResult);
    }

    @RequiresPermission("work-order:create-workorder-by-case")
    @ApiOperation("后门工具,创建工单")
    @PostMapping("create-workorder-by-case")
    public Response<Boolean> createWorkByCase(@RequestParam(name = "caseId") int caseId,
            @RequestParam(name = "orderType") int orderType, @RequestParam(name = "userId") int operId) {
        Response<WorkOrderTypeRecord> resp = cfWorkOrderTypeFeignClient.getByOrderTypeCode(orderType);
        if (resp.notOk() || resp.getData() == null) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return workOrderClient.createWorkByCase(caseId, orderType, operId, "后门工具生成工单");
    }

    @RequiresPermission("work-order:create-er-ci-work-order-by-fei-shu")
    @ApiOperation("后门工具,飞书创建二次审核工单")
    @PostMapping("create-er-ci-work-order-by-fei-shu")
    public Response<String> createErCiWorkOrderByFeiShu(@RequestParam(name = "caseId") int caseId,
                                                        @RequestParam(name = "userName") String userName,
                                                        @RequestParam(name = "password") String password) {
        if (!StringUtils.equals(password, "wfK+E2Y1UNObVYA22Le+bnHZ1iks26NUlLueLUIksME=")) {
            return NewResponseUtil.makeSuccess("鉴权失败");
        }
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(ErrorCode.CF_NOT_FOUND);
        }
        Response<WorkOrderVO> lastWorkOrder = workOrderClient.getLastWorkOrder(caseId, WorkOrderType.ai_erci.getType());
        if (Objects.nonNull(lastWorkOrder) && lastWorkOrder.ok() && Objects.nonNull(lastWorkOrder.getData())) {
            return NewResponseUtil.makeSuccess("案例已生成初审工单");
        }
        if (MapUtils.isEmpty(createErCiWorkOrderByFeiShuMap)) {
            return NewResponseUtil.makeSuccess("未权限");
        }
        String userId = createErCiWorkOrderByFeiShuMap.get(userName).toString();
        if (StringUtils.isEmpty(userId)) {
            return NewResponseUtil.makeSuccess("未权限");
        }
        boolean canCreate = initialAuditCreateOrder.checkCanCreate(caseId, 0);
        if (!canCreate) {
            return NewResponseUtil.makeSuccess("补单被风控拦截");
        }
        Response<Boolean> booleanResponse = workOrderClient.createWorkByCase(caseId, WorkOrderType.ai_erci.getType(), Integer.parseInt(userId), "飞书后门工具生成工单");
        if (Objects.isNull(booleanResponse) || booleanResponse.notOk() || !booleanResponse.getData()) {
            return NewResponseUtil.makeSuccess("补单失败");
        }

        return NewResponseUtil.makeSuccess("补单成功");
    }

    @RequiresPermission("work-order:view-work-classify")
    @ApiOperation("后门工具,查看工单分类")
    @PostMapping("view-work-classify")
    public Response<List<WorkItemMessage>> viewWorkClassify() {
        WorkOrderType[] values = WorkOrderType.values();
        List<WorkItemMessage> workItemMessageList = Lists.newArrayList();
        WorkItemMessage workItem = null;
        for (WorkOrderType workOrderType : values) {
            workItem = new WorkItemMessage();
            workItem.setItemCode(workOrderType.getType());
            workItem.setItemMessage(workOrderType.getMsg());
            workItemMessageList.add(workItem);
        }
        return NewResponseUtil.makeSuccess(workItemMessageList);
    }

    @RequiresPermission("work-order:view-work-handle-result")
    @ApiOperation("后门工具,查看操作分类")
    @PostMapping("view-work-handle-result")
    public Response<List<WorkItemMessage>> viewWorkHandleResult() {
        HandleResultEnum[] handleResultEnums = HandleResultEnum.values();
        List<WorkItemMessage> workItemMessageList = Lists.newArrayList();
        WorkItemMessage workItem = null;
        for (HandleResultEnum handleResult : handleResultEnums) {
            workItem = new WorkItemMessage();
            workItem.setItemCode(handleResult.getType());
            workItem.setItemMessage(handleResult.getShowMsg());
            workItemMessageList.add(workItem);
        }
        return NewResponseUtil.makeSuccess(workItemMessageList);
    }

    @Data
    @NoArgsConstructor
    public static class WorkItemMessage{
        //编号
        private int itemCode;
        //前端展示的说明
        private String itemMessage;
    }


    private AdminWorkOrderSecType getCount(long userId,int orderType){

        AdminWorkOrderSecType result = new AdminWorkOrderSecType();
        //处理中
        Integer selfDoingCount = workOrderClient.getUserCount(userId,orderType, HandleResultEnum.doing.getType()+"").getData();
        result.setSelfDoingCount(selfDoingCount);
        //处理完成
        String status = HandleResultEnum.done.getType()+"";

        List<Integer> types = getDoneList(orderType);
        if (CollectionUtils.isNotEmpty(types)){
            status = Joiner.on(",").join(types);
        }

        Integer selfFinishCount = workOrderClient.getUserCount(userId,orderType,status ).getData();
        result.setSelfFinishCount(selfFinishCount);

        //延后处理
        Integer selfYanhouCount = workOrderClient.getUserCount(userId,orderType, HandleResultEnum.later_doing.getType()+"").getData();
        result.setSelfYanhouCount(selfYanhouCount);

        //达成一致
        Integer selfReachAgree = workOrderClient.getUserCount(userId, orderType, HandleResultEnum.reach_agree.getType() + "").getData();
        result.setSelfReachAgree(selfReachAgree);

        //全组未处理
        Integer teamUndoCount = workOrderClient.getAllCountV1(orderType,HandleResultEnum.undoing.getType(), userId).getData();
        result.setTeamUndoCount(teamUndoCount);


        return result;
    }

    private List<Integer> getDoneList(int orderType) {
        return workOrderDoneListCache.getDoneList(orderType);
    }

    @RequiresPermission("work-order:select-by-property-list")
    @ApiOperation("查询工单类型的属性")
    @PostMapping("select-by-property-list")
    public Response<WorkTypeProperty.UpdatePropertyParam> selectByPropertyList(@RequestParam ("firstWorkOrder") int firstWorkOrder) {
        return workOrderClient.selectByPropertyList(firstWorkOrder);
    }

    @RequiresPermission("work-order:update-work-order-type-property")
    @ApiOperation("更新工单类型的属性")
    @PostMapping("update-work-order-type-property")
    Response<String> updateWorkOrderProperty(@RequestBody WorkTypeProperty.UpdatePropertyParam param) {

        workOrderClient.updateWorkOrderProperty(param);
        return NewResponseUtil.makeSuccess("");
    }

    @RequiresPermission("work-order:get-by-case-and-types")
    @ApiOperation("根据案例Id和类型查询工单")
    @PostMapping("getByCaseAndTypes")
    Response<List<WorkOrderVO>> getByCaseAndTypes(@RequestBody() WorkOrderListQueryOldParam queryListParam) {
        if (Objects.isNull(queryListParam)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        QueryListParam listParam = queryListParam.getQueryListParam();
        if (Objects.isNull(listParam)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<Integer> orderTypeList = Splitter.on(",")
                .splitToList(listParam.getOrderType())
                .stream()
                .map(Integer::valueOf)
                .collect(Collectors.toList());

        Response<List<WorkOrderVO>> listResponse = workOrderClient.queryByCaseAndTypes(listParam.getCaseId(), orderTypeList);
        if (Objects.isNull(listResponse) || listResponse.notOk()) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }
        return NewResponseUtil.makeSuccess(listResponse.getData());
    }


    private Map<Integer, StaffRealTimeWorkData> getPersonUnHandleWork(int oneType, long userId) {
        Map<Integer, StaffRealTimeWorkData> orderTypeTData = Maps.newHashMap();
        if (oneType == OneTypeEnum.juanzhuan.getType()) {
            Response<List<StaffRealTimeWorkData>> realTimeWorkData = workOrderClient.listStaffRealTimeWorkData(userId);
            if (realTimeWorkData.notOk() || realTimeWorkData.getData() == null) {
                log.info("调用远程接口失败:{}", JSON.toJSONString(realTimeWorkData));
                return orderTypeTData;
            }
            if (realTimeWorkData.ok() && realTimeWorkData.getData() != null) {
                orderTypeTData = realTimeWorkData.getData().stream().collect(Collectors.toMap(StaffRealTimeWorkData::getOrderType, Function.identity(), (before, after) -> before));
            }
        }
        return orderTypeTData;
    }

}
