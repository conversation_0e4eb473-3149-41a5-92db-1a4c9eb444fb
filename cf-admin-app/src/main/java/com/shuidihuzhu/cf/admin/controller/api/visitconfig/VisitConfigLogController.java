package com.shuidihuzhu.cf.admin.controller.api.visitconfig;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.delegate.commonservice.ICommonServiceDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.domain.visitconfig.VisitConfigLogDO;
import com.shuidihuzhu.cf.enums.visitconfig.VisitConfigLogActionTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.vo.visitconfig.VisitConfigLogVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018-09-20  14:49
 */
@RestController
@RequestMapping("admin/cf/visit-config/log")
public class VisitConfigLogController {

    @Resource
    private ICommonServiceDelegate commonServiceDelegate;

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Resource
    private SeaAccountClientV1 seaAccountClientV1;

    @RequiresPermission("visit-config-log:list")
    @PostMapping("list")
    public Response list(@RequestParam int caseId){
        CrowdfundingInfo fundingInfo = crowdfundingDelegate.getFundingInfoById(caseId);
        String infoUuid = fundingInfo.getInfoId();
        List<VisitConfigLogDO> visitConfigLogDOS = commonServiceDelegate.listByCondition(infoUuid, Lists.newArrayList(),
                Lists.newArrayList());
        List<VisitConfigLogVO> list = visitConfigLogDOS.stream().map(this::transformDO2VO)
                .collect(Collectors.toList());

        return NewResponseUtil.makeSuccess(list);
    }

    private VisitConfigLogVO transformDO2VO(VisitConfigLogDO d){

        AdminUserAccountModel adminUserAccountModel = seaAccountClientV1.getValidUserAccountById(d.getOperatorId()).getResult();
        String name = "";
        if (adminUserAccountModel != null) {
            name = adminUserAccountModel.getName();
        }
        if (StringUtils.isBlank(name) && d.getActionType() == VisitConfigLogActionTypeEnum.SYSTEM.getValue()) {
            name = "system";
        }

        DateFormat dataFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String operatorTimeString = dataFormat.format(d.getCreateTime());

        VisitConfigLogVO v = new VisitConfigLogVO();
        v.setId(d.getId());
        v.setActionInfo(d.getActionInfo());
        v.setOperatorId(d.getOperatorId());
        v.setOperatorName(name);
        v.setOperatorTime(operatorTimeString);

        return v;
    }

    public static void main(String[] args){
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp.getTime()), ZoneOffset.ofHours(8));
        String format = localDateTime.format(formatter);

        System.out.println("format = " + format);

    }
}
