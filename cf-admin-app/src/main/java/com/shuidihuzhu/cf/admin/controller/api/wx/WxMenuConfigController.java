package com.shuidihuzhu.cf.admin.controller.api.wx;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.delegate.service.WxCommonServiceBiz;
import com.shuidihuzhu.cf.delegate.service.WxConfigServiceBiz;
import com.shuidihuzhu.cf.delegate.service.WxGroupServiceBiz;
import com.shuidihuzhu.cf.delegate.service.WxMenuServiceBiz;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.wx.grpc.client.*;
import com.shuidihuzhu.wx.grpc.client.common.WxConfig;
import com.shuidihuzhu.wx.grpc.enums.WxMpGroupTypeEnum;
import com.shuidihuzhu.wx.grpc.model.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.shuidihuzhu.wx.grpc.enums.WxMpGroupTypeEnum.WX_MENU;

@RestController
@RequestMapping(path = "/admin/cf/wx")
public class WxMenuConfigController {

    @Resource
    private WxMenuServiceBiz wxMenuServiceBiz;

    @Resource
    private WxCommonServiceBiz wxCommonServiceBiz;

    @Resource
    private WxGroupServiceBiz wxGroupServiceBiz;

    @Resource
    private WxConfigServiceBiz wxConfigServiceBiz;

    /**
     * 菜单配置的列表
     *
     * @param current
     * @param pageSize
     * @return
     */
    @RequiresPermission("menu:config-list")
    @ApiOperation(value = "获取配置列表", notes = "")
    @RequestMapping(path = "menu/config-list", method = RequestMethod.POST)
    public Response getMenuByPage(Integer current, Integer pageSize, String token) {
        // 1. wxGroupGrpcClient.getAllGroup 分页读取接口 sd_mp_group_info
        // 2. 得到组信息之后，读关联菜单的信息。 wx_menu_group_map
        // 返回值：WxGroupMenuConfigVO 列表
        List<WxGroupModel> groupModels = wxGroupServiceBiz.getGroupList(current,
                pageSize,
                WxMpGroupTypeEnum.WX_MENU);
        List<Integer> groupIds = new ArrayList<>();
        groupModels.forEach(item->{
            groupIds.add(item.getId());
        });
        List<WxGroupMenuConfigVO> wxMenuConfigs = wxMenuServiceBiz.getWxMenuConfigList(groupIds);
        Map<Integer, List<WxGroupMenuConfigVO>> groupConfigVO = wxMenuConfigs.stream()
                .collect(Collectors.groupingBy(
                        WxGroupMenuConfigVO::getGroupId));
        List<WxGroupMenuConfigVO> res = new ArrayList<>();
        groupModels.forEach(item -> {
            if (groupConfigVO.containsKey(item.getId())) {
                res.addAll(groupConfigVO.get(item.getId()));
            } else {
                res.add(new WxGroupMenuConfigVO(item.getId(), item.getName(), "", ""));
            }
        });
        res.sort(Comparator.comparing(WxGroupMenuConfigVO::getGroupId));
        Map<String, Object> result = Maps.newHashMap();
        Map<String, Object> pageNation = new HashMap<>();
        pageNation.put("pageSize", pageSize);
        pageNation.put("current", current);
        pageNation.put("total", wxGroupServiceBiz.getGroupCount(WxMpGroupTypeEnum.WX_MENU));
        result.put("pagination", pageNation);
        result.put("data", res);
        return NewResponseUtil.makeSuccess(result);
    }

    /**
     * 添加一个菜单项
     *
     * @param menuConfig
     * @return
     */
    @RequiresPermission("menu:add-config")
    @ApiOperation(value = "添加一个菜单项", notes = "", response = WxMenuResponseModel.class)
    @RequestMapping(path = "menu/add", method = RequestMethod.POST)
    public Response addMenu(String menuName, String menuConfig, String token) {
        WxMenuResponseModel response = wxMenuServiceBiz.addMenu(menuName, menuConfig);
        return NewResponseUtil.makeSuccess(response);
    }

    /**
     * 获取菜单列表
     *
     * @return
     */
    @RequiresPermission("menu:list")
    @ApiOperation(value = "获取菜单的列表", notes = "", response = WxMenuModel.class)
    @RequestMapping(path = "menu/list", method = RequestMethod.POST)
    public Response menuList(String token) {
        List<WxMenuModel> response = wxMenuServiceBiz.getMenuList(1,99);
        return NewResponseUtil.makeSuccess(response);
    }

    /**
     * 获取菜单的配置详情
     *
     * @param menuId
     * @return
     */
    @RequiresPermission("menu:detail")
    @ApiOperation(value = "获取菜单的详情", notes = "")
    @RequestMapping(path = "menu/detail", method = RequestMethod.POST)
    public Response menuDetail(String menuId, String token) {
        MenuDetailResponseDto detail = wxMenuServiceBiz.getMenuDetail(menuId);
        String jsonConfig = detail.getJsonData();
        String menuName = detail.getMenuName();
        Map<String, Object> result = Maps.newHashMap();
        result.put("menuConfig", jsonConfig);
        result.put("menuId", menuId);
        result.put("menuName", menuName);
        return NewResponseUtil.makeSuccess(result);
    }

    /**
     * 根据menuName查询菜单列表，若为空或为null则返回全部内容
     *
     * @param menuName
     * @return
     */
    @RequiresPermission("menu:list-by-name")
    @ApiOperation(value = "根据menuName获取菜单列表，若值为空或null则返回全部", notes = "")
    @RequestMapping(path = "menu/list-by-name", method = RequestMethod.POST)
    public Response getMenuListByMenuName(Integer current, Integer pageSize, @RequestParam(value = "menuName", required = false) String menuName, String token) {
        List<WxMenuModel> wxMenuModelList = null;
        if (StringUtils.isEmpty(menuName)) {
            wxMenuModelList = wxMenuServiceBiz.getMenuList(current, pageSize);
        } else {
            wxMenuModelList = wxMenuServiceBiz.getMenuListByName(current, pageSize, menuName);
        }
        List<WxMenuModelVo> menuDetailList = Lists.newArrayList();
        wxMenuModelList.forEach(item -> {
            menuDetailList.add(new WxMenuModelVo(item.getMenuId(), item.getMenuName(), wxMenuServiceBiz.getMenuDetail(item.getMenuId()).getJsonData()));
        });
       // Map<String, Object> pageNation = PageUtil.transform2PageMap(menuDetailList);
        Map<String, Object> pageNation = new HashMap<>();
        pageNation.put("current", current);
        pageNation.put("total", wxMenuServiceBiz.menuCount());
        pageNation.put("pageSize", 10);
        Map<String, Object> result = Maps.newHashMap();
        result.put("pagination", pageNation);
        result.put("data", menuDetailList);
        return NewResponseUtil.makeSuccess(result);
    }

    /**
     * 修改菜单
     *
     * @param menuId
     * @param menuName
     * @param menuConfig
     * @return
     */
    @RequiresPermission("menu:edit")
    @ApiOperation(value = "修改菜单", notes = "")
    @RequestMapping(path = "menu/edit", method = RequestMethod.POST)
    public Response editMenu(String menuId, String menuName, String menuConfig, String token) {
        WxMenuResponseModel response = wxMenuServiceBiz.editMenu(menuId, menuName, menuConfig);
        return NewResponseUtil.makeSuccess(response);
    }

    /**
     * 公众号的菜单配置信息
     * 返回：组包含的公众号，组对应的菜单和个性化菜单
     *
     * @param groupId
     * @return
     */
    @RequiresPermission("menu:group-config-detail")
    @ApiOperation(value = "获取组的配置详情", notes = "")
    @RequestMapping(path = "group/config-detail", method = RequestMethod.POST)
    public Response groupConfigDetail(int groupId, String token) {
        WxGroupMenuConfigModel configModel = wxMenuServiceBiz.getWxGroupMenuConfig(groupId);

        List<WxMpGroupMapModel> wxMpGroupMapModels = wxGroupServiceBiz.getGroupDetail(groupId);

        List<Map<String, String>> mpList = Lists.newArrayList();
        wxMpGroupMapModels.forEach(item -> {
            Map<String, String> mpValue = Maps.newHashMap();
            mpValue.put("thirdType", String.valueOf(item.getThirdType()));

            WxConfigModel wxConfigModel = wxConfigServiceBiz.getWxConfigByThirdType(item.getThirdType());
            if (wxConfigModel != null) {
                mpValue.put("name", wxConfigModel.getName());
            }
            mpList.add(mpValue);
        });

        Map<String, Object> result = Maps.newHashMap();
        result.put("mplist", mpList);
        if (null != configModel) {
            result.put("menuId", configModel.getMenuId());
            result.put("personalizeMenu", configModel.getPersonalizeMenuConfigModelList());
        }

        List<WxGroupModel> groupModels = wxGroupServiceBiz.getAllGroup(WX_MENU);
        Map<Integer, List<WxGroupModel>> groupModelMap =
                groupModels.stream()
                        .collect(
                                Collectors.groupingBy(WxGroupModel::getId));
        List<WxGroupModel> groupModelList = groupModelMap.get(groupId);
        if (!CollectionUtils.isEmpty(groupModelList)) {
            result.put("groupName", groupModelList.get(0).getName());
        }

        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("menu:tag-list")
    @ApiOperation(value = "获取tag的列表", notes = "", response = WxTagModel.class)
    @RequestMapping(path = "tag/list", method = RequestMethod.POST)
    public Response tagList(String token, int groupId) {
        List<WxTagModel> response = wxCommonServiceBiz.getTagList(groupId);
        return NewResponseUtil.makeSuccess(response);
    }

    @RequiresPermission("menu:group-list")
    @ApiOperation(value = "获取公众号组", notes = "返回的是个数组", response = WxGroupModel.class)
    @RequestMapping(path = "group/list", method = RequestMethod.POST)
    public Response groupList(String token,
                              @RequestParam(required = false, defaultValue = "WX_MENU") WxMpGroupTypeEnum type) {
        List<WxGroupModel> groups = wxGroupServiceBiz.getAllGroup(type);
        return NewResponseUtil.makeSuccess(groups);
    }

    /**
     * 返回组里面包含的公众号列表
     *
     * @param groupId
     * @return
     */
    @RequiresPermission("menu:group-mplist")
    @ApiOperation(value = "获取公众号组里面公众号列表", notes = "返回的是个数组", response = WxMpGroupMapModel.class)
    @RequestMapping(path = "group/mplist", method = RequestMethod.POST)
    public Response groupDetail(int groupId, String token) {
        List<WxMpGroupMapModel> groupMaps = wxGroupServiceBiz.getGroupDetail(groupId);
        return NewResponseUtil.makeSuccess(groupMaps);
    }

    /**
     * 提交菜单给微信
     *
     * @param groupId
     * @param menuId
     * @param personalizeMenu
     * @return
     */
    @RequiresPermission("menu:create")
    @ApiOperation(value = "提交菜单和公众号组的关联，微信生效", notes = "", response = CommonAddResponseModel.class)
    @RequestMapping(path = "menu/create", method = RequestMethod.POST)
    public Response createWxMenu(int groupId, String menuId, String personalizeMenu, String token) {
        List<PersonalizeMenuConfigModel> menuList = JSON.parseArray(personalizeMenu, PersonalizeMenuConfigModel.class);//已检查过
        CommonAddResponseModel responseModel = wxMenuServiceBiz.createWxMenu(groupId, menuId, menuList);
        return NewResponseUtil.makeResponse(responseModel.getCode(),
                responseModel.getMessage(),
                responseModel.getId());
    }
}

