package com.shuidihuzhu.cf.admin.controller.api.crowdfunding.newreport;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfFundUseAuditDao;
import com.shuidihuzhu.cf.dao.crowdfunding.NewAdminCfFundUseAuditDao;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminCrowdfundingProgress;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.biz.crowdfunding.report.AdminCfInfoLostContactService;
import com.shuidihuzhu.cf.biz.crowdfunding.report.CfReportOfficialLetterBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.report.ReportWorkOrderFollowActionBiz;
import com.shuidihuzhu.cf.biz.es.CfSearch;
import com.shuidihuzhu.cf.client.adminpure.constants.ReportCons;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingReportDao;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.finance.IFinanceDelegate;
import com.shuidihuzhu.cf.enhancer.model.response.EhResponse;
import com.shuidihuzhu.cf.enhancer.utils.EhResponseUtils;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.dao.crowdfunding.CfLabelRiskDAO;
import com.shuidihuzhu.cf.enums.ReportFollowActionEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.enums.report.ReportPayMethodEnum;
import com.shuidihuzhu.cf.finance.model.vo.CfDrawCashApplyVo;
import com.shuidihuzhu.cf.model.admin.ReportWorkOrderVO;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReportLabel;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.report.BaseCaseReportInfoVo;
import com.shuidihuzhu.cf.model.report.CfReportOfficialLetter;
import com.shuidihuzhu.cf.model.report.ReportWorkOrderFollowAction;
import com.shuidihuzhu.cf.model.report.schedule.ReportScheduleVO;
import com.shuidihuzhu.cf.service.FinanceApproveService;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.crowdfunding.report.CfReportService;
import com.shuidihuzhu.cf.service.crowdfunding.report.transform.handler.IReportTransformHandler;
import com.shuidihuzhu.cf.service.crowdfunding.report.transform.ReportTransformParam;
import com.shuidihuzhu.cf.service.crowdfunding.report.transform.ReportTransformService;
import com.shuidihuzhu.cf.service.report.ReportOperationService;
import com.shuidihuzhu.cf.service.report.ReportScheduleService;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderIndexSearchResult;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderModel;
import com.shuidihuzhu.client.cf.workorder.*;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.model.vo.WorkOrderRecordVO;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2019/12/30 下午4:12
 * @desc
 */
@Slf4j
@RestController
@RequestMapping(path = "/admin/crowdfunding/newreport")
public class AdminNewReportController {

    @Autowired
    private CfReportWorkOrderClient reportWorkOrderClient;

    @Autowired
    private AdminCrowdfundingReportBiz crowdfundingReportBiz;

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private CrowdfundingFeignClient client;

    @Autowired
    private AdminCfRepeatInfoBiz repeatInfoBiz;

    @Autowired
    private AdminCrowdfundingReportDao adminCrowdfundingReportDao;

    @Resource
    private SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    private AdminCfInfoLostContactService lostContactService;

    @Autowired
    private IFinanceDelegate financeDelegate;

    @Autowired
    private CfSearch cfSearch;

    @Autowired
    private CfWorkOrderRecordClient cfWorkOrderRecordClient;

    @Autowired
    private AdminCrowdfundingReportBiz adminCrowdfundingReportBiz;

    @Autowired
    private CfLabelRiskDAO cfLabelRiskDAO;

    @Autowired
    private FinanceApproveService financeApproveService;

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private ApproveRemarkOldService approveRemarkOldService;

    @Autowired
    private AdminCfReportAddTrustBiz adminCfReportAddTrustBiz;

    @Autowired
    private CfReportService cfReportService;

    @Autowired
    private CfWorkOrderStaffClient cfWorkOrderStaffClient;

    @Autowired
    private CfReportOfficialLetterBiz cfReportOfficialLetterBiz;
    @Autowired
    private ReportWorkOrderFollowActionBiz reportWorkOrderFollowActionBiz;
    @Resource(name = "cf2RedissonHandler")
    private RedissonHandler cf2RedissonHandler;
    @Autowired
    private IAdminCredibleInfoService iAdminCredibleInfoService;

    @Autowired
    private ReportOperationService reportOperationService;

    @Autowired
    private ReportTransformService reportTransformService;

    @Autowired
    private ReportScheduleService reportScheduleService;

    @Resource
    private IRiskDelegate iRiskDelegate;
    @Autowired
    private NewAdminCfFundUseAuditDao adminCfFundUseAuditDao;

    @Autowired
    private CfWorkOrderTypeFeignClient cfWorkOrderTypeFeignClient;

    private static final List<Integer> REPORT_WORK_TYPE_LIST = Lists.newArrayList(WorkOrderType.casefirstreport.getType(),
            WorkOrderType.casehistoryreport.getType(), WorkOrderType.up_grade_second.getType(), WorkOrderType.lost_report.getType(),
            WorkOrderType.report_instead_input.getType(), WorkOrderType.report_split_draw.getType()
    );

    @RequestMapping(path = "reach-agree", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("newreport:reach-agree")
    public Response<String> reachAgree(@RequestParam(value = "caseId") int caseId, @RequestParam(value = "handleResult") int handleResult){

        AdminErrorCode errorCode = validate(caseId);
        if(AdminErrorCode.SUCCESS != errorCode){
            return NewResponseUtil.makeError(errorCode);
        }

        HandleResultEnum handleResultEnum = HandleResultEnum.getFromType(handleResult);
        if(HandleResultEnum.reach_agree != handleResultEnum){
            return NewResponseUtil.makeError(AdminErrorCode.REPORT_HANDLE_RESULT_ERROR);
        }
        Response<List<WorkOrderVO>> workOrderRes = cfWorkOrderClient.queryByCaseAndTypes(caseId,WorkOrderType.REPORT_TYPES);
        if(ErrorCode.SUCCESS.getCode() != workOrderRes.getCode() || CollectionUtils.isEmpty(workOrderRes.getData())){
            return NewResponseUtil.makeError(AdminErrorCode.GET_WORK_ORDER_FAILED);
        }

        List<WorkOrderVO> workOrderVOS = workOrderRes.getData();

        long doingWorkOrder = workOrderVOS.stream().filter(w -> w.getHandleResult() == HandleResultEnum.doing.getType()).count();
        long undoingWorkOrder = workOrderVOS.stream().filter(w -> w.getHandleResult() == HandleResultEnum.undoing.getType()).count();
        long laterDoingWorkOrder = workOrderVOS.stream().filter(w -> w.getHandleResult() == HandleResultEnum.later_doing.getType()).count();

        if(0 == doingWorkOrder && 0 == undoingWorkOrder && 0 == laterDoingWorkOrder){
            return NewResponseUtil.makeSuccess(AdminErrorCode.REPORT_REACH_OVER_ERROR.getMsg());
        }

        Optional<WorkOrderVO> optional = workOrderVOS.stream().filter(w -> w.getHandleResult() == HandleResultEnum.doing.getType()
                || w.getHandleResult() == HandleResultEnum.later_doing.getType()).findFirst();
        if (!optional.isPresent()) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_OPERATION_FAILED);
        }

        WorkOrderVO workOrderVO = optional.get();
        if (workOrderVO.getOrderType() == WorkOrderType.lost_report.getType()){
            return NewResponseUtil.makeError(AdminErrorCode.REPORT_LOST_NOT_MODIFY);
        }

        ReportHandleOrderParam param = new ReportHandleOrderParam();
        param.setCaseId(caseId);
        param.setWorkOrderId(workOrderVO.getWorkOrderId());
        param.setHandleResult(HandleResultEnum.reach_agree.getType());
        param.setOperComment(OrderExtName.reachAgreeTime.getDesc());
        param.setUserId(ContextUtil.getAdminUserId());
        param.setOrderType(workOrderVO.getOrderType());

        Response response = reportWorkOrderClient.handleReport(param);

        if(ErrorCode.SUCCESS.getCode() != response.getCode()){
            return ResponseUtil.makeSuccess(response.getCode(), response.getMsg());
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        financeApproveService.addApprove(crowdfundingInfo, "达成一致", "达成一致", ContextUtil.getAdminUserId());

        return ResponseUtil.makeSuccess("工单状态已变成达成一致");
    }

    @RequestMapping(path = "end-deal", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("newreport:end-deal")
    public Response<String> endDeal(@RequestParam(value = "caseId") int caseId, @RequestParam(value = "handleResult") int handleResult){

        AdminErrorCode errorCode = validate(caseId);
        if(AdminErrorCode.SUCCESS != errorCode){
            return NewResponseUtil.makeError(errorCode);
        }
        int adminUserId = ContextUtil.getAdminUserId();

        HandleResultEnum handleResultEnum = HandleResultEnum.getFromType(handleResult);
        if(HandleResultEnum.end_deal != handleResultEnum){
            return NewResponseUtil.makeError(AdminErrorCode.REPORT_HANDLE_RESULT_ERROR);
        }
        Response<List<WorkOrderVO>> workOrderRes = cfWorkOrderClient.queryByCaseAndTypes(caseId,WorkOrderType.REPORT_TYPES);
        if(ErrorCode.SUCCESS.getCode() != workOrderRes.getCode() || CollectionUtils.isEmpty(workOrderRes.getData())){
            return NewResponseUtil.makeError(AdminErrorCode.GET_WORK_ORDER_FAILED);
        }

        List<WorkOrderVO> workOrderVOS = workOrderRes.getData();

        long doneWorkOrder = workOrderVOS.stream().filter(w -> (w.getHandleResult() == HandleResultEnum.noneed_deal.getType()
                || w.getHandleResult() == HandleResultEnum.end_deal.getType())
                || w.getHandleResult() == HandleResultEnum.end_deal_upgrade.getType()
                || w.getHandleResult() == HandleResultEnum.end_deal_lost.getType()).count();
        if(doneWorkOrder == workOrderVOS.size()){
            return NewResponseUtil.makeSuccess(AdminErrorCode.REPORT_ALL_END_DEAL_ERROR.getMsg());
        }

        /**
         * 达成一致状态首次工单、历史工单、二线工单有
         * 失联工单没有达成一致状态
         */
        Optional<WorkOrderVO> optional = workOrderVOS.stream().filter(w -> w.getHandleResult() == HandleResultEnum.reach_agree.getType() || w.getHandleResult() == HandleResultEnum.later_doing.getType()).findFirst();
        if(optional.isEmpty()){
            optional = workOrderVOS.stream().filter(w -> w.getHandleResult() == HandleResultEnum.doing.getType()).findFirst();
        }
        if (optional.isEmpty()) {
            return NewResponseUtil.makeSuccess(AdminErrorCode.REPORT_NO_ASSIGN_ORDER_IGNORE_END_DEAL.getMsg());
        }

        WorkOrderVO workOrderVO = optional.get();

        ReportHandleOrderParam param = new ReportHandleOrderParam();
        param.setCaseId(caseId);
        param.setWorkOrderId(workOrderVO.getWorkOrderId());
        param.setHandleResult(HandleResultEnum.end_deal.getType());
        param.setOperComment(OrderExtName.endDealTime.getDesc());
        param.setUserId(adminUserId);
        param.setOrderType(workOrderVO.getOrderType());

        Response response = reportWorkOrderClient.handleReport(param);

        if(ErrorCode.SUCCESS.getCode() != response.getCode()){
            return ResponseUtil.makeSuccess(response.getCode(), response.getMsg());
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        financeApproveService.addApprove(crowdfundingInfo, "结束处理", "结束处理", adminUserId);
        reportOperationService.operation(caseId, ReportCons.ActionType.AuditDone, adminUserId);

        return ResponseUtil.makeSuccess("工单状态已变成处理完成-结束处理");
    }

    @RequestMapping(path = "noneed-deal", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("newreport:noneed-deal")
    public Response<String> noneedDeal(@RequestParam(value = "caseId") int caseId, @RequestParam(value = "handleResult") int handleResult){

        int adminUserId = ContextUtil.getAdminUserId();
        AdminErrorCode errorCode = validate(caseId);
        if(AdminErrorCode.SUCCESS != errorCode){
            return NewResponseUtil.makeError(errorCode);
        }

        HandleResultEnum handleResultEnum = HandleResultEnum.getFromType(handleResult);
        if(HandleResultEnum.noneed_deal != handleResultEnum){
            return NewResponseUtil.makeError(AdminErrorCode.REPORT_HANDLE_RESULT_ERROR);
        }
        Response<List<WorkOrderVO>> workOrderRes = cfWorkOrderClient.queryByCaseAndTypes(caseId, WorkOrderType.REPORT_TYPES);
        if(ErrorCode.SUCCESS.getCode() != workOrderRes.getCode() || CollectionUtils.isEmpty(workOrderRes.getData())){
            return NewResponseUtil.makeError(AdminErrorCode.GET_WORK_ORDER_FAILED);
        }

        List<WorkOrderVO> workOrderVOS = workOrderRes.getData();
        long doneWorkOrder = workOrderVOS.stream().filter(w -> (w.getHandleResult() == HandleResultEnum.noneed_deal.getType() || w.getHandleResult() == HandleResultEnum.end_deal.getType())).count();
        if(doneWorkOrder == workOrderVOS.size()){
            return NewResponseUtil.makeSuccess(AdminErrorCode.REPORT_ALL_END_DEAL_ERROR.getMsg());
        }

        Optional<WorkOrderVO> optional = workOrderVOS.stream().filter(w -> (w.getHandleResult() == HandleResultEnum.doing.getType()
                || w.getHandleResult() == HandleResultEnum.reach_agree.getType()
                || w.getHandleResult() == HandleResultEnum.later_doing.getType())).findFirst();
        if (!optional.isPresent()) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_OPERATION_FAILED);
        }

        WorkOrderVO workOrderVO = optional.get();
        if (workOrderVO.getOrderType() == WorkOrderType.lost_report.getType()){
            return NewResponseUtil.makeError(AdminErrorCode.REPORT_LOST_NOT_MODIFY);
        }

        ReportHandleOrderParam param = new ReportHandleOrderParam();
        param.setCaseId(caseId);
        param.setWorkOrderId(workOrderVO.getWorkOrderId());
        param.setHandleResult(HandleResultEnum.noneed_deal.getType());
        param.setOperComment(OrderExtName.noDealTime.getDesc());
        param.setUserId(adminUserId);
        param.setOrderType(workOrderVO.getOrderType());

        Response response = reportWorkOrderClient.handleReport(param);

        if(ErrorCode.SUCCESS.getCode() != response.getCode()){
            return ResponseUtil.makeSuccess(response.getCode(), response.getMsg());
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        financeApproveService.addApprove(crowdfundingInfo, "无需处理", "无需处理", adminUserId);
        reportOperationService.operation(caseId, ReportCons.ActionType.AuditDone, adminUserId);

        return ResponseUtil.makeSuccess("工单状态已变成处理完成-无需处理");
    }


    @RequestMapping(path = "report-orderlist", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @RequiresPermission("newreport:report-orderlist")
    public Response<PageResult<ReportWorkOrderVO>> reportWorkOrderList(@RequestParam("param") String param){
        long adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        WorkOrderListParam reportParam = JSON.parseObject(param, WorkOrderListParam.class);//已检查过
        if(Objects.isNull(reportParam)){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        if(!REPORT_WORK_TYPE_LIST.contains(reportParam.getOrderType())){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        reportParam.setUserId(ContextUtil.getAdminUserId());

        Response<PageResult<WorkOrderVO>> response = reportWorkOrderClient.reportOrderlist(reportParam);
        if (Objects.isNull(response) || response.notOk() || Objects.isNull(response.getData())){
            return ResponseUtil.makeResponse(response.getCode(), response.getMsg(), null);
        }

        PageResult<WorkOrderVO> pageResult = response.getData();

        List<WorkOrderVO> list = pageResult.getPageList();

        if (CollectionUtils.isEmpty(list)){
            PageResult<ReportWorkOrderVO> result = new PageResult<>();
            result.setHasNext(pageResult.isHasNext());
            result.setPageList(Lists.newArrayList());
            return NewResponseUtil.makeSuccess(result);
        }

        List<ReportWorkOrderVO> reportWorkOrderList = build(list);

        PageResult<ReportWorkOrderVO> result = new PageResult<>();
        result.setHasNext(pageResult.isHasNext());
        result.setPageList(reportWorkOrderList);

        return NewResponseUtil.makeSuccess(result);
    }


    /**
     * 检查工单是否仍属于本人处理，防止批量转移后上一个操作人仍能操作
     */
    @RequestMapping(path = "report-work-check", method = RequestMethod.POST)
    @RequiresPermission("newreport:report-work-check")
    public Response<Boolean> reportWorkCheck(@RequestParam("workOrderId") long workOrderId) {
        int adminUserId = ContextUtil.getAdminUserId();
        Response<WorkOrderVO> workOrderResponse = cfWorkOrderClient.getWorkOrderById(workOrderId);
        if (workOrderResponse.notOk()|| workOrderResponse.getData() == null) {
            return NewResponseUtil.makeFail("调用工单系统异常!");
        }
        WorkOrderVO data = workOrderResponse.getData();
        if (data.getOperatorId() != adminUserId) {
            return NewResponseUtil.makeSuccess(false);
        }
        return NewResponseUtil.makeSuccess(true);
    }

    /**
     * 接口缺少对员工是否在线状态的判断，建议使用
     * @see com.shuidihuzhu.cf.admin.controller.api.workorder.WorkOrderStaffController#getStaffStatusList
     */
    @RequestMapping(path = "query-staff-by-type", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @RequiresPermission("newreport:query-staff-by-type")
    public Response<List<AdminUserAccountModel>> queryStaffByType(@RequestParam(value = "orderTypes") String orderTypes){

        List<Integer> orderTypeList = Arrays.stream(orderTypes.split(",")).map(Integer::valueOf).collect(Collectors.toList());


        List<AdminUserAccountModel> adminUserAccountModels = Lists.newArrayList();

        for (Integer orderType : orderTypeList){
            Response<WorkOrderTypeRecord> resp = cfWorkOrderTypeFeignClient.getByOrderTypeCode(orderType);
            if (resp.notOk() || resp.getData() == null) {
                return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
            }
            WorkOrderTypeRecord workOrderType = resp.getData();
            List<AdminUserAccountModel> res = seaAccountClientV1.getUsersHavePermission(workOrderType.getPermission()).getResult();
            if(CollectionUtils.isNotEmpty(res)){
                adminUserAccountModels.addAll(res);
            }
        }

        if (CollectionUtils.isEmpty(adminUserAccountModels)){
            return ResponseUtil.makeSuccess(Lists.newArrayList());
        }

        List<Integer> userIds = adminUserAccountModels.stream().map(r->r.getId()).distinct().collect(Collectors.toList());

        adminUserAccountModels.stream().filter(accout -> userIds.contains(accout.getId())).collect(Collectors.toList());

        return ResponseUtil.makeSuccess(adminUserAccountModels);
    }

    @RequestMapping(path = "query-report-workorder", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @RequiresPermission("newreport:query-report-workorder")
    public Response<Map<String, Object>> reportWorkOrderList(@RequestParam(defaultValue = "1") int current,
                                                             @RequestParam(defaultValue = "20") int pageSize,
                                                             @RequestParam(value = "workOrderId", required = false) long workOrderId,
                                                             @RequestParam(value = "caseId", required = false) int caseId,
                                                             @RequestParam(value = "operatorId", required = false) long operatorId,
                                                             @RequestParam(value = "mobile", required = false) String mobile,
                                                             @RequestParam(value = "originatorMobile", required = false) String originatorMobile,
                                                             @RequestParam(value = "orderTypes", required = false) String orderTypes,
                                                             @RequestParam(value = "handleResult", required = false) String handleResult,
                                                             @RequestParam(value = "lostStatus", required = false) String lostStatus,
                                                             @RequestParam(value = "supplyAuditStatus", required = false) String supplyAuditStatus,
                                                             @RequestParam(value = "highRisk", required = false) String highRisk,
                                                             @RequestParam(value = "letterStatus",required = false)String letterStatus,
                                                             @RequestParam(value = "startHandleTime", required = false) String startHandleTime,
                                                             @RequestParam(value = "endHandleTime", required = false) String endHandleTime,
                                                             @RequestParam(value = "credibleStatus", required = false, defaultValue = "0") int credibleStatus,
                                                             @RequestParam(value = "paymentMethod", required = false, defaultValue = "0") int paymentMethod,
                                                             @RequestParam(value = "asrResult", required = false) String asrResult) {

        long adminUserId = ContextUtil.getAdminLongUserId();
        if(adminUserId <= 0L){
            return NewResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        if(caseId > 0 && Objects.isNull(crowdfundingDelegate.getCfInfoSimpleModelById(caseId))){
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        List<Integer> orderTypeList = WorkOrderType.REPORT_TYPES;
        if(StringUtils.isNotEmpty(orderTypes)){
            orderTypeList = Arrays.stream(orderTypes.split(",")).map(Integer::valueOf).collect(Collectors.toList());
        }

        List<Integer> handleResultList = Lists.newArrayList();
        if(StringUtils.isNotEmpty(handleResult)){
            handleResultList = Arrays.stream(handleResult.split(",")).map(Integer::valueOf).collect(Collectors.toList());
        }

        List<Integer> lostStatusList = Lists.newArrayList();
        if(StringUtils.isNotEmpty(lostStatus)){
            lostStatusList = Arrays.stream(lostStatus.split(",")).map(Integer::valueOf).collect(Collectors.toList());
        }

        List<Integer> supplyAuditStatusList = Lists.newArrayList();
        if(StringUtils.isNotEmpty(supplyAuditStatus)){
            supplyAuditStatusList = Arrays.stream(supplyAuditStatus.split(",")).map(Integer::valueOf).collect(Collectors.toList());
        }

        List<Integer> highRiskList = Lists.newArrayList();
        if(StringUtils.isNotEmpty(highRisk)){
            highRiskList = Arrays.stream(highRisk.split(",")).map(Integer::valueOf).collect(Collectors.toList());
        }
        List<Integer> letterStatusList = Lists.newArrayList();
        if (StringUtils.isNotEmpty(letterStatus)){
            letterStatusList = Arrays.stream(letterStatus.split(",")).map(Integer::valueOf).collect(Collectors.toList());
        }

        CfWorkOrderIndexSearchResult searchResult = cfSearch.queryWorkOrderBySearch(current, pageSize, workOrderId, caseId,
                operatorId, mobile, originatorMobile, orderTypeList, handleResultList, lostStatusList, supplyAuditStatusList,
                highRiskList, letterStatusList, startHandleTime, endHandleTime, credibleStatus, paymentMethod, asrResult);

        if(Objects.isNull(searchResult) ||searchResult.getTotal() <= 0 || CollectionUtils.isEmpty(searchResult.getModels())){
            Map<String, Object> pageMap = Maps.newHashMap();
            pageMap.put("current", current);
            pageMap.put("pageSize", pageSize);
            pageMap.put("total", 0);

            Map<String, Object> resultMap = Maps.newHashMap();
            resultMap.put("pagination", pageMap);
            resultMap.put("data", Lists.newArrayList());

            return NewResponseUtil.makeSuccess(resultMap);
        }


        List<Long> workOrderIds = searchResult.getModels().stream().map(CfWorkOrderModel::getWorkOrderId).collect(Collectors.toList());

        Response<List<WorkOrderVO>> workOrderVoRes = reportWorkOrderClient.queryReportByIds(workOrderIds);
        if (ErrorCode.SUCCESS.getCode() != workOrderVoRes.getCode() || CollectionUtils.isEmpty(workOrderVoRes.getData())){
            return NewResponseUtil.makeResponse(workOrderVoRes.getCode(), workOrderVoRes.getMsg(), null);
        }



        List<ReportWorkOrderVO> reportWorkOrderList = build(workOrderVoRes.getData());

        //倒叙排序 配置search顺序返回
        reportWorkOrderList = reportWorkOrderList.stream().sorted(Comparator.comparing(WorkOrderVO::getWorkOrderId).reversed()).collect(Collectors.toList());

        Map<String, Object> pageMap = Maps.newHashMap();
        pageMap.put("current", current);
        pageMap.put("pageSize", pageSize);
        pageMap.put("total", searchResult.getTotal());

        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("pagination", pageMap);
        resultMap.put("data", reportWorkOrderList);

        return NewResponseUtil.makeSuccess(resultMap);
    }

    @RequestMapping(path = "report-order-transfer",method = RequestMethod.POST)
    @RequiresPermission("newreport:report-order-transfer")
    public Response<String> reportWorkOrderTransfer(@RequestParam("ids") String ids,
                                                    @RequestParam("orderType") int orderType,
                                                    @RequestParam("recipientId") int recipientId){

        if (StringUtils.isBlank(ids) || orderType <= 0 || recipientId<=0 ){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<Long> list = Arrays.stream(ids.split(",")).map(Long::valueOf).collect(Collectors.toList());
        int operatorId = ContextUtil.getAdminUserId();

        return cfWorkOrderClient.reportTransfer(list,orderType,operatorId,recipientId);

    }


    @RequestMapping(path = "transfer-record",method = RequestMethod.POST)
    @RequiresPermission("newreport:transfer-record")
    public Response<List<WorkOrderRecordVO>> reportWorkOrderTransfer(@RequestParam("workOrderId") long workOrderId){
        Response<List<WorkOrderRecordVO>> response = cfWorkOrderRecordClient.listByWorkOrderId(workOrderId);

        List<WorkOrderRecordVO> list = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());

        //10在工单记录里面代表转移
       return NewResponseUtil.makeSuccess(list.stream().filter(r-> (r.getOperateMode() == 10 || r.getOperateMode() == 2)).collect(Collectors.toList()));
    }

    @RequestMapping(path = "/label-risk",method = RequestMethod.POST)
    @RequiresPermission("newreport:label-risk")
    public Response<CfLabelRiskDO> labelRisk(@RequestParam("caseId") int caseId, @RequestParam("riskType") int riskType){

        int adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if(Objects.isNull(crowdfundingInfo)){
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        log.info("newLabelRisk caseId:{},riskType:{},adminUserId:{}", caseId, riskType, adminUserId);

        CfLabelRiskDO labelRiskDO = cfLabelRiskDAO.query(caseId);
        if(Objects.nonNull(labelRiskDO) && labelRiskDO.getRiskType() == riskType){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        AdminWorkOrderReportConst.CaseRisk caseRisk = AdminWorkOrderReportConst.CaseRisk.getByCode(riskType);
        if(Objects.isNull(caseRisk)){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        String operation = caseRisk == AdminWorkOrderReportConst.CaseRisk.DEFAULT ? "取消标记高危" : "标记高危";
        String comment = caseRisk == AdminWorkOrderReportConst.CaseRisk.DEFAULT ? "案例被取消标记高危" : "将案例标记为高危";
        financeApproveService.addApprove(crowdfundingInfo, operation, comment, adminUserId);

        if(Objects.isNull(labelRiskDO)){
            labelRiskDO = new CfLabelRiskDO();
            labelRiskDO.setCaseId(caseId);
            labelRiskDO.setRiskType(caseRisk.getCode());
            labelRiskDO.setOperatorId(Long.valueOf(adminUserId));
            cfLabelRiskDAO.insert(labelRiskDO);

            return NewResponseUtil.makeSuccess(labelRiskDO);
        }

        labelRiskDO.setRiskType(caseRisk.getCode());
        labelRiskDO.setOperatorId(Long.valueOf(adminUserId));
        cfLabelRiskDAO.update(labelRiskDO);
        return NewResponseUtil.makeSuccess(labelRiskDO);
    }

    @RequestMapping(path = "query-label-risk-status",method = RequestMethod.POST)
    @RequiresPermission("newreport:query-label-risk-status")
    public Response<CfLabelRiskDO> queryLabelRiskStatus(@RequestParam("caseId") int caseId){
        long adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);

        if(Objects.isNull(crowdfundingInfo)){
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        CfLabelRiskDO labelRiskDO = cfLabelRiskDAO.query(caseId);

        return NewResponseUtil.makeSuccess(labelRiskDO);
    }

    @RequestMapping(path = "fund-recovery",method = RequestMethod.POST)
    @RequiresPermission("newreport:fund-recovery")
    public Response<String> fundRecovery(@RequestParam("caseId") int caseId, @RequestParam("content") String content){

        int adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if(Objects.isNull(crowdfundingInfo)){
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        if(StringUtils.isEmpty(content) || content.length() > 500){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        financeApproveService.addApprove(crowdfundingInfo, "款项追回", "原因及进度:" + content, adminUserId);

        return NewResponseUtil.makeSuccess("success");
    }

    private List<ReportWorkOrderVO> build(List<WorkOrderVO> workOrderVOS){
        if(CollectionUtils.isEmpty(workOrderVOS)){
            return Lists.newArrayList();
        }

        List<Integer> caseIds = workOrderVOS.stream().map(WorkOrderVO::getCaseId).collect(Collectors.toList());
        FeignResponse<List<CrowdfundingInfo>> feignResponse = client.getCrowdfundingListById(caseIds);
        if (feignResponse.notOk() || CollectionUtils.isEmpty(feignResponse.getData())){
            return Lists.newArrayList();
        }
        Map<Integer, CfFirsApproveMaterial> firsApproveMaterialMap = iRiskDelegate.getMapByInfoIds(caseIds);
        Set<Integer> fundUseProgressIds = workOrderVOS.stream()
                .map(WorkOrderVO::getFundUseProgressId)
                .filter(integer -> Objects.requireNonNullElse(integer, 0) > 0)
                .collect(Collectors.toSet());

        Map<Integer, AdminCrowdfundingProgress> crowdfundingProgressMap = CollectionUtils.isNotEmpty(fundUseProgressIds) ?
                adminCfFundUseAuditDao.selectByProgressIdList(new ArrayList<>(fundUseProgressIds))
                .stream()
                .collect(Collectors.toMap(AdminCrowdfundingProgress::getId, Function.identity(), (x, y) -> x))
                : new HashMap<>();

        List<CrowdfundingInfo> caseList = feignResponse.getData();
        Map<Integer,CrowdfundingInfo> caseMap = caseList.stream().collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity()));

        List<ReportWorkOrderVO> reportWorkOrderVOS = workOrderVOS.stream().map(l -> {

            CrowdfundingInfo info = caseMap.get(l.getCaseId());
            List<CrowdfundingReport> reportList = crowdfundingReportBiz.getListByInfoId(l.getCaseId());
            List<Integer> reportIds = Lists.newArrayList();
            List<CrowdfundingReport> reports = Lists.newArrayList();
            List<CrowdfundingReportLabel> labelsList = Lists.newArrayList();
            if(StringUtils.isNotEmpty(l.getReportIds())){
                reportIds = Arrays.stream(l.getReportIds().split(",")).map(Integer::valueOf).collect(Collectors.toList());
                reports = adminCrowdfundingReportDao.getListByReportIds(reportIds);
                labelsList = adminCrowdfundingReportBiz.getReportLabels(reportIds);

            }
            Optional<CrowdfundingReport> r = reports.stream().filter(report -> 1 == report.getRealNameReport()).findFirst();
            boolean realNameReport = r.isPresent();


            String labels = labelsList.stream().map(CrowdfundingReportLabel::getReportLabel).map(CfReportTypeEnum::getDescFromCode).collect(Collectors.joining(","));

            AdminUserAccountModel userAccount = seaAccountClientV1.getValidUserAccountById(Integer.valueOf(String.valueOf(l.getOperatorId()))).getResult();

            int caseId = info.getId();
            CfDrawCashApplyVo drawCashApplyVo = financeDelegate.getApplyInfo(caseId).getData();

            int newReport = Objects.nonNull(l.getHandleTime()) && l.getHandleTime().before(new Date(new DateTime().dayOfMonth().roundFloorCopy().getMillis())) ? 0 : 1 ;

            CfLabelRiskDO labelRiskDO = cfLabelRiskDAO.query(l.getCaseId());
            boolean highRisk = Objects.nonNull(labelRiskDO) && labelRiskDO.getRiskType() == AdminWorkOrderReportConst.CaseRisk.HIGH_RISK.getCode();

            List<CrowdfundingApprove> approves = approveRemarkOldService.listByCaseId(l.getCaseId());
            CrowdfundingApprove lastApprove = CollectionUtils.isNotEmpty(approves) ? approves.get(approves.size() - 1) : null;

            CfReportAddTrust reportAddTrust = adminCfReportAddTrustBiz.getByInfoUuid(info.getInfoId());

            List<CfReportOfficialLetter> cfReportOfficialLetter = cfReportOfficialLetterBiz.getLastByCaseId(l.getCaseId());
            List<Integer> cfLetterStatus = null;
            if (CollectionUtils.isNotEmpty(cfReportOfficialLetter)) {
                cfLetterStatus = cfReportOfficialLetter.stream()
                        .map(CfReportOfficialLetter::getLetterStatus)
                        .distinct()
                        .collect(Collectors.toList());
            }

            CfCredibleInfoDO cfCredibleInfoDO = iAdminCredibleInfoService.getLastOneByCaseId(l.getCaseId(), CredibleTypeEnum.HELP_PROVE.getKey());

            ReportWorkOrderVO reportWorkOrderVO = new ReportWorkOrderVO();
            BeanUtils.copyProperties(l,reportWorkOrderVO);

            reportWorkOrderVO.setTitle(info.getTitle());
            reportWorkOrderVO.setAuditStatus(info.getStatus().ordinal());
            reportWorkOrderVO.setApplyStatus(Objects.nonNull(drawCashApplyVo) ? drawCashApplyVo.getApplyStatus() : CfDrawCashConstant.ApplyStatus.EMPTY_VALUE.getCode());
            reportWorkOrderVO.setCashStatus(Objects.nonNull(drawCashApplyVo) ? drawCashApplyVo.getDrawStatus() : CfDrawCashConstant.DrawStatus.EMPTY_VALUE.getCode());
            reportWorkOrderVO.setRepeatStatusList(repeatInfoBiz.selectRepeatStatusByCaseId(l.getCaseId()));
            reportWorkOrderVO.setIsNewReport(newReport);
            reportWorkOrderVO.setRealNameReport(realNameReport);
            reportWorkOrderVO.setReportNumber(CollectionUtils.isNotEmpty(reportList) ? reportList.size() : 0);
            reportWorkOrderVO.setLostStatus(lostContactService.hasLost(info.getInfoId()));
            reportWorkOrderVO.setOperatorId(l.getOperatorId());
            reportWorkOrderVO.setOperator(Objects.nonNull(userAccount) ? userAccount.getName() : "");
            if (CollectionUtils.isNotEmpty(reportList)){
                reportWorkOrderVO.setLastReportTime(reportList.get(reportList.size()-1).getCreateTime());
                reportWorkOrderVO.setFirstReportTime(reportList.get(0).getCreateTime());
            }
            reportWorkOrderVO.setCaseUuid(info.getInfoId());
            reportWorkOrderVO.setReportLabels(labels);
            reportWorkOrderVO.setHighRisk(highRisk);
            reportWorkOrderVO.setLastApproveComment(Objects.nonNull(lastApprove) ? lastApprove.getComment() : "");
            reportWorkOrderVO.setLastApproveTime(Objects.nonNull(lastApprove) ? lastApprove.getOprtime() : null);
            reportWorkOrderVO.setSupplyAuditStatus(Objects.nonNull(reportAddTrust) ? reportAddTrust.getAuditStatus() : CrowdfundingInfoStatusEnum.UN_SAVE.getCode());
            if (CollectionUtils.isNotEmpty(cfLetterStatus)){
                reportWorkOrderVO.setLetterStatus(cfLetterStatus);
            }
            if (Objects.nonNull(cfCredibleInfoDO)) {
                reportWorkOrderVO.setProveStatus(cfCredibleInfoDO.getAuditStatus());
            }
            // 获取长时时间未触达提醒
            var followTip = cf2RedissonHandler.get(ReportFollowActionEnum.REPORT_FOLLOW_ACTION_PROMPT.name()
                    + reportWorkOrderVO.getWorkOrderId(), String.class);
            if (StringUtils.isNotBlank(followTip)) {
                reportWorkOrderVO.setFollowTip(followTip);
            }

            EhResponse<ReportScheduleVO> scheduleResp = reportScheduleService.getByCaseId(caseId);
            if (EhResponseUtils.isOk(scheduleResp)) {
                ReportScheduleVO scheduleVO = scheduleResp.getData();
                if (scheduleVO != null) {
                    reportWorkOrderVO.setScheduleTime(scheduleVO.getTargetTime());
                }
            }
            ReportPayMethodEnum payMethodEnum = reportScheduleService.getPayMethodByCaseId(caseId);
            reportWorkOrderVO.setPayMethod(Optional.ofNullable(payMethodEnum).map(ReportPayMethodEnum::getCode).orElse(0));

            if (l.getOrderType() == WorkOrderType.report_split_draw.getType()) {
                CfFirsApproveMaterial cfFirsApproveMaterial = firsApproveMaterialMap.get(l.getCaseId());
                AdminCrowdfundingProgress adminCrowdfundingProgress = crowdfundingProgressMap.get(l.getFundUseProgressId());
                reportWorkOrderVO.setPatientName(Objects.nonNull(cfFirsApproveMaterial) ? cfFirsApproveMaterial.getPatientRealName() : "");
                reportWorkOrderVO.setFundUseContent(Objects.nonNull(adminCrowdfundingProgress) ? adminCrowdfundingProgress.getContent() : "");
                reportWorkOrderVO.setFundUseAttachmentUrls(Objects.nonNull(adminCrowdfundingProgress) ? adminCrowdfundingProgress.getImageUrls() : "");
            }
            return reportWorkOrderVO;
        }).collect(Collectors.toList());

        return reportWorkOrderVOS;

    }

    private AdminErrorCode validate(int caseId){
        long adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return AdminErrorCode.SYSTEM_NOT_LOGIN_ERROR;
        }

        CfInfoSimpleModel cfInfo = crowdfundingDelegate.getCfInfoSimpleModelById(caseId);
        if(Objects.isNull(cfInfo)){
            return AdminErrorCode.CF_NOT_FOUND;
        }

        List<CrowdfundingReport> reports = crowdfundingReportBiz.getListByInfoId(caseId);
        if(CollectionUtils.isEmpty(reports)){
            return AdminErrorCode.REPORT_NOT_EXIST;
        }

        List<Integer> reportIds = reports.stream().map(CrowdfundingReport::getId).collect(Collectors.toList());
        List<CrowdfundingReportLabel> modifyLabels = adminCrowdfundingReportBiz.getReportLabelsModify(reportIds);
        if(CollectionUtils.isEmpty(modifyLabels)){
            return AdminErrorCode.REPORT_TYPE_ERROR;
        }

        long nohandleCount = reports.stream().filter(r -> r.getHandleStatus() == CfReportHandleStatus.NO_HANDLE.getKey()).count();
//        long handledCount = reports.stream().filter(r -> r.getHandleStatus() == CfReportHandleStatus.HANDLED.getKey()).count();

//        if(nohandleCount + handledCount != reports.size()){
//            return AdminErrorCode.REPORT_RECORD_ERROR;
//        }

        if(nohandleCount > 0){
            return AdminErrorCode.REPORT_NO_HANDLE_ERROR;
        }

        return AdminErrorCode.SUCCESS;
    }

    @RequestMapping(path = "update-case-report-status", method = RequestMethod.POST)
    @Deprecated
    @RequiresPermission("newreport:update-case-report-status")
    public Response updateCaseReportStatus() {
        cfReportService.updateCaseReportStatus();
        return ResponseUtil.makeSuccess(null);
    }

    @RequestMapping(path = "get-work-order-status", method = RequestMethod.POST)
    @ApiOperation("获取工单状态")
    @RequiresPermission("newreport:get-work-order-status")
    public Response getWorkOrderStatus(@ApiParam("工单id") @RequestParam("workOrderId") long workOrderId) {
        log.info("AdminNewReportController.getWorkOrderStatus workOrderId:{}", workOrderId);
        if (workOrderId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        Response<WorkOrderVO> workOrderVOResponse = cfWorkOrderClient.getWorkOrderById(workOrderId);
        Map<String, Object> resultMap = Maps.newHashMap();
        if (workOrderVOResponse.ok() && Objects.nonNull(workOrderVOResponse.getData())) {
            resultMap.put("workOrderId", workOrderId);
            resultMap.put("handleResult", workOrderVOResponse.getData().getHandleResult());
            resultMap.put("orderType",workOrderVOResponse.getData().getOrderType());
        }
        return ResponseUtil.makeSuccess(resultMap);
    }

    @RequestMapping(path = "work-order-upgrade")
    @ApiOperation("举报工单升级二线、失联工单")
    @RequiresPermission("newreport:work-order-upgrade")
    public Response reportWorkOrderUpgrade(@ApiParam("工单id") @RequestParam("workOrderId") long workOrderId,
                                           @ApiParam("案例id") @RequestParam("caseId") int caseId,
                                           @ApiParam("工单类型") @RequestParam("orderType") int orderType,
                                           @ApiParam("原因") @RequestParam(value = "reason",defaultValue = "") String reason,
                                           @ApiParam("备注") @RequestParam(value = "comment",defaultValue = "") String comment) {
        int userId = ContextUtil.getAdminUserId();
        log.info("AdminNewReportController.reportWorkOrderUpgrade workOrderId:{},caseId:{},userId:{}",
                workOrderId, caseId, userId);
        ReportTransformParam p = new ReportTransformParam();
        p.setWorkOrderId(workOrderId);
        p.setCaseId(caseId);
        p.setReason(reason);
        p.setComment(comment);
        p.setOperatorId(userId);
        if (workOrderId <= 0 || caseId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        IReportTransformHandler reportHandler = reportTransformService.getHandler(orderType);
        if (reportHandler == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        return reportHandler.handle(p);
    }

    @RequestMapping(path = "get-case-report-info", method = RequestMethod.POST)
    @ApiOperation("获取案例基本举报信息")
    @RequiresPermission("newreport:get-case-report-info")
    public Response<BaseCaseReportInfoVo> getCaseReportInfo(@ApiParam("案例id") @RequestParam("caseId") int caseId) {
        log.info("AdminNewReportController.getCaseReportInfo caseId:{}", caseId);
        if (caseId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        BaseCaseReportInfoVo baseCaseReportInfoVo = cfReportService.getCaseReportInfo(caseId);
        return NewResponseUtil.makeSuccess(baseCaseReportInfoVo);
    }

    @PostMapping(path = "work-order-upgrade-window")
    @ApiOperation("举报工单升级二线工单弹窗")
    @RequiresPermission("newreport:work-order-upgrade-window")
    public Response<Boolean> reportWorkOrderUpgradeWindow(@RequestParam(value = "reasonType")String reasonType,
                                                          @RequestParam(value = "remark", defaultValue = "") String remark,
                                                          @RequestParam(value = "workOrderId", required = false, defaultValue = "0") long workOrderId,
                                                          @RequestParam(value = "caseId")int caseId){
        int adminUserId = ContextUtil.getAdminUserId();
        return cfReportService.reportWorkOrderUpgradeWindow(reasonType, remark, caseId, workOrderId, adminUserId);
    }

    @PostMapping(path = "follow-action-dot")
    @ApiOperation("举报处理跟进动作点记录")
    @RequiresPermission("newreport:follow-action-dot")
    public Response reportFollowActionDot(@RequestParam(value = "workOrderId") long workOrderId,
                                          @RequestParam(value = "actionType") int actionType) {
        log.info("reportFollowActionDot workOrderId:{},actionType:{}", workOrderId, actionType);
        if (workOrderId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        ReportFollowActionEnum reportFollowActionEnum = ReportFollowActionEnum.getByCode(actionType);
        if (Objects.isNull(reportFollowActionEnum)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        Response<WorkOrderVO> workOrderVOResponse = cfWorkOrderClient.getWorkOrderById(workOrderId);
        if (workOrderVOResponse.ok() && Objects.isNull(workOrderVOResponse.getData())) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        WorkOrderVO workOrderVO = workOrderVOResponse.getData();
        int userId = ContextUtil.getAdminUserId();
        ReportWorkOrderFollowAction reportWorkOrderFollowAction = new ReportWorkOrderFollowAction(workOrderVO.getWorkOrderId(),
                userId, workOrderVO.getOrderType(), actionType);
        reportWorkOrderFollowActionBiz.insertOne(reportWorkOrderFollowAction);

        if (ReportFollowActionEnum.promptAction.contains(actionType)) {
            cf2RedissonHandler.del(ReportFollowActionEnum.REPORT_FOLLOW_ACTION_PROMPT.name() + workOrderId);
        }
        return NewResponseUtil.makeSuccess(null);
    }


    @RequestMapping(path = "report-order-allocation", method = RequestMethod.POST)
    @RequiresPermission("newreport:report-order-allocation")
    public Response<Integer> reportWorkOrderAllocation(@RequestParam("ids") String ids,
                                                       @RequestParam("orderType") int orderType,
                                                       @RequestParam("recipientId") int recipientId) {

        if (StringUtils.isBlank(ids) || orderType <= 0 || recipientId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<Long> list = Arrays.stream(ids.split(",")).map(Long::valueOf).collect(Collectors.toList());
        int operatorId = ContextUtil.getAdminUserId();

        return reportWorkOrderClient.batchAllocation(list, orderType, operatorId, recipientId);
    }

    @RequestMapping(path = "later-processing", method = RequestMethod.POST)
    @RequiresPermission("newreport:later-processing")
    @ApiOperation("稍后处理")
    public Response laterProcessing(@ApiParam("案例id") @RequestParam("caseId") int caseId,
                                             @ApiParam("工单id") @RequestParam("workOrderId") long workOrderId,
                                             @ApiParam("原因枚举") @RequestParam("reason") int reason,
                                             @ApiParam("其他原因") @RequestParam("otherReason") String otherReason) {
        log.info("AdminNewReportController.laterProcessing caseId:{},workOrderId:{},reason:{},otherReason:{}",
                caseId, workOrderId, reason, otherReason);
        if (caseId <= 0 || workOrderId <= 0 || reason <= 0 || otherReason.length() > 500) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        Response<WorkOrderVO> response = cfWorkOrderClient.getWorkOrderById(workOrderId);
        if (response.ok() && response.getData() == null) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        WorkOrderVO workOrderVO = response.getData();
        if (workOrderVO.getOrderType() != WorkOrderType.casefirstreport.getType()) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (workOrderVO.getHandleResult() != HandleResultEnum.doing.getType()) {
            return NewResponseUtil.makeFail("仅可将处理中工单置为稍后处理");
        }
        int adminUserId = ContextUtil.getAdminUserId();
        return NewResponseUtil.makeSuccess(cfReportService.laterProcessing(workOrderVO, reason, otherReason, adminUserId));
    }

    @RequestMapping(path = "set-doing-count")
    @RequiresPermission("newreport:set-doing-count")
    @ApiOperation("设置处理中阈值")
    public Response setDoingCount(@ApiParam("阈值") @RequestParam("count") int count) {
        log.info("AdminNewReportController.setDoingCount count:{}", count);
        if (count <= 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        cfReportService.setDoingCount(count);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequestMapping(path = "get-doing-count")
    @RequiresPermission("newreport:get-doing-count")
    @ApiOperation("获取处理中阈值")
    public Response<Integer> getDoingCount() {
        return reportWorkOrderClient.getDoingCount();
    }
}
