package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.collect.Maps;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.model.admin.UserCommentVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2018/7/31
 */
@RestController
@RequestMapping(path = "admin/cf/usercomment")
@Api(value = "用户评价记录")
public class UserCommentController {

    @Autowired
    private UserCommentBiz commentBiz;

    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @RequiresPermission("user-comment:getUgcComment")
    @ApiOperation("获取ugc评论")
    @RequestMapping(path = "/getugccomment",method=RequestMethod.POST)
    public Response getUgcComment(long caseId) {

        if (caseId <= 0){
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        Map<String, Object> result = Maps.newHashMap();

        List<UserComment> list = commentBiz.getUserComment(caseId,UserCommentSourceEnum.UGC.getCode());

        if (CollectionUtils.isEmpty(list)){
            return NewResponseUtil.makeSuccess(result);
        }

        Map<Integer, List<UserCommentVO>> map = list.stream().map(e->getUserCommentVO(e)).collect(Collectors.groupingBy(UserCommentVO::getCommentType));

        List<UserCommentVO> tuwen = map.get(UserCommentSourceEnum.CommentType.UGC_1.getCode());
        List<UserCommentVO> fangche = map.get(UserCommentSourceEnum.CommentType.UGC_2.getCode());

        result.put("tuwen", tuwen);
        result.put("fangche", fangche);
        return NewResponseUtil.makeSuccess(result);
    }


    @RequiresPermission("user-comment:getUgcCommentByType")
    @ApiOperation("获取ugc单一评论")
    @RequestMapping(path = "/getugccommentbytype",method=RequestMethod.POST)
    public Response getUgcCommentByType(long caseId, int commentType) {
        return getCommentBySourceAndType(caseId, UserCommentSourceEnum.UGC.getCode(), commentType);
    }

    @RequiresPermission("user-comment:getCommentByType")
    @ApiOperation("获取comment评论")
    @RequestMapping(path = "/getcommentbytype",method=RequestMethod.POST)
    public Response getCommentBySourceAndType(@ApiParam("int类型案例id") @RequestParam(value = "caseId") long caseId,
                                              @ApiParam("comment来源1:UGC 2:材料审核 3:发起手机号修改") @RequestParam(value = "commentSource") int commentSource,
                                              @ApiParam("参考UserCommentSourceEnum.CommentType") @RequestParam(value = "commentType") int commentType){
        UserCommentSourceEnum sourceEnum = UserCommentSourceEnum.parse(commentSource);

        if (caseId <= 0 || null == sourceEnum){
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        Map<String, Object> result = Maps.newHashMap();

        List<UserComment> list = commentBiz.getUserComment(caseId,sourceEnum.getCode(),commentType);

        if (CollectionUtils.isEmpty(list)){
            return NewResponseUtil.makeSuccess(result);
        }

        List<UserCommentVO> data = list.stream().map(e->getUserCommentVO(e)).collect(Collectors.toList());

        result.put("data", data);

        return NewResponseUtil.makeSuccess(result);
    }


    public UserCommentVO getUserCommentVO(UserComment userComment){

        UserCommentVO commentVO = new UserCommentVO(userComment);

        AdminUserAccountModel accountModel= seaAccountClientV1.getValidUserAccountById(Long.valueOf(userComment.getOperatorId()).intValue()).getResult();//已检查过
        if (accountModel != null){
            commentVO.setOperatorName(accountModel.getName());
        }
        commentVO.setCommentTypeStr(UserCommentSourceEnum.CommentType.getCommetTypefromCode(commentVO.getCommentType()).getDesc());
        commentVO.setCreateTime(DateFormatUtils.format(userComment.getCreateTime(),"yyyy-MM-dd HH:mm:ss"));

        return commentVO;

    }

}
