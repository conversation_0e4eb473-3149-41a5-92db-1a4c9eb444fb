package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingReportBiz;
import com.shuidihuzhu.cf.client.feign.AuthorFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.ugc.IUgcDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportView;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.model.CfUserInvitedLaunchCaseRecordModel;
import com.shuidihuzhu.client.cf.clewtrack.param.ChannelRefineDTO;
import com.shuidihuzhu.client.cf.growthtool.client.CfChannelFeignClient;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2019/8/20 下午2:25
 * @desc
 */
@Slf4j
@Controller
@RequestMapping(path = "/admin/crowdfunding/case/common")
public class AdminCaseCommonController {

    @Autowired
    private IUgcDelegate ugcDelegate;

    @Resource
    private IRiskDelegate riskDelegate;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Autowired
    private AuthorFeignClient authorFeignClient;

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Autowired
    private CfChannelFeignClient cfChannelFeignClient;

    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;

    @Autowired
    private AdminCrowdfundingReportBiz adminCrowdfundingReportBiz;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @ResponseBody
    @RequestMapping(path = "/query-case-detail-info", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("case-common:query-case-detail-info")
    public Response<CfCaseDetailInfo> queryCaseDetailInfo(@ApiParam("int类型的案例id") @RequestParam(name = "caseId") int caseId) {
        int adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        FeignResponse<CrowdfundingInfo> cfInfoRes = crowdfundingFeignClient.getCaseInfoById(caseId);
        if(Objects.isNull(cfInfoRes) || 0 != cfInfoRes.getCode() || Objects.isNull(cfInfoRes.getData())){
            return ResponseUtil.makeError(ErrorCode.CF_NOT_FOUND);
        }

        CrowdfundingInfo cfInfo = cfInfoRes.getData();

        List<CrowdfundingReport> reportList = riskDelegate.getCrowdfundingReportListByInfoId(caseId);
        Integer verifyCount = ugcDelegate.countCrowdFundingVerificationByInfoUuid(cfInfo.getInfoId());
        UserInfoModel cfUserInfo = userInfoServiceBiz.getUserInfoByUserId(cfInfo.getUserId());
        FeignResponse<CfFirsApproveMaterial> materialRes = authorFeignClient.getAuthorInfoByInfoId(caseId);
        FeignResponse<CrowdfundingTreatment> treatmentRes = crowdfundingFeignClient.getCrowdfundingTreatment(caseId);
        CrowdfundingInfoPayee payeeInfo = crowdfundingDelegate.getCrowdfundingInfoPayeeByInfoUuid(cfInfo.getInfoId());
        CfFirsApproveMaterial material = Objects.nonNull(materialRes) && Objects.nonNull(materialRes.getData()) ? materialRes.getData() : null;
        CrowdfundingTreatment treatment = Objects.nonNull(treatmentRes) && Objects.nonNull(treatmentRes.getData()) ? treatmentRes.getData() : null;


        String raiserName = Objects.nonNull(material) ? material.getSelfRealName() : "";
        String raiserIdentity = Objects.nonNull(material) ? material.getSelfCryptoIdcard() : "";

        String authorName = Objects.nonNull(material) ? material.getPatientRealName() : "";
        String authorIdCard = Objects.nonNull(material) ? shuidiCipher.decrypt(material.getPatientCryptoIdcard()) : "";

        int userRelationType = Objects.nonNull(material) ? material.getUserRelationType() : -1;
        UserIdentityType idType = UserIdentityType.getByCode(Objects.nonNull(material) ? material.getPatientIdType() : 0);

        if(StringUtils.isEmpty(raiserName) || StringUtils.isEmpty(raiserIdentity)){
            raiserName = userRelationType == UserRelTypeEnum.SELF.getValue() ? material.getPatientRealName() : material.getSelfRealName();
            raiserIdentity = userRelationType == UserRelTypeEnum.SELF.getValue() ? material.getPatientCryptoIdcard() : material.getSelfCryptoIdcard();
        }

        if(StringUtils.isEmpty(authorIdCard) && idType == UserIdentityType.birth){
            authorIdCard = Objects.nonNull(material) ? material.getPatientBornCard() : "";
        }

        ChannelRefineDTO channelRefineDTO = new ChannelRefineDTO();
        channelRefineDTO.setInfoId(Long.valueOf(caseId));
        channelRefineDTO.setUserId(cfInfo.getUserId());
        channelRefineDTO.setChannel(cfInfo.getChannel());
        Response<List<CfUserInvitedLaunchCaseRecordModel>> channelRes = cfChannelFeignClient.getCfUserInvitedLaunchCaseRecordByInfoIds(Lists.newArrayList(channelRefineDTO));

        CfCaseDetailInfo cfCaseDetail = new CfCaseDetailInfo();
        cfCaseDetail.setCreateTime(cfInfo.getCreateTime());
        cfCaseDetail.setBeginTime(cfInfo.getBeginTime());
        cfCaseDetail.setEndTime(cfInfo.getEndTime());
        cfCaseDetail.setAuditStatus(cfInfo.getStatus().value());
        cfCaseDetail.setChannel(Objects.nonNull(channelRes) && CollectionUtils.isNotEmpty(channelRes.getData()) ? channelRes.getData().get(0).getServiceUserInfo(shuidiCipher) : "");
        cfCaseDetail.setTargetAmount(cfInfo.getTargetAmount() / 100);
        cfCaseDetail.setAmount(cfInfo.getAmount() / 100);
        cfCaseDetail.setVerifyCount(Objects.nonNull(verifyCount) ? verifyCount : 0);
        cfCaseDetail.setReportCount(reportList.size());

        cfCaseDetail.setRaiserUserId(cfInfo.getUserId());
        cfCaseDetail.setRaiserNickName(Objects.nonNull(cfUserInfo) ? cfUserInfo.getNickname() : "");
        cfCaseDetail.setRaiserMobile(Objects.nonNull(cfUserInfo) ? shuidiCipher.decrypt(cfUserInfo.getCryptoMobile()) : "");
        cfCaseDetail.setRaiserName(raiserName);
        cfCaseDetail.setRaiserIdentity(shuidiCipher.decrypt(raiserIdentity));
        cfCaseDetail.setRaiserRelation(userRelationType);

        cfCaseDetail.setAuthorName(authorName);
        cfCaseDetail.setIdType(idType.getCode());
        cfCaseDetail.setAuthorIdcard(authorIdCard);
        cfCaseDetail.setDiagnoseHospital(Objects.nonNull(treatment) ? treatment.getDiagnoseHospitalName() : "");
        cfCaseDetail.setTreatHospital(Objects.nonNull(treatment) ? treatment.getHospitalName() : "");

        cfCaseDetail.setPayeeName(Objects.nonNull(payeeInfo) ? payeeInfo.getName() : "");
        cfCaseDetail.setPayeeIdentity(Objects.nonNull(payeeInfo) ? shuidiCipher.decrypt(payeeInfo.getIdCard()) : "");
        cfCaseDetail.setPayeeMobile(Objects.nonNull(payeeInfo) ? shuidiCipher.decrypt(payeeInfo.getMobile()) : "");
        cfCaseDetail.setPayeeRelation(Objects.nonNull(payeeInfo) ? payeeInfo.getRelationType() : 0);

        return ResponseUtil.makeSuccess(cfCaseDetail);
    }

    @ResponseBody
    @RequestMapping(path = "/query-user-report-item", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("case-common:query-user-report-item")
    public Response<List<CfReportView>> queryUserReportItem(@ApiParam("int类型的案例id") @RequestParam(name = "caseId") int caseId){

        int adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        FeignResponse<CrowdfundingInfo> cfInfoRes = crowdfundingFeignClient.getCaseInfoById(caseId);
        if(Objects.isNull(cfInfoRes) || 0 != cfInfoRes.getCode() || Objects.isNull(cfInfoRes.getData())){
            return ResponseUtil.makeError(ErrorCode.CF_NOT_FOUND);
        }

        List<CrowdfundingReport> reportList = riskDelegate.getCrowdfundingReportListByInfoId(caseId);

        List<Integer> reportIds = reportList.stream().map(r-> r.getId()).collect(Collectors.toList());

        List<CrowdfundingReportLabel> labels = adminCrowdfundingReportBiz.getReportLabels(reportIds);
        Map<Long,List<String>> map = labels.stream().collect(Collectors.groupingBy(CrowdfundingReportLabel::getReportId,Collectors.mapping(
                r -> org.apache.commons.lang3.StringUtils.isBlank(r.getReportComment()) ?
                        CfReportTypeEnum.getDescFromCode(r.getReportLabel()) : r.getReportComment(),Collectors.toList())));

        List<CfReportView> cfReportViews = Lists.newArrayList();

        for (CrowdfundingReport report : reportList){
            CfReportView cfReportView = new CfReportView();
            cfReportView.setUserId(report.getUserId());
            cfReportView.setCreateTime(report.getCreateTime());
            cfReportView.setReportTypes(map.get(Long.valueOf(report.getId())));
            cfReportView.setDealStatus(report.getDealStatus());
            cfReportView.setContent(report.getContent());
            cfReportViews.add(cfReportView);
        }

        return ResponseUtil.makeSuccess(cfReportViews);
    }
}
