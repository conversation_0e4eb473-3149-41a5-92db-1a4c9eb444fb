package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidi.weixin.common.util.StringUtils;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfSceceBiz;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfCaseScene;
import com.shuidihuzhu.cf.model.crowdfunding.CfScene;

import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * Created by wangsf on 17/4/24.
 */


@Controller
@RequestMapping(path="/admin/cf/scene")
public class CfCaseSceneController {

	private static final Logger LOGGER = LoggerFactory.getLogger(CfCaseSceneController.class);

	@Autowired
	private AdminCfSceceBiz cfSceceBiz;
	@Autowired
	private  SeaAccountClientV1 seaAccountClientV1;
	@Autowired
	private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;

	@RequiresPermission("casescene:list")
	@RequestMapping(path="/list", method = RequestMethod.POST)
	@ResponseBody
	public Response listScenes() {
		try {
			return NewResponseUtil.makeSuccess(this.cfSceceBiz.listScenes());
		} catch (Exception e) {
			LOGGER.error("CfCaseSceneController listScenes error", e);
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
		}
	}

	@RequiresPermission("casescene:case-get")
	@RequestMapping(path="/case/get", method = RequestMethod.POST)
	@ResponseBody
	public Response getByCase(@RequestParam(value = "infoUuid", required = true) String infoUuid) {

//		Integer userId = ContextUtil.getAdminUserId();
//		if(userId == null || userId <= 0) {
//			return NewResponseUtil.makeError(AdminErrorCode.USER_ACCOUNT_NO_LOGIN);
//		}

		LOGGER.info("CfCaseSceneController getByCase infoUuid={}", infoUuid);
		if(StringUtils.isBlank(infoUuid)) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}

		try {
			CfCaseScene cfCaseScene = this.cfSceceBiz.getByInfoUuid(infoUuid);
			return NewResponseUtil.makeSuccess(cfCaseScene);
		} catch (Exception e) {
			LOGGER.error("CfCaseSceneController getByCase error! infoUuid={}", infoUuid, e);
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
		}
	}


	@RequiresPermission("casescene:case-set")
	@RequestMapping(path="/case/set", method = RequestMethod.POST)
	@ResponseBody
	public Response setSceneForCase(@RequestParam(value="infoUuid", required = true) String infoUuid,
	                                @RequestParam(value = "sceneId", required = true) Integer sceneId,
	                                @RequestParam(value = "description", required = true) String description) {

		LOGGER.info("CfCaseSceneController setSceneForCase infoUuid={}, sceneId={}, description={}",
				infoUuid, sceneId, description);
		if(org.apache.commons.lang3.StringUtils.isBlank(infoUuid)
				|| sceneId == null || sceneId <= 0 || org.apache.commons.lang3.StringUtils.isBlank(description)) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}

		CfScene cfScene = this.cfSceceBiz.getScene(sceneId);
		if(cfScene == null) {
			LOGGER.info("CfCaseSceneController setSceneForCase infoUuid={}, sceneId={}", infoUuid, sceneId);
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}

		CfCaseScene caseScene = new CfCaseScene();
		caseScene.setInfoUuid(infoUuid);
		caseScene.setDescription(description);
		caseScene.setSceneName(cfScene.getName());
		caseScene.setSceneId(sceneId);
		try {
			this.cfSceceBiz.insertOrUpdateCaseScene(caseScene);
			int userId = ContextUtil.getAdminUserId();
			try {
				AdminUserAccountModel adminUserAccountModel = this.seaAccountClientV1.getValidUserAccountById(userId).getResult();
				String userName = adminUserAccountModel.getName();
				this.crowdfundingOperationDelegate.saveCfOperatingRecord(infoUuid, userId, userName, CfOperatingRecordEnum.Type.GET_FIRST_CONTACT,
						CfOperatingRecordEnum.Role.OPERATOR);
			} catch (Exception e) {
				LOGGER.error("", e);
			}
			return NewResponseUtil.makeSuccess(null);
		} catch (Exception e) {
			LOGGER.error("CfCaseSceneController setSceneForCase Error: infoUuid={}, sceneId={}", infoUuid, sceneId, e);
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
		}
	}
}
