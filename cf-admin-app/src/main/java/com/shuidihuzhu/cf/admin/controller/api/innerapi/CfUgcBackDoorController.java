package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.feign.CrowdfundingAttachmentFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResult;
import com.shuidihuzhu.cf.dao.ai.AiAdPromptConfigDao;
import com.shuidihuzhu.cf.dao.ai.AiJudgeRiskStrategyConfigDao;
import com.shuidihuzhu.cf.model.ai.AiAdPromptConfig;
import com.shuidihuzhu.cf.model.ai.AiRiskStrategyConfig;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment;
import com.shuidihuzhu.cf.model.crowdfunding.deliver.CrowdfundingCommentDeliver;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.ai.AdGenerationService;
import com.shuidihuzhu.cf.service.sensitive.adapter.SensitiveCommentAdapter;
import com.shuidihuzhu.cf.service.sensitive.checker.CanEnterCharacterChecker;
import com.shuidihuzhu.cf.service.sensitive.checker.IllegalSnippetChecker;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/7/20  4:48 下午
 */
@RestController
@RequestMapping("innerapi/cf/admin/ugc")
@Slf4j
public class CfUgcBackDoorController {

    @Resource
    private SensitiveCommentAdapter sensitiveCommentAdapter;

    @Autowired
    private CanEnterCharacterChecker canEnterCharacterChecker;

    @Autowired
    private IllegalSnippetChecker illegalSnippetChecker;

    @Resource
    private AiJudgeRiskStrategyConfigDao aiJudgeRiskStrategyConfigDao;
    @RequestMapping(value = "ai-risk-add", method = RequestMethod.POST)
    public Response<Void> aiRiskAdd(@RequestParam("sceneType") String sceneType,
                                    @RequestParam("riskFactor") String riskFactor,
                                    @RequestParam("modelType") Integer modelType,
                                    @RequestParam("judgePrompt") String judgePrompt,
                                    @RequestParam("judgeType") Integer judgeType) {

        AiRiskStrategyConfig aiRiskStrategyConfig = new AiRiskStrategyConfig();
        aiRiskStrategyConfig.setRiskFactor(riskFactor);
        aiRiskStrategyConfig.setSceneType(sceneType);
        aiRiskStrategyConfig.setModelType(modelType);
        aiRiskStrategyConfig.setJudgePrompt(judgePrompt);
        aiRiskStrategyConfig.setJudgeType(judgeType);
        aiJudgeRiskStrategyConfigDao.insert(aiRiskStrategyConfig);

        return NewResponseUtil.makeSuccess(null);
    }

    @Resource
    private AiAdPromptConfigDao aiAdPromptConfigDao;
    @RequestMapping(value = "add-or-update-ad-prompt", method = RequestMethod.POST)
    public Response<Void> addOrUpdateAdPrompt(@RequestParam("userProfile") String userProfile,
                                              @RequestParam("prompt") String prompt) {
        AiAdPromptConfig aiAdPromptConfig = aiAdPromptConfigDao.selectConfigByUserProfile(userProfile);
        if (Objects.isNull(aiAdPromptConfig)) {
            aiAdPromptConfigDao.insert(AiAdPromptConfig.builder().userProfile(userProfile).adPrompt(prompt).build());
        } else {
            aiAdPromptConfigDao.update(AiAdPromptConfig.builder().userProfile(userProfile).adPrompt(prompt).build());
        }
        return NewResponseUtil.makeSuccess(null);
    }

    @Resource
    private AdGenerationService adGenerationService;
    @RequestMapping(value = "ai-ad", method = RequestMethod.POST)
    public Response<Void> aiAd(@RequestParam("infoUuid") String infoUuid) {
        adGenerationService.generateAllAdsForCase(infoUuid);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequestMapping(value = "back-door", method = RequestMethod.POST)
    public Response<UgcBackDoor> ugcBackDoor(String content) {
        Map<String, Boolean> map = Maps.newHashMap();
        //非法字符检测
        CrowdfundingCommentDeliver comment = new CrowdfundingCommentDeliver();
        comment.setContent(content);
        OpResult<RiskWordResult> canEnterCharacter = canEnterCharacterChecker.isHit(comment, sensitiveCommentAdapter);
        RiskWordResult canEnterCharacterHit = Optional.ofNullable(canEnterCharacter).filter(OpResult::isSuccess).map(OpResult::getData).orElse(null);
        map.put("非法字符检测", Objects.nonNull(canEnterCharacterHit) && canEnterCharacterHit.isPassed());
        //敏感账号策略(沿用敏感渠道的自动处理逻辑，所以类型还是使用敏感号码的类型)
        OpResult<RiskWordResult> illegalSnippet = illegalSnippetChecker.backDoorIsHit(comment, sensitiveCommentAdapter);
        RiskWordResult illegalSnippetHit = Optional.ofNullable(illegalSnippet).filter(OpResult::isSuccess).map(OpResult::getData).orElse(null);
        map.put("敏感账号策略", Objects.nonNull(illegalSnippetHit) && illegalSnippetHit.isPassed());
        UgcBackDoor ugcBackDoor = new UgcBackDoor();
        ugcBackDoor.setDesc("true:未命中 | false:命中");
        ugcBackDoor.setMap(map);
        return NewResponseUtil.makeSuccess(ugcBackDoor);
    }

    @Data
    static class UgcBackDoor {
        private String desc;
        private Map<String, Boolean> map;
    }
}
