package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminWorkOrderCaseConst;
import com.shuidihuzhu.cf.enums.crowdfunding.CfCallOutConditionTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoVo;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;

/**
 * Created by ahrievil on 2017/5/15.crowdfunding_operation
 */
@Controller
@RequestMapping(path = "/admin/crowdfunding/call-out-condition")
public class CfCallOutConditionController {

	private static final Logger LOGGER = LoggerFactory.getLogger(CfCallOutConditionController.class);

	@Autowired
	private CfCallOutConditionBiz cfCallOutConditionBiz;
	@Autowired
	private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
	@Autowired
	private CfAdminOperationRecordBiz cfAdminOperationRecordBiz;
	@Autowired
	private AdminCrowdfundingOperationBiz adminCrowdfundingOperationBiz;
	@Autowired
	private AdminApproveService adminApproveService;
	@Autowired
	private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;
	@Autowired
	private  SeaAccountClientV1 seaAccountClientV1;
	@Resource
	private UserInfoServiceBiz userInfoServiceBiz;
	@Autowired
    private AdminWorkOrderCaseBiz adminWorkOrderCaseBiz;
	@Autowired
    private AdminWorkOrderCaseRecordBiz adminWorkOrderCaseRecordBiz;
	@Autowired
    private AdminWorkOrderBiz adminWorkOrderBiz;
	@Autowired
	private ShuidiCipher shuidiCipher;

	@RequiresPermission("call-out-condition:add")
	@RequestMapping(path = "add", method = RequestMethod.POST)
	@ResponseBody
	@Deprecated
	public Response add(String infoUuid, Integer status, String comment) {
		int userId = ContextUtil.getAdminUserId();
		LOGGER.info("CfCallOutConditionController add infoUuid:{};status:{};userId:{};comment:{}", infoUuid, status,
				userId, comment);
		if (StringUtil.isBlank(infoUuid) || status == null || StringUtil.isBlank(comment)) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}
		CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
		if (crowdfundingInfo == null) {
			return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
		}
		CfCallOutConditionTypeEnum cfCallOutConditionTypeEnum = CfCallOutConditionTypeEnum.getByValue(status);
		CfCallOutCondition cfCallOutCondition = new CfCallOutCondition();
		cfCallOutCondition.setInfoUuid(infoUuid);
		cfCallOutCondition.setOperator(userId);
		cfCallOutCondition.setStatus(status);
		cfCallOutCondition.setComment(comment);
		try {
			cfCallOutConditionBiz.add(cfCallOutCondition);
		} catch (Exception e) {
			LOGGER.error("CfCallOutConditionController add error!", e);
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
		}

		if (cfCallOutConditionTypeEnum == CfCallOutConditionTypeEnum.CALL_OUT_FAIL) {
			adminCrowdfundingOperationBiz.addCallCount(1, infoUuid);
		}
		//查询电话号码
		UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(crowdfundingInfo.getUserId());
		String mobile = "";
		if (userInfoModel != null && StringUtils.isNotBlank(userInfoModel.getCryptoMobile())) {
			 mobile = shuidiCipher.decrypt(userInfoModel.getCryptoMobile());
		} else {
			CrowdfundingInfoVo crowdfundingInfoVo = crowdfundingInfoBiz.getFundingInfoVoByInfoUuid(infoUuid);
			if(StringUtils.isNotBlank(crowdfundingInfoVo.getCryptoRegisterMobile())) {
				mobile = shuidiCipher.decrypt(crowdfundingInfoVo.getCryptoRegisterMobile());
			}
		}
		if (StringUtils.isNotBlank(mobile)) {
			//在更新operating 之前  先进行  判断 若为0 发送短信
			LOGGER.info(" mobile :{}", mobile);
			CrowdfundingOperation crowdfundingOperation = adminCrowdfundingOperationBiz.getByInfoId(infoUuid);
			if (crowdfundingOperation != null && crowdfundingOperation.getCallStatus() == CfCallOutConditionTypeEnum.DEFAULT.getValue()) {
				cfCallOutConditionBiz.sendFirstCallOutMsg(crowdfundingInfo.getUserId(), mobile, crowdfundingInfo);
			}
		} else {
			LOGGER.error("mobile is null infoUuid:{}", infoUuid);
		}
		adminCrowdfundingOperationBiz.updateCallStatus(cfCallOutConditionTypeEnum.getValue(), infoUuid);
		//工单业务
        AdminWorkOrderCase adminWorkOrderCase = adminWorkOrderCaseBiz.selectByCaseId(crowdfundingInfo.getId());
        if (adminWorkOrderCase != null) {
            this.handleWorkOrder(adminWorkOrderCase, cfCallOutConditionTypeEnum, comment);
        }

        adminApproveService.addApprove(crowdfundingInfo, cfCallOutConditionTypeEnum.getMessage(), comment, userId);
		// 操作记录入库
		CfOperatingRecordEnum.Type type = cfCallOutConditionTypeEnum.getType();
		if (type != null) {
			try {
				AdminUserAccountModel adminUserAccountModel = this.seaAccountClientV1.getValidUserAccountById(userId).getResult();
				String userName = adminUserAccountModel.getName();
				this.crowdfundingOperationDelegate.saveCfOperatingRecord(infoUuid, userId, userName, CfOperatingRecordEnum.Type.OUTBOUND_SUCCESS,
						CfOperatingRecordEnum.Role.OPERATOR);
			} catch (Exception e) {
				LOGGER.error("", e);
			}
		}
		cfAdminOperationRecordBiz.addOneOperationRecord(infoUuid, userId, status + 13/* BackgroundLogEnum */, comment);
		LOGGER.info(
				"客服后台log：call-out operationTime:{};operator:{};operationReason:{};operationType:{};infoId:{};status:{};dataStatus:{};infoStatus:{}",
				DateUtil.formatDateTime(new Date()), userId,
				cfCallOutConditionTypeEnum.getMessage(), cfCallOutConditionTypeEnum.getMessage(), infoUuid,
				crowdfundingInfo.getStatus(), crowdfundingInfo.getDataStatus(),
				crowdfundingInfo.getEndTime().before(new Date()) ? "结束" : "未结束");
		Map<String, Object> result = Maps.newHashMap();
		result.put("callStatus", status);
		result.put("list", adminApproveService.getCommentVoList(crowdfundingInfo.getId()));
		return NewResponseUtil.makeSuccess(result);
	}

	private void handleWorkOrder(AdminWorkOrderCase adminWorkOrderCase, CfCallOutConditionTypeEnum callOutConditionTypeEnum, String comment) {
	    if (callOutConditionTypeEnum == CfCallOutConditionTypeEnum.CALL_OUT_CALL_BACK) {
	        return ;
        }
        adminWorkOrderCaseBiz.updateCallStatusById(adminWorkOrderCase.getId(), callOutConditionTypeEnum);
        if (callOutConditionTypeEnum == CfCallOutConditionTypeEnum.CALL_OUT_SUCCESS) {
            adminWorkOrderCaseBiz.updateStatusById(adminWorkOrderCase.getId(), AdminWorkOrderCaseConst.Status.COMPLETE);
            adminWorkOrderCaseBiz.completeTask(adminWorkOrderCase, comment);
            AdminWorkOrderCase adminWorkOrderCase1 = adminWorkOrderCaseBiz.selectById(adminWorkOrderCase.getId());
            AdminWorkOrder adminWorkOrder = adminWorkOrderBiz.selectById(adminWorkOrderCase1.getWorkOrderId());
            AdminWorkOrderCaseRecord record = new AdminWorkOrderCaseRecord(adminWorkOrderCase1, adminWorkOrder.getOperatorId());
            adminWorkOrderCaseRecordBiz.insertOne(record);
        }
    }
}
