package com.shuidihuzhu.cf.admin.controller.api.monthdonate;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.charity.client.api.MonthDonateClient;
import com.shuidihuzhu.charity.client.dto.ProjectCommonConfigDTO;
import com.shuidihuzhu.charity.client.dto.ProjectDTO;
import com.shuidihuzhu.charity.client.vo.monthdonate.ProjectConfigVO;
import com.shuidihuzhu.charity.client.vo.monthdonate.SeaProjectInfoVo;
import com.shuidihuzhu.charity.client.vo.monthdonate.SeaProjectVO;
import com.shuidihuzhu.common.web.annotation.SessionKeyValidateRequired;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@RestController
@RequestMapping(value = "/admin/cf/monthdonate", produces = "application/json;charset=UTF-8")
@Slf4j
public class MonthDonateController {

    @Resource
    private MonthDonateClient monthDonateClient;

    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @PostMapping("project-list")
    Response<List<SeaProjectInfoVo>> seaProjectList() {
        return monthDonateClient.seaProjectList();
    }

    @PostMapping("update-online-status")
    Response<List<SeaProjectInfoVo>> updateOnlineStatus(@RequestParam("projectId") Long projectId,
                                                    @RequestParam("onlineStatus") Integer onlineStatus) {
        int adminUserId = ContextUtil.getAdminUserId();
        log.info("updateOnlineStatus projectId: {} ,onlineStatus : {}, adminUserId: {}", projectId, onlineStatus, adminUserId);
        AuthRpcResponse<AdminUserAccountModel> response = seaAccountClientV1.getValidUserAccountById(adminUserId);
        log.info("updateOnlineStatus response: {}" , response);
        if (Objects.isNull(response) || Objects.isNull(response.getResult())){
            return NewResponseUtil.makeFail("获取用户信息失败 userId : " + adminUserId);
        }
        AdminUserAccountModel result = response.getResult();
        String name = result.getName();
        return monthDonateClient.updateOnlineStatus(projectId,onlineStatus,Long.valueOf(adminUserId),name);
    }

    @PostMapping("add-or-update-project")
    Response addOrUpdateProject(@RequestBody ProjectDTO projectDTO){
        log.info("addOrUpdateProject projectDTO: {}", projectDTO);
        return monthDonateClient.addOrUpdateProject(projectDTO);
    }

    @PostMapping("add-or-update-project-config")
    Response addOrUpdateProjectConfig(@RequestBody ProjectCommonConfigDTO projectCommonConfigDTO){
        log.info("addOrUpdateProjectConfig projectCommonConfigDTO: {}", projectCommonConfigDTO);
        return monthDonateClient.addOrUpdateProjectConfig(projectCommonConfigDTO);
    }

    @PostMapping("sea-get-project-base-info")
    Response<SeaProjectVO> getProjectBaseInfo(@RequestParam("projectId") Long projectId){
        log.info("getProjectBaseInfo projectId: {}", projectId);
        return monthDonateClient.getProjectBaseInfo(projectId);
    }

    @PostMapping("sea-get-project-config")
    Response<ProjectConfigVO> seaGetProjectConfig(){
        return monthDonateClient.seaGetProjectConfig();
    }

    @PostMapping("get-biz-type")
    Response<Map<String, Integer>> getBizType() {

        Map<String, Integer> map = Maps.newHashMap();
        map.put("中华社会救助基金会", 23);
        map.put("中华慈善总会", 162);
        map.put("平顶山市慈善总会", 164);

        return NewResponseUtil.makeSuccess(map);
    }




}
