package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.collect.Maps;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoStatusBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfAdminOperationRecordBiz;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.FinanceApproveService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

/**
 * Created by ahrievil on 2017/3/27.
 */
@Controller
@RequestMapping(path = "/admin/crowdfunding/change-type")
public class CrowdfundingChangeTypeController {

    private static final Logger LOGGER = LoggerFactory.getLogger(CrowdfundingChangeTypeController.class);
    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private AdminCrowdfundingInfoStatusBiz crowdfundingInfoStatusBiz;
    @Autowired
    private CfAdminOperationRecordBiz cfAdminOperationRecordBiz;
	@Autowired
	private  SeaAccountClientV1 seaAccountClientV1;
	@Autowired
	private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;
    @Autowired
    private FinanceApproveService financeApproveService;

    @RequiresPermission("change-type:get")
    @RequestMapping(path = "get", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response showWhatWeGet (@RequestParam String infoId) {
        LOGGER.info("CrowdfundingChangeType showWhatWeGet infoId:{}", infoId);
        if (infoId == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoId);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        Map<String, Object> result = Maps.newHashMap();
        result.put("crowdfundingInfo", crowdfundingInfo);
        return NewResponseUtil.makeSuccess(result);
    }

}
