package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.admin.util.AnchorPageUtils;
import com.shuidihuzhu.cf.biz.crowdfunding.CfQuestionnaireBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.admin.CfQuestionnaire;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.vo.AnchorPageBigInt2VO;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.model.CfUserInvitedLaunchCaseRecordModel;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @DATE 2019/12/12
 */
@RestController
@Slf4j
@RequestMapping(path = "/admin/cf/wenjuan")
public class WenjuanController {

    @Autowired
    private CfQuestionnaireBiz biz;

    @Autowired
    private SeaAccountClientV1 clientV1;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private AdminApproveService adminApproveService;
    @Autowired
    private MaskUtil maskUtil;


    @RequiresPermission("wenjuan:detail")
    @RequestMapping(path = "detail", method = RequestMethod.POST)
    public Response<CfQuestionnaire> detail(@RequestParam(name = "id") long id) {

        if (id < 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        CfQuestionnaire cfQuestionnaire = biz.getById(id);

        if (cfQuestionnaire == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_NO_ID);
        }

        String mobile = cfQuestionnaire.getMobile();
        cfQuestionnaire.setMobileMask(maskUtil.buildByEncryptPhone(mobile));
        cfQuestionnaire.setMobile(null);

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (cfQuestionnaire.getQStatus() == CfQuestionnaire.q_status_submit) {
            cfQuestionnaire.setSubmitTimeStr(simpleDateFormat.format(cfQuestionnaire.getSubmitTime()));
        }

        cfQuestionnaire.setSendTimeStr(simpleDateFormat.format(cfQuestionnaire.getSendTime()));
        if (cfQuestionnaire.getOperatorId() != 0) {
            AuthRpcResponse<AdminUserAccountModel> response = clientV1.getValidUserAccountById(cfQuestionnaire.getOperatorId());
            cfQuestionnaire.setOperatorName(Objects.isNull(response.getResult()) ? StringUtils.EMPTY : response.getResult().getName());
            cfQuestionnaire.setUpdateTimeStr(simpleDateFormat.format(cfQuestionnaire.getUpdateTime()));
        }

        if (CfQuestionnaireBiz.chuci_source.equals(cfQuestionnaire.getSource())
                || CfQuestionnaireBiz.cailiao_source.equals(cfQuestionnaire.getSource())){

            Map<Integer, CfUserInvitedLaunchCaseRecordModel> caseRecordMap = adminApproveService
                    .getCaseChannelRecordMap(Lists.newArrayList(cfQuestionnaire.getCaseId()));
            if (caseRecordMap.get(cfQuestionnaire.getCaseId()) != null){
                cfQuestionnaire.setName(caseRecordMap.get(cfQuestionnaire.getCaseId()).getServiceUserInfo(shuidiCipher));
            }
        }
        
        cfQuestionnaire.setWenjuanURl(biz.getQurl());
        cfQuestionnaire.setWenjuanId(biz.getQid());

        return NewResponseUtil.makeSuccess(cfQuestionnaire);
    }


    @RequiresPermission("wenjuan:list")
    @RequestMapping(path = "list", method = RequestMethod.POST)
    public Response<AnchorPageBigInt2VO<CfQuestionnaire>> list(@RequestParam(name = "qid", required = false) String qid,
                                                               @RequestParam(name = "recordId", required = false, defaultValue = "0") long recordId,
                                                               @RequestParam(name = "uId", required = false, defaultValue = "0") long uId,
                                                               @RequestParam(name = "name", required = false) String name,
                                                               @RequestParam(name = "qname", required = false) String qname,
                                                               @RequestParam(name = "channel", required = false) String channel,
                                                               @RequestParam(name = "source", required = false) String source,
                                                               @RequestParam(name = "status", required = false, defaultValue = "-1") int status,
                                                               @RequestParam(name = "mobile", required = false) String mobile,
                                                               @RequestParam(name = "startTime", required = false) String startTime,
                                                               @RequestParam(name = "endTime", required = false) String endTime,
                                                               @RequestParam(name = "pageSize", required = false, defaultValue = "10") int pageSize,
                                                               @RequestParam(defaultValue = "false") boolean isPre,
                                                               @RequestParam(defaultValue = "0") long anchor) {


        AnchorPageBigInt2VO<CfQuestionnaire> result = AnchorPageUtils.list(
                pageSize,
                isPre,
                realSize -> biz.getList(qid, uId, qname, name, channel, source, status, mobile, startTime, endTime, realSize, isPre, anchor, recordId),
                CfQuestionnaire::getId);

        result.setTotal(biz.total(qid, uId, qname, name, channel, source, status, mobile, startTime, endTime, recordId));

        return NewResponseUtil.makeSuccess(result);
    }

}
