package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;


import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.model.crowdfunding.caseRepeat.InitialRepeatCaseView;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfRepeatInfoBiz;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCfRepeatView;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping(path = "/admin/crowdfunding/repeat")
@Slf4j
public class CrowdfundingRepeatController {


    @Autowired
    private AdminCfRepeatInfoBiz cfRepeatInfoBiz;

    @RequiresPermission("cf-repeat:get-list")
    @RequestMapping(path = "/get-repeat-list", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    @Deprecated
    public Response get(@RequestParam("caseId") int caseId) {
        return NewResponseUtil.makeSuccess(cfRepeatInfoBiz.selectRepeatInfoViewByCaseId(caseId));
    }

    @RequiresPermission("cf-repeat:run-case-repeat")
    @RequestMapping(path = "/run-case-repeat", method = RequestMethod.POST)
    public void runCaseIdRepeat(@RequestParam("caseId") int caseId) {
        cfRepeatInfoBiz.handleCfMaterialRepeat(caseId, true);
    }

    @RequiresPermission("cf-repeat:get-list-v2")
    @RequestMapping(path = "/get-repeat-list-v2", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response get(@RequestParam("caseId") int caseId, @RequestParam("current")int current, @RequestParam("pageSize") int pageSize) {

        PageInfo<AdminCfRepeatView>  repeatViewPageInfo = cfRepeatInfoBiz.selectRepeatInfoPageByCaseId(caseId, current, pageSize);
        Map<String, Object>  result = new HashMap<>();
        result.put("total", repeatViewPageInfo.getTotal());
        result.put("list", repeatViewPageInfo.getList() == null ? Lists.newArrayList() : repeatViewPageInfo.getList());
        return NewResponseUtil.makeSuccess(result);
    }

}
