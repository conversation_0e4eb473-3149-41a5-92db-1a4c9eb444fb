package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.model.service.MobileUserIdModel;
import com.shuidihuzhu.cf.admin.util.MaskUtil;
import com.shuidihuzhu.cf.adminfeign.CrowdfundingVolunteerClewFeignClient;
import com.shuidihuzhu.cf.biz.admin.AdminOrganizationBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfinfoShareRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdFundingVerificationBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingCityBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingVolunteerBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingVolunteerHospitalBiz;
import com.shuidihuzhu.cf.constants.crowdfunding.CrowdfundingCons;
import com.shuidihuzhu.cf.delegate.other.IOtherDelegate;
import com.shuidihuzhu.cf.delegate.other.IWeiXinDelegate;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.delegate.saas.SeaAuthClientV1;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.delegate.volunteer.IVolunteerDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminCfRefundEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.enums.volunteer.RoleVolunteerEnum;
import com.shuidihuzhu.cf.model.clew.CFClewInfoModelResult;
import com.shuidihuzhu.cf.model.clew.CFClewRegisterModel;
import com.shuidihuzhu.cf.model.common.CommonCityModel;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingVolunteerHospital;
import com.shuidihuzhu.cf.model.dedicated.DepartmentResultResult;
import com.shuidihuzhu.cf.model.dedicated.ProfessionalResultModel;
import com.shuidihuzhu.cf.model.volunteer.VolunteerTypeModel;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.api.client.CrowdfundingCityFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.growthtool.client.CfGenerateQRCodeFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CfVolunteerMaterialDO;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteerVo;
import com.shuidihuzhu.client.cf.growthtool.model.DepartmentModel;
import com.shuidihuzhu.client.cf.growthtool.model.ProfessionalModel;
import com.shuidihuzhu.client.cf.growthtool.model.VolunteerSearchModel;
import com.shuidihuzhu.client.grpc.account.v1.feign.SimpleUserAccountServiceClient;
import com.shuidihuzhu.common.web.enums.admin.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.IdCardUtil;
import com.shuidihuzhu.common.web.util.MobileUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import com.shuidihuzhu.frame.client.response.FeignResponse;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * Created by lixiaoshuang on 2018/2/27.
 */
@RestController
@RequestMapping(path = "/admin/crowdfunding/volunteer", produces = "application/json;charset=UTF-8")
@Slf4j
@RefreshScope
public class CrowdfundingVolunteerController {
    @Autowired
    private CrowdfundingVolunteerBiz crowdfundingVolunteerBiz;
    @Autowired
    private CrowdfundingVolunteerHospitalBiz crowdfundingVolunteerHospitalBiz;
    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Autowired
    private AdminCrowdFundingVerificationBiz adminCrowdFundingVerificationBiz;
    @Autowired
    private AdminCfinfoShareRecordBiz adminCfinfoShareRecordBiz;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired
    private AdminCfInfoExtBiz adminCfInfoExtBiz;
    @Autowired
    private CrowdfundingVolunteerClewFeignClient crowdfundingVolunteerClewFeignClient;
    @Resource
    private SeaAccountClientV1 seaAccountClientV1;
    @Autowired
    private SimpleUserAccountServiceClient simpleUserAccountServiceClient;
//    @Autowired
//    private IBaiduPhotoAiDelegate baiduPhotoAiDelegate;
    @Autowired
    private IOtherDelegate otherDelegate;
    @Autowired
    private CfClewtrackFeignClient cfClewtrackFeignClient;
    @Autowired
    private SeaAuthClientV1 seaAuthClientV1;
    @Autowired
    private IWeiXinDelegate weiXinDelegate;
    @Autowired
    private AdminOrganizationBiz adminOrganizationBiz;
    @Autowired
    private IVolunteerDelegate volunteerDelegate;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Resource
    private AdminCrowdfundingCityBiz adminCrowdfundingCityBiz;
    @Autowired
    private CfGenerateQRCodeFeignClient cfGenerateQRCodeFeignClient;
    @Resource
    private CrowdfundingCityFeignClient crowdfundingCityFeignClient;

    /**
     * 省直辖县级行政区划/自治区直辖县级行政区划集合
     */
    @Value("#{'${apollo.directly.under.the.jurisdiction.list:}'.split(',')}")
    private List<Integer> directlyUnderTheJurisdictionList;

    @ApiModelProperty("获取重复城市集合 简阳市：目前挂在省直辖跟成都市下，现属于成都市，过滤掉省直辖/" +
            "枞阳县：目前挂在安庆市跟铜陵市下，现属于铜陵市，过滤掉安庆市/" +
            "寿县：目前挂在淮南市跟六安市下，现属于淮南市，过滤掉六安市 ")
    @Value("#{'${apollo.repetition.city.list:}'.split(',')}")
    private List<Integer> repetitionCityList;

    // 专属链接 前缀
    private static String h5URL = "https://www.shuidichou.com/tf/volunteer/register";

    private final String COMMON_EMAIL = "<EMAIL>";
    private static final String COMMON_MOBILE = "18232696261";

    private static final Integer ONE_WEEK = 7;
    private static final Integer ONE_MONTH = 31;

    /**
     * 权限控制列表
     */
    public static final List<String> VOLUNTEERPERMISSION = Lists.newArrayList(RoleVolunteerEnum.TEAM.getPermission(),
            RoleVolunteerEnum.SERVE_ADMIN.getPermission(),RoleVolunteerEnum.PART_TIME.getPermission(),
            RoleVolunteerEnum.SERIOUS_ILLNESS_SALVAGE_ANGEL.getPermission());

    /**
     * 根据userid获取获取对应权限的查看类型
     * @param userid
     * @return
     */
    @RequiresPermission("volunteer:type-list")
    @RequestMapping(path = "/volunteertype-list", method = RequestMethod.POST)
    public Response getVolunteerTypeList(@RequestParam(name = "userid") Integer userid){
        Integer adminUserId = ContextUtil.getAdminUserId();
        if (userid == null){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        log.info("volunteertype-list,userid:{},adminUserId:{}",userid,adminUserId);
        return getVolunteerTypeListByUserId(userid);
    }

    private Response getVolunteerTypeListByUserId(Integer userid) {
        AuthRpcResponse<Set<String>> rpcResponse = seaAuthClientV1.validUserPermissions(userid, VOLUNTEERPERMISSION);
        Set<String> permissions = null;
        if (rpcResponse != null && rpcResponse.getCode() == 0){
            permissions = rpcResponse.getResult();
        }
        if (CollectionUtils.isEmpty(permissions)){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_NOT_PRIVILEGE);
        }
        List<Integer> values = permissions.stream().map(s -> RoleVolunteerEnum.parse(s).getValue()).collect(Collectors.toList());
        List<VolunteerTypeModel> volunteerTypeModelList = Lists.newArrayList();
        for (Integer value: values) {
            VolunteerTypeModel volunteerTypeModel = new VolunteerTypeModel();
            volunteerTypeModel.setVolunteerType(value);
            volunteerTypeModel.setVolunteerDesc(CrowdfundingVolunteerEnum.volunteerType.parse(value).getDesc());
            volunteerTypeModelList.add(volunteerTypeModel);
        }
        if (CollectionUtils.isEmpty(volunteerTypeModelList)){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_NOT_PRIVILEGE);
        }else{
            volunteerTypeModelList.sort(Comparator.comparing(volunteerTypeModel -> volunteerTypeModel.getVolunteerType()));
            return NewResponseUtil.makeSuccess(volunteerTypeModelList);
        }
    }

    /**
     * 志愿者列表
     *
     * @param current
     * @param pageSize
     * @return
     */
    @RequiresPermission("volunteer:volunteer-list")
    @RequestMapping(path = "/volunteer-list", method = RequestMethod.POST)
    public Response getVolunteer(@RequestParam(name = "current") Integer current,
                                 @RequestParam(name = "pageSize") Integer pageSize,
                                 @RequestParam(name = "volunteerType", defaultValue = "0") Integer volunteerType,
                                 @RequestParam(name = "volunteerName", defaultValue = "") String volunteerName,
                                 @RequestParam(name = "mobile", defaultValue = "") String mobile,
                                 @RequestParam(name = "provinceId", defaultValue = "0") Integer provinceId,
                                 @RequestParam(name = "cityId", defaultValue = "0") Integer cityId,
                                 @RequestParam(name = "beginDate",required = false) String beginDate,
                                 @RequestParam(name = "endDate",required = false) String endDate,
                                 @RequestParam(name = "workStatus",required = false) Integer workStatus,
                                 @RequestParam(name = "level", defaultValue = "",required = false) Integer level,
                                 @RequestParam(name = "applyStatus",required = false) Integer applyStatus) {
        if (pageSize == null || current == null || pageSize < CrowdfundingCons.MIN_PAGE_SIZE
                || pageSize > CrowdfundingCons.MAX_PAGE_SIZE) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if(!this.queryTimeLimit(beginDate, endDate, ONE_WEEK)){
            return NewResponseUtil.makeError(AdminErrorCode.DAY_RANGE_ERROR);
        }
        //不输入志愿者类型，不展示数据
        if (volunteerType <= 0){
            return NewResponseUtil.makeError(AdminErrorCode.NOT_CHOOSE_VOLUNTEERTYPE);
        }
        List<CrowdfundingVolunteer> volunteerList = null;
        try {
            VolunteerSearchModel volunteerSearchModel = new VolunteerSearchModel(volunteerType,volunteerName,mobile,provinceId,cityId,beginDate,endDate,applyStatus,workStatus,level, null);
            volunteerList = crowdfundingVolunteerBiz.getVolunteerNew(current, pageSize, volunteerSearchModel);
        } catch (Exception e) {
            log.error("CrowdfundingVolunteerController getVolunteer volunteerVoList error", e);
        }
        List<CrowdfundingVolunteerVo> volunteerVoList = getCrowdfundingVolunteerVos(volunteerList);
        Map<String, Object> result = Maps.newHashMap();
        result.put("pagination", PageUtil.transform2PageMap(volunteerList));
        result.put("data", volunteerVoList);
        return NewResponseUtil.makeSuccess(result);
    }

    private List<CrowdfundingVolunteerVo> getCrowdfundingVolunteerVos(List<CrowdfundingVolunteer> volunteerList) {
        List<CrowdfundingVolunteerVo> volunteerVoList = new ArrayList<>();
        List<CrowdfundingCity> provinceList = null;
        List<CrowdfundingCity> cityList = null;
        List<CrowdfundingCity> coutryList = null;
        if (!CollectionUtils.isEmpty(volunteerList)) {
            List<Integer> cityIds = new ArrayList<>();
            List<Integer> countryIds = Lists.newArrayList();
            List<Integer> provinceIds = Lists.newArrayList();
            for (CrowdfundingVolunteer crowdfundingVolunteer : volunteerList) {
                cityIds.add(crowdfundingVolunteer.getCityId());
                countryIds.add(crowdfundingVolunteer.getCountyId());
                provinceIds.add(crowdfundingVolunteer.getProvinceId());
            }
            cityList = adminCrowdfundingCityBiz.getByIds(cityIds);
            coutryList = adminCrowdfundingCityBiz.getByIds(countryIds);
            provinceList = adminCrowdfundingCityBiz.getByIds(provinceIds);
        }
        for (CrowdfundingVolunteer volunteer : volunteerList) {
            CrowdfundingVolunteerVo crowdfundingVolunteerVo = volunteer.buildCrowdfundingVolunteerVo(shuidiCipher);
            crowdfundingVolunteerVo.setIdCardNumber("");
            crowdfundingVolunteerVo.setEmail("");
            volunteerVoList.add(crowdfundingVolunteerVo);
            crowdfundingVolunteerVo.setCityName(getCityName(cityList,volunteer.getCityId()));
            //设置省份名称
            crowdfundingVolunteerVo.setProvinceName(getCityName(provinceList,volunteer.getProvinceId()));
            //设置区县名称
            crowdfundingVolunteerVo.setCountryName(getCityName(coutryList,volunteer.getCountyId()));
            CfVolunteerMaterialDO materialDO = crowdfundingVolunteerBiz.getCfVolunteerMaterialDOByUniqueCode(volunteer.getUniqueCode());
            if (materialDO != null) {
                if(volunteer.getCityId()>0) {
                    crowdfundingVolunteerVo.setCityName(getCityName(cityList,materialDO.getCityId()));
                    //设置省份名称
                    crowdfundingVolunteerVo.setProvinceName(getCityName(provinceList,materialDO.getProvinceId()));
                    //设置区县名称
                    crowdfundingVolunteerVo.setCountryName(getCityName(coutryList,materialDO.getCountryId()));
                }
                //设置医院名名称
                crowdfundingVolunteerVo.setHospitalName(materialDO.getHospitalName());
                //一级职称
                crowdfundingVolunteerVo.setFirstProfessionalTitle(materialDO.getFirstProfessionalTitle());
                //二级职称
                crowdfundingVolunteerVo.setSecondProfessionalTitle(materialDO.getSecondProfessionalTitle());
                //一级科室
                crowdfundingVolunteerVo.setFirstDepartment(materialDO.getFirstDepartment());
                //二级科室
                crowdfundingVolunteerVo.setSecondDepartment(materialDO.getSecondDepartment());
            }
        }
        return volunteerVoList;
    }

    private String getCityName(List<CrowdfundingCity>cityList,Integer cityId) {
        for (CrowdfundingCity city : cityList) {
            if (cityId!=null && cityId == city.getId()) {
                return city.getName();
            }
        }
        return "";
    }

    /**
     * 添加或更新志愿者
     *   新增：  职业、目前职称、推荐人手机号、邮寄地址、所在医院、所在科室、
     *   员工类型 新增 大病救助天使
     * @param param
     * @return
     */
    @RequiresPermission("volunteer:add-or-update")
    @RequestMapping(path = "/addOrUpdate", method = RequestMethod.POST)
    public Response addOrUpdate(@RequestParam(name = "param") String param) {
        //log.info("CrowdfundingVolunteerController addVolunteer param:{}", param);
        //CrowdfundingVolunteer crowdfundingVolunteer = JSON.parseObject(param, CrowdfundingVolunteer.class);//已检查过
        //if (crowdfundingVolunteer.getAccountType()== CrowdfundingVolunteerEnum.AccountTypeEnum.UNITARY_ACCOUNT.getValue()){
        //    crowdfundingVolunteer.setEmail(COMMON_EMAIL);
        //    crowdfundingVolunteer.setMobile(COMMON_MOBILE);
        //    crowdfundingVolunteer.setMis(null);
        //    crowdfundingVolunteer.setLevel(CrowdfundingVolunteerEnum.VolunteerLevelEnum.COMMON_LEADER.getLevel());
        //}
        //if (crowdfundingVolunteer.getVolunteerType() < 0 || StringUtils.isBlank(crowdfundingVolunteer.getVolunteerName()) ||
        //        crowdfundingVolunteer.getWorkStatus() < 0 || crowdfundingVolunteer.getEntryTime() == null ||
        //        StringUtils.isBlank(crowdfundingVolunteer.getMobile()) ) {
        //    return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        //}
        //// 身份证号设置成 非必填项
        //if (StringUtils.isNotEmpty(crowdfundingVolunteer.getIdCardNumber()) && IdCardUtil.illegal(crowdfundingVolunteer.getIdCardNumber())) {
        //    return NewResponseUtil.makeError(AdminErrorCode.IDCARD_VERIFY_FAILED);
        //}
        //if (!StringUtils.isBlank(crowdfundingVolunteer.getBankCard())) {
        //    if (!BackCardUtil.checkBankCard(crowdfundingVolunteer.getBankCard())) {
        //        return NewResponseUtil.makeError(AdminErrorCode.BACKCARD_VERIFY_FAILED);
        //    }
        //    String bankCard = oldShuidiCipher.aesEncrypt(crowdfundingVolunteer.getBankCard());
        //    crowdfundingVolunteer.setBankCard(bankCard);
        //}
        //String idCardNumber = oldShuidiCipher.aesEncrypt(crowdfundingVolunteer.getIdCardNumber());
        //String mobile = oldShuidiCipher.aesEncrypt(crowdfundingVolunteer.getMobile());
        //crowdfundingVolunteer.setIdCardNumber(idCardNumber);
        //crowdfundingVolunteer.setMobile(mobile);
        //CrowdfundingVolunteerVo volunteerInfo = null;
        //if (crowdfundingVolunteer.getId() != 0) {
        //    volunteerInfo = crowdfundingVolunteerBiz.getVolunteerInfo(crowdfundingVolunteer.getId());
        //}
        //if (null == volunteerInfo) {
        //    if (!StringUtils.isBlank(crowdfundingVolunteer.getQrCode())) {
        //        crowdfundingVolunteer.setIsGenerate(2);
        //    } else {
        //        crowdfundingVolunteer.setIsGenerate(1);
        //    }
        //    if (StringUtils.isBlank(crowdfundingVolunteer.getUniqueCode())) {
        //        crowdfundingVolunteer.setUniqueCode(generateRandomStr());
        //    }
        //    crowdfundingVolunteer.setAngelUrl(generateAngelUrl(crowdfundingVolunteer.getUniqueCode(),crowdfundingVolunteer.getVolunteerType()));
        //    try {
        //        Response<Integer> addResult = crowdfundingVolunteerBiz.addVolunteer(crowdfundingVolunteer);
        //        if (addResult.ok()) {
        //            return NewResponseUtil.makeSuccess(AdminErrorCode.SUCCESS);
        //        }
        //    } catch (Exception e) {
        //        log.error("CrowdfundingVolunteerController addVolunteer addVolunteer error", e);
        //    }
        //    return NewResponseUtil.makeError(AdminErrorCode.ADD_FAILED);
        //} else {
        //    if (StringUtils.isEmpty(volunteerInfo.getAngelUrl())) {
        //        crowdfundingVolunteer.setAngelUrl(generateAngelUrl(crowdfundingVolunteer.getUniqueCode(),crowdfundingVolunteer.getVolunteerType()));
        //    }
        //    try {
        //        Response<Integer> updateVolunteerInfo = crowdfundingVolunteerBiz.updateVolunteerInfo(crowdfundingVolunteer);
        //        if (updateVolunteerInfo.ok()) {
        //            return NewResponseUtil.makeSuccess(AdminErrorCode.SUCCESS);
        //        }
        //    } catch (Exception e) {
        //        log.error("CrowdfundingVolunteerController updateVolunteerINfo error", e);
        //    }
        //    return NewResponseUtil.makeError(AdminErrorCode.UPDATE_FAILURE);
        //}
        return NewResponseUtil.makeSuccess(true);
    }

    /**
     * 生成 天使 url
     * 专属URL生成规则：h5页面URL+站长/医护channel+站长/医护唯一标识
     * 站长channel：BD_zhanzhang_null
     * 医护channel：BD_doctor_null
     * @param uniqueCode
     * @param volunteerType
     * @return
     */
    public String generateAngelUrl(String uniqueCode,int volunteerType){
        String channel = getChannelByVolunteerType(volunteerType);
        return StringUtils.isEmpty(channel)?"":h5URL+"?channel="+channel+"&uniqueCode="+uniqueCode;
    }

    /**
     * 判断是否 需要生成天使url
     * 只有 服务站长、大病救助天使 才需要生成
     * @param volunteerType
     * @return channel 为"" 说明不需要生成
     */
    public String getChannelByVolunteerType(int volunteerType){
        String channel = "";
        if (CrowdfundingVolunteerEnum.volunteerType.SERVE_ADMIN.getValue() == volunteerType){
            channel = "BD_zhanzhang_null";
        }else if (CrowdfundingVolunteerEnum.volunteerType.SERIOUS_ILLNESS_SALVAGE_ANGEL.getValue() == volunteerType){
            channel = "BD_doctor_null";
        }
        return channel;
    }

    /**
     * 志愿者详情
     *
     * @param id
     * @return
     */
    @RequiresPermission("volunteer:detail")
    @RequestMapping(path = "/detail", method = RequestMethod.POST)
    public Response getDetail(@RequestParam(name = "id") long id) {
        log.info("CrowdfundingVolunteerController getDetail id:{}", id);
        if (id <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingVolunteerVo volunteerInfoVo = null;
        try {
            volunteerInfoVo = crowdfundingVolunteerBiz.getVolunteerInfo(id);

        } catch (Exception e) {
            log.error("CrowdfundingVolunteerController getDetail volunteerInfo error", e);
        }
        if (null == volunteerInfoVo) {
            return NewResponseUtil.makeError(AdminErrorCode.NO_SUCH_PERSON);
        }
        List<Integer> userIds = new ArrayList<>();
        userIds.add(volunteerInfoVo.getCityId());
        userIds.add(volunteerInfoVo.getProvinceId());
        List<CrowdfundingCity> byIds = adminCrowdfundingCityBiz.getByIds(userIds);
        for (CrowdfundingCity city : byIds) {
            if (city.getId() == volunteerInfoVo.getCityId()) {
                volunteerInfoVo.setCityName(city.getName());
            } else if (city.getId() == volunteerInfoVo.getProvinceId()) {
                volunteerInfoVo.setProvinceName(city.getName());
            }
        }
        return NewResponseUtil.makeSuccess(volunteerInfoVo);
    }

    /**
     * 获取省
     *
     * @return
     */
    @RequiresPermission("volunteer:get-province")
    @RequestMapping(path = "/get-province", method = RequestMethod.POST)
    public Response<List<CrowdfundingCity>> getProvince() {
        List<CrowdfundingCity> province = adminCrowdfundingCityBiz.getProvince();
        return NewResponseUtil.makeSuccess(province);
    }

    /**
     * 获取市
     *
     * @param id
     * @return
     */
    @RequiresPermission("volunteer:get-city")
    @RequestMapping(path = "/get-city", method = RequestMethod.POST)
    public Response<List<CrowdfundingCity>> getCity(@RequestParam(name = "id") int id) {
        log.info("CrowdfundingVolunteerController getCity id:{}", id);
        if (id <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<CrowdfundingCity> children = adminCrowdfundingCityBiz.getChildren(id);
        return NewResponseUtil.makeSuccess(children);
    }

    /**
     * 获取市or街道
     */
    @RequiresPermission("volunteer:get-city-or-street")
    @RequestMapping(path = "/get-city-or-street", method = RequestMethod.POST)
    public Response<List<CrowdfundingCity>> getCityOrStreet(@RequestParam(required = false) Integer realParentId,
                                                            @RequestParam(required = false) String areaCode) {
        log.info("CrowdfundingVolunteerController getCityOrStreet id:{} {}", realParentId, areaCode);
        if (Objects.isNull(realParentId) && StringUtils.isEmpty(areaCode)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (Objects.nonNull(realParentId)) {
            List<CrowdfundingCity> children = adminCrowdfundingCityBiz.getByRealParentId(realParentId);
            return NewResponseUtil.makeSuccess(children);
        }
        if (StringUtils.isNotEmpty(areaCode)) {
            return crowdfundingCityFeignClient.getCityStreetByCode(areaCode);
        }
        return NewResponseUtil.makeSuccess(Collections.emptyList());
    }

    @RequiresPermission("volunteer:get-counties")
    @RequestMapping(path = "/get-counties-by-name", method = RequestMethod.POST)
    public Response<List<CrowdfundingCity>> getCountiesByName(@RequestParam(name = "name") String name) {
       return NewResponseUtil.makeSuccess(adminCrowdfundingCityBiz.getCountiesByName(name));
    }

    @RequiresPermission("volunteer:get-name")
    @RequestMapping(path = "/get-by-name", method = RequestMethod.POST)
    public Response<CrowdfundingCity> getByName(@RequestParam(name = "name") String name) {
        return NewResponseUtil.makeSuccess(adminCrowdfundingCityBiz.getCityByName(name));
    }

    @RequestMapping(path = "/get-by-id", method = RequestMethod.POST)
    public Response<CrowdfundingCity> getById(@RequestParam(name = "id") int id) {
        return NewResponseUtil.makeSuccess(adminCrowdfundingCityBiz.getById(id));
    }


    @ApiOperation("获取所有的省份和城市")
    @PostMapping("list-all-city-info")
    public Response<List<CommonCityModel>> listAllCityInfo() {
        List<CrowdfundingCity> provinceList = adminCrowdfundingCityBiz.getProvince();
        List<CommonCityModel> commonCityModelList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(provinceList)) {
            for (CrowdfundingCity crowdfundingCity : provinceList) {
                CommonCityModel commonCityModel = new CommonCityModel();
                commonCityModel.setProvinceId(crowdfundingCity.getId());
                commonCityModel.setProvinceName(crowdfundingCity.getName());
                List<CrowdfundingCity> children = adminCrowdfundingCityBiz.getChildren(crowdfundingCity.getId());
                List<CommonCityModel.CityInfo> cityList = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(children)) {
                    children.forEach(item -> {
                        CommonCityModel.CityInfo cityInfo = new CommonCityModel.CityInfo();
                        cityInfo.setCityId(item.getId());
                        cityInfo.setCityName(item.getName());
                        cityList.add(cityInfo);
                    });
                }
                commonCityModel.setCityInfoList(cityList);
                commonCityModelList.add(commonCityModel);
            }
        }

        return NewResponseUtil.makeSuccess(commonCityModelList);
    }

    @ApiOperation("获取所有的省份和地级市以及省直辖县级行政区划/自治区直辖县级行政区划的县级行政单位")
    @PostMapping("list-prefecture-city-info")
    public Response<List<CommonCityModel>> listPrefectureCityInfo() {
        List<CrowdfundingCity> provinceList = adminCrowdfundingCityBiz.getProvince();
        if (CollectionUtils.isEmpty(provinceList)) {
            return NewResponseUtil.makeSuccess();
        }

        List<CommonCityModel> result = Lists.newArrayList();
        for (CrowdfundingCity province : provinceList) {
            CommonCityModel commonCityModel = new CommonCityModel();
            commonCityModel.setProvinceId(province.getId());
            commonCityModel.setProvinceName(province.getName());

            List<CommonCityModel.CityInfo> cityList = Lists.newArrayList();
            List<CrowdfundingCity> crowdfundingCityList = adminCrowdfundingCityBiz.getByRealParentId(province.getId());
            if (CollectionUtils.isEmpty(crowdfundingCityList)) {
                continue;
            }

            for (CrowdfundingCity city : crowdfundingCityList) {
                //属于省直辖县级行政区划/自治区直辖县级行政区划的，展示具体的县级行政单位
                if (directlyUnderTheJurisdictionList.contains(city.getId())) {
                    //获取县级行政单位
                    List<CrowdfundingCity> countyLevelCityList = adminCrowdfundingCityBiz.getByRealParentId(city.getId());
                    if (CollectionUtils.isNotEmpty(countyLevelCityList)) {
                        for (CrowdfundingCity countyLevelCity : countyLevelCityList) {
                            CommonCityModel.CityInfo cityInfo = new CommonCityModel.CityInfo();
                            cityInfo.setCityId(countyLevelCity.getId());
                            cityInfo.setCityName(countyLevelCity.getName());
                            cityList.add(cityInfo);
                        }
                    }
                } else {
                    CommonCityModel.CityInfo cityInfo = new CommonCityModel.CityInfo();
                    cityInfo.setCityId(city.getId());
                    cityInfo.setCityName(city.getName());
                    cityList.add(cityInfo);
                }
            }

            cityList = cityList.stream().filter(v -> !repetitionCityList.contains(v.getCityId())).collect(Collectors.toList());
            commonCityModel.setCityInfoList(cityList);
            result.add(commonCityModel);
        }

        return NewResponseUtil.makeSuccess(result);
    }

    /**
     * 获取医院名称
     *
     * @param cityId
     * @return
     */
    @RequiresPermission("volunteer:get-hospital")
    @RequestMapping(path = "/get-hospital", method = RequestMethod.POST)
    @Deprecated
    public Response getHospital(@RequestParam(name = "cityId") int cityId,
                                @RequestParam(name = "hostpitalName",required = false,defaultValue = "") String hostpitalName) {
        log.info("CrowdfundingVolunteerController getHospital cityId:{}", cityId);
        if (cityId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        Map<String, Object> hospitalNameList = null;
        try {
            hospitalNameList = otherDelegate.selectFromName(cityId,hostpitalName,1,30);
        } catch (Exception e) {
            log.error("CrowdfundingVolunteerController getHospital hospitalNameList error", e);
        }
        if (hospitalNameList.get("cfHospitals")!=null){
            return NewResponseUtil.makeSuccess(hospitalNameList.get("cfHospitals"));
        }
        return NewResponseUtil.makeSuccess(Lists.newArrayList());
    }

    /**
     * 添加医院信息
     *
     * @param name
     * @param level
     * @param cityId
     * @return
     */
    @RequiresPermission("volunteer:add-hospital")
    @RequestMapping(path = "/add-hospital", method = RequestMethod.POST)
    public Response addHospital(@RequestParam(name = "name") String name,
                                @RequestParam(name = "level", defaultValue = "0") int level,
                                @RequestParam(name = "cityId", defaultValue = "0") int cityId) {
        log.info("CrowdfundingVolunteerController addHospital name:{},level:{},cityId:{}", name, level, cityId);
        if (StringUtils.isBlank(name)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingVolunteerHospital crowdfundingVolunteerHospital = new CrowdfundingVolunteerHospital();
        crowdfundingVolunteerHospital.setName(name);
        crowdfundingVolunteerHospital.setLevel(level);
        crowdfundingVolunteerHospital.setCityId(cityId);
        System.out.println(crowdfundingVolunteerHospital.toString());
        try {
            int result = crowdfundingVolunteerHospitalBiz.insertHospital(crowdfundingVolunteerHospital);
            if (result > 0) {
                return NewResponseUtil.makeSuccess(AdminErrorCode.SUCCESS);
            }
        } catch (Exception e) {
            log.error("CrowdfundingVolunteerController addHospital inertHospital error", e);
        }
        return NewResponseUtil.makeError(AdminErrorCode.ADD_FAILED);
    }

    /**
     * 发送短信
     *
     * @param mobile
     * @return
     */
    @RequiresPermission("volunteer:send-sms")
    @RequestMapping(path = "/sendSms", method = RequestMethod.POST)
    public Response sendSms(@RequestParam(name = "id") long id,
                            @RequestParam(name = "mobile") String mobile) {
        log.info("CrowdfundingVolunteerController sendSms id:{}, mobile:{}", id, mobile);
        if (StringUtils.isBlank(mobile) || id <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        String qrCode = crowdfundingVolunteerBiz.getQRCode(id);
        if (StringUtils.isBlank(qrCode)) {
            return NewResponseUtil.makeError(AdminErrorCode.NO_QR_CODE);
        }
//        List<String> mobileList = new ArrayList<>();
//        mobileList.add(mobile);
//        StringBuilder message = new StringBuilder();
//        message.append("恭喜你，你已拥有自己专属的二维码，请立刻点击下方链接保存。当协助他人发起筹款时，【请务必让对方扫描你的二维码】，进入筹款专属通道发起筹款，" +
//                "系统将自动识别此筹款由你协助发起并进行记录。如有疑问，请联系与你沟通的工作人员。立刻开始你的专属服务之旅吧！加油!" + qrCode);
//
//
//        boolean b = smsBiz.sendSms(message.toString(), mobileList, RecordConstant.MSG_TYPE_USER_NORMAL, RecordConstant.BIZ_TYPE_AIXINCHOU, 553);

        crowdfundingVolunteerBiz.send553Sms(Lists.newArrayList(mobile), qrCode);
//        if (b) {
        return NewResponseUtil.makeSuccess(AdminErrorCode.SUCCESS);
//        }
//        return NewResponseUtil.makeError(AdminErrorCode.SMS_SEND_FAILED);
    }

    /**
     * 生成唯一二维码
     *
     * @return
     */
    @RequiresPermission("volunteer:generate-qr-code")
    @RequestMapping(path = "/generate-QRCode", method = RequestMethod.POST)
    public Response generateQRCode(@RequestParam(name = "id") long id,
                                   @RequestParam(name = "logo",required = false) String base64Logo) {
        log.info("CrowdfundingVolunteerController generateQRCode id:{}", id);
        if (id < 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        Map<String, Object> responseMap = new HashMap<>();
        int result = 0;
        if (id == 0) {
            String uniqueCode = generateRandomStr();//生成随机邀请码
            String scene = "cf_volunteer_" + uniqueCode;
            String ticket = weiXinDelegate.generateQrcode(scene);
            String qrCode = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=" + ticket;
            log.info("CrowdfundingVolunteerController generateQRCode qrCode:{}", qrCode);
            responseMap.put("qrCode", qrCode);
            responseMap.put("uniqueCode", uniqueCode);
            return NewResponseUtil.makeSuccess(responseMap);
        }
        String uniqueCode = null;
        try {
            uniqueCode = crowdfundingVolunteerBiz.getUniqueCodeById(id);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " getUniqueCodeById id:{} err:", id, e);
            return NewResponseUtil.makeError(AdminErrorCode.GENERATE_FAILED);
        }
        String scene = "cf_volunteer_" + uniqueCode;
        String ticket = weiXinDelegate.generateQrcode(scene);
        String qrCode = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=" + ticket;
        String qrCodeWithLogo = null;
        responseMap.put("uniqueCode", uniqueCode);
        //是否已经生成
        String code = crowdfundingVolunteerBiz.getQRCode(id);
        if (!StringUtils.isBlank(code)) {
            responseMap.put("qrCode", code);
            return NewResponseUtil.makeSuccess(responseMap);
        }
        //
        if (Strings.isEmpty(base64Logo)) {
            log.info("base64Logoerror:{}", base64Logo);
            result = crowdfundingVolunteerBiz.updateFields(qrCode, id);
            if (result > 0) {
                responseMap.put("qrCode", qrCode);
                return NewResponseUtil.makeSuccess(responseMap);
            }
        }else {
            //调用生成带有logo二维码的方法
            qrCodeWithLogo = cfGenerateQRCodeFeignClient.generateQrCcode(qrCode, base64Logo, uniqueCode).getData();
            if(Strings.isEmpty(qrCodeWithLogo)){
                return NewResponseUtil.makeError(AdminErrorCode.NOT_CONNECT);
            }
            log.info("CrowdfundingVolunteerController generateQRCode qrCodeWithLogo:{}", qrCodeWithLogo);
            result = crowdfundingVolunteerBiz.updateFields(qrCodeWithLogo, id);
            if (result > 0) {
                responseMap.put("qrCodeWithLogo", qrCodeWithLogo);
                return NewResponseUtil.makeSuccess(responseMap);
            }
        }
            return NewResponseUtil.makeError(AdminErrorCode.GENERATE_FAILED);

    }


    /**
     * 生成随机邀请码
     *
     * @return
     */
    public static String generateRandomStr() {
        //字符源，可以根据需要删减
        String generateSource = "23456789abcdefghgklmnpqrstuvwxyz";//去掉1和i ，0和o
        String rtnStr = "";
        for (int i = 0; i < 6; i++) {
            //循环随机获得当次字符，并移走选出的字符
            String nowStr = String.valueOf(generateSource.charAt((int) Math.floor(Math.random() * generateSource.length())));//已检查过
            rtnStr += nowStr;
            generateSource = generateSource.replaceAll(nowStr, "");
        }
        return rtnStr + ThreadLocalRandom.current().nextInt(100, 1000);
    }

    /**
     * 发起案例详情
     *
     * @param uniqueCode
     * @return
     */
    @RequiresPermission("volunteer:get-nomask-case-info")
    @RequestMapping(path = "get-nomask-case-info", method = RequestMethod.POST)
    public Response getNoMaskCaseInfo(@RequestParam(name = "caseId") Integer caseId,
                                @RequestParam(name = "uniqueCode") String uniqueCode,
                                @RequestParam(name = "volunteerType", defaultValue = "-1",required = false) Integer volunteerType) {
        AdminCrowdfundingInfo adminCrowdfundingInfo = adminCrowdfundingInfoBiz.getInfoByUniqueCodeAndCaseId(caseId);
        if(adminCrowdfundingInfo==null){
            return NewResponseUtil.makeFail(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<AdminCrowdfundingInfo> retList = getBdCaseInfo(Lists.newArrayList(adminCrowdfundingInfo));
        CrowdfundingVolunteer crowdfundingVolunteer = crowdfundingVolunteerBiz.getByUniqueCode(uniqueCode);
        getAdminCrowdfundingInfos(retList,false,volunteerType,Lists.newArrayList(crowdfundingVolunteer));
        return NewResponseUtil.makeSuccess(retList.get(0));
    }

    /**
     * 发起案例详情
     *
     * @param current
     * @param pageSize
     * @param mobile
     * @param uniqueCode
     * @param startTime
     * @param endTime
     * @return
     */
    @RequiresPermission("volunteer:case-info")
    @RequestMapping(path = "case-info", method = RequestMethod.POST)
    public Response getCaseInfo(@RequestParam(name = "current") Integer current,
                                @RequestParam(name = "pageSize") Integer pageSize,
                                @RequestParam(name = "provinceId", defaultValue = "-1") int provinceId,
                                @RequestParam(name = "mobile", defaultValue = "") String mobile,
                                @RequestParam(name = "uniqueCode") String uniqueCode,
                                @RequestParam(name = "volunteerType", defaultValue = "-1",required = false) Integer volunteerType,
                                @RequestParam(name = "volunteerMobile", defaultValue = "",required = false) String volunteerMobile,
                                @RequestParam(required = false) String startTime,
                                @RequestParam(required = false) String endTime) {
        log.info("CrowdfundingVolunteerController getCaseInfo mobile:{},startTime:{},endTime:{},provinceId:{}", mobile, startTime, endTime, provinceId);
        if (pageSize == null || current == null || pageSize < CrowdfundingCons.MIN_PAGE_SIZE
                || pageSize > CrowdfundingCons.MAX_PAGE_SIZE) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if(!this.queryTimeLimit(startTime, endTime, ONE_MONTH)){
            return NewResponseUtil.makeError(AdminErrorCode.DAY_RANGE_MONTH_ERROR);
        }
        //将手机号转为userId的筛选条件
        long userId = 0;
        if (!StringUtils.isBlank(mobile)) {
            MobileUserIdModel userIdByMobile = simpleUserAccountServiceClient.getUserIdByMobile(mobile);
            if (null != userIdByMobile) {
                userId = userIdByMobile.getUserId();
            }
            if (userId <= 0) {
                List emptyList = Lists.newArrayList();
                Map<String, Object> result = Maps.newHashMap();
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("current", 1);
                map.put("pageSize", 10);
                map.put("total", 0);
                result.put("pagination", map);
                result.put("data", emptyList);
                return NewResponseUtil.makeSuccess(result);
            }
        }
        if (provinceId >= 0) {
            volunteerMobile = oldShuidiCipher.aesEncrypt(volunteerMobile);
            VolunteerSearchModel volunteerSearchModel = new VolunteerSearchModel(volunteerType, null, volunteerMobile, provinceId, 0, null, null, null, null,null,null);
            List<CrowdfundingVolunteer> crowdfundingVolunteers = crowdfundingVolunteerBiz.getVolunteerNew(1, Integer.MAX_VALUE, volunteerSearchModel);
            List<AdminCrowdfundingInfo> crowdfundingInfoList = adminCrowdfundingInfoBiz.getInfoByVolunteerTypeWithMobileNew(current, pageSize,startTime, endTime, userId,crowdfundingVolunteers);
            //返回bd案例来源字段
            getBdCaseInfo(crowdfundingInfoList);
            Map<String, Object> result = Maps.newHashMap();
            result.put("pagination", PageUtil.transform2PageMap(crowdfundingInfoList));
            result.put("data", getAdminCrowdfundingInfos(crowdfundingInfoList,true,volunteerType,crowdfundingVolunteers));
            return NewResponseUtil.makeSuccess(result);
        } else {
            CrowdfundingVolunteer crowdfundingVolunteer = crowdfundingVolunteerBiz.getByUniqueCode(uniqueCode);
            List<AdminCrowdfundingInfo> crowdfundingInfoList = adminCrowdfundingInfoBiz.getInfoByVolunteerTypeWithMobileNew(current, pageSize, startTime, endTime, userId,Lists.newArrayList(crowdfundingVolunteer));
            //返回bd案例来源字段
            getBdCaseInfo(crowdfundingInfoList);
            Map<String, Object> result = Maps.newHashMap();
            result.put("pagination", PageUtil.transform2PageMap(crowdfundingInfoList));
            result.put("data", getAdminCrowdfundingInfos(crowdfundingInfoList,true,volunteerType,Lists.newArrayList(crowdfundingVolunteer)));
            return NewResponseUtil.makeSuccess(result);
        }
    }

    /**
     * 获取BD渠道信息
     *
     * @param crowdfundingInfoList
     * @return
     */
    private List<AdminCrowdfundingInfo> getBdCaseInfo(List<AdminCrowdfundingInfo> crowdfundingInfoList) {
        if(crowdfundingInfoList == null || crowdfundingInfoList.size() == 0){
            return crowdfundingInfoList;
        }
        List<String> infoIds = crowdfundingInfoList.stream().map(AdminCrowdfundingInfo::getInfoId).collect(Collectors.toList());
        Response<List<CfBdCaseInfoDo>> res = cfClewtrackFeignClient.getBdCaseInfoByInfoIds(infoIds);
        if (res != null && res.ok()) {
            List<String> listIds = res.getData().stream().map(CfBdCaseInfoDo::getInfoUuid).collect(Collectors.toList());
            crowdfundingInfoList.stream().filter(p -> listIds.contains(p.getInfoId())).forEach(s -> {
                CfBdCaseInfoDo bdCaseInfo = res.getData().stream().filter(p -> p.getInfoUuid().equals(s.getInfoId())).collect(Collectors.toList()).get(0);
                s.setCaseMethod(bdCaseInfo.getCaseMethod());
                s.setCaseSource(bdCaseInfo.getCaseSource());
                s.setFirstRaise(bdCaseInfo.getFirstRaise());
                s.setCaseFirstCount(bdCaseInfo.getCaseFirstCount() == null ? 0 : bdCaseInfo.getCaseFirstCount());
            });
        }
        crowdfundingInfoList.stream().filter(h -> h.getCaseFirstCount() == null).forEach(m -> {
            m.setCaseFirstCount(0);
        });
        return crowdfundingInfoList;
    }

    @ApiOperation("查询志愿者邀请的登记线索")
    @RequiresPermission("volunteer:clew-info")
    @RequestMapping(path = "clew-info", method = RequestMethod.POST)
    public Response getClewInfo(@RequestParam(value = "uniqueCode") String uniqueCode,
                                @RequestParam(value = "clewStatus", required = false) Integer clewStatus,
                                @RequestParam(value = "registerStartTime", required = false) String registerStartTime,
                                @RequestParam(value = "registerEndTime", required = false) String registerEndTime,
                                @RequestParam(value = "phone", required = false) String phone,
                                @RequestParam(value = "pageSize", defaultValue = "10", required = false) Integer pageSize,
                                @RequestParam(value = "pageNo", defaultValue = "1", required = false) Integer pageNo){
        if(!this.queryTimeLimit(registerStartTime, registerEndTime, ONE_WEEK)){
            return NewResponseUtil.makeError(AdminErrorCode.DAY_RANGE_ERROR);
        }
        FeignResponse<CFClewInfoModelResult<CFClewRegisterModel>> feignResult = crowdfundingVolunteerClewFeignClient.getClewlist(uniqueCode, clewStatus,
                registerStartTime, registerEndTime, phone, pageSize, pageNo);
        if (feignResult.notOk()){
            NewResponseUtil.makeFail(feignResult.getMsg());
        }
        return NewResponseUtil.makeSuccess(feignResult.getData());
    }

    @ApiOperation("添加一条指定志愿者的登记线索")
    @RequiresPermission("volunteer:save-clew-info")
    @RequestMapping(path = "save-clew-info", method = RequestMethod.POST)
    public Response saveCkewInfo(@RequestParam(value = "uniqueCode") String uniqueCode,
                                 @RequestParam(value = "phone") String phone,
                                 @RequestParam(value = "diseaseName", defaultValue = "", required = false) String diseaseName,
                                 @RequestParam(value = "fundraisingObject", defaultValue = "", required = false) String fundraisingObject,
                                 @RequestParam(value = "specialMark", defaultValue = "", required = false) String specialMark,
                                 @RequestParam(value = "userId", required = false) Integer userId
                                 ){
        AdminUserAccountModel adminUser = seaAccountClientV1.getValidUserAccountById(userId).getResult();
        CrowdfundingVolunteer volunteer = crowdfundingVolunteerBiz.getByUniqueCode(uniqueCode);
        String channel = volunteer==null?"":getChannelByVolunteerType(volunteer.getVolunteerType());
        FeignResponse<Boolean> feignResponse = crowdfundingVolunteerClewFeignClient.addClew(uniqueCode, phone, diseaseName, fundraisingObject, adminUser == null ? "" : adminUser.getName(), specialMark,channel);
        if (feignResponse.notOk() && feignResponse.getCode()!=FeignResponse.fallback().getCode()){
            return NewResponseUtil.makeFail(feignResponse.getMsg());
        }else if (feignResponse.getCode()==FeignResponse.fallback().getCode()){
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_SOCKET_TIMEOUT_ERROR);
        }
        return NewResponseUtil.makeSuccess(null);
    }

    /**
     * 添加上省市县的名称
     *
     * @param crowdfundingInfoList
     * @return
     */
    private List<AdminCrowdfundingInfo> getAdminCrowdfundingInfos
    (List<AdminCrowdfundingInfo> crowdfundingInfoList,boolean needMask,int volunteerType,List<CrowdfundingVolunteer> crowdfundingVolunteerList) {
        List<Integer> ids = Lists.newArrayList();
        Map<String,String> unqueCodeMapgroupWithAreaName  = new HashedMap();
        for (CrowdfundingVolunteer crowdfundingVolunteer : crowdfundingVolunteerList) {
            ids.add(crowdfundingVolunteer.getProvinceId());
            ids.add(crowdfundingVolunteer.getCityId());
            ids.add(crowdfundingVolunteer.getCountyId());
            if (volunteerType==CrowdfundingVolunteerEnum.volunteerType.TEAM.getValue()){
                String groupWithAreaName = adminOrganizationBiz.getGroupWithAreaNameByMis(crowdfundingVolunteer.getMis());
                if (StringUtils.isBlank(groupWithAreaName)){
                    continue;
                }
                unqueCodeMapgroupWithAreaName.put(crowdfundingVolunteer.getMis(),groupWithAreaName);
            }
        }
        List<CrowdfundingCity> cityList = null;
        if (!CollectionUtils.isEmpty(ids)) {
            cityList = adminCrowdfundingCityBiz.getByIds(ids);
        }
        List<AdminCrowdfundingInfo> crowdfundingInfos = getCrowdfundingInfoList(crowdfundingInfoList);
        for (AdminCrowdfundingInfo crowdfundingInfo : crowdfundingInfos) {
            if (volunteerType==CrowdfundingVolunteerEnum.volunteerType.TEAM.getValue()){
                if (unqueCodeMapgroupWithAreaName.size()==1){
                    crowdfundingInfo.setGroupName(unqueCodeMapgroupWithAreaName.values().toArray()[0].toString().split("&")[0]);
                    crowdfundingInfo.setAreaName(unqueCodeMapgroupWithAreaName.values().toArray()[0].toString().split("&")[1]);
                }else {
                    String groupWithAreaName = unqueCodeMapgroupWithAreaName.get(StringUtils.defaultIfEmpty(crowdfundingInfo.getMis(), ""));
                    crowdfundingInfo.setGroupName(StringUtils.isEmpty(groupWithAreaName)?"":groupWithAreaName.split("&")[0]);
                    crowdfundingInfo.setAreaName(StringUtils.isEmpty(groupWithAreaName)?"":groupWithAreaName.split("&")[1]);
                }
            }
            for (CrowdfundingCity city : cityList) {
                if (city.getId() == crowdfundingInfo.getProvinceId()) {
                    crowdfundingInfo.setProvinceName(city.getName());
                }
                if (city.getId() == crowdfundingInfo.getCityId()) {
                    crowdfundingInfo.setCityName(city.getName());
                }
                if (city.getId() == crowdfundingInfo.getCountyId()) {
                    crowdfundingInfo.setCountyName(city.getName());
                }
            }
            if(needMask){
                crowdfundingInfo.setMobile(MaskUtil.getTelephoneMask(crowdfundingInfo.getMobile()));
            }
        }
        return crowdfundingInfos;
    }

    private List<AdminCrowdfundingInfo> getCrowdfundingInfoList(List<AdminCrowdfundingInfo> crowdfundingInfoList) {
        List<String> uniqueCodes = new ArrayList<>();
        List<String> crowdfundingIds = new ArrayList<>();
        List<Integer> infoIds = new ArrayList<>();
        List<Long> userIds = new ArrayList<>();
        for (AdminCrowdfundingInfo adminCrowdfundingInfo : crowdfundingInfoList) {
            uniqueCodes.add(adminCrowdfundingInfo.getVolunteerUniqueCode());
            crowdfundingIds.add(adminCrowdfundingInfo.getInfoId());
            infoIds.add(adminCrowdfundingInfo.getId());
            userIds.add(adminCrowdfundingInfo.getUserId());
        }

        List<CrowdfundingVolunteer> volunteerList = crowdfundingVolunteerBiz.getVolunteerName(uniqueCodes);
        Map<String, Integer> verifyMap = adminCrowdFundingVerificationBiz.getVerify(crowdfundingIds);
        Map<Integer, Integer> shareCount = adminCfinfoShareRecordBiz.getShareCount(infoIds);
        List<UserInfoModel> byUserIds = Lists.newArrayList();
        Lists.partition(userIds, 500).forEach(item -> byUserIds.addAll(userInfoServiceBiz.getUserInfoByUserIdBatch(item)));
        Map<Long, String> userMap = new HashMap<>();
        for (UserInfoModel byUserId : byUserIds) {
            long userId = byUserId.getUserId();
            String cryptoMobile = byUserId.getCryptoMobile();
            String realMobile = shuidiCipher.decrypt(cryptoMobile);
            userMap.put(userId, realMobile);
        }
        for (AdminCrowdfundingInfo crowdfundingInfo : crowdfundingInfoList) {
            for (CrowdfundingVolunteer volunteer : volunteerList) {
                if (crowdfundingInfo.getVolunteerUniqueCode().equals(volunteer.getUniqueCode())) {
                    crowdfundingInfo.setVolunteerType(volunteer.getVolunteerType());
                    crowdfundingInfo.setVolunteerName(volunteer.getVolunteerName());
                    crowdfundingInfo.setVolunteerMobile(shuidiCipher.decrypt(volunteer.getMobile()));
                    crowdfundingInfo.setProvinceId(volunteer.getProvinceId());
                    crowdfundingInfo.setCityId(volunteer.getCityId());
                    crowdfundingInfo.setCountyId(volunteer.getCountyId());
                    crowdfundingInfo.setMis(volunteer.getMis());
                }
            }
            for (Map.Entry<String, Integer> entry : verifyMap.entrySet()) {
                if (crowdfundingInfo.getInfoId().equals(entry.getKey())) {
                    crowdfundingInfo.setVerifyCount(entry.getValue());
                }
            }
            for (Map.Entry<Integer, Integer> entry : shareCount.entrySet()) {
                if (crowdfundingInfo.getId() == entry.getKey()) {
                    crowdfundingInfo.setShareCount(entry.getValue());
                }
            }
            for (Map.Entry<Long, String> entry : userMap.entrySet()) {
                if (crowdfundingInfo.getUserId() == entry.getKey()) {
                    crowdfundingInfo.setMobile(entry.getValue());
                }
            }
            try {
                crowdfundingInfo.setTargetAmount(crowdfundingInfo.getTargetAmount() / 100);
                crowdfundingInfo.setAmount(crowdfundingInfo.getAmount() / 100);
            } catch (Exception e) {
                log.error("CrowdfundingVolunteerController getCrowdfundingInfoList error", e);
            }
            crowdfundingInfo.setVolunteerUniqueCode("");
            crowdfundingInfo.setUserId(0);
        }
        List<String> infoIdList = Lists.newArrayList();
        for (AdminCrowdfundingInfo adminCrowdfundingInfo : crowdfundingInfoList) {
            if (StringUtils.isBlank(adminCrowdfundingInfo.getMobile())) {
                infoIdList.add(adminCrowdfundingInfo.getInfoId());
            }
        }
        Map<String, CfInfoExt> cfInfoExtMap = adminCfInfoExtBiz.getMapByInfoUuids(infoIdList);
        for (AdminCrowdfundingInfo adminCrowdfundingInfo : crowdfundingInfoList) {
            if (StringUtils.isBlank(adminCrowdfundingInfo.getMobile())) {
                CfInfoExt cfInfoExt = cfInfoExtMap.get(adminCrowdfundingInfo.getInfoId());
                adminCrowdfundingInfo.setMobile(adminCfInfoExtBiz.getRegisterMobile(cfInfoExt));
            }
        }
        return crowdfundingInfoList;
    }

    @RequiresPermission("volunteer:add-or-update-new")
    @RequestMapping(path = "/addOrUpdateNew", method = RequestMethod.POST)
    public Response addOrUpdateNew(@RequestParam(name = "param") String param) {
        AdminUserAccountModel accountModel = seaAccountClientV1.getValidUserAccountById(ContextUtil.getAdminUserId()).getResult();
        log.info("CrowdfundingVolunteerController addVolunteer param:{}", param);
        CrowdfundingVolunteerVo crowdfundingVolunteerVo = JSON.parseObject(param, CrowdfundingVolunteerVo.class);//已检查过
        if (crowdfundingVolunteerVo.getAccountType()== CrowdfundingVolunteerEnum.AccountTypeEnum.UNITARY_ACCOUNT.getValue()){
            crowdfundingVolunteerVo.setEmail(COMMON_EMAIL);
            crowdfundingVolunteerVo.setMobile(COMMON_MOBILE);
            crowdfundingVolunteerVo.setMis(null);
            crowdfundingVolunteerVo.setLevel(CrowdfundingVolunteerEnum.VolunteerLevelEnum.COMMON_LEADER.getLevel());
        }
        if (crowdfundingVolunteerVo.getVolunteerType() < 0 || StringUtils.isBlank(crowdfundingVolunteerVo.getVolunteerName()) ||
                crowdfundingVolunteerVo.getWorkStatus() < 0 ||
                StringUtils.isBlank(crowdfundingVolunteerVo.getMobile())) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (StringUtils.isNotEmpty(crowdfundingVolunteerVo.getIdCardNumber()) && IdCardUtil.illegal(crowdfundingVolunteerVo.getIdCardNumber())) {
            return NewResponseUtil.makeError(AdminErrorCode.IDCARD_VERIFY_FAILED);
        }
        if (StringUtils.isNotEmpty(crowdfundingVolunteerVo.getMobile()) && MobileUtil.illegal(crowdfundingVolunteerVo.getMobile())) {
            return NewResponseUtil.makeError(AdminErrorCode.MOBILE_FORMAT_ERROR);
        }

        String userMobile = crowdfundingVolunteerVo.getMobile();
        crowdfundingVolunteerVo.setOperatorUserId(accountModel.getId());
        crowdfundingVolunteerVo.setOperatorName(accountModel.getName());
        String bankCard = oldShuidiCipher.aesEncrypt(crowdfundingVolunteerVo.getBankCard());
        String idCardNumber = oldShuidiCipher.aesEncrypt(crowdfundingVolunteerVo.getIdCardNumber());
        String mobile = oldShuidiCipher.aesEncrypt(crowdfundingVolunteerVo.getMobile());
        crowdfundingVolunteerVo.setBankCard(bankCard);
        crowdfundingVolunteerVo.setIdCardNumber(idCardNumber);
        crowdfundingVolunteerVo.setMobile(mobile);
        crowdfundingVolunteerVo.setCountryId(crowdfundingVolunteerVo.getCountyId());
        CrowdfundingVolunteerVo volunteerInfo = null;
        if (crowdfundingVolunteerVo.getId() != 0) {
            volunteerInfo = crowdfundingVolunteerBiz.getVolunteerInfo(crowdfundingVolunteerVo.getId());
        }
        Long volunteerId = volunteerInfo == null ? -1L : crowdfundingVolunteerVo.getId();
        //校验在离职人员身份证号码是否存在
        if(StringUtils.isNotBlank(crowdfundingVolunteerVo.getIdCardNumber())) {
            Boolean isExist = volunteerDelegate.checkIsExistByTypeOrIdentity(crowdfundingVolunteerVo.getVolunteerType(),
                    crowdfundingVolunteerVo.getIdCardNumber(), volunteerId);
            if (isExist) {
                return NewResponseUtil.makeError(AdminErrorCode.CF_VOLUNTEER_IDENTITYREPEAT_ERROR);
            }
        }
        //校验在职人员email是否存在
        if(crowdfundingVolunteerVo.getWorkStatus() == 2 && StringUtils.isNotBlank(crowdfundingVolunteerVo.getEmail()) && !COMMON_EMAIL.equals(crowdfundingVolunteerVo.getEmail())){
            Boolean isExist = volunteerDelegate.checkIsExistByTypeOrEmail(crowdfundingVolunteerVo.getVolunteerType(),
                    crowdfundingVolunteerVo.getEmail(), volunteerId);
            if(isExist){
                return NewResponseUtil.makeError(AdminErrorCode.CF_VOLUNTEER_EMAILREPEAT_ERROR);
            }
        }
        //如果个人帐号设置为离职,校验一下二维码状态
        if (crowdfundingVolunteerVo.getWorkStatus() == 1 && crowdfundingVolunteerVo.getAccountType() == 0){
            if (crowdfundingVolunteerVo.getQrCodeStatus() != 1){
                //二维码状态必须是冻结状态
                return NewResponseUtil.makeError(AdminErrorCode.CF_VOLUNTEER_QRCODESTATUS_ERROR);
            }
        }
        //新增顾问 确保在用户中心已注册
        volunteerDelegate.checkRegister(userMobile);

        if (null == volunteerInfo) {
            if (!StringUtils.isBlank(crowdfundingVolunteerVo.getQrCode())) {
                crowdfundingVolunteerVo.setIsGenerate(2);
            } else {
                crowdfundingVolunteerVo.setIsGenerate(1);
            }
            if (StringUtils.isBlank(crowdfundingVolunteerVo.getUniqueCode())) {
                crowdfundingVolunteerVo.setUniqueCode(generateRandomStr());
            }
            crowdfundingVolunteerVo.setAngelUrl(generateAngelUrl(crowdfundingVolunteerVo.getUniqueCode(),crowdfundingVolunteerVo.getVolunteerType()));
            try {
                Response<Integer> addResult = crowdfundingVolunteerBiz.addVolunteer(crowdfundingVolunteerVo.buildCrowdfundingVolunteer());
                if (addResult.notOk()) {
                    return NewResponseUtil.makeFail(addResult.getMsg());
                }
                crowdfundingVolunteerBiz.addCfVolunteerMaterial(crowdfundingVolunteerVo.buildCfVolunteerMaterialDO());
                return NewResponseUtil.makeSuccess(AdminErrorCode.SUCCESS);
            } catch (Exception e) {
                log.error("CrowdfundingVolunteerController addVolunteer addVolunteer error", e);
            }
            return NewResponseUtil.makeError(AdminErrorCode.ADD_FAILED);
        } else {
            if (StringUtils.isEmpty(volunteerInfo.getAngelUrl())) {
                crowdfundingVolunteerVo.setAngelUrl(generateAngelUrl(crowdfundingVolunteerVo.getUniqueCode(),crowdfundingVolunteerVo.getVolunteerType()));
            }
            try {
                Response<Integer> updateVolunteerInfo = crowdfundingVolunteerBiz.updateVolunteerInfo(crowdfundingVolunteerVo.buildCrowdfundingVolunteer());
                if (updateVolunteerInfo.notOk()) {
                    return updateVolunteerInfo;
                }
                // 修改信息时  兼容前端不传 uniqueCode这个值
                if (StringUtils.isBlank(crowdfundingVolunteerVo.getUniqueCode())) {
                    crowdfundingVolunteerVo.setUniqueCode(volunteerInfo.getUniqueCode());
                }
                CfVolunteerMaterialDO materialDO = crowdfundingVolunteerBiz.getCfVolunteerMaterialDOByUniqueCode(crowdfundingVolunteerVo.getUniqueCode());
                if(materialDO!=null) {
                    crowdfundingVolunteerBiz.updateCfVolunteerMaterial(crowdfundingVolunteerVo.buildCfVolunteerMaterialDO());
                }else{
                    crowdfundingVolunteerBiz.addCfVolunteerMaterial(crowdfundingVolunteerVo.buildCfVolunteerMaterialDO());
                }
                return NewResponseUtil.makeSuccess(AdminErrorCode.SUCCESS);
            } catch (Exception e) {
                log.error("CrowdfundingVolunteerController updateVolunteerINfo error", e);
            }
            return NewResponseUtil.makeError(AdminErrorCode.UPDATE_FAILURE);
        }
    }

    @RequiresPermission("volunteer:apply-status")
    @RequestMapping(path = "/applyStatus", method = RequestMethod.POST)
    public Response applyStatus(@RequestParam(name = "id") Long id,
                                @RequestParam(name = "applyStatus") int applyStatus,
                                @RequestParam(name = "refuseReasons",required = false) String refuseReasons){
        AdminUserAccountModel accountModel = seaAccountClientV1.getValidUserAccountById(ContextUtil.getAdminUserId()).getResult();
        CrowdfundingVolunteerVo volunteerInfo = crowdfundingVolunteerBiz.getVolunteerInfo(id);
        if (volunteerInfo==null){
            return NewResponseUtil.makeError(AdminErrorCode.NO_SUCH_PERSON);
        }
        String angelUrl = null;
        if (StringUtils.isEmpty(volunteerInfo.getAngelUrl()) && AdminCfRefundEnum.ApplyStatus.APPROVE_SUCCESS.getCode()==applyStatus) {
            angelUrl = generateAngelUrl(volunteerInfo.getUniqueCode(),volunteerInfo.getVolunteerType());
        }
        String qrCode = null;
        if (StringUtils.isEmpty(volunteerInfo.getQrCode()) && AdminCfRefundEnum.ApplyStatus.APPROVE_SUCCESS.getCode()==applyStatus) {

            String scene = "cf_volunteer_" + volunteerInfo.getUniqueCode();
            String ticket = weiXinDelegate.generateQrcode(scene);
            qrCode = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=" + ticket;
        }
        crowdfundingVolunteerBiz.updateApplyStatusById(id,applyStatus,accountModel.getName(),accountModel.getId(),angelUrl,refuseReasons,qrCode);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("volunteer:get-professionals")
    @ApiOperation("获取职称数据")
    @ResponseBody
    @RequestMapping(path = "/get-professionals",produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    public Response getProfessionals(){
        List<ProfessionalModel> professionalModels = ProfessionalModel.ProfessionalModelDict.getAllProfessinals();
        ProfessionalResultModel resultModel = new ProfessionalResultModel();
        resultModel.setFirst(professionalModels);
        Map<Integer,List<ProfessionalModel>> second = Maps.newHashMap();
        for(ProfessionalModel professionalModel : professionalModels){
            second.put(professionalModel.getId(),professionalModel.getChildModels());
        }
        resultModel.setSecond(second);
        return NewResponseUtil.makeSuccess(resultModel);
    }

    @RequiresPermission("volunteer:get-departments")
    @ApiOperation("获取科室数据")
    @ResponseBody
    @RequestMapping(path = "/get-departments",produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    public Response getDepartments(){
        List<DepartmentModel> departmentModels = DepartmentModel.DepartmentModelDict.getAllDepartments();
        DepartmentResultResult resultModel = new DepartmentResultResult();
        resultModel.setFirst(departmentModels);
        Map<Integer,List<DepartmentModel>> second = Maps.newHashMap();
        for(DepartmentModel departmentModel : departmentModels){
            second.put(departmentModel.getId(),departmentModel.getChildModels());
        }
        resultModel.setSecond(second);
        return NewResponseUtil.makeSuccess(resultModel);
    }

    /**
     * 判断是否符合查询时间限制
     * @param startTime
     * @param endTime
     * @param limit
     * @return
     */
    private Boolean queryTimeLimit(String startTime, String endTime, Integer limit){
        try {
            DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            if(StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)){
                Date date = new Date();
                startTime = sdf.format(DateUtil.addDays(date, -limit));
                endTime = sdf.format(date);
                return true;
            }
            Long diffDate = DateUtil.diffDate(sdf.parse(endTime), sdf.parse(startTime));
            if (limit >= Math.abs(diffDate))
                return true;
        }catch (Exception e){
            log.error("CrowdfundingVolunteerController.queryTimeLimit startTime:"+startTime+",endTime:"+endTime+",limit:"+limit,  e);
        }
        return false;
    }

}
