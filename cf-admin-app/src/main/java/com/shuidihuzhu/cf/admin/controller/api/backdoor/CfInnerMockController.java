package com.shuidihuzhu.cf.admin.controller.api.backdoor;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.controller.api.workorder.WorkOrderController;
import com.shuidihuzhu.cf.admin.util.result.ResultUtils;
import com.shuidihuzhu.cf.constants.CaseRiskConstants;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.domain.CfCaseRiskDO;
import com.shuidihuzhu.cf.enums.crowdfunding.risk.CfCaseRiskTypeEnum;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 内部 mock 接口
 *
 * <AUTHOR>
 * @since 2023-06-13 4:26 下午
 **/
@RestController
@RequestMapping("/innerapi/cf/admin")
public class CfInnerMockController {

    @Resource
    private IRiskDelegate riskDelegate;

    @ApiOperation("测试-MOCK 案例首次审核通过已经72小时")
    @PostMapping("test-mock-case-delay-72")
    public Response testCaseFirstApproveHasDelay72(String infoUuid) {
        OpResult opResult = riskDelegate.onCaseFirstApproveHasDelay72(infoUuid);
        return ResultUtils.transformOpResult2Response(opResult);
    }

    @ApiOperation("测试-MOCK 案例结束")
    @PostMapping("test-mock-case-end")
    public Response testCaseEnd(String infoUuid) {
        OpResult opResult = riskDelegate.onCaseEnd(infoUuid);
        return ResultUtils.transformOpResult2Response(opResult);
    }

    @ApiOperation("测试-主动验证风险")
    @PostMapping("test-verify")
    public Response testVerify(String infoUuid,
                               @ApiParam("1: 材料审核, 2: 提现审核")
                               @RequestParam int riskTYpe) {
        OpResult<CfCaseRiskDO> res = riskDelegate.getByInfoUuid(infoUuid, CfCaseRiskTypeEnum.parse(riskTYpe));
        if (res.isFailOrNullData()) {
            return ResultUtils.transformOpResult2Response(res);
        }
        CfCaseRiskDO riskDO = res.getData();
        OpResult<CfCaseRiskDO> result = riskDelegate.doVerify(riskDO, CfCaseRiskTypeEnum.parse(riskTYpe),
                CaseRiskConstants.DataLevel.LEVEL_ALL);

        return ResultUtils.transformOpResult2Response(result);
    }

    @ApiOperation("后门工具,查看工单分类")
    @PostMapping("view-work-classify")
    public Response<List<WorkOrderController.WorkItemMessage>> viewWorkClassify() {
        WorkOrderType[] values = WorkOrderType.values();
        List<WorkOrderController.WorkItemMessage> workItemMessageList = Lists.newArrayList();
        WorkOrderController.WorkItemMessage workItem = null;
        for (WorkOrderType workOrderType : values) {
            workItem = new WorkOrderController.WorkItemMessage();
            workItem.setItemCode(workOrderType.getType());
            workItem.setItemMessage(workOrderType.getMsg());
            workItemMessageList.add(workItem);
        }
        return NewResponseUtil.makeSuccess(workItemMessageList);
    }

    @ApiOperation("后门工具,查看操作分类")
    @PostMapping("view-work-handle-result")
    public Response<List<WorkOrderController.WorkItemMessage>> viewWorkHandleResult() {
        HandleResultEnum[] handleResultEnums = HandleResultEnum.values();
        List<WorkOrderController.WorkItemMessage> workItemMessageList = Lists.newArrayList();
        WorkOrderController.WorkItemMessage workItem = null;
        for (HandleResultEnum handleResult : handleResultEnums) {
            workItem = new WorkOrderController.WorkItemMessage();
            workItem.setItemCode(handleResult.getType());
            workItem.setItemMessage(handleResult.getShowMsg());
            workItemMessageList.add(workItem);
        }
        return NewResponseUtil.makeSuccess(workItemMessageList);
    }
}
