package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Maps;

import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfDailyCharityCfInfoDao;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CfDailyCharityCfInfo;
import com.shuidihuzhu.common.web.constants.CrowdfundingCons;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.BasicExample;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import jodd.util.StringUtil;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Author: Alvin Tian
 * Date: 2017/6/5 13:21
 */
@Controller
@RequestMapping(path = "/admin/crowdfunding/daily-charity/cf-info")
public class CfDailyCharityController {

	private static final Logger LOGGER = LoggerFactory.getLogger(CfDailyCharityController.class);

	@Autowired
	private AdminCfDailyCharityCfInfoDao adminCfDailyCharityCfInfoDao;

	@RequiresPermission("daily-charity:get-list")
	@RequestMapping(path = "get-list", method = RequestMethod.POST)
	@ResponseBody
	public Response list(Integer current, Integer pageSize, @RequestParam(required = false) String dt) {
		LOGGER.info("CfDailyCharityController get-list current:{},pageSize:{},startDate:{}", current, pageSize);
		if (pageSize == null || pageSize < CrowdfundingCons.MIN_PAGE_SIZE
		    || pageSize > CrowdfundingCons.MAX_PAGE_SIZE) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}
		BasicExample basicExample = new BasicExample();
		BasicExample.Criteria criteria = basicExample.or();
		if (StringUtil.isNotBlank(dt)) {
			criteria.andEqualTo("dt", dt);
		}
		criteria.andEqualTo("valid", true);
		basicExample.setOrderByClause("dt desc, order_num asc");
		PageHelper.startPage(current, pageSize);
		List<CfDailyCharityCfInfo> cfDailyCharityMessages = adminCfDailyCharityCfInfoDao.selectByPage(basicExample);
		Map<String, Object> result = Maps.newHashMap();
		result.put("pagination", PageUtil.transform2PageMap(cfDailyCharityMessages));
		result.put("list", cfDailyCharityMessages);
		return NewResponseUtil.makeSuccess(result);
	}

	@RequiresPermission("daily-charity:add-or-update")
	@RequestMapping(path = "add-or-update", method = RequestMethod.POST)
	@ResponseBody
	public Response addOrUpdate(String param) {
		LOGGER.info("CfDailyCharityController update param:{}", param);
		CfDailyCharityCfInfo cfDailyCharityCfInfo = JSON.parseObject(param, CfDailyCharityCfInfo.class);//已检查过
		LOGGER.info("CfDailyCharityController update param:{}", cfDailyCharityCfInfo);
		if (cfDailyCharityCfInfo == null) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}
		if (cfDailyCharityCfInfo.getCaseType() == null || cfDailyCharityCfInfo.getCaseId() == null ||
		    cfDailyCharityCfInfo.getDt() == null || cfDailyCharityCfInfo.getOrderNum() == null) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}
		CfDailyCharityCfInfo cfDailyCharityCfInfo1 = adminCfDailyCharityCfInfoDao.selectByPrimaryKey(cfDailyCharityCfInfo.getId());
		if (cfDailyCharityCfInfo1 == null) {
			cfDailyCharityCfInfo.setCreateTime(new Date());
			cfDailyCharityCfInfo.setLastModified(new Date());
			cfDailyCharityCfInfo.setValid(true);
			adminCfDailyCharityCfInfoDao.insert(cfDailyCharityCfInfo);
		} else {
			cfDailyCharityCfInfo.setLastModified(new Date());
			cfDailyCharityCfInfo.setValid(true);
			cfDailyCharityCfInfo.setCreateTime(cfDailyCharityCfInfo1.getCreateTime());
			adminCfDailyCharityCfInfoDao.updateByPrimaryKey(cfDailyCharityCfInfo);
		}
		return NewResponseUtil.makeSuccess(null);
	}

	@RequiresPermission("daily-charity:delete")
	@RequestMapping(path = "delete", method = RequestMethod.POST)
	@ResponseBody
	public Response delete(Integer id) {
		LOGGER.info("CfDailyCharityController delete id:{},userId:{}", id, ContextUtil.getAdminUserId());
		if (id == null) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}
		try {
			adminCfDailyCharityCfInfoDao.deleteByPrimaryKey(id);
		} catch (Exception e) {
			LOGGER.error("CfDailyCharityController delete error", e);
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
		}
		return NewResponseUtil.makeSuccess(null);
	}


}
