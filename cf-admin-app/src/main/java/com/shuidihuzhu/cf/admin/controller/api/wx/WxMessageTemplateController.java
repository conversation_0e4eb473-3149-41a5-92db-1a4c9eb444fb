package com.shuidihuzhu.cf.admin.controller.api.wx;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.constants.crowdfunding.CrowdfundingCons;
import com.shuidihuzhu.cf.enums.AdminErrorCode;

import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.wx.grpc.client.MsgTemplateResp;
import com.shuidihuzhu.wx.grpc.client.TemplateGrpcClient;
import com.shuidihuzhu.wx.grpc.model.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by lixiaoshuang on 2018/4/26.
 */
@RestController
@Slf4j
@RequestMapping("admin/cf/wx/message/template")
public class WxMessageTemplateController {
    @Autowired
    private TemplateGrpcClient templateGrpcClient;
    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @RequiresPermission("message-template:is-exist")
    @ApiOperation(value = "验证模板编号是否存在", notes = "")
    @RequestMapping(path = "/is-exist", method = RequestMethod.POST)
    public Response getTemplateByNumber(@RequestParam(name = "templateNumber") String templateNumber,
                                        @RequestParam(name = "token") String token) {
        log.info("WxMessageTemplateController getTemplateByNumber templateNumber:{}", templateNumber);
        if (StringUtils.isEmpty(templateNumber)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        MsgTemplateResp templateByNumber = templateGrpcClient.getTemplateByNumber(templateNumber);
        if (null == templateByNumber) {
            return NewResponseUtil.makeFail("验证模板是否存在失败");
        }
        boolean result = templateByNumber.getResult();
        Map<String, Boolean> map = Maps.newHashMap();
        map.put("result", result);
        return NewResponseUtil.makeSuccess(map);
    }

    @RequiresPermission("message-template:get-template-id")
    @ApiOperation(value = "添加模板,申请模板id", notes = "")
    @RequestMapping(path = "/get-template-id", method = RequestMethod.POST)
    public Response getTemplateId(@RequestParam(name = "templateNumber") String templateNumber,
                                  @RequestParam(name = "thirdType") int thirdType,
                                  @RequestParam(name = "token") String token) {
        Integer userId = ContextUtil.getAdminUserId();
        log.info("WxMessageTemplateController getTemplateId templateNumber:{},thirdType:{},userId:{},token:{}",
                templateNumber, thirdType, userId, token);
        if (StringUtils.isEmpty(templateNumber) || userId == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        AdminUserAccountModel accountModel = seaAccountClientV1.getValidUserAccountById(userId).getResult();
        if (null == accountModel) {
            return NewResponseUtil.makeError(AdminErrorCode.USER_ACCOUNT_NOT_EXISTS);
        }
        MsgTemplateResp msgTemplateResp = templateGrpcClient.getTemplateId(templateNumber, thirdType, userId, accountModel.getName());
        boolean result = msgTemplateResp.getResult();
        if (result) {
            return NewResponseUtil.makeResponse(0, "操作成功", null);
        }
        return NewResponseUtil.makeResponse(AdminErrorCode.OPERATION_FAILED.getCode(), AdminErrorCode.OPERATION_FAILED.getMsg(), null);
    }

    @RequiresPermission("message-template:get-template-list")
    @ApiOperation(value = "获取消息模板列表", notes = "")
    @RequestMapping(path = "/get-template-list", method = RequestMethod.POST)
    public Response getTemplateList(@RequestParam(name = "templateId", defaultValue = "") String templateId,
                                    @RequestParam(name = "templateTitle", defaultValue = "") String templateTitle,
                                    @RequestParam(name = "thirdType", defaultValue = "0") Integer thirdType,
                                    @RequestParam(name = "current") Integer current,
                                    @RequestParam(name = "pageSize") Integer pageSize,
                                    @RequestParam(name = "token") String token) {
        log.info("WxMessageTemplateController getTemplateList templateId：{},templateTitle:{},thirdType:{}", templateId,
                templateTitle, thirdType);
        if (pageSize == null || current == null || pageSize < CrowdfundingCons.MIN_PAGE_SIZE
                || pageSize > CrowdfundingCons.MAX_COMMENT_LEN) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        WxTemplateResponseModel wxTemplateResponseModel = templateGrpcClient.getTemplateList(templateId, templateTitle, thirdType, current, pageSize);
        if (null == wxTemplateResponseModel) {
            return NewResponseUtil.makeFail("获取消息模板列表失败");
        }
        Map<String, Object> result = Maps.newHashMap();
        Map<String, Object> map = Maps.newHashMap();
        map.put("current", wxTemplateResponseModel.getCurrent());
        map.put("pageSize", wxTemplateResponseModel.getPageSize());
        map.put("total", wxTemplateResponseModel.getTotal());
        result.put("pagination", map);
        result.put("data", wxTemplateResponseModel.getWxMsgTemplateModelList());
        return NewResponseUtil.makeSuccess(result);
    }

//    @ApiOperation(value = "批量复制模板消息", notes = "")
//    @RequestMapping(path = "/batch-copy-template", method = RequestMethod.POST)
//    public Response batchCopyTemplate(@RequestParam(name = "newThirdType") Integer newThirdType,
//                                      @RequestParam(name = "templateNumbers") String templateNumbers,
//                                      @RequestParam(name = "userId") Integer userId,
//                                      @RequestParam(name = "token") String token) {
//        log.info("WxMessageTemplateController batchCopyTemplate newThirdType:{},templateNumbers:{},userId:{}",
//                newThirdType, templateNumbers, userId);
//        if (null == newThirdType || StringUtils.isEmpty(templateNumbers) || null == userId) {
//            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
//        }
//        AdminUserAccountModel validById = adminAuthUserAccountBiz.getValidById(userId);
//        MsgTemplateResp msgTemplateResp = templateGrpcClient.batchCopyTemplate(newThirdType, templateNumbers, userId, validById.getName());
//        boolean result = msgTemplateResp.getResult();
//        Map<String, Boolean> map = Maps.newHashMap();
//        map.put("result", result);
//        return NewResponseUtil.makeSuccess(map);
//    }

    @RequiresPermission("message-template:get-template-info")
    @ApiOperation(value = "消息模板详情", notes = "")
    @RequestMapping(path = "/get-template-info", method = RequestMethod.POST)
    public Response getTemplateInfo(@RequestParam(name = "templateNumber") String templateNumber,
                                    @RequestParam(name = "token") String token) {
        log.info("WxMessageTemplateController getTemplateInfo templateNumber:{}", templateNumber);
        if (StringUtils.isEmpty(templateNumber)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        WxTemplateRelResponseMobdel wxTemplateRelResponseMobdel = templateGrpcClient.getTemplateInfo(templateNumber);
        List<WxMsgTemplateRelModel> wxMsgTemplateRelModelList = wxTemplateRelResponseMobdel.getWxMsgTemplateRelModelList();
        List<Integer> groupIdList = wxTemplateRelResponseMobdel.getGroupIdList();
        HashMap<String, Object> map = Maps.newHashMap();
        map.put("result", wxMsgTemplateRelModelList);
        map.put("groupId", groupIdList);
        return NewResponseUtil.makeSuccess(map);
    }

    @RequiresPermission("message-template:delete")
    @ApiOperation(value = "删除模板", notes = "")
    @RequestMapping(path = "/delete-message-template", method = RequestMethod.POST)
    public Response deleteMessageTemplate(@RequestParam("templateNumber") String templateNumber,
                                          @RequestParam(name = "token") String token) {
        Integer userId = ContextUtil.getAdminUserId();
        log.info("WxMessageTemplateController deleteMessageTemplate templateNumber:{},userId:{}", templateNumber, userId);
        if (StringUtils.isEmpty(templateNumber) || null == userId) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        AdminUserAccountModel validById = seaAccountClientV1.getValidUserAccountById(userId).getResult();
        MsgTemplateResp msgTemplateResp = templateGrpcClient.deleteMessageTemplate(templateNumber, userId, validById.getName());
        boolean result = msgTemplateResp.getResult();
        Map<String, Boolean> map = Maps.newHashMap();
        map.put("result", result);
        return NewResponseUtil.makeSuccess(map);
    }

    @RequiresPermission("message-template:get-wx-config")
    @RequestMapping(path = "/get-wx-config", method = RequestMethod.POST)
    @ApiOperation(value = "公众号模糊搜索", notes = "")
    public Response getWxConfig(@RequestParam("wxName") String wxName,
                                @RequestParam(name = "token") String token) {
        log.info("WxMessageTemplateController getWxConfig wxName:{}", wxName);
        if (StringUtils.isEmpty(wxName)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<WxInfoModel> wxByName = templateGrpcClient.getWxByName(wxName);
        if (CollectionUtils.isEmpty(wxByName)) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        return NewResponseUtil.makeSuccess(wxByName);
    }

    //获取日志
    @RequiresPermission("message-template:get-record")
    @ApiOperation(value = "获取日志", notes = "")
    @RequestMapping(path = "/get-record", method = RequestMethod.POST)
    public Response getRecord(@RequestParam("current") Integer current,
                              @RequestParam("pageSize") Integer pageSize,
                              @RequestParam(name = "token") String token) {
        log.info("WxMessageTemplateController getRecord current:{},pageSize:{}", current, pageSize);
        if (pageSize == null || current == null || pageSize < CrowdfundingCons.MIN_PAGE_SIZE
                || pageSize > CrowdfundingCons.MAX_PAGE_SIZE) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        WxTemplateRecordResponseModel msgTemplateRecord = templateGrpcClient.getMsgTemplateRecord(current, pageSize);
        List<WxMsgTemplateRecordModel> wxMsgTemplateRecordModelList = msgTemplateRecord.getWxMsgTemplateRecordModelList();
        Map<String, Object> result = Maps.newHashMap();
        Map<String, Object> map = Maps.newHashMap();
        map.put("current", msgTemplateRecord.getCurrent());
        map.put("pageSize", msgTemplateRecord.getPageSize());
        map.put("total", msgTemplateRecord.getTotal());
        result.put("pagination", map);
        result.put("data", wxMsgTemplateRecordModelList);
        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("message-template:add-template-number")
    @RequestMapping(path = "/add-template-number", method = RequestMethod.POST)
    @ApiOperation("添加模板编号")
    public Response addTemplateNumber(@RequestParam("templateNumber") String templateNumber, @RequestParam("userId") int userId) {
        log.info("WxMessageTemplateController addTemplateNumber templateNumber:{}", templateNumber);
        if (StringUtils.isEmpty(templateNumber)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        AdminUserAccountModel accountModel = seaAccountClientV1.getValidUserAccountById(userId).getResult();
        if (null == accountModel) {
            return NewResponseUtil.makeError(AdminErrorCode.USER_ACCOUNT_NOT_EXISTS);
        }
        MsgTemplateResp msgTemplateResp = templateGrpcClient.addMessageTemplate(templateNumber, userId, accountModel.getName());
        if (msgTemplateResp.getResult()) {
            return NewResponseUtil.makeSuccess(msgTemplateResp.getResult());
        }
        return NewResponseUtil.makeError(AdminErrorCode.OPERATION_FAILED);
    }

    @RequiresPermission("message-template:delete-by-id")
    @RequestMapping(path = "/delete-template-id", method = RequestMethod.POST)
    @ApiOperation("删除模板id")
    public Response deleteTemplate(@RequestParam("thirdType") int thirdType, @RequestParam("templateId") String templateId) {
        log.info("WxMessageTemplateController deleteTemplate thirdType:{},templateId:{}", thirdType, templateId);
        if (thirdType <= 0 || StringUtils.isBlank(templateId)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        int userId = ContextUtil.getAdminUserId();
        AdminUserAccountModel accountModel = seaAccountClientV1.getValidUserAccountById(userId).getResult();
        if (null == accountModel) {
            return NewResponseUtil.makeError(AdminErrorCode.USER_ACCOUNT_NOT_EXISTS);
        }
        MsgTemplateResp msgTemplateResp = templateGrpcClient.deleteTemplateId(thirdType, templateId, userId, accountModel.getName());
        if (msgTemplateResp.getResult()) {
            return NewResponseUtil.makeSuccess(msgTemplateResp.getResult());
        }
        return NewResponseUtil.makeError(AdminErrorCode.OPERATION_FAILED);
    }

    @RequiresPermission("message-template:update-number")
    @RequestMapping(path = "/update-template-number", method = RequestMethod.POST)
    @ApiOperation("更新模板编号")
    public Response updateTemplateNumber(@RequestParam("id") int id, @RequestParam("templateNumber") String templateNumber) {
        log.info("WxMessageTemplateController updateTemplateNumber id:{},templateNumber:{}", id, templateNumber);
        if (id <= 0 || StringUtils.isBlank(templateNumber)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        int userId = ContextUtil.getAdminUserId();
        AdminUserAccountModel accountModel = seaAccountClientV1.getValidUserAccountById(userId).getResult();
        if (null == accountModel) {
            return NewResponseUtil.makeError(AdminErrorCode.USER_ACCOUNT_NOT_EXISTS);
        }
        MsgTemplateResp msgTemplateResp = templateGrpcClient.addTemplateNumber(id, templateNumber, userId, accountModel.getName());
        if (msgTemplateResp.getResult()) {
            return NewResponseUtil.makeSuccess(msgTemplateResp.getResult());
        }
        return NewResponseUtil.makeError(AdminErrorCode.OPERATION_FAILED);
    }

    @RequiresPermission("message-template:update")
    @RequestMapping(path = "/update-message-template", method = RequestMethod.POST)
    @ApiOperation(value = "按钮更新模板操作", notes = "")
    public Response updateMessageTemplate() {
        MsgTemplateResp msgTemplateResp = templateGrpcClient.updateMessageTemplate();
        if (msgTemplateResp.getResult()) {
            return NewResponseUtil.makeSuccess(msgTemplateResp.getResult());
        }
        return NewResponseUtil.makeError(AdminErrorCode.OPERATION_FAILED);
    }

    @RequiresPermission("message-template:get-id-by-group")
    @RequestMapping(path = "/get-id-by-group", method = RequestMethod.POST)
    @ApiOperation(value = "关联公众号组", notes = "")
    public Response templateIdByGroup(@RequestParam(name = "templateNumber") String templateNumber,
                                      @RequestParam(name = "groupId") int groupId,
                                      @RequestParam(name = "token") String token) {
        Integer userId = ContextUtil.getAdminUserId();
        log.info("WxMessageTemplateController templateIdByGroup templateNumber:{},groupId:{},userId:{},token:{}",
                templateNumber, groupId, userId, token);
        if (StringUtils.isEmpty(templateNumber) || groupId <= 0 || userId == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        AdminUserAccountModel accountModel = seaAccountClientV1.getValidUserAccountById(userId).getResult();
        if (null == accountModel) {
            return NewResponseUtil.makeError(AdminErrorCode.USER_ACCOUNT_NOT_EXISTS);
        }
        MsgTemplateResp msgTemplateResp = templateGrpcClient.getTemplateIdBatch(templateNumber, groupId, userId, accountModel.getName());
        if (msgTemplateResp.getResult()) {
            return NewResponseUtil.makeSuccess(msgTemplateResp.getResult());
        }
        return NewResponseUtil.makeError(AdminErrorCode.OPERATION_FAILED);
    }
}

