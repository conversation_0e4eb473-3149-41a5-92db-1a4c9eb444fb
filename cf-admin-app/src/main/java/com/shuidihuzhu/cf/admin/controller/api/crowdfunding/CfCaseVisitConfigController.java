package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidi.weixin.common.util.StringUtils;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.client.apipure.feign.dp.DpClewFeignClient;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.admin.util.ParamTimeRangeHandler;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCaseVisitConfigBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCfCaseVisitConfig;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminCfCaseVisitConfigVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminOperatorVo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.crowdfunding.CfRiskService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.risk.client.CfRiskClient;
import com.shuidihuzhu.client.cf.risk.model.CfRiskLimitDetail;
import com.shuidihuzhu.client.cf.risk.model.CfRiskOperateDetailParam;
import com.shuidihuzhu.client.cf.risk.model.CfRiskOperateLimitExtParam;
import com.shuidihuzhu.client.cf.risk.model.CfRiskOperateLimitParam;
import com.shuidihuzhu.client.cf.risk.model.enums.RiskOperateSourceEnum;
import com.shuidihuzhu.client.cf.risk.model.enums.UserOperationEnum;
import com.shuidihuzhu.common.util.BeanUtils;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by wangsf on 18/5/20.
 */
@RestController
@Slf4j
@RefreshScope
@RequestMapping("/admin/cf/visit-config")
public class CfCaseVisitConfigController {

    @Autowired
    private CfRiskClient cfRiskClient;

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private AdminCaseVisitConfigBiz caseVisitConfigBiz;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @Value("${baseinfo.visit-config.default-popup-text:}")
    private String defaultPopupText;

    @Autowired
    private CfRiskService cfRiskService;

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private DpClewFeignClient dpClewFeignClient;


    @Value("${cf-risk.read-new-table:false}")
    private boolean readNewTable;

    @ApiOperation("修改或者更新一个案例的访问控制")
    @RequestMapping(value = "/add-or-update", method = RequestMethod.POST)
    @RequiresPermission("visit-config:add-or-update")
    public Response addOrUpdateConfig(
            @ApiParam("案例id") @RequestParam(value = "caseId", defaultValue = "0") int caseId,
            @ApiParam("案例uuid") @RequestParam(value = "infoUuid", defaultValue = "") String infoUuid,
            @ApiParam("是否显示banner") @RequestParam(value = "showBanner", defaultValue = "false") boolean showBanner,
            @ApiParam("banner的图片") @RequestParam(value = "bannerImg", defaultValue = "") String bannerImg,
            @ApiParam("banner跳转地址") @RequestParam(value = "bannerUrl", defaultValue = "") String bannerUrl,
            @ApiParam("案例是否可以分享") @RequestParam(value = "sharable", defaultValue = "true") boolean sharable,
            @ApiParam("是否显示弹窗") @RequestParam(value = "showPopup", defaultValue = "false") boolean showPopup,
            @ApiParam("弹窗文案") @RequestParam(value = "popupText", defaultValue = "") String popupText,
            @ApiParam("弹窗标题") @RequestParam(value = "popupTitle", defaultValue = "") String popupTitle,
            @ApiParam("异常案例是否屏蔽详情页") @RequestParam(required = false) Boolean abnormalHidden,
            @ApiParam("异常案例 屏蔽详情页筹款人看到的文案") @RequestParam(required = false) String abnormalHiddenSelfTitle,
            @ApiParam("异常案例 屏蔽详情页其他人看到的文案") @RequestParam(required = false) String abnormalHiddenOtherTitle) {

        log.info("CfCaseVisitConfigController addOrUpdateConfig caseId={}", caseId);

		if(!StringUtils.isBlank(infoUuid)) {
			CrowdfundingInfo crowdfundingInfo = this.crowdfundingInfoBiz.getFundingInfo(infoUuid);
			if(crowdfundingInfo == null) {
				return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
			}
			caseId = crowdfundingInfo.getId();
		}

		if(caseId <= 0) {
			return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
		}

		//双写至cf-risk新表
        //TODO 若该功能不迁至新页面，还需return该方法。
		writeRisk(caseId, sharable, showBanner, bannerImg, bannerUrl, abnormalHidden, abnormalHiddenSelfTitle, abnormalHiddenOtherTitle);

        AdminCfCaseVisitConfig config = this.caseVisitConfigBiz.get(caseId);
        boolean newConfig = false;
        if (config == null) {
            config = new AdminCfCaseVisitConfig();
            newConfig = true;
        }

        config.setCaseId(caseId);
        config.setShowBanner(showBanner);
        config.setBannerImgUrl(bannerImg);
        config.setBannerUrl(bannerUrl);
        config.setSharable(sharable);
        config.setShowPopup(showPopup);

        popupText = (StringUtils.isBlank(popupText) ? defaultPopupText : popupText);
        config.setPopupText(popupText);
        config.setPopupTitle(popupTitle);

        int userId = ContextUtil.getAdminUserId();
        AdminUserAccountModel model = seaAccountClientV1.getValidUserAccountById(userId).getResult();

        if (model != null) {
            config.setOperator(model.getName());
            config.setOperatorId(userId);
        }

        if (newConfig) {
            this.caseVisitConfigBiz.add(config);
        } else {
            this.caseVisitConfigBiz.update(config);
        }

        Boolean show = null == abnormalHidden ? true : (abnormalHidden ? false : true);
        cfRiskService.addOperateLimit(caseId, sharable, null, show, abnormalHiddenSelfTitle, abnormalHiddenOtherTitle,
                RiskOperateSourceEnum.CUSTOMER_OPERATE.getCode(), null != model ? model.getName() : null, userId);
        // 异常案例屏蔽详情页
        config.setAbnormalHidden(abnormalHidden);
        config.setAbnormalHiddenSelfTitle(abnormalHiddenSelfTitle);
        config.setAbnormalHiddenOtherTitle(abnormalHiddenOtherTitle);
        caseVisitConfigBiz.saveAbnormalHidden(caseId, abnormalHidden, abnormalHiddenSelfTitle,
                abnormalHiddenOtherTitle, userId);

		AdminCfCaseVisitConfigVo configVo = new AdminCfCaseVisitConfigVo();
		BeanUtils.copyProperties(config, configVo);

		// 案例屏蔽通知缙云更新列表
        if (!show) {
            dpClewFeignClient.removeDpListByCaseId(caseId);
        }
		return NewResponseUtil.makeSuccess(configVo);
	}

	private Response<AdminCfCaseVisitConfigVo> writeRisk(int caseId, boolean sharable,
                                                         boolean showBanner, String bannerImg, String bannerUrl,
                                                         Boolean abnormalHidden, String selfTitle, String otherTitle){

        List<CfRiskOperateDetailParam> detailParams = Lists.newArrayList();

        CfRiskOperateDetailParam detailParam = null;
        CfRiskOperateLimitExtParam extParam = null;

        /**
         * 转发
         */
        detailParam = new CfRiskOperateDetailParam();
        detailParam.setUserOperate(UserOperationEnum.SHARE.getCode());
        detailParam.setAction(sharable);
        detailParam.setReason("风控管理-异常案例处理");
        detailParams.add(detailParam);

        /**
         * 谣言banner
         */
        if(StringUtils.isNotBlank(bannerImg) || StringUtils.isNotBlank(bannerUrl)){
            extParam = new CfRiskOperateLimitExtParam();
            extParam.setBannerImgUrl(bannerImg);
            extParam.setBannerUrl(bannerUrl);
        }
        detailParam = new CfRiskOperateDetailParam();
        detailParam.setUserOperate(UserOperationEnum.BANNER.getCode());
        detailParam.setAction(!showBanner);
        detailParam.setReason("风控管理-异常案例处理");
        detailParam.setExtParam(extParam);
        detailParams.add(detailParam);

        /**
         * 屏蔽筹款页
         */
        if(Objects.nonNull(abnormalHidden)){
            extParam = null;
            if(StringUtils.isNotBlank(selfTitle) || StringUtils.isNotBlank(otherTitle)){
                extParam = new CfRiskOperateLimitExtParam();
                extParam.setShowSelfTitle(selfTitle);
                extParam.setShowOtherTitle(otherTitle);
            }

            detailParam = new CfRiskOperateDetailParam();
            detailParam.setUserOperate(UserOperationEnum.SHOW.getCode());
            detailParam.setAction(!abnormalHidden);
            detailParam.setReason("风控管理-异常案例处理");
            detailParam.setExtParam(extParam);
            detailParams.add(detailParam);
        }

        int userId = ContextUtil.getAdminUserId();
        AdminUserAccountModel model = seaAccountClientV1.getValidUserAccountById(userId).getResult();
        String adminUserName = Objects.nonNull(model) ? model.getName() : null;

        CfRiskOperateLimitParam riskParam = new CfRiskOperateLimitParam();
        riskParam.setCaseId(caseId);
        riskParam.setOperateSource(RiskOperateSourceEnum.RISK_QUERY.getCode());
        riskParam.setOperator(adminUserName);
        riskParam.setOperatorId(userId <= 0 ? 1 : userId);
        riskParam.setDetailParams(detailParams);

        Response<String> result = cfRiskClient.writeRiskOperate(riskParam);
        if(0 != result.getCode()){
            return ResponseUtil.makeResponse(result.getCode(), result.getMsg(), null);
        }

        Response<CfRiskLimitDetail> response = cfRiskClient.queryOperateDetail(caseId);
        return AdminCfCaseVisitConfigVo.build(response.getData());
    }

    @ApiOperation("获取一个案例的访问控制")
    @RequestMapping(value = "/get", method = RequestMethod.POST)
    @RequiresPermission("visit-config:get")
    public Response get(String infoUuid) {

		if(StringUtils.isBlank(infoUuid)) {
			return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
		}

		CrowdfundingInfo crowdfundingInfo = this.crowdfundingInfoBiz.getFundingInfo(infoUuid);
		if(crowdfundingInfo == null) {
			return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
		}

		AdminCfCaseVisitConfig config = this.caseVisitConfigBiz.get(crowdfundingInfo.getId());
		if(config == null) {
			return NewResponseUtil.makeSuccess(null);
		}

		AdminCfCaseVisitConfigVo configVo = new AdminCfCaseVisitConfigVo();
		BeanUtils.copyProperties(config, configVo);
		return NewResponseUtil.makeSuccess(configVo);
	}


    @ApiOperation("获取加入配置的案例列表")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @RequiresPermission("visit-config:list")
    public Response getList(
            @RequestParam(value = "current", defaultValue = "0") int current,
            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @RequestParam(value = "caseId", required = false) Integer caseId,
            @RequestParam(value = "type", required = false) Integer type,
            @RequestParam(value = "start", required = false) String start,
            @RequestParam(value = "end", required = false) String end,
            @RequestParam(value = "operatorId", required = false) Integer operatorId

    ) {
        //注意一下 sharable  false表示不能转发，默认是true 可转发
        log.info("CfCaseVisitConfigController getList caseId={},type={}", caseId, type);

        //判断时间
        Response dateCheck = ParamTimeRangeHandler.illegalTimeRange(start, end);
        if (dateCheck.notOk()) {
            return dateCheck;
        }

        //时间限制处理
        Pair<String, String> startAndEndTime = ParamTimeRangeHandler.handleCaseVisitConfig(caseId, start, end);
        start = startAndEndTime.getLeft();
        end = startAndEndTime.getRight();

        List<AdminCfCaseVisitConfig> cfCaseVisitConfigs = this.caseVisitConfigBiz.getList(current, pageSize, caseId, type, operatorId, start, end);
        log.info("CfCaseVisitConfigController getList cfCaseVisitConfigs={}", cfCaseVisitConfigs);
        if (CollectionUtils.isEmpty(cfCaseVisitConfigs)) {
            Map<String, Object> result = Maps.newHashMap();
            result.put("data", Collections.EMPTY_LIST);
            result.put("pagination", PageUtil.transform2PageMap(cfCaseVisitConfigs));
            return NewResponseUtil.makeSuccess(result);
        }

        List<Integer> caseIds = cfCaseVisitConfigs.stream()
                .map(config -> config.getCaseId())
                .collect(Collectors.toList());
        Map<Integer, CrowdfundingInfo> crowdfundingInfoMap = this.crowdfundingInfoBiz.getMapByIds(caseIds);
        if (crowdfundingInfoMap == null) {
            crowdfundingInfoMap = Maps.newHashMap();
        }

        List<Long> userIds = Lists.newLinkedList();
        for (Map.Entry<Integer, CrowdfundingInfo> entry : crowdfundingInfoMap.entrySet()) {
            userIds.add(entry.getValue().getUserId());
        }

        List<UserInfoModel> userInfoModelList = this.userInfoServiceBiz.getUserInfoByUserIdBatch(userIds);
        if (CollectionUtils.isEmpty(userInfoModelList)) {
            userInfoModelList = Lists.newLinkedList();
        }

        Map<Long, UserInfoModel> userInfoModelMap = Maps.newHashMap();
        for (UserInfoModel userInfoModel : userInfoModelList) {
            userInfoModelMap.put(userInfoModel.getUserId(), userInfoModel);
        }

        //拼响应
        List<AdminCfCaseVisitConfigVo> cfCaseVisitConfigVos = Lists.newLinkedList();
        for (AdminCfCaseVisitConfig cfCaseVisitConfig : cfCaseVisitConfigs) {
            AdminCfCaseVisitConfigVo cfCaseVisitConfigVo = new AdminCfCaseVisitConfigVo();
            BeanUtils.copyProperties(cfCaseVisitConfig, cfCaseVisitConfigVo);
            CrowdfundingInfo crowdfundingInfo = crowdfundingInfoMap.get(cfCaseVisitConfig.getCaseId());
            if (crowdfundingInfo != null) {
                cfCaseVisitConfigVo.setCaseTitle(crowdfundingInfo.getTitle());
                cfCaseVisitConfigVo.setInfoUuid(crowdfundingInfo.getInfoId());

                UserInfoModel userInfoModel = userInfoModelMap.get(crowdfundingInfo.getUserId());
                if (userInfoModel != null && StringUtils.isBlank(userInfoModel.getCryptoMobile())) {
                    cfCaseVisitConfigVo.setRaiserMobile(shuidiCipher.decrypt(userInfoModel.getCryptoMobile()));
                }
            }
            cfCaseVisitConfigVo.setEnd(DateFormatUtils.format(cfCaseVisitConfig.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"));
            cfCaseVisitConfigVos.add(cfCaseVisitConfigVo);
        }
        log.info("CfCaseVisitConfigController getList cfCaseVisitConfigVos={}", cfCaseVisitConfigVos);
        Map<String, Object> result = Maps.newHashMap();
        result.put("data", cfCaseVisitConfigVos);
        result.put("pagination", PageUtil.transform2PageMap(cfCaseVisitConfigs));
        return NewResponseUtil.makeSuccess(result);
    }


    @ApiOperation("获取操作人案例列表")
    @RequestMapping(value = "/operator", method = RequestMethod.POST)
    @RequiresPermission("visit-config:operator")
    public Response getOperatorList() {

        List<AdminOperatorVo> list = caseVisitConfigBiz.getOperatorList();

		return NewResponseUtil.makeSuccess(list);
	}

	@ApiOperation("控制案例展示")
	@RequestMapping(value = "/update-show", method = RequestMethod.POST)
    @RequiresPermission("visit-config:update-show")
	public Response updateShow(@ApiParam("案例标识 id或info_id") @RequestParam(value = "caseKey") String caseKey,
							   @ApiParam("能否显示") @RequestParam("canShow") Integer canShow){

        //该功能和【风控管理-异常案例处理-屏蔽筹款页】的操作类似，所以此处的禁止案例展示下线，但是操作案例展示的功能应该保留
        if(null != canShow && 0 == canShow){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_FUNCTION_OFFLINE);
        }

		Integer caseId = handlerCaseKey(caseKey);
		if (caseId==null || caseId==0) return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);

        int adminUserId = ContextUtil.getAdminUserId();
        AdminUserAccountModel model = seaAccountClientV1.getValidUserAccountById(adminUserId).getResult();
        String adminUserName = Objects.nonNull(model) ? model.getName() : null;

        CfRiskOperateLimitExtParam extParam = new CfRiskOperateLimitExtParam();
        extParam.setShowSelfTitle("案例异常");
        extParam.setShowOtherTitle("案例异常，请按规定操作");

        CfRiskOperateDetailParam detailParam = new CfRiskOperateDetailParam();
        detailParam.setUserOperate(UserOperationEnum.SHOW.getCode());
        detailParam.setAction(1 == canShow ? true : false);
        detailParam.setExtParam(extParam);
        detailParam.setReason("开发者工具-案例相关属性维护-禁止案例展示");

        CfRiskOperateLimitParam riskParam = new CfRiskOperateLimitParam();
        riskParam.setCaseId(caseId);
        riskParam.setOperateSource(RiskOperateSourceEnum.DEV_ATTRIBUTE.getCode());
        riskParam.setOperatorId(adminUserId > 0 ? adminUserId : 1);
        riskParam.setOperator(StringUtils.isNotBlank(adminUserName) ? adminUserName : "system");
        riskParam.setDetailParams(Lists.newArrayList(detailParam));

        Response<String> response = cfRiskClient.writeRiskOperate(riskParam);
        if(response.getCode() != ErrorCode.SUCCESS.getCode()){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }

        //TODO ###请勿改动、待删除####
        OpResult updateResult = caseVisitConfigBiz.updateCanShowByCaseId(caseId,canShow);
		if (updateResult.isSuccess()){
			return NewResponseUtil.makeSuccess(null);
		}
        //TODO ###请勿改动、待删除####

		return NewResponseUtil.makeSuccess(null);
	}

	@ApiOperation("增加案例bannerText")
	@RequestMapping(value = "/update-banner-text", method = RequestMethod.POST)
    @RequiresPermission("visit-config:update-banner-text")
	public Response updateShow(@ApiParam("案例标识 id或info_id") @RequestParam(value = "caseKey") String caseKey,
							   @ApiParam("bannerText") @RequestParam("bannerText") String bannerText,
							   @ApiParam("开始时间") @RequestParam("startTime") Date startTime,
							   @ApiParam("结束时间") @RequestParam("endTime") Date endTime){
		Integer caseId = handlerCaseKey(caseKey);
		if (caseId==null || caseId==0) return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);

        int adminUserId = ContextUtil.getAdminUserId();
        AdminUserAccountModel model = seaAccountClientV1.getValidUserAccountById(adminUserId).getResult();
        String adminUserName = Objects.nonNull(model) ? model.getName() : null;

        CfRiskOperateLimitExtParam extParam = new CfRiskOperateLimitExtParam();
        extParam.setBannerText(StringUtils.isBlank(bannerText) ? "" : bannerText);
        extParam.setStartTime(startTime);
        extParam.setEndTime(endTime);

        CfRiskOperateDetailParam detailParam = new CfRiskOperateDetailParam();
        detailParam.setUserOperate(UserOperationEnum.BANNER_TEXT.getCode());
        detailParam.setAction(false);
        detailParam.setReason("开发者工具-案例相关属性维护-增加案例banner");
        detailParam.setExtParam(extParam);

        CfRiskOperateLimitParam riskParam = new CfRiskOperateLimitParam();
        riskParam.setCaseId(caseId);
        riskParam.setOperateSource(RiskOperateSourceEnum.DEV_ATTRIBUTE.getCode());
        riskParam.setOperatorId(adminUserId > 0 ? adminUserId : 1);
        riskParam.setOperator(StringUtils.isNotBlank(adminUserName) ? adminUserName : "system");
        riskParam.setDetailParams(Lists.newArrayList(detailParam));

        Response<String> response = cfRiskClient.writeRiskOperate(riskParam);
        if(response.getCode() != ErrorCode.SUCCESS.getCode()){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }

        //TODO ###请勿改动、待删除####
        OpResult updateResult = caseVisitConfigBiz.updateBannerTextAndStartEndTime(caseId,bannerText,startTime,endTime);
		if (updateResult.isSuccess()){
			return NewResponseUtil.makeSuccess(null);
		}
        //TODO ###请勿改动、待删除####

		return NewResponseUtil.makeSuccess(null);
	}

    Integer handlerCaseKey(String caseKey) {
        Integer caseId = 0;
        // caseKey 带有- 说明 值为 info_uuid
        if (caseKey.contains("-")) {
            CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(caseKey);
            caseId = crowdfundingInfo == null ? null : crowdfundingInfo.getId();
        } else {
            caseId = Integer.parseInt(caseKey);//已检查过
        }
        return caseId;
    }
}
