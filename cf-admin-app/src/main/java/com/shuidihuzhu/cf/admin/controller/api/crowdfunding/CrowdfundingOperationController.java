package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.admin.common.MaskCodeOperationRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoPayeeBiz;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.MaskCodeOperationType;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingOperationEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.enums.maskcode.MaskCodePageEnum;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfCaseOperateStatus;
import com.shuidihuzhu.cf.model.common.MaskCodeOperationRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation;

import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.cfOperatingProfile.CfOperateStatusBiz;
import com.shuidihuzhu.client.cf.api.model.enums.RelativesTypeEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.Objects;

/**
 * Created by suxinmin on 17/1/15.
 */

@Slf4j
@Controller
@RequestMapping(path = "/admin/crowdfunding/operation")
public class CrowdfundingOperationController {

	private static final Logger LOGGER = LoggerFactory.getLogger(CrowdfundingOperationController.class);

	@Autowired
	private MaskCodeOperationRecordBiz maskCodeOperationRecordBiz;
	@Autowired
	private SeaAccountClientV1 seaAccountClientV1;
	@Autowired
	private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;

	@Autowired
	private ICrowdfundingDelegate crowdfundingDelegate;
	@Autowired
	private CfOperateStatusBiz caseOperateStatusBiz;
	@Autowired
	private AdminCrowdfundingInfoPayeeBiz infoPayeeBiz;
	@Resource
	private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
	@Resource
	private ApproveRemarkOldService approveRemarkOldService;

	@RequestMapping(path = "operation", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@ResponseBody
	@RequiresPermission("cf-operation:operation")
	public Response operation(String infoId, int status) {

		if(status > 2 ){
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}

		if (null == infoId) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}

		CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getCrowdfundingInfoByInfoId(infoId);
		if (crowdfundingInfo == null) {
			return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
		}

		CrowdfundingOperation crowdfundingOperatoin = crowdfundingOperationDelegate.getByInfoId(infoId);
		if (crowdfundingOperatoin == null) {
			return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
		}

		if(crowdfundingOperatoin.getOperation() == CrowdfundingOperationEnum.OPERATED.value()){
			return NewResponseUtil.makeSuccess(null);
		}

		if(crowdfundingOperatoin.getOperation() > status ){
			return NewResponseUtil.makeSuccess(null);
		}

		CrowdfundingOperation crowdfundingOperatioinNew = new CrowdfundingOperation();
		crowdfundingOperatioinNew.setInfoId(infoId);

		Date endTime = crowdfundingInfo.getEndTime();
		if(endTime != null && endTime.before(new Date())){
			crowdfundingOperatioinNew.setOperation(CrowdfundingOperationEnum.OPERATED.value());
		}

		if(crowdfundingInfo.getStatus().value() == 2) {
			crowdfundingOperatioinNew.setOperation(CrowdfundingOperationEnum.OPERATED.value());
		}

		crowdfundingOperatioinNew.setOperation(status);
		crowdfundingOperatioinNew.setOperateTime(new Timestamp(System.currentTimeMillis()));

		try {
		    crowdfundingOperationDelegate.updateCrowdfundingOperation(crowdfundingOperatioinNew);
		} catch (Exception e) {
			LOGGER.error("CrowdfundingOperationController Operation update error", e);
		}

		return NewResponseUtil.makeSuccess(null);

	}

	@RequestMapping(path = "mask-code-operation", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@ResponseBody
	@RequiresPermission("cf-operation:mask-code-operation")
	public Response recordPhoneNumberQuery(@RequestParam(required = false, defaultValue = "0") Integer caseId, MaskCodeOperationType maskCodeOperationType,
										   MaskCodePageEnum maskCodePageEnum,
										   String phoneNum, @RequestParam(required = false, defaultValue = "0") int clewId,
										   @RequestParam(required = false, defaultValue = "") String wechatId) {
		MaskCodeOperationRecord maskCodeOperationRecord = new MaskCodeOperationRecord();
		maskCodeOperationRecord.setCaseId(caseId);
		if (null != maskCodePageEnum) {
			maskCodeOperationRecord.setOperationPage(maskCodePageEnum.getPageName());
		}
		if (null != maskCodeOperationType) {
			maskCodeOperationRecord.setOperationType(maskCodeOperationType.getOperationTypeDesc());
		}
		maskCodeOperationRecord.setQueryContent(phoneNum);
		maskCodeOperationRecord.setOperationTime(DateUtil.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
		maskCodeOperationRecord.setOperatorId(ContextUtil.getAdminUserId());
		maskCodeOperationRecord.setClewId(clewId);
		maskCodeOperationRecord.setWechatId(wechatId);
		AuthRpcResponse<String> mis = seaAccountClientV1.getMisByUserId(ContextUtil.getAdminUserId());
		if (null != mis && mis.isSuccess()) {
			maskCodeOperationRecord.setOperatorMis(mis.getResult());
		}
		maskCodeOperationRecordBiz.addMaskOperationRecord(maskCodeOperationRecord);
		return NewResponseUtil.makeSuccess(null);
	}


	@RequestMapping(path = "payee-relation-video/can-operate-type", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@ResponseBody
	@RequiresPermission("payee-relation-video:operate")
	public Response<Integer> getRelationVideoOperateType(String infoUuid) {

		return NewResponseUtil.makeSuccess(caseOperateStatusBiz.getRelationVideoOperateType(infoUuid));
	}

	@RequestMapping(path = "payee-relation-video/change-operate-type", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@ResponseBody
	@RequiresPermission("payee-relation-video:operate")
	public Response<String> operateRelationVideo(String infoUuid, int operateType, String operateComment) {

		AdminErrorCode errorCode = caseOperateStatusBiz.operateRelationVideo(ContextUtil.getAdminUserId(), infoUuid, operateType, operateComment);

		return errorCode == AdminErrorCode.SUCCESS ? NewResponseUtil.makeSuccess("") : NewResponseUtil.makeError(errorCode);
	}

	@RequestMapping(path = "payee-relation-video/select-last--logs", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@ResponseBody
	@RequiresPermission("payee-relation-video:operate")
	public Response<CfCaseOperateStatus> selectLastOperateReason(String infoUuid) {

		return  NewResponseUtil.makeSuccess(caseOperateStatusBiz.selectLastOperateReason(infoUuid)) ;
	}

	@RequestMapping(path = "payee-relation/update-type", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@ResponseBody
	@RequiresPermission("payee-relation:update-type")
	public Response<Integer> updatePayeeRelation(@RequestParam("caseId") int caseId, @RequestParam("relation") int relationCode) {
		// 新增操作备注
		try {
			handlerCrowdfundingApprove(caseId, relationCode, ContextUtil.getAdminUserId());
		} catch (Exception e) {
			log.error("crowdfundingOperationController updatePayeeRelation is error {}, {}, {}, {}", caseId, relationCode, ContextUtil.getAdminUserId(), e.getMessage());
		}
		return  NewResponseUtil.makeSuccess(infoPayeeBiz.updatePayeeRelation(caseId, relationCode)) ;
	}

	private void handlerCrowdfundingApprove(int caseId, int relationCode, int adminUserId) {
		CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
		if (Objects.isNull(crowdfundingInfo)) {
			return;
		}
		CrowdfundingRelationType crowdfundingRelationType = CrowdfundingRelationType.getByCode(relationCode);
		if (Objects.isNull(crowdfundingRelationType)) {
			return;
		}
		if (Objects.equals(crowdfundingInfo.getRelationType().ordinal(), crowdfundingRelationType.ordinal())) {
			return;
		}
		String convertRelationMsg = convertRelationMsg(crowdfundingInfo);
		String content = "修改收款人与患者的关系：由”" +
				convertRelationMsg +
				"“改为”" +
				crowdfundingRelationType.getDescription() +
				"“";
		approveRemarkOldService.add(caseId, adminUserId, content);
	}

	private String convertRelationMsg(CrowdfundingInfo crowdfundingInfo) {
		if (crowdfundingInfo.getMaterialPlanId() < 100) {
			return Objects.equals(crowdfundingInfo.getRelationType().ordinal(), CrowdfundingRelationType.other.ordinal()) ? "患者的父母/子女/亲属" : crowdfundingInfo.getRelationType().getDescription();
		}
		if (crowdfundingInfo.getMaterialPlanId() >= 100) {
			CrowdfundingInfoPayee payee = crowdfundingDelegate.getCrowdfundingInfoPayeeByInfoUuid(crowdfundingInfo.getInfoId());
			return Objects.equals(crowdfundingInfo.getRelationType().ordinal(), CrowdfundingRelationType.other.ordinal()) && Objects.nonNull(payee) ?
					"配偶或近亲属-" + RelativesTypeEnum.getByCode(payee.getRelativesType()).getMsg() : crowdfundingInfo.getRelationType().getDescription();
		}

		return crowdfundingInfo.getRelationType().getDescription();
	}

}
