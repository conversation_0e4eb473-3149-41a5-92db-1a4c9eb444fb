package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.client.feign.combine.CfContributeFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.combine.CombineCommonEnum;
import com.shuidihuzhu.cf.model.contribute.CfContributeOrder;
import com.shuidihuzhu.cf.service.ai.AiImageMaskServiceImpl;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.aiphoto.ImageWatermarkService;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.client.feign.CfCommonFeignClient;
import com.shuidihuzhu.cf.client.feign.CfPlatformEsFeignClient;
import com.shuidihuzhu.cf.constants.crowdfunding.status.RelationShip;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.ugc.IUgcDelegate;
import com.shuidihuzhu.cf.domain.risk.RiskUgcVerifyDO;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.service.crowdfunding.AdminUserOrgService;
import com.shuidihuzhu.cf.service.crowdfunding.CfRiskService;
import com.shuidihuzhu.cf.service.crowdfunding.complaint.ComplaintService;
import com.shuidihuzhu.cf.service.record.CfCrowdfundingAttachmentRecordService;
import com.shuidihuzhu.cf.service.risk.dark.DarkListService;
import com.shuidihuzhu.cf.vo.crowdfunding.CfCommentVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CfOrderVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CfProgressVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CfVerificationVo;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.api.client.CfImageMaskFeignClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.model.enums.ImageMaskBizEnum;
import com.shuidihuzhu.common.web.constants.CrowdfundingCons;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import com.shuidihuzhu.common.web.util.admin.BasicExample;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import com.shuidihuzhu.msg.util.DateUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by ahrievil on 2017/4/27.
 */
@Controller
@Slf4j
@RequestMapping(path = "/admin/crowdfunding/delete-comment")
public class CrowdfundingCommentController {

    private static final Logger LOGGER = LoggerFactory.getLogger(CrowdfundingCommentController.class);
    private final static List<Integer> ugcTypes = Lists.newArrayList(ImageMaskBizEnum.CF_DETAIL_IMAGE.getCode(), ImageMaskBizEnum.CF_MEDICAL_IMAGE.getCode());
    @Autowired
    private IUgcDelegate ugcDelegate;
    @Autowired
    private AdminCrowdfundingCommentBiz adminCrowdfundingCommentBiz;
    @Autowired
    private AdminCrowdFundingProgressBiz adminCrowdFundingProgressBiz;
    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Autowired
    private AdminCrowdfundingAttachmentBiz adminCrowdfundingAttachmentBiz;
    @Autowired
    private IRiskDelegate riskDelegate;
    @Autowired
    private AdminCrowdFundingVerificationBiz adminCrowdFundingVerificationBiz;
    @Autowired
    private AdminCrowdfundingOrderBiz adminCrowdfundingOrderBiz;
    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Autowired
    private CfPlatformEsFeignClient cfPlatformEsFeignClient;
    @Autowired
    private CfCommonFeignClient cfCommonFeignClient;
    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired
    private IUgcOperateRecordService ugcOperateRecordService;
    @Resource
    private SeaAccountClientV1 seaAccountClientV1;
    @Autowired
    private AdminUserOrgService adminUserOrgService;
    @Autowired
    private CfRiskService cfRiskService;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Autowired
    private ImageWatermarkService watermarkService;

    @Autowired
    private DarkListService darkListService;

    @Resource
    private ComplaintService complaintService;
    @Autowired
    private CfContributeFeignClient cfContributeFeignClient;

    @Autowired
    private CfCrowdfundingAttachmentRecordService cfCrowdfundingAttachmentRecordService;

    @Autowired
    private AdminCrowdfundingPayRecordBiz payRecordBiz;
    @Resource
    private ApproveRemarkOldService approveRemarkOldService;
    @Resource
    private AiImageMaskServiceImpl aiImageMaskService;
    @Resource
    private CfImageMaskFeignClient cfImageMaskFeignClient;
    @Autowired
    private MaskUtil maskUtil;

    //证实信息操作

    @RequiresPermission("comment:remove-verification")
    @RequestMapping(path = "/remove-verification", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response removeVerification(Integer id) {
        Integer userId = ContextUtil.getAdminUserId();
        LOGGER.info("CrowdfundingCommentController removeAttachment userId={},id={}", userId, id);
        if (userId == null || id == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        riskDelegate.updateValid(0, id);
        return NewResponseUtil.makeSuccess(null);
    }

    @ResponseBody
    @RequiresPermission("comment:save-ugc-operate-record")
    @RequestMapping(path = "/save-ugc-operate-record", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response<String> saveUgcOperateRecord(@RequestParam(name = "infoUuid") String infoUuid,
                                                 @RequestParam(name = "bizType") int bizType,
                                                 @RequestParam(name = "bizId", required = false, defaultValue = "0") long bizId,
                                                 @RequestParam(name = "operateType") int operateType,
                                                 @RequestParam(name = "content", defaultValue = "") String content){

        int adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        CfInfoSimpleModel cfInfo = crowdfundingDelegate.getCfInfoSimpleModel(infoUuid);
        if(Objects.isNull(cfInfo)){
            return ResponseUtil.makeError(ErrorCode.CF_NOT_FOUND);
        }

        UgcManageEnum ugcManageEnum = UgcManageEnum.parse(operateType);
        UgcBizType ugcBizType = UgcBizType.parse(bizType);
        if(Objects.isNull(ugcBizType) || Objects.isNull(ugcManageEnum)){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        AuthRpcResponse<AdminUserAccountModel> userAccountRes = seaAccountClientV1.getValidUserAccountById(adminUserId);
        String adminUserName = 0 == userAccountRes.getCode() && Objects.nonNull(userAccountRes.getResult()) ? userAccountRes.getResult().getName() : "";

        AdminUgcOperateRecordDO ugcOperateRecordDO = new AdminUgcOperateRecordDO();
        ugcOperateRecordDO.setCaseId(Long.valueOf(cfInfo.getId()));
        ugcOperateRecordDO.setBizType(bizType);
        ugcOperateRecordDO.setBizId(bizId);
        ugcOperateRecordDO.setOperateType(operateType);
        ugcOperateRecordDO.setContent(content);
        ugcOperateRecordDO.setOperatorId(adminUserId);
        ugcOperateRecordDO.setOperator(adminUserName);
        ugcOperateRecordDO.setDepartment(adminUserOrgService.getOrganization(adminUserId));

        int result = ugcOperateRecordService.insert(ugcOperateRecordDO);

        if(result > 0){
            return ResponseUtil.makeSuccess("success");
        }

        return ResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
    }

    @ResponseBody
    @RequiresPermission("comment:query-ugc-operate-record")
    @RequestMapping(path = "/query-ugc-operate-record", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response<List<AdminUgcOperateRecordDO>> queryUgcOperateRecord(@RequestParam(name = "infoUuid") String infoUuid,
                                                                         @RequestParam(name = "bizType") int bizType,
                                                                         @RequestParam(name = "bizId", required = false) long bizId){

        int adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        CfInfoSimpleModel cfInfo = crowdfundingDelegate.getCfInfoSimpleModel(infoUuid);
        if(Objects.isNull(cfInfo)){
            return ResponseUtil.makeError(ErrorCode.CF_NOT_FOUND);
        }

        UgcBizType ugcBizType = UgcBizType.parse(bizType);
        if(Objects.isNull(ugcBizType) || bizId <= 0){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<AdminUgcOperateRecordDO> ugcOperateRecordDOS = ugcOperateRecordService.query(cfInfo.getId(), bizType, bizId);
        if (bizType == UgcBizType.PROGRESS.getKey()) {
            ugcOperateRecordDOS.addAll(Optional.ofNullable(ugcOperateRecordService.query(cfInfo.getId(), UgcBizType.MASK_PROGRESS.getKey(), bizId)).orElse(Lists.newArrayList()));
        }

        return ResponseUtil.makeSuccess(ugcOperateRecordDOS.stream().sorted(Comparator.comparing(AdminUgcOperateRecordDO::getCreateTime)).collect(Collectors.toList()));
    }

    @RequiresPermission("comment:get-verification")
    @RequestMapping(path = "/get-verification", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response getVerification(Integer current, Integer pageSize,
                                    String infoUuid,
                                    @RequestParam(required = false) String userName,
                                    @RequestParam(required = false) String description,
                                    @RequestParam(required = false) String startTime,
                                    @RequestParam(required = false) String endTime,
                                    @RequestParam(required = false) Integer id) {
        return getVerificationV2(current, pageSize, infoUuid, userName, description, startTime, endTime, id, 0,"");
    }

    @RequiresPermission("comment:get-verification-v2")
    @RequestMapping(path = "/get-verification-v2", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response getVerificationV2(Integer current, Integer pageSize, String infoUuid,
                                      @RequestParam(required = false) String userName, @RequestParam(required = false) String description,
                                      @RequestParam(required = false) String startTime, @RequestParam(required = false) String endTime,
                                      @RequestParam(required = false) Integer id, @RequestParam(required = false) long verifyUserId, @RequestParam(required = false) String mobile){

        Integer userId = ContextUtil.getAdminUserId();
        LOGGER.info("CrowdfundingCommentController getVerificationV2 userId={},current={},pageSize={},infoUuid={},userName={},description={},startTime={},endTime={},id:{}"
                , userId, current, pageSize, infoUuid, userName, description, startTime, endTime, id);
        if (userId == null || StringUtil.isBlank(infoUuid)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (pageSize == null || pageSize < CrowdfundingCons.MIN_PAGE_SIZE
                || pageSize > CrowdfundingCons.MAX_PAGE_SIZE) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        BasicExample basicExample = new BasicExample();
        BasicExample.Criteria criteria = basicExample.or();
        if (id != null) {
            criteria.andEqualTo("id", id);
        }
        if(verifyUserId > 0){
            criteria.andEqualTo("verify_user_id", verifyUserId);
        }

        if(StringUtil.isEmpty(mobile)){
            criteria.andEqualTo("encrypt_mobile", StringUtils.isNotEmpty(mobile) ? oldShuidiCipher.aesEncrypt(mobile) : "");
        }

        criteria.andEqualTo("crowd_funding_info_id", infoUuid);
        if (StringUtil.isNotBlank(userName)) {
            criteria.andLike("user_name", userName);
        }
        if (StringUtil.isNotBlank(description)) {
            criteria.andLike("description", description);
        }
        if (StringUtil.isNotBlank(startTime) && StringUtil.isNotBlank(endTime)) {
            criteria.andGreaterThanOrEqualTo("create_time", startTime);
            criteria.andLessThanOrEqualTo("create_time", endTime);
        }
        basicExample.setOrderByClause(" create_time desc");
        List<CrowdFundingVerification> crowdFundingVerifications = adminCrowdFundingVerificationBiz.selectByPage(basicExample, current, pageSize);
        List<Long> verifyUserIds = crowdFundingVerifications.stream().map(CrowdFundingVerification::getVerifyUserId).collect(Collectors.toList());
        List<UserInfoModel> userInfoModels = userInfoServiceBiz.getUserInfoByUserIdBatch(verifyUserIds);
        Map<Long, UserInfoModel> userInfoModelMap = userInfoModels.stream().collect(Collectors.toMap(UserInfoModel::getUserId, Function.identity()));
        List<CfVerificationVo> crowdfundingfundingVerificationVos = Lists.newArrayList();
        crowdFundingVerifications.forEach(crowdFundingVerification -> {

            UserRealInfo userRealInfo = new UserRealInfo();
            userRealInfo.setUserId(crowdFundingVerification.getVerifyUserId());
            userRealInfo.setName(crowdFundingVerification.getUserName());
            userRealInfo.setIdcardVerifyStatus(IdcardVerifyStatus.HANDLE_SUCCESS.getCode());
            List<UserRealInfo> userRealInfos = cfPlatformEsFeignClient.queryUserRealInfo(userRealInfo).getData();
            UserRealInfo userRealInfoRes = CollectionUtils.isNotEmpty(userRealInfos) ? userRealInfos.get(0) : null;
            String identity = Objects.nonNull(userRealInfoRes) ? shuidiCipher.decrypt(userRealInfoRes.getCryptoIdCard()) : "";

            CfInfoSimpleModel cfInfo = crowdfundingDelegate.getCfInfoSimpleModel(crowdFundingVerification.getCrowdFundingInfoId());
            RiskUgcVerifyDO verifyDO = cfCommonFeignClient.queryUgcVerify(Objects.nonNull(cfInfo) ? cfInfo.getId() : 0, UgcTypeEnum.VERIFICATION.getValue(), crowdFundingVerification.getId()).getData();
            boolean isBlackList = !darkListService.checkUGCPassed(crowdFundingVerification.getVerifyUserId(), true);
//            boolean isBlackList = cfRiskService.queryBlackValid(crowdFundingVerification.getVerifyUserId(), CfRiskBlackListEnum.LimitType.UGC);
            boolean seeOnlySelf = Objects.nonNull(verifyDO) || isBlackList;

            List<WorkOrderExt> workOrderExts = cfWorkOrderClient.queryWorkOrderByCaseAndType(Objects.nonNull(cfInfo) ? cfInfo.getId() : 0, WorkOrderType.ugcpinglun.getType(), String.valueOf(crowdFundingVerification.getId()), String.valueOf(CfSensitiveWordRecordEnum.BizType.VERIFICATION.value())).getData();
            long workOrderId = CollectionUtils.isNotEmpty(workOrderExts) ? workOrderExts.get(0).getWorkOrderId() : 0;
            CfVerificationVo cfVerificationVo = new CfVerificationVo();
            BeanUtils.copyProperties(crowdFundingVerification, cfVerificationVo);
            cfVerificationVo.setRelation(RelationShip.codeOf(crowdFundingVerification.getRelationShip()).getDescription());
            cfVerificationVo.setIdentityMask(maskUtil.buildByDecryptStrAndType(identity, DesensitizeEnum.IDCARD));
//            cfVerificationVo.setIdentity(cfVerificationVo.getIdentityMask() == null ? "" : cfVerificationVo.getIdentityMask().getMaskNumber());
            cfVerificationVo.setStatus(seeOnlySelf ? 1 : 0);
            cfVerificationVo.setWorkOrderId(workOrderId);
            UserInfoModel userInfoModel = userInfoModelMap.get(crowdFundingVerification.getVerifyUserId());
            cfVerificationVo.setMobileMask(maskUtil.buildByEncryptPhone(Optional.ofNullable(userInfoModel).map(UserInfoModel::getCryptoMobile).orElse("")));
//            cfVerificationVo.setMobile(cfVerificationVo.getMobileMask() == null ? "" : cfVerificationVo.getMobileMask().getMaskNumber());
            // 投诉信息
            complaintService.buildComplaintInfo(cfVerificationVo,crowdFundingVerification);
            crowdfundingfundingVerificationVos.add(cfVerificationVo);
        });
        Map<String, Object> result = Maps.newHashMap();
        result.put("list", crowdfundingfundingVerificationVos);
        result.put("pagination", PageUtil.transform2PageMap(crowdFundingVerifications));
        return NewResponseUtil.makeSuccess(result);


    }

    //动态信息操作

    @RequiresPermission("comment:remove-progress")
    @RequestMapping(path = "/remove-progress", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response removeProgress(String infoUuid, Integer id) {
        Integer userId = ContextUtil.getAdminUserId();
        LOGGER.info("CrowdfundingCommentController removeProgress userId={},infoUuid={},id={}", userId, infoUuid, id);
        if (userId == null || StringUtil.isBlank(infoUuid) || id == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        cfCommonFeignClient.deleteProgress(crowdfundingInfo.getId(), id);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("comment:get-progress")
    @PostMapping(path = "/update-progress-images", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    @ApiOperation("修改筹款动态图片")
    public Response updateProgressImages(Integer userId,
                                         @RequestParam @ApiParam("动态id") int progressId,
                                         @RequestParam @ApiParam("图片url字符串 逗号分隔") String imageUrls) {
        LOGGER.info("CrowdfundingCommentController updateProgressImages userId={},progressId={},imageUrls={}",
                userId, progressId, imageUrls);
        boolean res = crowdfundingDelegate.updateImageUrls(progressId, imageUrls);
        return res ? NewResponseUtil.makeSuccess(null) : NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
    }

    @RequiresPermission("comment:get-progress")
    @PostMapping(path = "/delete-mask-progress-images", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    @ApiOperation("删除掩码动态图片")
    public Response deleteMaskProgressImages(@RequestParam @ApiParam("动态id") int progressId,
                                             @RequestParam @ApiParam("删除的图片url字符串") String imageUrl) {
        LOGGER.info("CrowdfundingCommentController deleteMaskProgressImages progressId={},imageUrl={}",
                progressId, imageUrl);
        boolean res = crowdfundingDelegate.deleteMaskImageUrls(progressId, imageUrl);
        return res ? NewResponseUtil.makeSuccess(null) : NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
    }

    @RequiresPermission("comment:get-progress")
    @RequestMapping(path = "/get-progress", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response getProgress(String infoUuid, Integer current, Integer pageSize,
                                @RequestParam(required = false) String content, Integer id) {
        return getProgressV2(infoUuid, current, pageSize, content, id, "", "");
    }

    @RequiresPermission("comment:get-progress-v2")
    @RequestMapping(path = "/get-progress-v2", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response getProgressV2(String infoUuid, Integer current, Integer pageSize,
                                  @RequestParam(required = false) String content, @RequestParam(required = false) Integer id,
                                  @RequestParam(required = false) String startTime, @RequestParam(required = false) String endTime){

        Integer userId = ContextUtil.getAdminUserId();
        LOGGER.info("CrowdfundingCommentController getProgress userId={},infoUuid={},current={},pageSize={},content={},id={}", userId, infoUuid, current, pageSize, content, id);
        if (userId == null || StringUtil.isBlank(infoUuid)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (pageSize == null || pageSize < CrowdfundingCons.MIN_PAGE_SIZE
                || pageSize > CrowdfundingCons.MAX_PAGE_SIZE) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        BasicExample basicExample = new BasicExample();
        BasicExample.Criteria criteria = basicExample.or();
        if (id != null) {
            criteria.andEqualTo("id", id);
        }
        criteria.andEqualTo("crowdfunding_id", crowdfundingInfo.getId());
        if (StringUtil.isNotBlank(content)) {
            criteria.andLike("content", content);
        }
        if (StringUtil.isNotBlank(startTime) && StringUtil.isNotBlank(endTime)) {
            criteria.andGreaterThanOrEqualTo("create_time", startTime);
            criteria.andLessThanOrEqualTo("create_time", endTime);
        }
        basicExample.setOrderByClause(" create_time desc");
        List<CrowdFundingProgress> crowdFundingProgresses = adminCrowdFundingProgressBiz.queryAllByBasicExample(basicExample, current, pageSize);
        List<CfProgressVo> crowdFundingProgressVos = adminCrowdFundingProgressBiz.getProgressOfActivity(crowdFundingProgresses);
        Map<String, Object> result = Maps.newHashMap();
        result.put("list", crowdFundingProgressVos);
        result.put("pagination", PageUtil.transform2PageMap(crowdFundingProgresses));
        return NewResponseUtil.makeSuccess(result);

    }

    //图片信息操作
    @RequiresPermission("comment:remove-attachment")
    @RequestMapping(path = "/remove-attachment", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response removeAttachment(String infoUuid, Integer id, @RequestParam(name = "reason", required = false, defaultValue = "") String reason) {
        Integer userId = ContextUtil.getAdminUserId();
        LOGGER.info("CrowdfundingCommentController removeAttachment userId={},infoUuid={},id={}", userId, infoUuid, id);
        if (userId == null || StringUtil.isBlank(infoUuid) || id == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
//        int num = crowdfundingDelegate.updateForDelete(- crowdfundingInfo.getId(), id);

        int num = adminCrowdfundingAttachmentBiz.deleteByIds(Lists.newArrayList(id),crowdfundingInfo.getId());

        if (num > 0) {
            CrowdfundingAttachment attachment = adminCrowdfundingAttachmentBiz.getAttachmentById(crowdfundingInfo.getId(), id);
//            adminCrowdfundingAttachmentBiz.shieldedAccessFromOss(attachment);
            adminCrowdfundingAttachmentBiz.updateTitleImgAfterDeleteImg(attachment);

            adminCrowdfundingAttachmentBiz.deleteImageFromBaseStage(crowdfundingInfo.getId(), attachment);

            approveRemarkOldService.add(crowdfundingInfo.getId(), userId, "ugc管理删除了一张图片(" + AdminAttachmentTypeEnum.getWord(attachment.getType().value()) +") 原因：" + reason);
        }
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("comment:remove-attachment")
    @RequestMapping(path = "/remove-mask-attachment", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response removeMaskAttachment(String infoUuid, Integer id,
                                         @RequestParam(name = "reason", required = false, defaultValue = "") String reason) {
        Integer userId = ContextUtil.getAdminUserId();
        LOGGER.info("CrowdfundingCommentController removeMaskAttachment userId={},infoUuid={},id={}", userId, infoUuid, id);
        if (userId == null || StringUtil.isBlank(infoUuid) || id == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        Response<Integer> response = cfImageMaskFeignClient.deleteMaskImage(Lists.newArrayList(id.longValue()), ugcTypes);
        Integer num = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(0);

        if (num > 0) {
            CrowdfundingAttachment attachment = adminCrowdfundingAttachmentBiz.getAttachmentById(crowdfundingInfo.getId(), id);
            if (Objects.isNull(attachment)) {
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }
            approveRemarkOldService.add(crowdfundingInfo.getId(), userId, "ugc管理删除了一张掩码图片(" + AdminAttachmentTypeEnum.getWord(attachment.getType().value()) +"(掩码后)) 原因：" + reason);
        }
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("comment:get-attachment")
    @RequestMapping(path = "/get-attachment", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response getAttachment(String infoUuid, @RequestParam(required = false) Integer type) {
        Integer userId = ContextUtil.getAdminUserId();
        LOGGER.info("CrowdfundingCommentController removeAttachment userId={},infoUuid={},type={}", userId, infoUuid, type);
        if (userId == null || infoUuid == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (!infoUuid.equals(" ") && crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        List<CrowdfundingAttachment> fundingAttachment = null;
        if (StringUtil.isNotBlank(infoUuid)) {
            fundingAttachment = adminCrowdfundingAttachmentBiz.getListByInfoIdListAndType(Lists.newArrayList(crowdfundingInfo.getId()), AdminAttachmentTypeEnum.map.get(type));
        }
        if (type == null) {
            fundingAttachment = adminCrowdfundingAttachmentBiz.queryAttachment(crowdfundingInfo.getId());
        }

        List<CfAttachmentVo> attachmentVos = Lists.newArrayList();
        List<Integer> attachmentIds = Lists.newArrayList();
        for (CrowdfundingAttachment attachment : fundingAttachment){
            if (adminCrowdfundingAttachmentBiz.canNotShowInUgcManage(attachment.getType())) {
                continue;
            }
            CfAttachmentVo attachmentVo = new CfAttachmentVo();
            BeanUtils.copyProperties(attachment, attachmentVo);
            attachmentVo.setPriority(priority(attachment.getType()));
            attachmentVos.add(attachmentVo);
            attachmentIds.add(attachmentVo.getId());
        }
        watermarkService.fillUgcManageWatermark(attachmentIds, attachmentVos);
        Collections.sort(attachmentVos, new Comparator<CfAttachmentVo>() {
            @Override
            public int compare(CfAttachmentVo o1, CfAttachmentVo o2) {
                if(o1.getPriority() == o2.getPriority()){
                    return 0;
                } else if(o1.getPriority() < o2.getPriority()){
                    return 1;
                } else {
                    return -1;
                }
            }
        });

        Map<Integer, String> attachmentMap = AdminAttachmentTypeEnum.emnuMap;
        Map<String, Object> result = Maps.newHashMap();
        result.put("attachmentMap", attachmentMap);
        result.put("fundingAttachment", attachmentVos);
        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("comment:get-attachment-ugc")
    @RequestMapping(path = "/get-mask-attachment-ugc", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response getMaskAttachmentUgc(String infoUuid, String param) {
        if (StringUtils.isEmpty(infoUuid)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        return aiImageMaskService.queryUgcMaskAttachment(infoUuid, param);
    }

    @RequiresPermission("comment:get-attachment-ugc")
    @RequestMapping(path = "/get-attachment-ugc", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response<Map<String, Object>> getAttachmentUgc(String infoUuid, String param) {

        if (StringUtils.isEmpty(infoUuid)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        CfAttachmentFilter cfAttachmentFilter = null;
        try {
            cfAttachmentFilter = JSON.parseObject(param, CfAttachmentFilter.class);
        } catch (Exception e) {
            log.error("筛选失败 infoUuid:{}", infoUuid, e);
        }

        List<CrowdfundingAttachment> fundingAttachment = adminCrowdfundingAttachmentBiz.queryAttachment(crowdfundingInfo.getId());

        List<CfAttachmentVo> attachmentVos = Lists.newArrayList();
        List<Integer> attachmentIds = Lists.newArrayList();
        for (CrowdfundingAttachment attachment : fundingAttachment) {
            if (adminCrowdfundingAttachmentBiz.canNotShowInUgcManage(attachment.getType())) {
                continue;
            }
            CfAttachmentVo attachmentVo = new CfAttachmentVo();
            BeanUtils.copyProperties(attachment, attachmentVo);
            attachmentVo.setPriority(priority(attachment.getType()));
            attachmentVos.add(attachmentVo);
            attachmentIds.add(attachmentVo.getId());
        }
        watermarkService.fillUgcManageWatermark(attachmentIds, attachmentVos);
        Collections.sort(attachmentVos, new Comparator<CfAttachmentVo>() {
            @Override
            public int compare(CfAttachmentVo o1, CfAttachmentVo o2) {
                if (o1.getPriority() == o2.getPriority()) {
                    return 0;
                } else if (o1.getPriority() < o2.getPriority()) {
                    return 1;
                } else {
                    return -1;
                }
            }
        });

        //过滤掉未删除的照片
        attachmentVos = cfCrowdfundingAttachmentRecordService.filterByCaseId(crowdfundingInfo.getId(), attachmentVos);

        if (Objects.nonNull(cfAttachmentFilter)) {
            Integer isDelete = cfAttachmentFilter.getIsDelete();
            Integer type = cfAttachmentFilter.getType();
            String uploadTimeStart = cfAttachmentFilter.getUploadTimeStart();
            String uploadTimeEnd = cfAttachmentFilter.getUploadTimeEnd();

            if (Objects.nonNull(isDelete)) {
                attachmentVos = attachmentVos.stream().filter(v -> v.getIsDelete() == isDelete).collect(Collectors.toList());
            }

            if (Objects.nonNull(type)) {
                AttachmentTypeEnum attachmentTypeEnum  = AttachmentTypeEnum.getAttachmentTypeEnum(type);
                attachmentVos = attachmentVos.stream().filter(v -> v.getTypeValue() == attachmentTypeEnum.value()).collect(Collectors.toList());
            }

            if (StringUtils.isNotEmpty(uploadTimeStart) && StringUtils.isNotEmpty(uploadTimeEnd)) {
                attachmentVos = attachmentVos.stream().filter(v -> v.getCreateTime().getTime() >= DateUtil.getStr2LDate(uploadTimeStart).getTime()
                        && v.getCreateTime().getTime() <= DateUtil.getStr2LDate(uploadTimeEnd).getTime()).collect(Collectors.toList());
            }
        }

        Map<Integer, String> attachmentMap = AdminAttachmentTypeEnum.emnuMap;
        Map<String, Object> result = Maps.newHashMap();
        result.put("attachmentMap", attachmentMap);
        result.put("fundingAttachment", attachmentVos);
        return NewResponseUtil.makeSuccess(result);
    }

    private int priority(AttachmentTypeEnum type){
        if(Objects.isNull(type)){
            return 0;
        }

        switch (type){
            case ATTACH_ONLY_ID_CARD:
                return 0;
            case ATTACH_ID_CARD:
                return 10;
            case ATTACH_PAYEE_ID_CARD:
                return 20;
            case ATTACH_PAYEE_RELATION:
                return 30;
            case ATTACH_TREATMENT:
                return 40;
            default:
                return Integer.MAX_VALUE;
        }

    }

    //评论信息操作
    @RequiresPermission("comment:remove-comment")
    @RequestMapping(path = "/remove-comment", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response removeComment(Long id) {
        Integer userId = ContextUtil.getAdminUserId();
        LOGGER.info("CrowdfundingCommentController removeComment userId={},id={}", userId, id);
        if (userId == null || id == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        ugcDelegate.removeCrowdfundingCommentById(id);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("comment:get-comment")
    @RequestMapping(path = "/get-comment", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response getComment(Integer current, Integer pageSize,
                               @RequestParam(required = false) Long parentId,
                               @RequestParam(required = false) Integer type,
                               @RequestParam(required = false) String content,
                               @RequestParam(required = false) Integer id) {
        LOGGER.info("CrowdfundingCommentController getComment userId={},commentId={},current={},pageSize={},type={},content={},id={}", ContextUtil.getAdminUserId(), parentId, current, pageSize, type, content, id);
        if (pageSize == null || pageSize < CrowdfundingCons.MIN_PAGE_SIZE || pageSize > CrowdfundingCons.MAX_PAGE_SIZE) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        CrowdfundingCommentType commentType = CrowdfundingCommentType.fromValue(type);
        if(Objects.isNull(commentType)){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        BasicExample basicExample = new BasicExample();
        BasicExample.Criteria criteria = basicExample.or();
        if (id != null) {
            criteria.andEqualTo("id", id);
        }
        if (parentId != null) {
            criteria.andEqualTo("parent_id", parentId);
        }
        if (type != null) {
            criteria.andEqualTo("type", type);
        }
        if (content != null) {
            criteria.andLike("content", content);
        }
        basicExample.setOrderByClause(" create_time desc");
        List<CrowdfundingComment> comments = adminCrowdfundingCommentBiz.getByPage(basicExample, current, pageSize);
        List<CfCommentVo> crowdfundingCommentVos = adminCrowdfundingCommentBiz.getCrowdfundingCommentVo(comments, commentType);
        Map<String, Object> result = Maps.newHashMap();
        result.put("list", crowdfundingCommentVos);
        result.put("pagination", PageUtil.transform2PageMap(comments));
        return NewResponseUtil.makeSuccess(result);
    }

    //订单操作
    @RequiresPermission("comment:get-order")
    @RequestMapping(path = "/get-order", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response getOrder(String infoUuid,Integer current, Integer pageSize,
                             @RequestParam(required = false) String comment,
                             @RequestParam(required = false) Integer amount,
                             @RequestParam(required = false) String startTime,
                             @RequestParam(required = false) String endTime,
                             @RequestParam(required = false) Long id,
                             @RequestParam(required = false) String mobile,
                             @ApiParam("金额区间开始")
                             @RequestParam(value = "amountStart", required = false, defaultValue = "-1") int amountStart,
                             @ApiParam("金额区间结束")
                             @RequestParam(value = "amountEnd", required = false, defaultValue = "-1") int amountEnd) {
        Integer userId = ContextUtil.getAdminUserId();
        LOGGER.info("CrowdfundingCommentController getOrder userId={},infoUuid={},current={},pageSize={},comment={},amount={},startTime={},endTime={},id={}",
                userId, infoUuid, current, pageSize, comment, amount, startTime, endTime, id);
        if (userId == null || StringUtil.isBlank(infoUuid)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (pageSize == null || pageSize < CrowdfundingCons.MIN_PAGE_SIZE
                || pageSize > CrowdfundingCons.MAX_PAGE_SIZE) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo fundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (fundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        List<CrowdfundingOrder> successByPage = adminCrowdfundingOrderBiz.getSuccessByPage(id,
                fundingInfo.getId(), amount, comment, startTime, endTime, current, pageSize, mobile, amountStart, amountEnd);
        List<CfContributeOrder> contributeOrders = new ArrayList<>();
        Map<Long, CrowdfundingPayRecord> payRecordMapping = Maps.newConcurrentMap();
        if (CollectionUtils.isNotEmpty(successByPage)) {
            // 通过订单payUid获取捐赠订单
            FeignResponse<List<CfContributeOrder>> response = cfContributeFeignClient.selectBySourceChannel(CombineCommonEnum.CombineOrderType.CASE_DONATE.name(), successByPage.stream().map(CrowdfundingOrder::getId).map(String::valueOf).distinct().collect(Collectors.toList()));
            if (Objects.nonNull(response) && response.ok() && CollectionUtils.isNotEmpty(response.getData())) {
                contributeOrders = response.getData();
            }

            payRecordMapping = payRecordBiz.getPaySuccessMapping(successByPage.stream().map(CrowdfundingOrder::getId).collect(Collectors.toList()));
        }
        Map<String, Object> result = Maps.newHashMap();
        List<CfOrderVo> crowdfundingCommentVo = adminCrowdfundingOrderBiz.getCrowdfundingCommentVo(successByPage, contributeOrders, payRecordMapping);
        crowdfundingCommentVo.stream().filter(r -> StringUtils.isNotBlank(r.getOriginMobile()))
                        .forEach(r -> {
                            r.setOriginMobileMask(maskUtil.buildByDecryptPhone(r.getOriginMobile()));
                            r.setOriginMobile(StringUtils.EMPTY);
                        });
        result.put("list", crowdfundingCommentVo);
        result.put("pagination", PageUtil.transform2PageMap(successByPage));
        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("comment:edit-order")
    @RequestMapping(path = "/edit-order", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response editOrder(@RequestParam("id") Long id,
                              @RequestParam(name = "comment", defaultValue = "") String comment) {
        Integer userId = ContextUtil.getAdminUserId();
        LOGGER.info("CrowdfundingCommentController editOrder userId={},id={},comment:{}", userId, id, comment);
        if (userId == null || id == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        adminCrowdfundingOrderBiz.editByIdAndComment(comment, id);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("comment:remove-order")
    @RequestMapping(path = "/remove-order", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response removeOrder(Long id) {
        Integer userId = ContextUtil.getAdminUserId();
        LOGGER.info("CrowdfundingCommentController removeOrder userId={},id={},comment:{}", userId, id);
        if (userId == null || id == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        adminCrowdfundingOrderBiz.updateValid(0, id);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("comment:upload")
    @RequestMapping(path = "/upload", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response upload(String imgUrl, String infoUuid, Integer attachType, Integer rank) {
        LOGGER.info("CrowdfundingCommentController upload imgUrl={},infoUuid={},attachType:{},rank:{}", imgUrl, infoUuid, attachType, rank);
        if (imgUrl == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_IS_NULL);
        }
        if (attachType == null || attachType > AdminAttachmentTypeEnum.values().length || attachType < 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        if (StringUtil.isBlank(imgUrl)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }
        AttachmentTypeEnum attachmentTypeEnum = AdminAttachmentTypeEnum.map.get(attachType);
        int id = crowdfundingInfo.getId();
        CrowdfundingAttachment attachment = new CrowdfundingAttachment(id, attachmentTypeEnum, imgUrl, rank == null ? 0 : rank);
        crowdfundingDelegate.addCrowdfundingAttachment(attachment);
        adminCrowdfundingAttachmentBiz.updateTitleImgAfterUploadImg(attachment);

        adminCrowdfundingAttachmentBiz.addImageToBaseStage(id, attachmentTypeEnum, imgUrl);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("comment:update-progress-picture")
    @RequestMapping(path = "/update-progress-picture", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response updateProgressPicture(String infoUuid, Integer id, String imageUrls) {
        Integer userId = ContextUtil.getAdminUserId();
        LOGGER.info("CrowdfundingCommentController updateProgressPicture infoUuid:{},id:{},userId:{},imageUrls:{}", infoUuid, id, userId, imageUrls);
        if (id == null || userId == null) {
            LOGGER.error("CrowdfundingCommentController updateProgressPicture error!");
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        String[] split = imageUrls.split(",");
        for (String s : split) {
            if (!s.contains("http://cf.alioss.shuidihuzhu.com/img/ck")) {
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }
        }
        try {
            adminCrowdFundingProgressBiz.updateImageUrls(imageUrls, id, crowdfundingInfo.getId());
        } catch (Exception e) {
            LOGGER.error("CrowdfundingCommentController updateProgressPicture error", e);
        }
        return NewResponseUtil.makeSuccess(null);
    }


    @ResponseBody
    @RequestMapping(path = "/query-ugc-operate-record-count", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response<Long> count(@RequestParam(name = "infoUuid") String infoUuid,
                                                                         @RequestParam(name = "bizType") int bizType,
                                                                         @RequestParam(name = "bizId", required = false) long bizId){
        long count = 0;
        int adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        CfInfoSimpleModel cfInfo = crowdfundingDelegate.getCfInfoSimpleModel(infoUuid);
        if(Objects.isNull(cfInfo)){
            return ResponseUtil.makeError(ErrorCode.CF_NOT_FOUND);
        }

        UgcBizType ugcBizType = UgcBizType.parse(bizType);
        if(Objects.isNull(ugcBizType) || bizId <= 0){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<AdminUgcOperateRecordDO> ugcOperateRecordDOS = ugcOperateRecordService.query(cfInfo.getId(), bizType, bizId);
        if (CollectionUtils.isNotEmpty(ugcOperateRecordDOS)) {
            count = ugcOperateRecordDOS.stream().filter(e -> UgcBizType.VERIFICATION.getKey() == e.getBizType() && UgcManageEnum.VERIFY_SEE_ONESELF.getKey() == e.getOperateType()).count();
            return NewResponseUtil.makeSuccess(count);
        }

        return ResponseUtil.makeSuccess(count);
    }

}
