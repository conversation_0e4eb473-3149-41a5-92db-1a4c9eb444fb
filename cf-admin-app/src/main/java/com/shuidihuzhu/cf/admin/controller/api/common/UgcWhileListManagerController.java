package com.shuidihuzhu.cf.admin.controller.api.common;

import com.shuidihuzhu.cf.delegate.saas.AdminOrganization;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.OrganizationClientV1;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.service.crowdfunding.UgcWhileListManagerService;
import com.shuidihuzhu.client.cf.risk.client.CfUgcWhileListClient;
import com.shuidihuzhu.client.cf.risk.model.*;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * @author: fengxuan
 * @create 2019-11-14 13:32
 * UGC 白名单
 **/
@Slf4j
@Api("ugc白名单管理")
@RestController
@RequestMapping(path = "/admin/ugc/while-list/manager")
public class UgcWhileListManagerController {

    @Autowired
    CfUgcWhileListClient cfUgcWhileListClient;

    @Autowired
    UgcWhileListManagerService managerService;

    @Autowired
    SeaAccountClientV1 seaAccountClientV1;

    @Resource
    private OrganizationClientV1 organizationClientV1;

    DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");

    @ApiOperation("创建")
    @RequestMapping(value = "create", method = RequestMethod.POST)
    public Response<Boolean> create(@RequestParam(value = "whileListUserId") long whileListUserId, @RequestParam(value = "reason") String reason,
                                    @RequestParam(value = "validTime") String validTime) {
        if (StringUtils.isBlank(reason) || StringUtils.isBlank(validTime)) {
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        int adminUserId = ContextUtil.getAdminUserId();
        AuthRpcResponse<AdminUserAccountModel> account = seaAccountClientV1.getValidUserAccountById(adminUserId);
        String name = Optional.ofNullable(account).map(AuthRpcResponse::getResult)
                .map(AdminUserAccountModel::getName).orElse("");
        String org = Optional.ofNullable(getOrg(adminUserId)).orElse("");
        CfUgcWhileListInfo whileListInfo = buildWhileListInfo(whileListUserId, reason, validTime, name, org);
        return managerService.add(whileListInfo, adminUserId);
    }


    @ApiOperation("编辑")
    @RequestMapping(value = "edit", method = RequestMethod.POST)
    public Response<Boolean> edit(@RequestParam(value = "whileListId") long whileListId,
                                  @RequestParam(value = "validTime") String validTime) {
        int adminUserId = ContextUtil.getAdminUserId();
        if (StringUtils.isBlank(validTime)) {
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        AuthRpcResponse<AdminUserAccountModel> account = seaAccountClientV1.getValidUserAccountById(adminUserId);
        String name = Optional.ofNullable(account).map(AuthRpcResponse::getResult)
                .map(AdminUserAccountModel::getName).orElse("");
        String org = getOrg(adminUserId);
        String operatorName = StringUtils.isBlank(org) ? name : org + "-" + name;
        return managerService.edit(whileListId, validTime, adminUserId, operatorName);
    }

    @RequiresPermission("white-list:get-reason")
    @ApiOperation("查看添加原因")
    @RequestMapping(value = "get-reason", method = RequestMethod.POST)
    public Response<String> getReason(@RequestParam(value = "whileListId") long whileListId) {
        return cfUgcWhileListClient.getReason(whileListId);
    }


    @ApiOperation("点查")
    @RequestMapping(value = "get", method = RequestMethod.POST)
    public Response<CfUgcWhileListInfo> get(@RequestParam(value = "whileListId") long whileListId) {
        return cfUgcWhileListClient.getById(whileListId);
    }


    @ApiOperation("列表查询")
    @RequestMapping(value = "list", method = RequestMethod.POST)
    public Response<CfUgcWhileListPageResult> list(@RequestParam(value = "uid", defaultValue = "0") Long whileListUserId,
                                                   @RequestParam(value = "operator", defaultValue = "") String creator,
                                                   @RequestParam(value = "status", defaultValue = "0") int status,
                                                   @RequestParam(value = "current", defaultValue = "1") int current,
                                                   @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        //check
        WhileListStatusEnum statusEnum = WhileListStatusEnum.findByCode(status);
        if (statusEnum == null) {
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return cfUgcWhileListClient.listByPage(whileListUserId, creator, status, current, pageSize);
    }


    @ApiOperation("查看操作历史")
    @RequestMapping(value = "list-record", method = RequestMethod.POST)
    public Response<List<CfUgcWhileListRecord>> listRecord(@RequestParam(value = "whileListId") long whileListId) {
        return cfUgcWhileListClient.listRecord(whileListId);
    }


    private CfUgcWhileListInfo buildWhileListInfo(long whileListUserId, String reason, String validTime, String name, String org) {
        CfUgcWhileListInfo cfUgcWhileListInfo = new CfUgcWhileListInfo();
        cfUgcWhileListInfo.setUserId(whileListUserId);
        cfUgcWhileListInfo.setValidTime(formatter.parseDateTime(validTime).toDate());
        cfUgcWhileListInfo.setCreatorOrg(org);
        cfUgcWhileListInfo.setCreatorName(name);
        cfUgcWhileListInfo.setReason(reason);
        return cfUgcWhileListInfo;
    }


    private String getOrg(int adminUserId) {
        String organizationName = "";
        AuthRpcResponse<AdminOrganization> userOrgInfo = organizationClientV1.getUserOrgInfo(adminUserId);
        if (userOrgInfo.isSuccess()) {
            AdminOrganization organization = userOrgInfo.getResult();
            if(organization != null){
                organizationName = organization.getName();
            }
        } else {
            log.warn("获取org信息异常!userId:{}", adminUserId);
        }
        return organizationName;
    }


    @Getter
    public enum WhileListStatusEnum {
        ALL(0, "全部"),
        IN_TIME(1, "有效"),
        OUT_TIME(2, "失效"),
        ;
        private int code;
        private String desc;

        WhileListStatusEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static WhileListStatusEnum findByCode(int code) {
            for (WhileListStatusEnum value : WhileListStatusEnum.values()) {
                if (value.getCode() == code) {
                    return value;
                }
            }
            return null;
        }
    }
}
