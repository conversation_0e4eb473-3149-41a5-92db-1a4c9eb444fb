package com.shuidihuzhu.cf.admin.controller.api.backdoor;


import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.delegate.service.TokenServiceBiz;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.service.crowdfunding.BackdoorService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 * @DATE 2018/12/12
 */

@Api(value = "/admin/operation/back", description = "后门操作权限")
@Slf4j
@RestController
@RequestMapping(path = "/admin/operation/back")
public class  CfInfoController {

    @Autowired
    private SeaAccountClientV1 clientV1;
    @Autowired
    private BackdoorService backdoorService;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Resource
    private TokenServiceBiz tokenServiceBiz;
    @Autowired
    private ApplicationService applicationService;

    @RequestMapping(value = "/getInfo", method = RequestMethod.POST)
    @RequiresPermission("backdoor:getInfo")
    public Response getInfo(int caseId){

        long userId = ContextUtil.getAdminUserId();

        if (userId <= 0){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_NOT_LOGIN_ERROR);
        }

        if (caseId <=0){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        log.info("getInfo caseId={} userId={}",caseId,userId);

        return backdoorService.getInfo(caseId);
    }


    @RequestMapping(value = "/changeInfoStatus", method = RequestMethod.POST)
    @RequiresPermission("backdoor:changeInfoStatus")
    public Response changeInfoStatus(int caseId,int status){

        long userId = ContextUtil.getAdminUserId();

        if (userId <= 0){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_NOT_LOGIN_ERROR);
        }

        if (caseId <=0){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        log.info("changeInfoStatus caseId={} userId={} status={}",caseId,userId,status);

        if ( CrowdfundingStatus.SUBMITTED.value() != status){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        return backdoorService.changeInfoStatus(caseId,status);
    }



    @RequestMapping(value = "/changeInformationStatus", method = RequestMethod.POST)
    // @see CrowdfundingInfoDataStatusTypeEnum  type类型
    @RequiresPermission("backdoor:changeInformationStatus")
    public Response changeInformationStatus(String caseUuid,int type){

        long userId = ContextUtil.getAdminUserId();

        if (userId <= 0){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_NOT_LOGIN_ERROR);
        }

        if (StringUtils.isEmpty(caseUuid)){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        log.info("changeInformationStatus caseUuid={} userId={} type={}",caseUuid,userId,type);

        boolean result = backdoorService.changeInformationStatus(caseUuid,type);

        return NewResponseUtil.makeSuccess(result);
    }

    @ApiOperation(value = "根据userId清除此用户所有缓存")
    @RequestMapping(value = "/clear-user-cache", method = RequestMethod.POST)
    @RequiresPermission("backdoor:clear-user-cache")
    public Response clearUserAllCache(@RequestParam(value = "accountId", defaultValue = "0") long accountId) {
        if (accountId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        try {
            userInfoServiceBiz.adminClearUserAllCache(accountId);
            return NewResponseUtil.makeSuccess("ok");
        } catch (Exception e) {
            log.error("清除userCache失败..", e);
        }
        return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
    }

    @ApiOperation(value = "手动过期userToken")
    @RequestMapping(value = "/expires-token", method = RequestMethod.POST)
    @RequiresPermission("backdoor:expires-token")
    public Response expiresToken(@RequestParam(value = "sdUserToken", defaultValue = "") String sdUserToken) {
        if (StringUtils.isBlank(sdUserToken)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        try {
            tokenServiceBiz.adminExpiresSdToken(sdUserToken);
            return NewResponseUtil.makeSuccess("ok");
        } catch (Exception e) {
            log.error("token手动过期失败..token:{}", sdUserToken, e);
        }
        return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
    }
    @ApiOperation("自动审核通过工单")
    @RequestMapping("/audit-pass-work-order")
    @RequiresPermission("backdoor:audit-pass-work-order")
    public Response<Void> auditPassWorkOrder(@RequestParam(value = "caseId", defaultValue = "0", required = false) int caseId,
                                             @RequestParam(value = "infoUuid", defaultValue = "", required = false) String infoUuid,
                                             @RequestParam(value = "userName", defaultValue = "") String userName) {
        if (!applicationService.isDevelopment()) {
            return NewResponseUtil.makeSuccess();
        }
        if (StringUtils.isEmpty(userName)) {
            return NewResponseUtil.makeSuccess();
        }
        AuthRpcResponse<List<AdminUserAccountModel>> response = clientV1.getUserAccountsByNameLike(userName);
        if (response == null || CollectionUtils.isEmpty(response.getResult())) {
            return NewResponseUtil.makeSuccess();
        }
        AdminUserAccountModel accountModel = response.getResult().stream().filter(userMode -> userMode.getMis().equals(userName)).findFirst().orElse(null);
        if (accountModel == null) {
            return NewResponseUtil.makeSuccess();
        }
        long userId = accountModel.getId();
        backdoorService.auditPassWorkOrder(caseId, (int) userId, infoUuid);
        return NewResponseUtil.makeSuccess();
    }

}
