package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.admin.util.admin.IntegerUtil;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.service.dingding.AdminDingdingBiz;
import com.shuidihuzhu.client.baseservice.userpoints.grpc.GetUserPointsStatReq;
import com.shuidihuzhu.client.baseservice.userpoints.grpc.UserPointsServiceGrpc;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import net.devh.springboot.autoconfigure.grpc.client.GrpcStub;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.ServletRequest;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;

/**
 * @package: com.shuidihuzhu.cf.admin.controller.api.crowdfunding
 * @Author: liujiawei
 * @Date: 2018/8/23  20:16
 */

@Controller
@RequestMapping(path = "/admin/crowdfunding/integral")
@Slf4j
public class AdminBalanceUserPointsAccout {
    public static final String KEY = "<EMAIL>";
    @Autowired
    AdminDingdingBiz dingdingBiz;
    @Autowired
    SeaAccountClientV1 seaAccountClientV1;
    @GrpcStub("user-points")
    private UserPointsServiceGrpc.UserPointsServiceBlockingStub userPointsServiceBlockingStub;
    @RequestMapping(path = "balance-account", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    @RequiresPermission("cf-integral:balance-account")
    public Response refresh(Long uuserId, String Key, ServletRequest request) {
        if (!Key.equals(KEY)) {
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_NO_PCODE_PRIVILEGE);
            }
            if (uuserId == null || uuserId <= 0) {
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }
        AdminUserAccountModel admin = seaAccountClientV1.getValidUserAccountById(IntegerUtil.parseInt(request.getParameter("userId"))).getResult();
        Integer res = -1;
        try {
            //rpc平账
             res = userPointsServiceBlockingStub.balanceUserAccout(GetUserPointsStatReq.newBuilder().setUserId(uuserId).build()).getStatus();
             String success = "SUCCESS";
             if (res == -1){
                 success = "FAIL";
             }
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String errorMsg = "【管理员平账接口报警】\n状态:" + success + "\n操作者："+ admin.getName() + "\n平账userId:" + uuserId + "\n当前时间：" + simpleDateFormat.format(new Date(System.currentTimeMillis()));
            dingdingBiz.sendMsg(55, errorMsg, true, new ArrayList<>());
        } catch (Exception e) {
            log.error("钉钉异常", e);
        }
        if (res == -1){
            return NewResponseUtil.makeError(AdminErrorCode.ERROR_RESULT);
        }
            return NewResponseUtil.makeSuccess(AdminErrorCode.SUCCESS);
        }

}

