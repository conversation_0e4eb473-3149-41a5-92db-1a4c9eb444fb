package com.shuidihuzhu.cf.admin.controller.api.report;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportOfficialLetterStatusEnum;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.report.CfReportOfficialLetter;
import com.shuidihuzhu.cf.service.report.CfReportOfficialLetterService;
import com.shuidihuzhu.cf.vo.report.CfReportOfficialLetterLogVo;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * @author: lixiaoshuang
 * @create: 2020-05-21 14:46
 **/
@RestController
@RequestMapping(path = "admin/cf/report/official-letter")
@Slf4j
@Api("举报公函")
public class CfReportOfficialLetterController {

    @Autowired
    private CfReportOfficialLetterService cfReportOfficialLetterService;

    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @RequiresPermission("official-letter:notice")
    @RequestMapping(path = "notice")
    @ApiOperation("通知开函")
    public Response<Void> noticeOfficialLetter(@ApiParam("案例id") @RequestParam("caseId") int caseId) {

        if (caseId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        int userId = ContextUtil.getAdminUserId();
        if(userId <= 0){
            return NewResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        AdminUserAccountModel userModel = seaAccountClientV1.getValidUserAccountById(userId).getResult();
        String userName = userModel.getName();

        CfReportOfficialLetter cfReportOfficialLetter = new CfReportOfficialLetter();
        cfReportOfficialLetter.setCaseId(caseId);
        cfReportOfficialLetter.setName(userName);
        cfReportOfficialLetter.setLetterStatus(CfReportOfficialLetterStatusEnum.WAIT_LETTER.getCode());
        cfReportOfficialLetter.setLetterType("");
        cfReportOfficialLetter.setComment("");
        cfReportOfficialLetter.setImages("");
        cfReportOfficialLetter.setNum("");

        cfReportOfficialLetterService.addOfficialLetter(cfReportOfficialLetter, userId);

        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("official-letter:add-or-update")
    @RequestMapping(path = "add-or-update")
    @ApiOperation("新建公函/编辑公函")
    public Response<Void> addOfficialLetter(@ApiParam("公函信息") @RequestBody CfReportOfficialLetter cfReportOfficialLetter) {
        log.info("CfReportOfficialLetterController.addOfficialLetter cfReportOfficialLetter:{}", JSON.toJSONString(cfReportOfficialLetter));
        if (Objects.isNull(cfReportOfficialLetter)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (StringUtils.isEmpty(cfReportOfficialLetter.getLetterType())
                || cfReportOfficialLetter.getLetterStatus() <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (cfReportOfficialLetter.getCaseId() <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (Objects.isNull(cfReportOfficialLetter.getImages())) {
            cfReportOfficialLetter.setImages(StringUtils.EMPTY);
        }
        int userId = ContextUtil.getAdminUserId();
        if (cfReportOfficialLetter.getId() == 0) {
            cfReportOfficialLetterService.addOfficialLetter(cfReportOfficialLetter, userId);
        } else {
            cfReportOfficialLetterService.editOfficialLetter(cfReportOfficialLetter);
        }
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("official-letter:list")
    @RequestMapping(path = "list")
    @ApiOperation("公函列表")
    public Response<List<CfReportOfficialLetter>> officialLetterList(@ApiParam("案例id") @RequestParam("caseId") int caseId) {
        log.info("CfReportOfficialLetterController.officialLetterList caseId:{}", caseId);
        if (caseId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<CfReportOfficialLetter> cfReportOfficialLetters = cfReportOfficialLetterService.officialLetterList(caseId);

        cfReportOfficialLetters.forEach(f -> {
            if (f.getLetterStatus() == CfReportOfficialLetterStatusEnum.WAIT_LETTER.getCode()) {
                f.setUpdateTime(null);
            }
        });

        return NewResponseUtil.makeSuccess(cfReportOfficialLetters);
    }

    @RequiresPermission("official-letter:get")
    @RequestMapping(path = "get")
    @ApiOperation("公函信息")
    public Response<CfReportOfficialLetter> officialLetterInfo(@ApiParam("公函id") @RequestParam("id") long id) {
        log.info("CfReportOfficialLetterController.officialLetterInfo id:{}", id);
        if (id <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CfReportOfficialLetter cfReportOfficialLetter = cfReportOfficialLetterService.officialLetterInfo(id);
        return NewResponseUtil.makeSuccess(cfReportOfficialLetter);
    }

    @RequiresPermission("official-letter:get-record")
    @RequestMapping(path = "get-record")
    @ApiOperation("公函操作记录")
    public Response<List<CfReportOfficialLetterLogVo>> getRecord(@ApiParam("公函id") @RequestParam("id") long id) {
        log.info("CfReportOfficialLetterController.getRecord id:{}", id);
        if (id <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<CfReportOfficialLetterLogVo> cfReportOfficialLetterLogVos = cfReportOfficialLetterService.getRecord(id);
        return NewResponseUtil.makeSuccess(cfReportOfficialLetterLogVos);
    }


}
