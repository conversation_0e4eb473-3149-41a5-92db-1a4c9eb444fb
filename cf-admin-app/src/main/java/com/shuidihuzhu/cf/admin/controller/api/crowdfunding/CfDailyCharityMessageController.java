package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfDailyCharityMessageBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CfDailyCharityMessage;
import com.shuidihuzhu.common.web.constants.CrowdfundingCons;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.BasicExample;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import jodd.util.StringUtil;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by ahrievil on 2017/5/11.
 */
@Controller
@RequestMapping(path = "/admin/crowdfunding/daily-charity")
public class CfDailyCharityMessageController {

    private static final Logger LOGGER = LoggerFactory.getLogger(CfDailyCharityMessageController.class);

    @Autowired
    private AdminCfDailyCharityMessageBiz adminCfDailyCharityMessageBiz;

    @RequiresPermission("daily-charity:get-list")
    @RequestMapping(path = "get-list", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response getList(Integer current, Integer pageSize, @RequestParam(required = false) String startDate) {
        LOGGER.info("CfDailyCharityMessageController getList current:{},pageSize:{},startDate:{}", current, pageSize, startDate);
        if (pageSize == null || pageSize < CrowdfundingCons.MIN_PAGE_SIZE
                || pageSize > CrowdfundingCons.MAX_PAGE_SIZE) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        BasicExample basicExample = new BasicExample();
        BasicExample.Criteria criteria = basicExample.or();
        if (StringUtil.isNotBlank(startDate)) {
            criteria.andLike("start_date", startDate, BasicExample.LikeType.WITH_RIGHT_WILDCARD);
        }
        basicExample.setOrderByClause(" start_date desc");
        List<CfDailyCharityMessage> cfDailyCharityMessages = adminCfDailyCharityMessageBiz.selectByPage(basicExample, current, pageSize);
        Map<String, Object> result = Maps.newHashMap();
        result.put("pagination", PageUtil.transform2PageMap(cfDailyCharityMessages));
        result.put("list", cfDailyCharityMessages);
        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("daily-charity:add-or-update")
    @RequestMapping(path = "add-or-update", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response addOrUpdate(String param) {
        LOGGER.info("CfDailyCharityMessageController update param:{}", param);
        CfDailyCharityMessage cfDailyCharityMessage = JSON.parseObject(param, CfDailyCharityMessage.class);//已检查过
        LOGGER.info("cfDailyCharityMessage:{}", cfDailyCharityMessage);
        if (cfDailyCharityMessage == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        Date date = cfDailyCharityMessage.getStartDate();
        CfDailyCharityMessage cfDailyCharityMessage1 = adminCfDailyCharityMessageBiz.selectByStartDate(date);
        try {
            if (cfDailyCharityMessage.getId() == null || cfDailyCharityMessage.getId() == 0) {
                if (cfDailyCharityMessage1 != null) {
                    return NewResponseUtil.makeResponse(1, "今天已有，请编辑", null);
                }
                adminCfDailyCharityMessageBiz.insert(cfDailyCharityMessage);
            } else {
                adminCfDailyCharityMessageBiz.updateByPrimaryKey(cfDailyCharityMessage);
            }
        } catch (Exception e) {
            LOGGER.error("CfDailyCharityMessageController update error!", e);
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("daily-charity:delete")
    @RequestMapping(path = "delete", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response delete(Integer id) {
        LOGGER.info("CfDailyCharityMessageController delete id:{},userId:{}", id, ContextUtil.getAdminUserId());
        if (id == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        try {
            adminCfDailyCharityMessageBiz.deleteByPrimaryKey(id);
        } catch (Exception e) {
            LOGGER.error("CfDailyCharityMessageController delete error", e);
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }
        return NewResponseUtil.makeSuccess(null);
    }
}
