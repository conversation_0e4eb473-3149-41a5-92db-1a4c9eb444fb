package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidi.weixin.common.util.StringUtils;
import com.shuidihuzhu.account.model.service.MobileUserIdModel;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.admin.delegate.SeaUserAuthDelegate;
import com.shuidihuzhu.cf.admin.util.ParamTimeRangeHandler;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderBiz;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.biz.admin.exception.ServiceRuntimeException;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdFundingProgressBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfAdminOperationRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfFirstApproveOperatorBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.delegate.commonservice.ICommonServiceDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperationRecordEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdFundingProgressType;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveIdcardVerifyStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.UserLabelAlterEnum;
import com.shuidihuzhu.cf.enums.sona.RoleEnum;
import com.shuidihuzhu.cf.enums.visitconfig.VisitConfigLogActionInfoEnum;
import com.shuidihuzhu.cf.enums.visitconfig.VisitConfigSourceEnum;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.model.admin.workorder.FirsApproveMaterialWithAmount;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.IdcardVerifyWhiteList;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.mq.producer.impl.MQProdecer;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.resulthandler.ResultSensitiveHandler;
import com.shuidihuzhu.cf.service.workorder.WorkOrderExtService;
import com.shuidihuzhu.cf.vo.crowdfunding.WorkOrderFirstApprove;
import com.shuidihuzhu.cf.vo.crowdfunding.WorkOrderUgcBaseInfoVo;
import com.shuidihuzhu.cf.vo.crowdfunding.firstapprove.FirstApproveConfiguration;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.risk.client.UserTagClient;
import com.shuidihuzhu.client.cf.risk.model.result.UserTagHistory;
import com.shuidihuzhu.client.dataservice.faceApi.v1.FaceApiClient;
import com.shuidihuzhu.client.grpc.account.v1.feign.SimpleUserAccountServiceClient;
import com.shuidihuzhu.common.web.constants.CrowdfundingCons;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.IdCardUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by sven on 18/8/4.
 *
 * <AUTHOR>
 */
@RestController
@RefreshScope
@RequestMapping(path = "/admin/crowdfunding/first")
@Api("用户发起之后初审的api")
@Slf4j
public class FirstApproveController {

    /**
     * 记录在sea后台操作记录的文案（成功）
     */
    private final static String APPLY_SUCCESS_MSG = "首次审核通过";

    /**
     * 记录在sea后台操作记录的文案（失败）
     */
    private final static String APPLY_FAIL_MSG = "首次审核驳回";

    @Resource
    private CfAdminOperationRecordBiz cfAdminOperationRecordBiz;

    @Resource
    private AdminCrowdFundingProgressBiz progressBiz;

    @Autowired(required = false)
    private Producer producer;

    @Autowired
    private AdminWorkOrderBiz adminWorkOrderBiz;

    @Resource
    private SeaUserAuthDelegate seaUserAuthDelegate;

    @Autowired
    private UserCommentBiz commentBiz;

    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    public final static int FIFTY_W = ********;
    @Autowired
    private CfFirstApproveOperatorBiz cfFirstApproveOperatorBiz;

    @Autowired
    private MQProdecer mqProdecer;

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Resource
    private IRiskDelegate riskDelegate;

    @Autowired
    private FaceApiClient faceApiClient;

    @Autowired
    private AdminCfInfoExtBiz adminCfInfoExtBiz;

    @Value("${approve.first-approve.amount.need-desc:********}")
    private int minAmountDesc;


    @Value("${approve.first-approve.success.progress:医疗材料已公示}")
    private String progressContent;

    @Resource
    private UserTagClient userTagClient;

    @Resource
    private WorkOrderExtService workOrderExtService;

    @Resource
    private ICommonServiceDelegate commonServiceDelegate;


    @Autowired
    private SimpleUserAccountServiceClient simpleUserAccountServiceClient;
    @Autowired
    private AdminApproveService adminApproveService;
    @Autowired
    private ShuidiCipher shuidiCipher;


    @Value("${diff.get-my-first-aprrove-list:false}")
    private boolean getMyFirstAprroveList;

    private final static Set<String> IMPORTANT_CITY_LIST = Sets.newHashSet("北京","上海","广州");


    @RequiresPermission("firstApprove:get-my-first-aprrove-list")
    @RequestMapping(path = "get-my-first-aprrove-list", method = RequestMethod.POST)
    @ApiOperation(value = "获取自己名下的前置审核列表", response = WorkOrderUgcBaseInfoVo.class)
    @ResultSensitiveHandler
    public Response getMyFirstAprroveList(@ApiParam("userId")int userId, @RequestParam(defaultValue = "1") int current,
                                          @RequestParam(defaultValue = "" + CrowdfundingCons.MAX_PAGE_SIZE) int pageSize,
                                          @ApiParam("渠道  默认0  线下 1")Integer channel,  @ApiParam("案例id")Integer caseId,
                                          @ApiParam("状态  -1默认  0 待处理 15 驳回  16 成功")Integer status,
                                          @RequestParam(required = false, defaultValue = "0") int operatorId,
                                          @RequestParam(required = false, defaultValue = "0") long operatorStartDateMills,
                                          @RequestParam(required = false, defaultValue = "0") long operatorEndDateMills,
                                          @RequestParam(required = false, defaultValue = "") String raiserPhoneNum) {
        log.info("SensitiveWordController getMyBaseInfoList userId:{}, current:{}, pageSize:{}, channel:{} ,status={}, raiserPhoneNum={}",
                userId, current, pageSize, channel,status, raiserPhoneNum);
        //验证时间参数
        Response dateCheck = ParamTimeRangeHandler.illegalTimeRangeForLong(operatorStartDateMills, operatorEndDateMills);
        if (dateCheck.notOk()) {
            return dateCheck;
        }
        //设置默认时间
        Pair<Long, Long> startAndEndTime = ParamTimeRangeHandler.handleGetMyFirstAprroveList(caseId, raiserPhoneNum, operatorStartDateMills, operatorEndDateMills);
        operatorStartDateMills = startAndEndTime.getLeft();
        operatorEndDateMills = startAndEndTime.getRight();

        int right = getRight(userId);

        Date operatorStartDate = null;
        Date operatorEndDate = null;
        if (operatorStartDateMills != 0) {
            operatorStartDate = new Date(operatorStartDateMills);
        }

        if (operatorEndDateMills != 0) {
            operatorEndDate = new Date(operatorEndDateMills);
        }

        long raiserId = 0;
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(raiserPhoneNum)) {
            MobileUserIdModel userIdByMobile = simpleUserAccountServiceClient.getUserIdByMobile(raiserPhoneNum);
            if (null != userIdByMobile) {
                raiserId = userIdByMobile.getUserId();
            }
        }

        Map<String, Object> result = Maps.newHashMap();

        int count = adminWorkOrderBiz.selectUnHandleCount(AdminWorkOrderConst.Type.UGC,
                ImmutableList.of(AdminWorkOrderConst.Task.FIRST_APPROVE));
        result.put("unhandleCount", count);

        List<WorkOrderFirstApprove> firstApproves = Lists.newArrayList();
        /**
         * getMyFirstAprroveList == true 开关打开
         * right ==1 代表组长权限，如果不是组长权限，一定会根据操作人id筛选，暂时这种情况不走es
         * 都满足走es。
         */
        if (getMyFirstAprroveList && right ==1) {
            Pair<Long, List<WorkOrderFirstApprove>> pair = adminWorkOrderBiz.getFirstUGCFromEs(current, pageSize, caseId,
                    channel, status, right, userId, operatorId, operatorStartDate, operatorEndDate, raiserId);
            firstApproves = pair.getRight();
            Map<String, Object> pageMap = Maps.newHashMap();
            pageMap.put("total", pair.getLeft());
            pageMap.put("current", current < 1 ? 1 : current);
            pageMap.put("pageSize", pageSize);

            result.put("pagination", pageMap);
        }else {
            firstApproves = adminWorkOrderBiz.getFirstUGC( current, pageSize, caseId, channel,status, right,userId,
                    operatorId, operatorStartDate, operatorEndDate, raiserId);

            if (CollectionUtils.isEmpty(firstApproves)) {
                result.put("pagination", PageUtil.transform2PageMap(firstApproves));
                result.put("data", null);
                return NewResponseUtil.makeSuccess(result);
            }
            result.put("pagination", PageUtil.transform2PageMap(firstApproves));
        }

        for (WorkOrderFirstApprove f : firstApproves) {
            fillInfo(f);
        }

        // 填充操作人姓名
        fillOperatorName(firstApproves);
        // 填充渠道
        adminApproveService.fillChannel(firstApproves);

        //排序
        List<WorkOrderFirstApprove> sort = firstApproves.stream().sorted(Comparator.comparing(WorkOrderFirstApprove::getChannelStr).reversed()).collect(Collectors.toList());

        result.put("data", sort);
        return NewResponseUtil.makeSuccess(result);
    }

    private void fillInfo(WorkOrderFirstApprove f) {
        int workOrderId = f.getWorkOrderId();

        /*
         * 获取工单附加信息的时候优先取表中的。
         * 如果表中没有则取最新的案例信息
         */
        FirsApproveMaterialWithAmount m = workOrderExtService.getFirstApproveWorkOrderExt(workOrderId);
        CfFirsApproveMaterial material = null;
        Integer targetAmount = null;
        if (m != null) {
            material = m.getCfFirsApproveMaterial();
            targetAmount = m.getTargetAmount();
        }
        if (targetAmount == null) {
            targetAmount = f.getTargetAmount();
        }
        // 若快照中有值 则优先使用快照中的目标金额
        f.setTargetAmount(targetAmount);

        if (material == null) {
            material = riskDelegate.getCfFirsApproveMaterialByInfoId(f.getCaseId());
        }
        injectInfo(f, material);

        CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getFundingInfoById(f.getCaseId());
        List<CrowdfundingAttachmentVo> attachmentVos = crowdfundingDelegate.getAttachmentsByType(f.getCaseId(),
                AttachmentTypeEnum.ATTACH_CF);

        com.shuidihuzhu.client.model.Response<Map<String, String>> response =  faceApiClient.caseQuery(null != crowdfundingInfo ? crowdfundingInfo.getInfoId() : "", Lists.newArrayList("cf_fqr_phone_city"));
        CfInfoExt cfInfoExt = adminCfInfoExtBiz.selectByInfoUuidFromMaster(null != crowdfundingInfo ? crowdfundingInfo.getInfoId() : "");

        log.info("raiseArea dsApiClient caseId:{},result:{}", null != crowdfundingInfo ? crowdfundingInfo.getInfoId() : "", JSON.toJSONString(response));
        String raiseArea = "";
        if(null != response && null != response.getData()){
            Map<String, String> map = (Map<String, String>) response.getData();
            if(MapUtils.isNotEmpty(map) && map.containsKey("cf_fqr_phone_city") && StringUtils.isNotBlank(map.get("cf_fqr_phone_city"))){
                raiseArea = map.get("cf_fqr_phone_city");
            }
        }

        f.setTitle(null != crowdfundingInfo ? crowdfundingInfo.getTitle() : "");
        f.setContent(null != crowdfundingInfo ? crowdfundingInfo.getContent() : "");
        f.setAttachments(CollectionUtils.isNotEmpty(attachmentVos) ? attachmentVos : Lists.newArrayList());
        f.setData(riskDelegate.getByInfoUuid(null != crowdfundingInfo ? crowdfundingInfo.getInfoId() : ""));
        f.setSensitiveArea(buildRaiseArea(cfInfoExt, raiseArea));

        // 驳回的理由 和 ugc 相关的状态
        adminApproveService.fillRejectDetail(f);
        cfFirstApproveOperatorBiz.fillUgcBaseInfoDetail(f);
    }

    /**
     * 获取风险城市
     * @param cfInfoExt
     * @param raiseArea
     * @return
     */
    private String buildRaiseArea(CfInfoExt cfInfoExt, String raiseArea){

        //是BD发起的不展示
        if(cfInfoExt != null && StringUtils.isNotBlank(cfInfoExt.getVolunteerUniqueCode()) ){
            return "";
        }

        //非北上广的不展示
        if(!IMPORTANT_CITY_LIST.contains(raiseArea)){
            return "";
        }

        return raiseArea;
    }

    private void injectInfo(WorkOrderFirstApprove f, CfFirsApproveMaterial material) {
        if (material != null) {
            f.setSelfRealName(material.getSelfRealName());
            f.setSelfIdCard(cfFirstApproveOperatorBiz.getIdcardWithWildchar(material.getSelfCryptoIdcard()));
            f.setPatientRealName(material.getPatientRealName());
            f.setPatientIdCard(cfFirstApproveOperatorBiz.getIdcardWithWildchar(material.getPatientCryptoIdcard()));
            f.setImageUrl(material.getImageUrl());
            f.setUserRelationType(material.getUserRelationType());
            f.setFirstApproveIdcardVerifyStatus(material.getStatus());
            if(f.getTargetAmount() >= minAmountDesc) {
                f.setTargetAmountDesc(material.getTargetAmountDesc());
            }
            f.setPoverty(material.getPoverty());
            f.setPovertyImageUrl(material.getPovertyImageUrl());
            f.setPatientBornCard(material.getPatientBornCard());
            f.setPatientIdType(material.getPatientIdType());
            f.setChild(cfFirstApproveOperatorBiz.isChild(material));
            f.setInfoUuid(material.getInfoUuid());
        }

        UserTagHistory userTagHistory = userTagClient.getOperatorValid(f.getCaseId());
        if (userTagHistory != null && CollectionUtils.isNotEmpty(userTagHistory.getUnitList())) {
            f.setUnits(userTagHistory.getUnitList());
        }
    }

    private void fillOperatorName(List<WorkOrderFirstApprove> firstApproves ) {
        if (CollectionUtils.isEmpty(firstApproves)) {
            return;
        }
        List<Integer> operatorIds = Lists.newArrayList();
        for (WorkOrderFirstApprove fa : firstApproves) {
            if (fa.getOperatorId() != 0) {
                operatorIds.add(fa.getOperatorId());
            }
        }

        if (CollectionUtils.isEmpty(operatorIds)) {
            return;
        }
        AuthRpcResponse<List<AdminUserAccountModel>> accountRes = seaAccountClientV1.getUserAccountsByIds(operatorIds);
        if (accountRes == null || accountRes.getCode() != 0 || CollectionUtils.isEmpty(accountRes.getResult())) {
            log.error("ugc首次审核查询 userId:{} 结果为空", operatorIds);
            return;
        }

        for (WorkOrderFirstApprove fa : firstApproves) {
            if (fa.getOperatorId() == 0) {
                continue;
            }
            for (AdminUserAccountModel model : accountRes.getResult()) {
                if (model.getId() == fa.getOperatorId()) {
                    fa.setOperateName(model.getName());
                    break;
                }
            }
        }
    }
    /**
     * 检查某userId是否拥有组长权限
     * @param userId
     * @return 1为拥有 其他为没有
     */
    private int getRight(int userId) {
        boolean b = seaUserAuthDelegate.hasPermissionSimple(userId, RoleEnum.SENSITIVE_LEADER.getPermission());
        return b ? 1 : 0;
    }


    /**
     * 如果通过，解禁转发，解禁捐款，给捐款人发消息（固定模板）
     *
     * 如果不通过，给捐款人发特定消息
     *
     * @param infoId
     * @param result
     * @return
     */
    @RequiresPermission("first-approve:approve")
    @ApiOperation("初审结果按钮")
    @RequestMapping(path = "/approve",method = RequestMethod.POST)
    public Response approve(@ApiParam("案例id，int那种")
                            @RequestParam(value = "infoId")
                            int infoId,
                            @ApiParam("审核结果  true 表示通过   false 表示驳回")
                            @RequestParam(value = "result")
                            boolean result,
                            @ApiParam("驳回时填写的理由，会发送给用户")
                            @RequestParam(value = "failMsg", required = false)
                            String failMsg,

                            @ApiParam("admin用户的userId")
                            @RequestParam(value = "userId", required = true)
                            int userId,
                            @RequestParam(value = "rejectType", required = false, defaultValue = "0")
                                        int rejectType){

        log.info("前置审核材料初审: caseId: {}, result:{}, failMsg:{}, userId:{}, rejectType:{}", infoId, result, failMsg,
                userId, rejectType);

        CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getFundingInfoById(infoId);

        if(crowdfundingInfo == null){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        int actualCode = cfFirstApproveOperatorBiz.getActualRejectCode(rejectType);

        log.info("首次审核 审核code:{}, actualCode:{}", rejectType, actualCode);
        updateApproveStatus(crowdfundingInfo, result, actualCode, failMsg);

        if(result) {
            int caseId = crowdfundingInfo.getId();
            // 必须在更新ApproveStatus状态之后 因为里面check了状态
            commonServiceDelegate.touchSetCanShareAndDonateWithAdminUser(infoId, userId);
            commonServiceDelegate.pin(caseId,
                    VisitConfigSourceEnum.FIRST_APPROVE,
                    ImmutableList.of(
                            VisitConfigLogActionInfoEnum.UNLOCK_SHARE,
                            VisitConfigLogActionInfoEnum.UNLOCK_DONATION
                    ),
                    ContextUtil.getAdminUserId());

            sendProgress(crowdfundingInfo);

            /*改相应的beginTime和endTime
             *如果案例未结束才修改时间
             *防止图文或者客服进线关闭案例后又被重新打开
             */
            Date now = new Date();
            if (crowdfundingInfo.getEndTime().after(now)){
                //默认都是开始筹款30天
                crowdfundingDelegate.updateBeginAndEndTime(infoId, now, DateUtils.addDays(now, 30));
            }

            adminCfInfoExtBiz.updateFirstApproveTime(crowdfundingInfo.getInfoId());

//            cfRepeatHandleBiz.finishRepeatCase(crowdfundingInfo);
        }

        recordOpreation(crowdfundingInfo, result, userId,failMsg);

        //如果存在ugc工单 需要关闭
        cfFirstApproveOperatorBiz.closeFirstUgc(infoId, failMsg, result, userId);

        //全部操作成功后再发送mq
        sendMqMessage(crowdfundingInfo, result);

        //审核通过或者驳回，更新用户的标签信息
        mqProdecer.sendUserLabelAlterMq(crowdfundingInfo.getId(), crowdfundingInfo.getUserId(), result ? UserLabelAlterEnum.FIRST_PPROVE : UserLabelAlterEnum.FIRST_REJECT, DelayLevel.S1);

        cfFirstApproveOperatorBiz.finishUgcBaseInfo(userId, actualCode, infoId);
        return Response.OK;
    }

    @RequiresPermission("first-approve:verify-idcard")
    @ApiOperation("校验案例的初审身份证信息")
    @RequestMapping(path = "/verify-idcard", method = RequestMethod.POST)
    public Response verifyIdCard(int infoId) {
        CfFirsApproveMaterial firsApproveMaterial = riskDelegate.getCfFirsApproveMaterialByInfoId(infoId);
        if (firsApproveMaterial == null){
            return NewResponseUtil.makeError(AdminErrorCode.CF_NO_PRE_APPROVE_MATERIAL);
        }
        if (firsApproveMaterial.getStatus() != FirstApproveIdcardVerifyStatusEnum.SYSTEM_ERROR.getCode() &&
            firsApproveMaterial.getStatus() != FirstApproveIdcardVerifyStatusEnum.NOT_MATCH.getCode()) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NO_NEED_VERIFY);
        }

        UserRelTypeEnum relTypeEnum = UserRelTypeEnum.SELF;
        if (firsApproveMaterial.getUserRelationType() == 8) {
            relTypeEnum = UserRelTypeEnum.OTHER;
        }

        String selfIdCard = "";
        if (StringUtils.isNotEmpty(firsApproveMaterial.getSelfCryptoIdcard())) {
            selfIdCard = shuidiCipher.decrypt(firsApproveMaterial.getSelfCryptoIdcard());
        }

        String patientIdCard = "";
        if (StringUtils.isNotEmpty(firsApproveMaterial.getPatientCryptoIdcard())) {
            patientIdCard = shuidiCipher.decrypt(firsApproveMaterial.getPatientCryptoIdcard());
        }

        CfErrorCode verifyIdcardResult = riskDelegate.verifyIdcard(firsApproveMaterial.getSelfRealName(),
                                                                        selfIdCard,
                                                                        firsApproveMaterial.getPatientRealName(),
                                                                        patientIdCard,
                                                                        firsApproveMaterial.getPatientIdType(),
                                                                        relTypeEnum,
                                                                        firsApproveMaterial.getUserId());

        if (verifyIdcardResult == CfErrorCode.ADD_CROWDFUNDING_VERIFY_SUCCESS_IN_WHITELIST) {
            verifyIdcardResult = CfErrorCode.SUCCESS;
        }
        if (verifyIdcardResult != CfErrorCode.SUCCESS) {
            return NewResponseUtil.makeResponse(-1, "校验失败，并且已经给用户发消息", null);
        } else {
            riskDelegate.updateStatusByCaseId(firsApproveMaterial.getInfoId(), FirstApproveIdcardVerifyStatusEnum.MATCH);
            return NewResponseUtil.makeSuccess(null, "验证成功");
        }
    }

    private void sendMqMessage(CrowdfundingInfo crowdfundingInfo, boolean result) {
        //sendmq
        if(producer == null){
            return;
        }

        String tag = result? MQTagCons.ADMIN_MQ_TAG_FIRST_APPROVE_SUCCESS: MQTagCons.ADMIN_MQ_TAG_FIRST_APPROVE_FAIL;

        //发送成功消息
        Message<CrowdfundingInfo> message = new Message(MQTopicCons.CF, tag,
                tag+ "_" + crowdfundingInfo.getId(),
                crowdfundingInfo);
        MessageResult messageResult = producer.send(message);
        log.info("first approve msg send:{}, {}", message, messageResult);
    }


    /**
     * 仅发布动态，不发送微信消息
     * @param crowdfundingInfo
     */
    private void sendProgress(CrowdfundingInfo crowdfundingInfo){

        CfFirsApproveMaterial material = riskDelegate.getCfFirsApproveMaterialByInfoId(crowdfundingInfo.getId());

        if(material == null){
            return;
        }

        CrowdFundingProgress progress = new CrowdFundingProgress();
        progress.setImageUrls(material.getImageUrl());
        progress.setContent(progressContent);
        progress.setUserId(crowdfundingInfo.getUserId());
        progress.setActivityId(crowdfundingInfo.getId());
        progress.setTitle("");
        progress.setType(CrowdFundingProgressType.PROGRESS.value());
        progressBiz.insertInCrowdfundingProgress(progress);

        if (crowdfundingInfo.getTargetAmount() > FIFTY_W) {
            progress.setImageUrls("");
            progress.setContent("筹款金额大于50万原因说明: " + material.getTargetAmountDesc());
            progressBiz.insertInCrowdfundingProgress(progress);
        }

    }

    private void updateApproveStatus(CrowdfundingInfo crowdfundingInfo, boolean result, int rejectType, String failMsg) {

        FirstApproveStatusEnum statusEnum = result? FirstApproveStatusEnum.APPLY_SUCCESS: FirstApproveStatusEnum.APPLY_FAIL;


        adminCfInfoExtBiz.updateApproveStatus(crowdfundingInfo, statusEnum);
        riskDelegate.updateRejectTypeByInfoId(crowdfundingInfo.getId(), rejectType, failMsg);

    }

    /**
     * 记录在材料审核页的操作历史记录
     * @param crowdfundingInfo
     * @param result
     * @param userId
     */
    private void recordOpreation(CrowdfundingInfo crowdfundingInfo, boolean result, int userId,String failMsg){

        String content = result? APPLY_SUCCESS_MSG : failMsg;
        CfOperationRecordEnum typeEnum = result? CfOperationRecordEnum.FIRST_APPROVE_SUCCESS: CfOperationRecordEnum.FIRST_APPROVE_FAIL;
        cfAdminOperationRecordBiz.addOneOperationRecord(crowdfundingInfo.getInfoId(), userId, typeEnum.value(), content);

        //增加UGC前置审核记录
        UserComment userComment = new UserComment(crowdfundingInfo.getId(), UserCommentSourceEnum.UGC,UserCommentSourceEnum.CommentType.UGC_4,userId,content,result?"首审通过":"首审失败","客服操作");
        commentBiz.add(userComment);
    }

    @ApiOperation("前置审核工单配置")
    @RequiresPermission("first-approve:configuration")
    @RequestMapping(value = "get-first-approve-configuration", method = RequestMethod.POST)
    public Response getFirstApproveConfiguration(int current, int pageSize, String date, @RequestParam(required = false) String operatorId) {
        int operatorIntId = null == JSON.parse(operatorId) ? 0 : (Integer) JSON.parse(operatorId);//已检查过
        FirstApproveConfiguration firstApproveConfiguration = adminWorkOrderBiz.getFirstApproveConfiguration(current, pageSize, date, operatorIntId);
        return NewResponseUtil.makeSuccess(firstApproveConfiguration);
    }

    @ApiOperation("撤回前置审核工单")
    @RequiresPermission("first-approve:cancel")
    @RequestMapping(value = "cancel-first-approves", method = RequestMethod.POST)
    public Response cancelFirstApproves(int operatorId, int count, String date) {
        //查询该人员未完成的材料审核工单
        List<AdminWorkOrder> adminWorkOrders = adminWorkOrderBiz.selectApplyingFirstApprovesByOperator(operatorId, count, date);
        if (CollectionUtils.isEmpty(adminWorkOrders)) {
            return NewResponseUtil.makeError(AdminErrorCode.NO_MORE_WORK_ORDER);
        }
        adminWorkOrderBiz.cancelWorkOrders(adminWorkOrders, ContextUtil.getAdminUserId());
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation("修改前置审核工单领取数量")
    @RequiresPermission("first-approve:update-drawing-count")
    @RequestMapping(value = "update-drawing-count", method = RequestMethod.POST)
    public Response updateFirstApprovesCount(int operatorId, int count) {
        try {
            cfFirstApproveOperatorBiz.updateFirstApprovesCount(operatorId, count);
        } catch (ServiceRuntimeException e) {
            return NewResponseUtil.makeFail(e.getErrorEnum().getDescription());
        }
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("first-approve:get-drawing-count")
    @ApiOperation("获取员工前置审核工单领取数量")
    @RequestMapping(value = "get-drawing-count", method = RequestMethod.POST)
    public Response getFirstApprovesCount() {
        return NewResponseUtil.makeSuccess(cfFirstApproveOperatorBiz.getFirstApproveOperatorById(ContextUtil.getAdminUserId()));
    }

    @RequiresPermission("first-approve:add-idcard-whiteList")
    @RequestMapping(value = "add-first-approve-idcard-whiteList", method = RequestMethod.POST)
    public Response addCfFirstApproveIdCardWhiteList(@RequestParam("name") String name,
                                                     @RequestParam("idCard") String idCard,
                                                     @RequestParam("images") String images,
                                                     @RequestParam("reason") int reason,
                                                     @RequestParam("otherReason") String otherReason) {

        if (IdCardUtil.illegal(idCard)) {
            return NewResponseUtil.makeFail("身份证过不了最基本的校验，请仔细核对身份证信息");
        }

        boolean result = cfFirstApproveOperatorBiz.addFirstApproveWhiteIdCard(name, idCard, images, reason, otherReason, ContextUtil.getAdminUserId());
        return result ? NewResponseUtil.makeSuccess("") : NewResponseUtil.makeFail("添加身份证到白名单失败，联系后端查看原因");
    }

    @RequiresPermission("first-approve:idcard-whiteList-operation-record")
    @RequestMapping(value = "idcard-whiteList-operation-record", method = RequestMethod.POST)
    public Response idCardWhiteListOperationRecord(@RequestParam("id") int id) {
        return NewResponseUtil.makeSuccess(cfFirstApproveOperatorBiz.idCardWhiteListOperationRecord(id));
    }


    @RequiresPermission("first-approve:query-first-approve-idcard-whiteList")
    @RequestMapping(value = "query-first-approve-idcard-whiteList", method = RequestMethod.POST)
    public Response queryCfFirstApproveIdCardWhiteList(@RequestParam("current") int current,
                                                       @RequestParam("pageSize") int pageSize,
                                                       @RequestParam("name") @ApiParam("姓名") String name,
                                                       @RequestParam("idCard") @ApiParam("身份证号") String idCard) {

        PageInfo<IdcardVerifyWhiteList> datePage = cfFirstApproveOperatorBiz.queryAllWhiteIdCardList(current, pageSize, name , idCard);

        Map result = new HashMap<>();
        result.put("total", datePage.getTotal());
        result.put("list", datePage.getList() == null ? Lists.newArrayList() : datePage.getList());

        return  NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("first-approve:delete-first-approve-idcard-whiteList")
    @RequestMapping(value = "delete-first-approve-idcard-whiteList", method = RequestMethod.POST)
    public Response deleteCfFirstApproveIdCardWhiteList(@RequestParam("id") int id) {

        log.info("前置审核身份证检验白名单. userId：{}, id:{}", ContextUtil.getAdminUserId(), id);
        cfFirstApproveOperatorBiz.deleteFirstApproveWhiteIdById(id);

        return  NewResponseUtil.makeSuccess("");
    }
}
