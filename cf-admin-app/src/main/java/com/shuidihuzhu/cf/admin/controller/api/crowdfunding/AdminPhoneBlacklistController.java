package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.client.cf.risk.client.CfRiskBlackListClient;
import com.shuidihuzhu.client.cf.risk.model.enums.PhoneOperateEnum;
import com.shuidihuzhu.client.cf.risk.model.enums.RiskOperateSourceEnum;
import com.shuidihuzhu.client.param.BlackListPhoneLimitParam;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/6/20 上午10:53
 * @desc
 */
@Slf4j
@Controller
@RequestMapping(path = "/admin/cf/blacklist")
public class AdminPhoneBlacklistController {

    @Resource
    private SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    private CfRiskBlackListClient cfRiskBlackListClient;

    @ResponseBody
    @ApiOperation(value = "批量添加手机黑名单", notes = "添加黑名单", response = Response.class)
    @RequestMapping(path = "/phone-batch-insert", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("blacklist:phone-batch-insert")
    public Response add(@ApiParam(value = "phoneList", required = true) String phoneList,
                        @ApiParam(value = "opSource", required = true) int opSource,
                        @ApiParam(value = "opLimitList", required = true) String opLimitList){

        int adminUserId = ContextUtil.getAdminUserId();
        log.info("batchInsertPhone request adminUserId:{},phone:{},opSource:{},opLimits:{}", adminUserId ,phoneList, opSource, opLimitList);
        if(adminUserId <= 0){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_NOT_LOGIN_ERROR);
        }

        RiskOperateSourceEnum opEnum = RiskOperateSourceEnum.parse(opSource);
        if(StringUtils.isEmpty(phoneList) || Objects.isNull(opEnum) || StringUtils.isEmpty(opLimitList)){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<String> phones = Lists.newArrayList(phoneList.split(","));
        List<String> opLimits = Lists.newArrayList(opLimitList.split(","));

        Map<Integer, Boolean> limitMap = Maps.newHashMap();
        for (String opLimit : opLimits){
            PhoneOperateEnum phoneOpEnum = PhoneOperateEnum.parse(Integer.parseInt(opLimit));//已检查过
            if(Objects.isNull(phoneOpEnum)){
                return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
            }
            limitMap.put(phoneOpEnum.getKey(), false);
        }

        AdminUserAccountModel model = seaAccountClientV1.getValidUserAccountById(adminUserId).getResult();
        String adminUserName = Objects.nonNull(model) ? model.getName() : "system";


        List<BlackListPhoneLimitParam> phoneLimitParams = Lists.newArrayList();

        for (String phone : phones){
            BlackListPhoneLimitParam param = new BlackListPhoneLimitParam();
            param.setMobilePhone(StringUtils.trim(phone));
            param.setOpSourceEnum(opEnum.getCode());
            param.setLimits(limitMap);
            param.setOperatorId(adminUserId);
            param.setOperator(adminUserName);
            phoneLimitParams.add(param);
        }

        if(CollectionUtils.isEmpty(phoneLimitParams)){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        Response response = cfRiskBlackListClient.batchInsertPhoneLimit(phoneLimitParams);

        return response;
    }
}
