package com.shuidihuzhu.cf.admin.mq.report;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.IAdminCredibleInfoService;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.enhancer.mq.BaseMessageConsumer;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.enums.crowdfunding.AddTrustAuditStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoDO;
import com.shuidihuzhu.cf.model.report.schedule.ReportPendingEntryPayload;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderTypeFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderTypeRecord;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@RocketMQListener(id = MQTagCons.REPORT_SCHEDULE_PUSH_V1,
        group = "cf-admin-" + MQTagCons.REPORT_SCHEDULE_PUSH_V1,
        tags = MQTagCons.REPORT_SCHEDULE_PUSH_V1,
        topic = MQTopicCons.CF)
@Slf4j
public class ReportPendingEntryConsumer extends BaseMessageConsumer<ReportPendingEntryPayload> implements MessageListener<ReportPendingEntryPayload> {

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private SeaAccountDelegate seaAccountDelegate;

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private IAdminCredibleInfoService credibleInfoService;

    @Autowired
    private CfWorkOrderTypeFeignClient cfWorkOrderTypeFeignClient;

    @Override
    protected boolean handle(ConsumerMessage<ReportPendingEntryPayload> consumerMessage) {
        log.info("ReportPendingEntryConsumer consumerMessage:{} ", JSON.toJSONString(consumerMessage));
        if (Objects.isNull(consumerMessage) || Objects.isNull(consumerMessage.getPayload())) {
            return true;
        }
        ReportPendingEntryPayload reportPendingEntryPayload = consumerMessage.getPayload();

        long subId = reportPendingEntryPayload.getSubId();
        int credibleType = reportPendingEntryPayload.getCredibleType();
        String time = reportPendingEntryPayload.getTime();

        CfCredibleInfoDO cfCredibleInfoDO = credibleInfoService.queryBySubId(subId, credibleType);
        if (Objects.isNull(cfCredibleInfoDO)) {
            return true;
        }

        int caseId = Objects.isNull(cfCredibleInfoDO.getCaseId()) ? 0 : cfCredibleInfoDO.getCaseId();
        int auditStatus = Objects.isNull(cfCredibleInfoDO.getAuditStatus()) ? 0 : cfCredibleInfoDO.getAuditStatus();

        if (auditStatus == AddTrustAuditStatusEnum.PASSED.getCode()) {
            return true;
        }

        Response<WorkOrderVO> response = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, WorkOrderType.REPORT_TYPES);
        WorkOrderVO workOrderVO = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);
        if (Objects.isNull(workOrderVO)) {
            return true;
        }

        sendWxNotice(workOrderVO.getCaseId(), workOrderVO.getOrderType(), workOrderVO.getHandleResult(), time, workOrderVO.getOperatorId());

        return true;
    }

    private void sendWxNotice(int caseId, int orderType, int handleResult, String time, long operatorId) {
        String temp = "【举报代录入提交提醒】\n" +
                "\n" +
                "【案例ID】：%d\n" +
                "\n" +
                "【举报工单类型】：%s\n" +
                "\n" +
                "【案例工单状态】：%s\n" +
                "\n" +
                "【举报代录入提交时间】：%s\n" +
                "\n" +
                "【最新处理人】%s";
        String orderTypeMsg = "";
        Response<WorkOrderTypeRecord> resp = cfWorkOrderTypeFeignClient.getByOrderTypeCode(orderType);
        if(resp.ok() && resp.getData() != null) {
            orderTypeMsg = resp.getData().getMsg();
        } else {
            log.error("cfWorkOrderTypeFeignClient获取工单类型失败,orderType:{}", orderType);
        }
        String orderResultMsg = Optional.ofNullable(HandleResultEnum.getFromType(handleResult)).map(HandleResultEnum::getShowMsg).orElse("");

        ArrayList<String> noticer = Lists.newArrayList();

        String misName = StringUtils.EMPTY;

        if (operatorId > 0) {
            AdminUserAccountModel adminUserAccountModel = seaAccountDelegate.getAccountModel(Math.toIntExact(operatorId));
            String misByUserId = Optional.ofNullable(adminUserAccountModel).map(AdminUserAccountModel::getMis).orElse("");
            misName = Optional.ofNullable(adminUserAccountModel).map(AdminUserAccountModel::getName).orElse("");
            noticer.add(misByUserId);
        }

        String content = String.format(temp, caseId, orderTypeMsg, orderResultMsg, time, misName);

        final String noticeStr = ConfigService.getAppConfig().getProperty("apollo.report.notice.jv_bao_dai_lu_ru_ti_jiao", "liyuanyuan");
        noticer.addAll(Lists.newArrayList(StringUtils.split(noticeStr, ",")));

        String[] noticerArr = noticer.toArray(String[]::new);
        String key;
        if (applicationService.isProduction()) {
            key = "e5f3acd4-36fb-44c1-9d42-20267bd4310a";
        } else {
            key = "a0c714a2-8449-4391-a00f-38ff672138ac";
        }
        AlarmBotService.sentText(key, content, noticerArr, null);
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
