package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import com.google.common.collect.Ordering;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.service.MobileUserIdModel;
import com.shuidihuzhu.cf.admin.util.ParamTimeRangeHandler;
import com.shuidihuzhu.cf.admin.util.admin.IntegerUtil;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderBiz;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfRepeatInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingReportBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderReportBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWrokOrderReportRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.BuildDrawStatusUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.CfReportFollowCommentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingReportRecordBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminWorkOrderReportDao;
import com.shuidihuzhu.cf.delegate.finance.impl.FinanceDelegate;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.BooleanEnum;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminCfCaseStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminWorkOrderReportConst;
import com.shuidihuzhu.cf.enums.crowdfunding.CfDrawCashConstant;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportHandleStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.NewCfRefundConstant;
import com.shuidihuzhu.cf.finance.model.vo.CfDrawCashApplyVo;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrderRecord;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCfRefund;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCrowdfundingReportChild;
import com.shuidihuzhu.cf.model.crowdfunding.AdminReportAddTrustBo;
import com.shuidihuzhu.cf.model.crowdfunding.AdminReportDataVo;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderReport;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkReportMap;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportFollowComment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReportLabel;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderDataVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingReportRecordVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.WorkOrderReportWorkStatus;
import com.shuidihuzhu.cf.service.workorder.WorkOrderPermissionService;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminWorkOrderPassRecordVo;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminWorkOrderReportVo;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.grpc.account.v1.feign.SimpleUserAccountServiceClient;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.constants.CrowdfundingCons;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by lixiaoshuang on 2018/5/14.
 */
@RefreshScope
@RestController
@Slf4j
@RequestMapping(path = "admin/cf/work/order/report")
public class AdminWorkOrderReportController {
    @Autowired
    private AdminWorkOrderReportBiz adminWorkOrderReportBiz;
    @Autowired
    private AdminWorkOrderBiz adminWorkOrderBiz;
    @Autowired
    private AdminWorkOrderRecordBiz adminWorkOrderRecordBiz;
    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;
    @Autowired
    private AdminCrowdfundingReportBiz adminCrowdfundingReportBiz;
    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Autowired
    private CrowdfundingReportRecordBiz crowdfundingReportRecordBiz;
    @Autowired
    private AdminWrokOrderReportRecordBiz adminWrokOrderReportRecordBiz;
    @Autowired
    private SimpleUserAccountServiceClient simpleUserAccountServiceClient;
    @Autowired
    private AdminCfRepeatInfoBiz repeatInfoBiz;

    @Resource
    private WorkOrderPermissionService workOrderPermissionService;

    @Autowired
    private AdminWorkOrderReportDao adminWorkOrderReportDao;

    @Autowired
    private Environment environment;

    @Autowired
    private FinanceDelegate financeDelegate;
    @Autowired
    private AdminCrowdfundingReportBiz crowdfundingReportBiz;
    @Autowired
    private CfReportFollowCommentBiz reportCommentBiz;
    @Autowired
    private CfWorkOrderClient workOrderClient;

    @Value("${diff.report-work-order-list:false}")
    private boolean reportWorkOrderList;

    @Value("${diff.report-select:false}")
    private boolean reportSelect;

    @Value("${diff.crowdfunidng-select:false}")
    private boolean crowdfunidngSelect;

    private final static String SYSTEM_TIPS = "系统创建";
    private final static String ASSIGN_TIPS = "%s领取";
    private final static String PASS_TIPS = "%s传递给%s";


    @RequiresPermission("orderReport:get-mission")
    @RequestMapping(path = "get-mission", method = RequestMethod.POST)
    @ApiOperation(value = "领取任务", notes = "")
    public Response getMission(@RequestParam(name = "count") int count,
                               @RequestParam(name = "token") String token) {
        int userId = ContextUtil.getAdminUserId();
        log.info("AdminWorkOrderReportController getMission count:{},userId:{}", count, userId);
        if (count == 0 || userId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<AdminWorkOrderReport> adminWorkOrderReportList = adminWorkOrderReportBiz.getAdminWorkOrderReportByCount(AdminWorkOrderConst.Type.CASE_REPORT.getCode(),
                AdminWorkOrderConst.Task.CASE_REPORT_DEAL.getCode(), count);
        if (CollectionUtils.isEmpty(adminWorkOrderReportList)) {
            return NewResponseUtil.makeError(AdminErrorCode.NO_MORE_WORK_ORDER);
        }
        List<Long> workOrderIdList = Lists.newArrayList();
        adminWorkOrderReportList.forEach(adminWorkOrderReport -> {
            workOrderIdList.add(new Long(adminWorkOrderReport.getWorkOrderId()));
        });
        int result_01 = adminWorkOrderBiz.updateWithOperatorIds(workOrderIdList, userId, AdminWorkOrderConst.Status.HANDLING.getCode());

        //插入工单领取时间
        if (CollectionUtils.isNotEmpty(workOrderIdList)){
            adminWorkOrderReportBiz.updateHandkeTime(workOrderIdList);
        }

        List<AdminWorkOrder> adminWorkOrders = adminWorkOrderBiz.selectByIdList(workOrderIdList);
        List<AdminWorkOrderRecord> adminWorkOrderRecordList = Lists.newArrayList();
        for (AdminWorkOrder adminWorkOrder : adminWorkOrders) {
            AdminWorkOrderRecord adminWorkOrderRecord = new AdminWorkOrderRecord(adminWorkOrder, AdminWorkOrderConst.Role.PERSON.getCode(), userId,
                    AdminWorkOrderConst.Status.HANDLING.getCode(), AdminWorkOrderConst.Result.HANDLING.getCode(), "");
            adminWorkOrderRecord.setOperateType(AdminWorkOrderConst.AdminWorkOrderOperateTypeEnum.ASSIGN__REPORT_ORDER.getCode());
            adminWorkOrderRecordList.add(adminWorkOrderRecord);
        }
        //插入工单操作记录
        int result_02 = this.adminWorkOrderRecordBiz.insertList(adminWorkOrderRecordList);
        if (result_01 > 0 && result_02 > 0) {
            return NewResponseUtil.makeResponse(0, "ok", null);
        }
        return NewResponseUtil.makeError(AdminErrorCode.OPERATION_FAILED);
    }

    @RequiresPermission("orderReport:work-order-report-personnel")
    @RequestMapping(path = "work-order-report-personnel", method = RequestMethod.POST)
    @ApiOperation(value = "获取举报工单全体人员", notes = "")
    public Response getWorkOrderReportPersonnel() {
        Set<Integer> allReportUserIds = workOrderPermissionService.getAllReportUserIds();
        if (CollectionUtils.isEmpty(allReportUserIds)) {
            return NewResponseUtil.makeError(AdminErrorCode.ROLE_NOT_EXISTS);
        }
        List<Integer> userIds = Lists.newArrayList(allReportUserIds);
        List<AdminUserAccountModel> adminUserAccountModels = seaAccountClientV1.getUserAccountsByIds(userIds).getResult();
        return NewResponseUtil.makeSuccess(adminUserAccountModels);
    }

    @RequiresPermission("orderReport:work-order-transmit")
    @RequestMapping(path = "work-order-transmit", method = RequestMethod.POST)
    @ApiOperation(value = "工单传递", notes = "")
    public Response workOrderTransmit(@RequestParam(name = "orderId") String orderId,
                                      @RequestParam(name = "realId") int realId,
                                      @RequestParam(name = "token") String token) {
        int userId = ContextUtil.getAdminUserId();
        log.info("AdminWorkOrderReportController workOrderTransmit orderId:{},realId:{},userId:{}", orderId, realId, userId);
        if (StringUtils.isEmpty(orderId) || userId <= 0 || realId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<Integer> ids = Lists.newArrayList();
        if (orderId.contains(",")) {
            String[] split = orderId.split(",");
            for (String s : split) {
                ids.add(IntegerUtil.parseInt(s));
            }
        } else {
            ids.add(IntegerUtil.parseInt(orderId));
        }
        List<AdminWorkOrderReport> adminWorkOrderReportList = adminWorkOrderReportBiz.getAdminWorkOrderReportById(ids);
        if (CollectionUtils.isEmpty(adminWorkOrderReportList)) {
            return NewResponseUtil.makeError(AdminErrorCode.WORK_ORDER_NOT_FOUND);
        }
        List<Long> workOrderIdList = Lists.newArrayList();
        for (AdminWorkOrderReport adminWorkOrderReport : adminWorkOrderReportList) {
            workOrderIdList.add(new Long(adminWorkOrderReport.getWorkOrderId()));
        }
        if (CollectionUtils.isEmpty(workOrderIdList)) {
            return NewResponseUtil.makeError(AdminErrorCode.WORK_ORDER_NOT_FOUND);
        }
        int result = adminWorkOrderBiz.updateRoleOrOperatorIdWithIdList(realId, workOrderIdList);
        //记录
        List<AdminWorkOrder> adminWorkOrders = adminWorkOrderBiz.selectByIdList(workOrderIdList);
        List<AdminWorkOrderRecord> adminWorkOrderRecordList = Lists.newArrayList();
        adminWorkOrders.forEach(adminWorkOrder -> {
            AdminWorkOrderRecord adminWorkOrderRecord = new AdminWorkOrderRecord(adminWorkOrder);
            adminWorkOrderRecord.setOperateType(AdminWorkOrderConst.AdminWorkOrderOperateTypeEnum.WORK_REPORT_ORDER_PASS.getCode());
            adminWorkOrderRecord.setOperatorId(realId);
            adminWorkOrderRecordList.add(adminWorkOrderRecord);
        });
        adminWorkOrderRecordBiz.insertList(adminWorkOrderRecordList);
        if (result > 0) {
            return NewResponseUtil.makeResponse(0, "ok", null);
        }
        return NewResponseUtil.makeError(AdminErrorCode.OPERATION_FAILED);
    }

    /**
     *
     * QA线下自动化测试用，不提供线上功能
     * QA线下自动化测试用，不提供线上功能
     * QA线下自动化测试用，不提供线上功能
     */
    @RequestMapping(path = "recover-report-work-order", method = RequestMethod.POST)
    @ApiOperation(value = "线下重置举报工单")
    public Response recoverReportWorkOrder(@RequestParam(name = "adminUserId")  int adminUserId){
        if(environment.acceptsProfiles("production")){
            return NewResponseUtil.makeFail("线上不提供重置工单功能");
        }

        if(adminUserId <= 0){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<AdminWorkOrderReportVo> reportVos = adminWorkOrderReportDao.getAdminWorkOrderReportList(adminUserId, 0, null,
                null, null, CfDrawCashConstant.DrawStatus.UNHANDLE.getCode(), 0, null, null,
                null, null, null, null);

        List<Long> workOrderIds = Lists.newArrayList();
        reportVos.forEach(reportVo -> {
            workOrderIds.add(reportVo.getWorkOrderId());
        });

        if(CollectionUtils.isEmpty(workOrderIds)){
            return NewResponseUtil.makeFail("没有更多工单");
        }

        int result = adminWorkOrderBiz.recoverStatusAndOperator(workOrderIds, AdminWorkOrderConst.Status.CREATED.getCode(), 0,
                AdminWorkOrderConst.Type.CASE_REPORT.getCode(), AdminWorkOrderConst.Task.CASE_REPORT_DEAL.getCode());

        return NewResponseUtil.makeSuccess(result > 0 ? "重置工单成功" : "重置工单失败");
    }

    @RequiresPermission("orderReport:work-order-list")
    @RequestMapping(path = "work-order-list", method = RequestMethod.POST)
    @ApiOperation(value = "工单列表", notes = "")
    public Response workOrderList(@RequestParam(name = "caseStatus") Integer caseStatus,
                                  @RequestParam(name = "addTrustStatus") Integer addTrustStatus,
                                  @RequestParam(name = "followStatus") Integer followStatus,
                                  @RequestParam(name = "token") String token,
                                  @RequestParam(required = false, defaultValue = "0") int lostContact,
                                  @RequestParam(name = "current", defaultValue = "1") int current,
                                  @RequestParam(name = "pageSize") int pageSize,
                                  @ApiParam("案例Id")
                                  @RequestParam(name = "infoId", required = false) Integer infoId,
                                  @ApiParam("工单类型") @RequestParam(name = "reprotType",required = false) Integer reprotType,
                                  @ApiParam("是否是实名举报, 1：实名举报，2：非实名举报") @RequestParam(name = "realName", required = false) Integer realName,
                                  @ApiParam("约定跟进开始时间") @RequestParam(name ="appointStartTime", required = false) String appointStartTime,
                                  @ApiParam("约定跟进结束时间") @RequestParam(name ="appointEndTime", required = false) String appointEndTime
    ) {
        int userId = ContextUtil.getAdminUserId();
        log.info("AdminWorkOrderReportController workOrderList caseStatus:{},addTrustStatus:{},followStatus:{},userId:{},current:{},pageSize:{}",
                caseStatus, addTrustStatus, followStatus, userId, current, pageSize);
        if (pageSize < CrowdfundingCons.MIN_PAGE_SIZE || pageSize > CrowdfundingCons.MAX_PAGE_SIZE) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        //判断时间范围
        Response timeCheck = ParamTimeRangeHandler.reportWorkOrderList(appointStartTime, appointEndTime);
        if (timeCheck.notOk()) {
            return timeCheck;
        }

        Map<String, Object> result = Maps.newHashMap();
        List<AdminWorkOrderReportVo> adminWorkOrderReportList = null;
        if (reportWorkOrderList) {
            Pair<Long, List<AdminWorkOrderReportVo>> pair = adminWorkOrderReportBiz.getAdminWorkOrderReportListFromEs(userId,
                    lostContact, caseStatus, addTrustStatus, followStatus, infoId, current, pageSize, reprotType, realName, appointStartTime, appointEndTime);
            adminWorkOrderReportList = pair.getRight();
            Map<String, Object> pageMap = Maps.newHashMap();
            pageMap.put("total", pair.getLeft());
            pageMap.put("current", current);
            pageMap.put("pageSize", pageSize);

            result.put("pagination", pageMap);

        }else {
            adminWorkOrderReportList = adminWorkOrderReportBiz.getAdminWorkOrderReportList(
                    userId,
                    lostContact,
                    caseStatus,
                    addTrustStatus,
                    followStatus,
                    infoId,
                    current,
                    pageSize,
                    reprotType,
                    realName,
                    appointStartTime,
                    appointEndTime);
            result.put("pagination", PageUtil.transform2PageMap(adminWorkOrderReportList));
        }

        List<Integer> caseIds = Lists.newArrayList();
        for (AdminWorkOrderReportVo adminWorkOrderReportVo : adminWorkOrderReportList) {
            int amount = adminWorkOrderReportVo.getAmount() / 100;
            adminWorkOrderReportVo.setAmount(amount);
            adminWorkOrderReportVo.setRepeatStatusList(repeatInfoBiz
                    .selectRepeatStatusByCaseId(adminWorkOrderReportVo.getCaseId()));
            caseIds.add(adminWorkOrderReportVo.getCaseId());
        }
        if (!CollectionUtils.isEmpty(adminWorkOrderReportList)) {
            this.insertOperator(adminWorkOrderReportList);
            this.insertReportNumberAndCaseStatus(adminWorkOrderReportList, caseIds);
            this.fillInfoUuidAndUserId(adminWorkOrderReportList, caseIds);
            this.insertReportComment(adminWorkOrderReportList);
            this.insertReportTime(adminWorkOrderReportList, caseIds);
            boolean getFinanceDataResult = this.insertCaseStatus(adminWorkOrderReportList, caseIds);
            if (getFinanceDataResult == false) {
                return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, null);
            }
            this.setReprotType(adminWorkOrderReportList);
            this.insertRealNameReportInfo(adminWorkOrderReportList);
            this.insertLastComment(adminWorkOrderReportList, caseIds);
        }
        int adminWorkOrderReportCount = adminWorkOrderReportBiz.getAdminWorkOrderReportCount(AdminWorkOrderConst.Type.CASE_REPORT.getCode(),
                AdminWorkOrderConst.Task.CASE_REPORT_DEAL.getCode(),
                null, null);


        result.put("list", adminWorkOrderReportList);
        result.put("dealCount", adminWorkOrderReportCount);
        return NewResponseUtil.makeSuccess(result);
    }

    @RequestMapping(path = "getReportType", method = RequestMethod.POST)
    @ApiOperation(value = "举报类型查询", notes = "")
    public Response getReportType(){

        Map<Integer, String> result = Maps.newHashMap();
        result.put(0,"全部");
        List<Map<String, String>> list = CfReportTypeEnum.show();
        list.stream().forEach(r->{
            result.put(Integer.valueOf(r.get("id")),r.get("name"));
        });
        return NewResponseUtil.makeSuccess(result);
    }

    @ApiOperation("修改约定跟进时间")
    @RequestMapping(path = "edit-appoint-time", method = RequestMethod.POST)
    public Response<Boolean> editAppointTime(@ApiParam("约定跟进时间yyyy-MM-dd HH:mm:ss") @RequestParam(name = "appointTime") String appointTime,
                                             @ApiParam("举报工单id") @RequestParam(name = "workOrderId") int workOrderId) {
        boolean editResult = adminWorkOrderReportBiz.editAppointTime(workOrderId, appointTime);
        return NewResponseUtil.makeSuccess(editResult);
    }

    @RequiresPermission("orderReport:label-risk")
    @RequestMapping(path = "label-risk", method = RequestMethod.POST)
    @ApiOperation(value = "标记高风险", notes = "")
    public Response labelRisk(@Param("id") int id, @ApiParam("是否为高风险 0：否 1：是") @Param("isHighRisk") int isHighRisk) {
        log.info("AdminWorkOrderReportController labelRisk id:{} , isHighRisk:{}", id, isHighRisk);
        if (id <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        int result = adminWorkOrderReportBiz.updateCaseRisk(id, AdminWorkOrderReportConst.CaseRisk.getByCode(isHighRisk).getCode());
        if (result > 0) {
            return NewResponseUtil.makeResponse(0, "标记成功", null);
        }
        return NewResponseUtil.makeError(AdminErrorCode.OPERATION_FAILED);
    }

    @RequiresPermission("orderReport:report-select")
    @RequestMapping(path = "report-select", method = RequestMethod.POST)
    @ApiOperation(value = "举报查询", notes = "")
    public Response reportSelect(@RequestParam(name = "addTrustStatus", required = false) Integer addTrustStatus,
                                 @RequestParam(name = "followStatus", required = false) Integer followStatus,
                                 @ApiParam("筛选筹款人是否已失联 默认未失联")
                                 @RequestParam(required = false, defaultValue = "0") int lostContact,
                                 @RequestParam(name = "caseRisk", required = false) Integer caseRisk,
                                 @RequestParam(name = "startTime") String startTime,
                                 @RequestParam(name = "endTime") String endTime,
                                 @RequestParam(name = "current", defaultValue = "1") int current,
                                 @RequestParam(name = "pageSize") int pageSize,
                                 @ApiParam("是否打款： 0 ：否 1：是")
                                 @RequestParam(name = "isDrawCash", required = false) Integer isDrawCash,
                                 @ApiParam("举报跟进人")
                                 @RequestParam(name = "reportFollowOperator") String reportFollowOperator,
                                 @ApiParam("是否是实名举报, 1：实名举报，2：非实名举报")
                                 @RequestParam(name = "realName", required = false) Integer realName) {
        log.info("AdminWorkOrderReportController reportSelect addTrustStatus:{},followStatus:{},caseRisk:{},startTime:{},endTime:{} " +
                        "isDrawCash:{} reportFollowOperator:{}",
                addTrustStatus, followStatus, caseRisk, startTime, endTime, isDrawCash, reportFollowOperator);
        if (pageSize < CrowdfundingCons.MIN_PAGE_SIZE || pageSize > CrowdfundingCons.MAX_PAGE_SIZE) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        Map<String, Object> result = Maps.newHashMap();
        List<AdminWorkOrderReportVo> adminWorkOrderReportVos = null;
        if (reportSelect) {
            Pair<Long, List<AdminWorkOrderReportVo>> pair = adminWorkOrderReportBiz.selectAdminWorkOrderReportFromEs(lostContact,
                    addTrustStatus,
                    followStatus,
                    caseRisk,
                    startTime,
                    endTime,
                    current,
                    pageSize,
                    isDrawCash,
                    reportFollowOperator,
                    realName);
            adminWorkOrderReportVos = pair.getRight();
            Map<String, Object> pageMap = Maps.newHashMap();
            pageMap.put("total", pair.getLeft());
            pageMap.put("current", current);
            pageMap.put("pageSize", pageSize);

            result.put("pagination", pageMap);
        } else {
            adminWorkOrderReportVos = adminWorkOrderReportBiz.selectAdminWorkOrderReport(
                    lostContact,
                    addTrustStatus,
                    followStatus,
                    caseRisk,
                    startTime,
                    endTime,
                    current,
                    pageSize,
                    isDrawCash,
                    reportFollowOperator,
                    realName);
            result.put("pagination", PageUtil.transform2PageMap(adminWorkOrderReportVos));
        }

        List<Integer> caseIds = Lists.newArrayList();
        for (AdminWorkOrderReportVo adminWorkOrderReportVo : adminWorkOrderReportVos) {
            int amount = adminWorkOrderReportVo.getAmount() / 100;
            adminWorkOrderReportVo.setAmount(amount);
            adminWorkOrderReportVo.setRepeatStatusList(repeatInfoBiz
                    .selectRepeatStatusByCaseId(adminWorkOrderReportVo.getCaseId()));
            caseIds.add(adminWorkOrderReportVo.getCaseId());
        }
        if (!CollectionUtils.isEmpty(adminWorkOrderReportVos)) {
            this.insertOperator(adminWorkOrderReportVos);
            this.insertReportNumberAndCaseStatus(adminWorkOrderReportVos, caseIds);
            this.fillInfoUuidAndUserId(adminWorkOrderReportVos, caseIds);
            this.insertReportComment(adminWorkOrderReportVos);
            this.insertReportTime(adminWorkOrderReportVos, caseIds);

            boolean getFinanceDataResult = this.insertCaseStatus(adminWorkOrderReportVos, caseIds);
            if (getFinanceDataResult == false) {
                return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, null);
            }

            this.setReprotType(adminWorkOrderReportVos);
            this.insertRealNameReportInfo(adminWorkOrderReportVos);
        }

        result.put("list", adminWorkOrderReportVos);
        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("orderReport:crowdfundingInfo-select")
    @RequestMapping(path = "crowdfundingInfo-select", method = RequestMethod.POST)
    @ApiOperation(value = "案例搜索查询")
    public Response crowdfundingInfoSelect(@RequestParam(name = "title", defaultValue = "") String title,
                                           @RequestParam(name = "mobile", defaultValue = "") String mobile,
                                           @RequestParam(name = "caseId", defaultValue = "0") Integer caseId,
                                           @RequestParam(name = "name", defaultValue = "") String name,
                                           @RequestParam(name = "realId", defaultValue = "0") Integer realId,
                                           @RequestParam(name = "current", defaultValue = "1") int current,
                                           @RequestParam(name = "pageSize", defaultValue = "10") int pageSize) {
        log.info("AdminWorkOrderReportController crowdfundingInfoSelect title:{},mobile:{},caseId:{},name:{},realId:{}",
                title, mobile, caseId, name, realId);
        if (!StringUtils.isAllBlank(title, mobile, name)) {
            return NewResponseUtil.makeFail("参数已不可用，请使用案例id查询");
        }
        if (pageSize < CrowdfundingCons.MIN_PAGE_SIZE || pageSize > CrowdfundingCons.MAX_PAGE_SIZE) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        if (StringUtils.isBlank(title) && StringUtils.isBlank(mobile) && StringUtils.isBlank(name) && (caseId == null || caseId <= 0) && (realId == null || realId <= 0)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_IS_NULL);
        }

        long realIdLong = 0;
        if (realId == null || realId <= 0) {
            long userId = 0;
            if (StringUtils.isNotBlank(mobile)) {
                //将手机号转为userId的筛选条件
                MobileUserIdModel userIdByMobile = simpleUserAccountServiceClient.getUserIdByMobile(mobile);
                if (null != userIdByMobile) {
                    userId = userIdByMobile.getUserId();
                } else {
                    return NewResponseUtil.makeError(AdminErrorCode.USER_MOBILE_NOT_EXISTS);
                }
            }
            if (userId != 0) {
                realIdLong = userId;
            }
        } else {
            realIdLong = realId;
        }

        Map<String, Object> result = Maps.newHashMap();
        List<AdminWorkOrderReportVo> crowdfundingReportInfo = null;
        if (crowdfunidngSelect) {
            Pair<Long, List<AdminWorkOrderReportVo>> pair = adminWorkOrderReportBiz.getSelectAdminWorkOrderReportEs(
                    title,
                    caseId,
                    realIdLong,
                    name,
                    current,
                    pageSize);
            crowdfundingReportInfo = pair.getRight();
            Map<String, Object> pageMap = Maps.newHashMap();
            pageMap.put("total", pair.getLeft());
            pageMap.put("current", current);
            pageMap.put("pageSize", pageSize);
            result.put("pagination", pageMap);
        } else {
            crowdfundingReportInfo = adminCrowdfundingInfoBiz.getCrowdfundingReportInfo(
                    title,
                    caseId,
                    realIdLong,
                    name,
                    current,
                    pageSize);
            result.put("pagination", PageUtil.transform2PageMap(crowdfundingReportInfo));
        }

        if (!CollectionUtils.isEmpty(crowdfundingReportInfo)) {
            List<Integer> caseIds = Lists.newArrayList();
            crowdfundingReportInfo.forEach(adminWorkOrderReportVo -> {
                int amount = adminWorkOrderReportVo.getAmount() / 100;
                adminWorkOrderReportVo.setAmount(amount);
                adminWorkOrderReportVo.setRepeatStatusList(repeatInfoBiz
                        .selectRepeatStatusByCaseId(adminWorkOrderReportVo.getCaseId()));
                caseIds.add(adminWorkOrderReportVo.getCaseId());
            });
            this.fillInfoUuidAndUserId(crowdfundingReportInfo, caseIds);

            boolean getFinanceDataResult = this.insertCaseStatus(crowdfundingReportInfo, caseIds);
            if (getFinanceDataResult == false) {
                return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, null);
            }

            this.insertReportData(crowdfundingReportInfo, caseIds);
            this.setReprotType(crowdfundingReportInfo);
            this.insertRealNameReportInfo(crowdfundingReportInfo);
        }
        result.put("list", crowdfundingReportInfo);
        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("orderReport:data-statistical")
    @RequestMapping(path = "new-data-stat", method = RequestMethod.POST)
    @ApiOperation(value = "举报管理", notes = "")
    public Response newDataStat(@ApiParam("日期 格式yyyy-MM-dd") @RequestParam(name = "date") String date) {

        Date d = null;
        try {
            d = DateUtils.parseDate(date,"yyyy-MM-dd");
        } catch (ParseException e) {
            NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        Date yesterday = d;
        Date today = DateUtils.addDays(d,1);

        //计算人员的工作情况
        Set<Integer> allReportUserIds = workOrderPermissionService.getAllReportUserIds();

        Map<String, Object> map = adminWorkOrderReportBiz.getReportWorkStat(today,yesterday, allReportUserIds);

        return NewResponseUtil.makeSuccess(map);
    }

    @RequiresPermission("orderReport:data-statistical")
    @RequestMapping(path = "data-statistical", method = RequestMethod.POST)
    @ApiOperation(value = "举报管理", notes = "")
    @Deprecated
    public Response dataStatistical(@RequestParam(name = "date") String date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date parse = null;
        try {
            parse = simpleDateFormat.parse(date);
        } catch (ParseException e) {
            log.info("AdminWorkOrderReportController date parse error", e);
        }
        String format = simpleDateFormat.format(parse);
        String endTime = format + " 19:00:00";
        Date beforeDate = DateUtil.getPastdayDate(parse, 1);
        String formateBeforeDate = simpleDateFormat.format(beforeDate);
        String startTime = formateBeforeDate + " 19:00:00";
        //待处理
        int dealCount = adminWorkOrderReportBiz.getAdminWorkOrderReportCount(AdminWorkOrderConst.Type.CASE_REPORT.getCode(), AdminWorkOrderConst.Task.CASE_REPORT_DEAL.getCode(),
                startTime, endTime);
        //已领取
        int getCount = adminWorkOrderReportBiz.getCount(AdminWorkOrderConst.Type.CASE_REPORT.getCode(), AdminWorkOrderConst.Task.CASE_REPORT_DEAL.getCode(),
                startTime, endTime);
        //不需要处理
        int nodealCount = adminWorkOrderReportBiz.getDealCount(AdminWorkOrderConst.Type.CASE_REPORT.getCode(), AdminWorkOrderConst.Task.CASE_REPORT_DEAL.getCode(),
                startTime, endTime, AdminWorkOrderReportConst.DealResult.NO_DEAL.getCode());
        //处理完成
        int dealCompleteCount = adminWorkOrderReportBiz.getDealCount(AdminWorkOrderConst.Type.CASE_REPORT.getCode(), AdminWorkOrderConst.Task.CASE_REPORT_DEAL.getCode(),
                startTime, endTime, AdminWorkOrderReportConst.DealResult.DEAL_COMPLETE.getCode());
        //新增跟进
        int newFollowCount = adminWorkOrderReportBiz.getDealCount(AdminWorkOrderConst.Type.CASE_REPORT.getCode(), AdminWorkOrderConst.Task.CASE_REPORT_DEAL.getCode(),
                startTime, endTime, AdminWorkOrderReportConst.DealResult.REPORT_FOLLOW.getCode());
        //遗留跟进
        int oldFollowCount = adminWorkOrderReportBiz.getOldFollowCount(AdminWorkOrderConst.Type.CASE_REPORT.getCode(), AdminWorkOrderConst.Task.CASE_REPORT_DEAL.getCode(),
                startTime, AdminWorkOrderReportConst.DealResult.REPORT_FOLLOW.getCode());
        //今日有跟进动作
        int nowFollowCount = adminWrokOrderReportRecordBiz.getFollowCountByTime(startTime, endTime);
        //今日遗留跟进
        int nowOldFollowCount = newFollowCount + oldFollowCount;
        //完成率
        String ompleteRate = "0.00";
        DecimalFormat df = new DecimalFormat("0.00");
        if (nowFollowCount != 0) {
            ompleteRate = df.format((float) dealCompleteCount / newFollowCount);
        }
        //计算人员的工作情况
        Set<Integer> allReportUserids = workOrderPermissionService.getAllReportUserIds();

        List<WorkOrderReportWorkStatus> workOrderReportWorkStatusList = this.CountData(endTime, startTime, Lists.newArrayList(allReportUserids));
        Map<String, Object> map = Maps.newHashMap();
        map.put("dealCount", dealCount);
        map.put("getCount", getCount);
        map.put("nodealCount", nodealCount);
        map.put("dealCompleteCount", dealCompleteCount);
        map.put("newFollowCount", newFollowCount);
        map.put("oldFollowCount", oldFollowCount);
        map.put("nowFollowCount", nowFollowCount);
        map.put("nowOldFollowCount", nowOldFollowCount);
        map.put("ompleteRate", ompleteRate);
        map.put("list", workOrderReportWorkStatusList);
        return NewResponseUtil.makeSuccess(map);
    }

    //写入举报时间
    private void insertReportTime(List<AdminWorkOrderReportVo> adminWorkOrderReportList, List<Integer> caseIds) {
        List<CrowdfundingReport> firstReportList = adminCrowdfundingReportBiz.getFirstCreateTimeByInfoIds(caseIds);
        List<CrowdfundingReport> lastReportList = adminCrowdfundingReportBiz.getLastCreateTimeByInfoIds(caseIds);
        for (AdminWorkOrderReportVo adminWorkOrderReportVo : adminWorkOrderReportList) {
            if (!CollectionUtils.isEmpty(firstReportList)) {
                for (CrowdfundingReport crowdfundingReport : firstReportList) {
                    if (adminWorkOrderReportVo.getCaseId() == crowdfundingReport.getActivityId()) {
                        adminWorkOrderReportVo.setFirstReportTime(crowdfundingReport.getCreateTime());
                    }
                }
            }
            if (!CollectionUtils.isEmpty(lastReportList)) {
                for (CrowdfundingReport crowdfundingReport : lastReportList) {
                    if (adminWorkOrderReportVo.getCaseId() == crowdfundingReport.getActivityId()) {
                        adminWorkOrderReportVo.setLastReportTime(crowdfundingReport.getCreateTime());
                    }
                }
            }
        }
    }


    /**
     * 1，从crowdfunding_report表中查找用户提交的已处理、或者不需要处理（dealstatus==(3||2)）的最近的一条
     * 2，查找出对应的cf_report_record表中的comment，赋值上去
     *
     * @param adminWorkOrderReportList
     */
    private void insertReportComment(List<AdminWorkOrderReportVo> adminWorkOrderReportList) {

        if(CollectionUtils.isEmpty(adminWorkOrderReportList)){
            return;
        }

        List<Integer> caseIds = Lists.newArrayList();
        adminWorkOrderReportList.forEach(adminWorkOrderReportVo -> {
            caseIds.add(adminWorkOrderReportVo.getReportId());
        });

        List<Integer> reportIds = getLastestReportByCaseIds(caseIds);

        if(CollectionUtils.isEmpty(reportIds)){
            return;
        }

        List<CrowdfundingReportRecordVo> reportRecordGroupByReportId = crowdfundingReportRecordBiz.getReportRecordGroupByReportId(reportIds);
        if (CollectionUtils.isEmpty(reportRecordGroupByReportId)) {
            return;
        }
        for (AdminWorkOrderReportVo adminWorkOrderReportVo : adminWorkOrderReportList) {
            for (CrowdfundingReportRecordVo crowdfundingReportRecordVo : reportRecordGroupByReportId) {
                if (adminWorkOrderReportVo.getCaseId() == crowdfundingReportRecordVo.getActivityId()) {
                    adminWorkOrderReportVo.setReportRemark(crowdfundingReportRecordVo.getComment());
                }
            }
        }
    }

    /**
     * 根据案例获取他最近一条reportId
     * 1，选取案例id的所有report
     * 2，根据dealStatus来判断，只取有运营处理过的，（已处理完|不需要跟进）
     * 3，根据时间倒序排列，取最新的一条id
     * 4，组成list返回
     * @param caseIds 案例id
     * @return 举报id
     */
    private List<Integer> getLastestReportByCaseIds(List<Integer> caseIds) {
        List<CrowdfundingReport> reports = adminCrowdfundingReportBiz.getByInfoIds(caseIds);

        //key: caseId， value: List<CrowdfundingReport>
        Multimap<Integer, CrowdfundingReport> multimap = ArrayListMultimap.create();

        for(CrowdfundingReport crowdfundingReport : reports){
            //如果是还没处理完毕的，先不用显示备注了
            if(crowdfundingReport.getDealStatus() == AdminWorkOrderReportConst.DealResult.REPORT_FOLLOW.getCode() ||
                    crowdfundingReport.getDealStatus() == AdminWorkOrderReportConst.DealResult.DEFAULT.getCode()){
                continue;
            }
            multimap.put(crowdfundingReport.getActivityId(), crowdfundingReport);
        }

        List<Integer> reportIds = Lists.newArrayList();

        for(int caseId : multimap.keySet()){
            //根据createTime倒序排列后，取第一个report
            CrowdfundingReport report = multimap.get(caseId).stream().sorted(Comparator.comparing(CrowdfundingReport::getCreateTime).reversed()).findFirst().get();
            reportIds.add(report.getId());
        }
        return reportIds;
    }

    //写入举报次数
    private void insertReportNumberAndCaseStatus(List<AdminWorkOrderReportVo> adminWorkOrderReportList, List<Integer> caseIds) {
        List<AdminCrowdfundingReportChild> caseReportCount = adminCrowdfundingReportBiz.getCaseReportCount(caseIds);
        if (CollectionUtils.isEmpty(caseReportCount)) {
            return;
        }
        for (AdminWorkOrderReportVo adminWorkOrderReportVo : adminWorkOrderReportList) {
            for (AdminCrowdfundingReportChild adminCrowdfundingReportChild : caseReportCount) {
                if (adminWorkOrderReportVo.getCaseId() == adminCrowdfundingReportChild.getActivityId()) {
                    adminWorkOrderReportVo.setReportNumber(adminCrowdfundingReportChild.getCount());
                }
            }
        }
    }

    //写入操作人名称
    private void insertOperator(List<AdminWorkOrderReportVo> adminWorkOrderReportList) {
        List<Integer> operatorIds = Lists.newArrayList();
        adminWorkOrderReportList.forEach(adminWorkOrderReportVo -> {
            operatorIds.add(adminWorkOrderReportVo.getOperatorId());
        });
        if (CollectionUtils.isEmpty(operatorIds)) {
            return;
        }
        List<AdminUserAccountModel> adminUserAccountModels = seaAccountClientV1.getUserAccountsByIds(operatorIds).getResult();
        if (CollectionUtils.isEmpty(adminUserAccountModels)) {
            return;
        }
        adminWorkOrderReportList.forEach(adminWorkOrderReportVo -> {
            adminUserAccountModels.forEach(adminUserAccountModel -> {
                if (adminWorkOrderReportVo.getOperatorId() == adminUserAccountModel.getId()) {
                    adminWorkOrderReportVo.setFollowPeople(adminUserAccountModel.getName());
                }
            });
        });
    }

    private void insertRealNameReportInfo(List<AdminWorkOrderReportVo> adminWorkOrderReportList){
        for (AdminWorkOrderReportVo reportVo : adminWorkOrderReportList){
            List<AdminWorkReportMap> reportMaps = adminWorkOrderReportDao.getWorkReportMapList(Lists.newArrayList(reportVo.getWorkOrderId()));
            if(CollectionUtils.isEmpty(reportMaps)){
                return;
            }

            List<Integer> reportIds = Lists.newArrayList();
            for (AdminWorkReportMap reportMap : reportMaps){
                reportIds.add(Integer.valueOf(String.valueOf(reportMap.getReportId())));
            }

            List<CrowdfundingReport> reportList = crowdfundingReportBiz.getListByReportIds(reportIds);

            boolean realNameReport = false;
            for (CrowdfundingReport report : reportList){
                if(BooleanEnum.TRUE_FLAG.getValue() == report.getRealNameReport()){
                    realNameReport = true;
                    break;
                }
            }
            reportVo.setRealNameReport(realNameReport);
        }
    }

    //最新一条备注内容
    private void insertLastComment(List<AdminWorkOrderReportVo> reportVoList, List<Integer> caseIds) {
        if (CollectionUtils.isEmpty(caseIds) || CollectionUtils.isEmpty(reportVoList)){
            return;
        }
        List<CfReportFollowComment> commentList = reportCommentBiz.getCommentListByInfoIds(caseIds);
        //get last comment info
        Ordering<CfReportFollowComment> orderByTime = Ordering.natural().reverse().onResultOf(CfReportFollowComment::getCreateTime);
        Map<Integer, String> caseIdTLastComment = commentList.stream().sorted(orderByTime)
                .collect(Collectors.toMap(CfReportFollowComment::getInfoId, CfReportFollowComment::getComment, (before, after) -> before));

        reportVoList.forEach(item -> {
            String lastComment = caseIdTLastComment.getOrDefault(item.getCaseId(), "");
            item.setLastComment(lastComment);
        });

    }

    //写入案例状态
    private boolean insertCaseStatus(List<AdminWorkOrderReportVo> crowdfundingReportInfo, List<Integer> caseIds) {
        //获取筹款集合
        List<CrowdfundingInfo> crowdfundingInfoList = adminCrowdfundingInfoBiz.getListByIds(caseIds);
        List<String> infoUuids = Lists.newArrayList();
        crowdfundingInfoList.forEach(crowdfundingInfo -> {
            infoUuids.add(crowdfundingInfo.getInfoId());
        });
        //获取提现记录集合
        Response<Map<Integer, CfDrawCashApplyVo>>cfDrawCashesResponse = financeDelegate.getApplyInfoMap(caseIds);
        Map<Integer, CfDrawCashApplyVo> drawCashApplyVoMap = cfDrawCashesResponse.getData();
        if (null == drawCashApplyVoMap) {
            drawCashApplyVoMap = Maps.newHashMap();
        }
        //获取退款记录集合
        Response<List<AdminCfRefund>> cfRefundsResponse = financeDelegate.getRefundByInfoUuids(infoUuids);
        if (cfDrawCashesResponse.notOk() || cfRefundsResponse.notOk()) {
            return false;
        }

        //写入案例状态
        for (AdminWorkOrderReportVo adminWorkOrderReportVo : crowdfundingReportInfo) {
            for (CrowdfundingInfo crowdfundingInfo : crowdfundingInfoList) {
                if (adminWorkOrderReportVo.getCaseId() == crowdfundingInfo.getId()) {
                    if (crowdfundingInfo.getStatus().value() != 2) {
                        adminWorkOrderReportVo.setCaseStatus(AdminCfCaseStatus.APPROVE_NO.getValue());
                    } else {
                        adminWorkOrderReportVo.setCaseStatus(AdminCfCaseStatus.APPROVE_FINISH.getValue());
                        this.setCaseDrawStatus(adminWorkOrderReportVo, drawCashApplyVoMap.get(crowdfundingInfo.getId()));
                        this.setCaseRefundStatus(adminWorkOrderReportVo, cfRefundsResponse.getData(), crowdfundingInfo.getInfoId());
                    }
                }
            }
        }
        return true;
    }

    private void setReprotType(List<AdminWorkOrderReportVo> list){
        List<Long> workIds = list.stream().map(AdminWorkOrderReportVo::getWorkOrderId).collect(Collectors.toList());
        List<AdminWorkReportMap> workReportMapList = adminWorkOrderReportDao.getWorkReportMapList(workIds);
        //reportId-workId
        Map<Integer, Long> reportIdTWorkId = workReportMapList.stream().collect(Collectors.toMap(item -> Math.toIntExact(item.getReportId()), AdminWorkReportMap::getWorkOrderId, (before, after) -> before));//已检查过
        List<Integer> reportIds = Lists.newArrayList(reportIdTWorkId.keySet());
        List<CrowdfundingReportLabel> labels = adminCrowdfundingReportBiz.getReportLabels(reportIds);
        //report 对应的 label
        Map<Long, List<String>> map = labels.stream().collect(Collectors.groupingBy(CrowdfundingReportLabel::getReportId, Collectors.mapping(r -> CfReportTypeEnum.getDescFromCode(r.getReportLabel()), Collectors.toList())));
        //workId 对应的 label
        Map<Long, Set<String>> workIdTLabel = Maps.newHashMap();
        reportIdTWorkId.keySet().forEach(item -> {
            List<String> oneReportLabels = map.getOrDefault(item.longValue(), Lists.newArrayList());
            Long workId = reportIdTWorkId.get(item);
            Set<String> oneWorkLabels = workIdTLabel.getOrDefault(workId, Sets.newHashSet());
            oneWorkLabels.addAll(oneReportLabels);
            workIdTLabel.put(workId, oneWorkLabels);
        });

        for (AdminWorkOrderReportVo reportVo : list) {
            Set<String> labelSet = workIdTLabel.get(reportVo.getWorkOrderId());
            if (CollectionUtils.isNotEmpty(labelSet)) {
                List<String> reportTypes = Lists.newArrayList(labelSet);
                reportVo.setReportTypes(reportTypes);
            }
        }
    }

    //写入案例体现状态
    private void setCaseDrawStatus(AdminWorkOrderReportVo reportCaseVo, CfDrawCashApplyVo cfDrawCashApplyVo) {
        if (null == cfDrawCashApplyVo) {
            return;
        }
        BuildDrawStatusUtil.buildDrawStatus(reportCaseVo, cfDrawCashApplyVo);
    }

    //写入案例退款状态
    private void setCaseRefundStatus(AdminWorkOrderReportVo reportCaseVo,
                                     List<AdminCfRefund> cfRefunds, String infoUuid) {
        if (CollectionUtils.isEmpty(cfRefunds)) {
            return;
        }
        for (AdminCfRefund cfRefund : cfRefunds) {
            if (cfRefund.getInfoUuid().equals(infoUuid)) {
                if (cfRefund.getApplyStatus() > NewCfRefundConstant.ApplyStatus.UNSUBMIT.getCode()) {
                    //7.已申请退款
                    reportCaseVo.setCaseStatus(AdminCfCaseStatus.REFUND_SUBMIT.getValue());
                }
            }
        }
    }

    private void insertReportData(List<AdminWorkOrderReportVo> crowdfundingReportInfo, List<Integer> caseIds) {
        if (CollectionUtils.isEmpty(caseIds)) {
            return;
        }
        List<AdminWorkOrderReport> adminWorkOrderReportList = adminWorkOrderReportBiz.selectWorkOrderReportByCaseIds(caseIds);
        List<Long> workIds = crowdfundingReportInfo.stream().map(AdminWorkOrderReportVo::getWorkOrderId).filter(id -> id > 0).collect(Collectors.toList());
        List<AdminWorkOrder> adminWorkOrders = adminWorkOrderBiz.selectByIdList(workIds);
        for (AdminWorkOrderReportVo adminWorkOrderReportVo : crowdfundingReportInfo) {
            for (AdminWorkOrderReport adminWorkOrderReport : adminWorkOrderReportList) {
                if (adminWorkOrderReportVo.getCaseId() == adminWorkOrderReport.getCaseId()) {
                    //案例有举报，写入风险，处理状态，操作时间
                    adminWorkOrderReportVo.setCaseRisk(adminWorkOrderReport.getCaseRisk());
                }
            }
            for (AdminWorkOrder adminWorkOrder : adminWorkOrders) {
                if (adminWorkOrder.getId() == adminWorkOrderReportVo.getWorkOrderId()) {
                    adminWorkOrderReportVo.setLastOperationTime(adminWorkOrder.getUpdateTime());
                }
            }
            this.formNewOperationTime(adminWorkOrderReportVo);
            //新老举报兼容状态
            int reportDealResult = this.getReportDealResult(adminWorkOrderReportVo);
            adminWorkOrderReportVo.setDealResult(reportDealResult);
        }
        this.insertCrowdfundingInfoSelectOperatorName(crowdfundingReportInfo, caseIds);
        this.insertIsNewReport(crowdfundingReportInfo, caseIds);
        this.insertCrowdfundingSelectAddTrust(crowdfundingReportInfo, caseIds);
        this.insertReportNumberAndCaseStatus(crowdfundingReportInfo, caseIds);
        this.insertReportComment(crowdfundingReportInfo);
        this.insertReportTime(crowdfundingReportInfo, caseIds);
    }

    /**
     * 兼容新举报系统中工单操作时间
     *
     * @param adminWorkOrderReportVo
     */
    private void formNewOperationTime(AdminWorkOrderReportVo adminWorkOrderReportVo) {
        try {
            Response<WorkOrderVO> workOrderVOResponse = workOrderClient.getLastWorkOrderByTypes(adminWorkOrderReportVo.getCaseId(), WorkOrderType.REPORT_TYPES);
            if (workOrderVOResponse.ok() && workOrderVOResponse.getData() != null) {
                WorkOrderVO workOrderVO = workOrderVOResponse.getData();
                if (adminWorkOrderReportVo.getLastOperationTime() != null
                        && workOrderVO.getUpdateTime().getTime() > adminWorkOrderReportVo.getLastOperationTime().getTime()){
                        adminWorkOrderReportVo.setLastOperationTime(workOrderVO.getUpdateTime());
                }else {
                    adminWorkOrderReportVo.setLastOperationTime(workOrderVO.getUpdateTime());
                }
            }
        } catch (Exception e) {
            log.error("get new report operation time error", e);
        }
    }

    /**
     * 兼容新老举报工单的处理状态
     *
     * @param adminWorkOrderReportVo
     * @return
     */
    private int getReportDealResult(AdminWorkOrderReportVo adminWorkOrderReportVo) {
        List<CrowdfundingReport> crowdfundingReports = crowdfundingReportBiz.getListByInfoId(adminWorkOrderReportVo.getCaseId());
        //举报未处理
        boolean oldUnDoingStatusMatch = crowdfundingReports.stream()
                .anyMatch(crowdfundingReport -> crowdfundingReport.getDealStatus() == AdminWorkOrderReportConst.DealResult.DEFAULT.getCode());
        boolean newUnDoingStatusMatch = crowdfundingReports.stream()
                .anyMatch(crowdfundingReport -> crowdfundingReport.getHandleStatus() == CfReportHandleStatus.NO_HANDLE.getKey());
        if (oldUnDoingStatusMatch || newUnDoingStatusMatch) {
            return AdminWorkOrderReportConst.DealResult.DEFAULT.getCode();
        }
        //举报跟进中
        boolean oldFollowStatusMatch = crowdfundingReports.stream()
                .anyMatch(crowdfundingReport -> crowdfundingReport.getDealStatus() == AdminWorkOrderReportConst.DealResult.REPORT_FOLLOW.getCode());
        if (oldFollowStatusMatch) {
            return AdminWorkOrderReportConst.DealResult.REPORT_FOLLOW.getCode();
        }
        //处理完成
        boolean oldDefaultStatusMatch = crowdfundingReports.stream()
                .allMatch(crowdfundingReport -> crowdfundingReport.getDealStatus() != AdminWorkOrderReportConst.DealResult.DEFAULT.getCode());
        boolean followStatusMatch = crowdfundingReports.stream()
                .allMatch(crowdfundingReport -> crowdfundingReport.getDealStatus() != AdminWorkOrderReportConst.DealResult.REPORT_FOLLOW.getCode());
        boolean oldCompleteStatusMatch = crowdfundingReports.stream()
                .anyMatch(crowdfundingReport -> crowdfundingReport.getDealStatus() == AdminWorkOrderReportConst.DealResult.DEAL_COMPLETE.getCode());
        boolean newCompleteStatusMatch = crowdfundingReports.stream()
                .allMatch(crowdfundingReport -> crowdfundingReport.getHandleStatus() == CfReportHandleStatus.HANDLED.getKey());

        if (oldDefaultStatusMatch || followStatusMatch && oldCompleteStatusMatch && newCompleteStatusMatch) {
            return AdminWorkOrderReportConst.DealResult.DEAL_COMPLETE.getCode();
        }
        //无需处理
        boolean oldNodealStatusMatch = crowdfundingReports.stream()
                .anyMatch(crowdfundingReport -> crowdfundingReport.getDealStatus() == AdminWorkOrderReportConst.DealResult.NO_DEAL.getCode());
        if (oldNodealStatusMatch) {
            return AdminWorkOrderReportConst.DealResult.NO_DEAL.getCode();
        }
        return AdminWorkOrderReportConst.DealResult.DEFAULT.getCode();
    }

    //写入是否新增举报
    private void insertIsNewReport(List<AdminWorkOrderReportVo> crowdfundingReportInfo, List<Integer> reportCaseIds) {
        List<CrowdfundingReport> crowdfundingReports = adminCrowdfundingReportBiz.getByInfoIds(reportCaseIds);
        for (AdminWorkOrderReportVo adminWorkOrderReportVo : crowdfundingReportInfo) {
            for (CrowdfundingReport crowdfundingReport : crowdfundingReports) {
                if (adminWorkOrderReportVo.getCaseId() == crowdfundingReport.getActivityId()) {
                    adminWorkOrderReportVo.setIsNewReport(crowdfundingReport.getIsNewreport());
                }
            }
        }
    }

    //写入案例查询增信状态
    private void insertCrowdfundingSelectAddTrust(List<AdminWorkOrderReportVo> crowdfundingReportInfo, List<Integer> reportCaseIds) {
        List<CrowdfundingInfo> crowdfundingInfos = adminCrowdfundingInfoBiz.getListByIds(reportCaseIds);
        List<String> infoIdList = Lists.newArrayList();
        crowdfundingInfos.forEach(crowdfundingInfo -> {
            infoIdList.add(crowdfundingInfo.getInfoId());
        });
        List<AdminReportAddTrustBo> adminReportAddTrustBos = adminCrowdfundingInfoBiz.selectCaseAuditStatusByCaseIds(infoIdList);
        for (AdminWorkOrderReportVo adminWorkOrderReportVo : crowdfundingReportInfo) {
            for (AdminReportAddTrustBo adminReportAddTrustBo : adminReportAddTrustBos) {
                if (adminWorkOrderReportVo.getCaseId() == adminReportAddTrustBo.getCaseId()) {
                    adminWorkOrderReportVo.setAddTrustAuditStatus(adminReportAddTrustBo.getAuditStatus());
                }
            }
        }
    }

    //写入案例查询操作人名称
    private void insertCrowdfundingInfoSelectOperatorName(List<AdminWorkOrderReportVo> crowdfundingReportInfo, List<Integer> reportCaseIds) {
        List<AdminWorkOrderReportVo> workOrderOperatorIdByCaseIds = adminWorkOrderReportBiz.getWorkOrderOperatorIdByCaseIds(reportCaseIds);
        if (CollectionUtils.isEmpty(workOrderOperatorIdByCaseIds)) {
            return;
        }
        crowdfundingReportInfo.forEach(adminWorkOrderReportVo -> {
            workOrderOperatorIdByCaseIds.forEach(adminWorkOrderReport -> {
                if (adminWorkOrderReportVo.getCaseId() == adminWorkOrderReport.getCaseId()) {
                    adminWorkOrderReportVo.setOperatorId(adminWorkOrderReport.getOperatorId());
                }
            });
        });
        List<Integer> operatorIds = Lists.newArrayList();
        workOrderOperatorIdByCaseIds.forEach(adminWorkOrderReportVo -> {
            operatorIds.add(adminWorkOrderReportVo.getOperatorId());
        });
        List<AdminUserAccountModel> adminUserAccountModels = seaAccountClientV1.getUserAccountsByIds(operatorIds).getResult();
        if (CollectionUtils.isEmpty(adminUserAccountModels)) {
            return;
        }
        crowdfundingReportInfo.forEach(adminWorkOrderReportVo -> {
            adminUserAccountModels.forEach(adminUserAccountModel -> {
                if (adminWorkOrderReportVo.getOperatorId() == adminUserAccountModel.getId()) {
                    adminWorkOrderReportVo.setFollowPeople(adminUserAccountModel.getName());
                }
            });
        });
        return;
    }

    private List<WorkOrderReportWorkStatus> CountData(String endTime, String startTime, List<Integer> userIds) {
        List<AdminUserAccountModel> adminUserAccountModels = seaAccountClientV1.getUserAccountsByIds(userIds).getResult();
        List<WorkOrderReportWorkStatus> workOrderReportWorkStatusList = Lists.newArrayList();
        for (AdminUserAccountModel adminUserAccountModel : adminUserAccountModels) {
            WorkOrderReportWorkStatus workOrderReportWorkStatus = new WorkOrderReportWorkStatus();
            workOrderReportWorkStatus.setUserId(adminUserAccountModel.getId());
            workOrderReportWorkStatus.setName(adminUserAccountModel.getName());
            workOrderReportWorkStatusList.add(workOrderReportWorkStatus);
        }
        //每个人已领取
        List<AdminWorkOrderDataVo> countByUserIds = adminWorkOrderReportBiz.getCountByUserIds(AdminWorkOrderConst.Type.CASE_REPORT.getCode(),
                AdminWorkOrderConst.Task.CASE_REPORT_DEAL.getCode(), startTime, endTime, userIds);
        //每个人的不需要处理
        List<AdminWorkOrderDataVo> noNeedDealCountByUserIds = adminWorkOrderReportBiz.getNoNeedDealCountByUserIds(AdminWorkOrderConst.Type.CASE_REPORT.getCode(),
                AdminWorkOrderConst.Task.CASE_REPORT_DEAL.getCode(), startTime, endTime, AdminWorkOrderReportConst.DealResult.NO_DEAL.getCode(), userIds);
        //每个人的新增跟进中
        List<AdminWorkOrderDataVo> newFollows = adminWorkOrderReportBiz.getNoNeedDealCountByUserIds(AdminWorkOrderConst.Type.CASE_REPORT.getCode(),
                AdminWorkOrderConst.Task.CASE_REPORT_DEAL.getCode(), startTime, endTime, AdminWorkOrderReportConst.DealResult.REPORT_FOLLOW.getCode(), userIds);
        //每个人的遗留跟进中
        List<AdminWorkOrderDataVo> oldFollowCountByUserIds = adminWorkOrderReportBiz.getOldFollowCountByUserIds(AdminWorkOrderConst.Type.CASE_REPORT.getCode(),
                AdminWorkOrderConst.Task.CASE_REPORT_DEAL.getCode(), startTime, AdminWorkOrderReportConst.DealResult.REPORT_FOLLOW.getCode(), userIds);
        //每个人处理完成
        List<AdminWorkOrderDataVo> dealCompleteCountByUserIds = adminWorkOrderReportBiz.getNoNeedDealCountByUserIds(AdminWorkOrderConst.Type.CASE_REPORT.getCode(),
                AdminWorkOrderConst.Task.CASE_REPORT_DEAL.getCode(), startTime, endTime, AdminWorkOrderReportConst.DealResult.DEAL_COMPLETE.getCode(), userIds);
        //每个人的今日有跟进动作
        List<AdminWorkOrderDataVo> followCountByUserIds = adminWrokOrderReportRecordBiz.getFollowCountByUserIds(AdminWorkOrderConst.Type.CASE_REPORT.getCode(),
                AdminWorkOrderConst.Task.CASE_REPORT_DEAL.getCode(), startTime, endTime, userIds);
        if (!CollectionUtils.isEmpty(countByUserIds)) {
            for (WorkOrderReportWorkStatus workOrderReportWorkStatus : workOrderReportWorkStatusList) {
                for (AdminWorkOrderDataVo countByUserId : countByUserIds) {
                    if (workOrderReportWorkStatus.getUserId() == countByUserId.getOperatorId()) {
                        workOrderReportWorkStatus.setGetCount(countByUserId.getCount());
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(noNeedDealCountByUserIds)) {
            for (WorkOrderReportWorkStatus workOrderReportWorkStatus : workOrderReportWorkStatusList) {
                for (AdminWorkOrderDataVo noNeedDealCountByUserId : noNeedDealCountByUserIds) {
                    if (workOrderReportWorkStatus.getUserId() == noNeedDealCountByUserId.getOperatorId()) {
                        workOrderReportWorkStatus.setNoDeal(noNeedDealCountByUserId.getCount());
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(newFollows)) {
            for (WorkOrderReportWorkStatus workOrderReportWorkStatus : workOrderReportWorkStatusList) {
                for (AdminWorkOrderDataVo newFollow : newFollows) {
                    if (workOrderReportWorkStatus.getUserId() == newFollow.getOperatorId()) {
                        workOrderReportWorkStatus.setNewFollow(newFollow.getCount());
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(oldFollowCountByUserIds)) {
            for (WorkOrderReportWorkStatus workOrderReportWorkStatus : workOrderReportWorkStatusList) {
                for (AdminWorkOrderDataVo oldFollowCountByUserId : oldFollowCountByUserIds) {
                    if (workOrderReportWorkStatus.getUserId() == oldFollowCountByUserId.getOperatorId()) {
                        workOrderReportWorkStatus.setOldFollow(oldFollowCountByUserId.getCount());
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(followCountByUserIds)) {
            for (WorkOrderReportWorkStatus workOrderReportWorkStatus : workOrderReportWorkStatusList) {
                for (AdminWorkOrderDataVo followCountByUserId : followCountByUserIds) {
                    if (workOrderReportWorkStatus.getUserId() == followCountByUserId.getOperatorId()) {
                        workOrderReportWorkStatus.setNowFollow(followCountByUserId.getCount());
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(dealCompleteCountByUserIds)) {
            for (WorkOrderReportWorkStatus workOrderReportWorkStatus : workOrderReportWorkStatusList) {
                for (AdminWorkOrderDataVo dealCompleteCountByUserId : dealCompleteCountByUserIds) {
                    if (workOrderReportWorkStatus.getUserId() == dealCompleteCountByUserId.getOperatorId()) {
                        workOrderReportWorkStatus.setDealComplete(dealCompleteCountByUserId.getCount());
                    }
                }
            }
        }
        for (WorkOrderReportWorkStatus workOrderReportWorkStatus : workOrderReportWorkStatusList) {
            workOrderReportWorkStatus.setNowLeaveFollow(workOrderReportWorkStatus.getNewFollow() + workOrderReportWorkStatus.getOldFollow());
            int dealComplete = workOrderReportWorkStatus.getDealComplete();
            int newFollow = workOrderReportWorkStatus.getNewFollow();
            //完成率
            String ompleteRate = "0.00";
            DecimalFormat df = new DecimalFormat("0.00");
            if (newFollow != 0) {
                ompleteRate = df.format((float) dealComplete / newFollow);
                System.err.println(ompleteRate);
            }
            workOrderReportWorkStatus.setOmpleteRate(ompleteRate);
        }
        return workOrderReportWorkStatusList;
    }


    @RequiresPermission("orderReport:get-mission")
    @RequestMapping(path = "pass-record", method = RequestMethod.POST)
    @ApiOperation(value = "工单传递记录", notes = "")
    public Response queryWorkOrderPassRecord(@RequestParam(name = "workOrderId") long workOrderId,
                                                 @RequestParam(name = "token") String token) {


        List<AdminWorkOrderRecord> records = adminWorkOrderRecordBiz.selectByWorkIdAndOperateTypes(Arrays.asList(workOrderId),
                Arrays.asList(AdminWorkOrderConst.AdminWorkOrderOperateTypeEnum.ASSIGN__REPORT_ORDER.getCode(),
                AdminWorkOrderConst.AdminWorkOrderOperateTypeEnum.CREATE_REPORT_ORDER.getCode(),
                        AdminWorkOrderConst.AdminWorkOrderOperateTypeEnum.WORK_REPORT_ORDER_PASS.getCode()));
        return  NewResponseUtil.makeSuccess(toRecordVo(records));
    }

    List<AdminWorkOrderPassRecordVo> toRecordVo(List<AdminWorkOrderRecord> recordList) {
        List<AdminWorkOrderPassRecordVo> resultVos = new ArrayList<>();

        if (CollectionUtils.isEmpty(recordList)) {
            return resultVos;
        }

        Collections.sort(recordList, new Comparator<AdminWorkOrderRecord>() {
            @Override
            public int compare(AdminWorkOrderRecord o1, AdminWorkOrderRecord o2) {
                return Long.compare(o1.getId(), o2.getId());
            }
        });

        String prevName = "";
        for (AdminWorkOrderRecord record : recordList) {
            AdminWorkOrderPassRecordVo vo = new AdminWorkOrderPassRecordVo();
            vo.setRecordDate(DateUtil.getDate2LStr(record.getCreateTime()));
            String userName = "";
            if (record.getOperatorId() != 0) {
                AdminUserAccountModel adminUserAccountModel = this.seaAccountClientV1.getValidUserAccountById(record.getOperatorId()).getResult();
                userName = Optional.ofNullable(adminUserAccountModel).map(AdminUserAccountModel::getName).orElse("");
            }
            switch (AdminWorkOrderConst.AdminWorkOrderOperateTypeEnum.getEnumByCode(record.getOperateType())) {
                case CREATE_REPORT_ORDER:
                    vo.setRecordDesc(SYSTEM_TIPS);
                    break;
                case ASSIGN__REPORT_ORDER:
                    vo.setRecordDesc(String.format(ASSIGN_TIPS, userName));
                    prevName = userName;
                    break;
                case WORK_REPORT_ORDER_PASS:
                    vo.setRecordDesc(String.format(PASS_TIPS, prevName, userName));
                    prevName = userName;
                    break;
                default:
                    break;
            }
            resultVos.add(vo);
        }

        return resultVos.stream().sorted(Comparator.comparing(AdminWorkOrderPassRecordVo::getRecordDate).reversed()).collect(Collectors.toList());
    }



    @RequiresPermission("orderReport:data-statistical")
    @RequestMapping(path = "reportdatalist", method = RequestMethod.POST)
    @ApiOperation(value = "举报统计列表", notes = "举报统计列表" ,response = AdminReportDataVo.class)
    public Response reportDataList(@ApiParam("工单号 ") Long workId,
                                    @ApiParam("状态  0, 未处理 1, 处理中 2, 处理成功 3, 处理失败 " +
                                            "4, 无需处理5, 系统自动处理") Integer status,
                                    @ApiParam("操作人id")Long operator,
                                    @ApiParam("案例id")Long caseId,
                                    @ApiParam("处理时间")String startDealTime,
                                    @ApiParam("处理时间")String endDealTime,
                                    @ApiParam("领取时间")String startHandleTime,
                                    @ApiParam("领取时间")String endHandleTime,
                                    @ApiParam("创建时间")String startCreateTime,
                                    @ApiParam("创建时间")String endCreateTime,
                                    @ApiParam("当前页数")int current,
                                    @ApiParam("每页数量")int pageSize) {

        log.info("reportDataList workId={},status={},operator={},caseId={}",workId,status,operator,caseId);

        List<AdminReportDataVo> list = adminWorkOrderReportBiz.getAdminReportDataVo(current,pageSize,workId,status,operator,caseId, StringUtils.trimToNull(startDealTime),StringUtils.trimToNull(endDealTime),
                StringUtils.trimToNull(startHandleTime),StringUtils.trimToNull(endHandleTime),StringUtils.trimToNull(startCreateTime),StringUtils.trimToNull(endCreateTime));

        if (CollectionUtils.isEmpty(list)){
            Map<String, Object> result = Maps.newHashMap();
            list = Lists.newArrayList();
            result.put("pagination", PageUtil.transform2PageMap(list));
            result.put("list", list);
            return NewResponseUtil.makeSuccess(result);
        }
        Map<String, Object> result = Maps.newHashMap();
        result.put("pagination", PageUtil.transform2PageMap(list));
        result.put("list", list);

        return NewResponseUtil.makeSuccess(result);
    }


    private void fillInfoUuidAndUserId(List<AdminWorkOrderReportVo> adminWorkOrderReportList, List<Integer> caseIds) {
        List<CrowdfundingInfo> crowdfundingInfos = adminCrowdfundingInfoBiz.getListByIds(caseIds);
        if (CollectionUtils.isEmpty(crowdfundingInfos)) {
            return;
        }
        Map<Integer, CrowdfundingInfo> crowdfundingInfoMap = crowdfundingInfos.stream().collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity()));
        adminWorkOrderReportList.forEach(adminWorkOrderReportVo -> {
            CrowdfundingInfo crowdfundingInfo = crowdfundingInfoMap.get(adminWorkOrderReportVo.getCaseId());
            if (crowdfundingInfo == null) {
                log.error("caseId:{}不能找到筹款案例", adminWorkOrderReportVo.getCaseId());
            } else {
                adminWorkOrderReportVo.setInfoUuid(crowdfundingInfo.getInfoId());
                //写入案例发起人id
                adminWorkOrderReportVo.setUserId(crowdfundingInfo.getUserId());
            }
        });
    }


    public static void main(String[] args) {
    }
}
