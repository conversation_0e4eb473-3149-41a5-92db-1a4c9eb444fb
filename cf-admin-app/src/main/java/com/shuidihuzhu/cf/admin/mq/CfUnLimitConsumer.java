package com.shuidihuzhu.cf.admin.mq;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingOperationBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingOperationEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation;
import com.shuidihuzhu.cf.model.event.CailiaoConditionEvent;
import com.shuidihuzhu.cf.param.InitialAuditCreateOrderParam;
import com.shuidihuzhu.cf.risk.constant.CfRiskMQTagCons;
import com.shuidihuzhu.cf.risk.constant.CfRiskMQTopicCons;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistCallPhaseEnum;
import com.shuidihuzhu.cf.risk.model.risk.BlacklistLiftingDto;
import com.shuidihuzhu.cf.service.AdminEventPublishService;
import com.shuidihuzhu.cf.service.crowdfunding.CfCailiaoService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditCreateOrder;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditCreateOrderService;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @DATE 2020/8/20
 */
@Service
@RocketMQListener(id = CfRiskMQTagCons.BLACKLIST_LIFTING+"_workorder",
        group = "cf-admin-unlimit-group",
        tags = CfRiskMQTagCons.BLACKLIST_LIFTING ,
        topic = CfRiskMQTopicCons.CF_RISK_TOPIC )
@Slf4j
public class CfUnLimitConsumer implements MessageListener<BlacklistLiftingDto> {

    @Autowired
    private InitialAuditCreateOrder initialAuditCreateOrder;

    @Autowired
    private AdminEventPublishService adminEventPublishService;

    @Autowired
    private CfCailiaoService cfCailiaoService;

    @Autowired
    private AdminCrowdfundingOperationBiz adminCrowdfundingOperationBiz;

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private InitialAuditCreateOrderService auditCreateOrderService;

    private static final List<Integer> ORDER_TYPES = Lists.newArrayList(WorkOrderType.yiliaoshenhe.getType(),
            WorkOrderType.highriskshenhe.getType(), WorkOrderType.ai_photo.getType(), WorkOrderType.ai_content.getType(),
            WorkOrderType.ai_erci.getType());

    @Resource
    private CfWorkOrderClient workOrderClient;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<BlacklistLiftingDto> mqMessage) {

        BlacklistLiftingDto dto = mqMessage.getPayload();

        int caseId = dto.getCaseId();
        int phase = dto.getBlacklistCallPhase();

        log.info("CfUnLimitConsumer.consumeMessage.{} mqMessage={}", caseId, mqMessage);

        if (BlacklistCallPhaseEnum.SUBMIT_PRE_TRIAL.getCode() == phase) {
            log.info("CfUnLimitConsumer.consumeMessage.{} 限制预审通过操作解锁 caseId={}", caseId, caseId);

            Response<WorkOrderVO> lastWorkOrder = workOrderClient.getLastWorkOrderByTypes(caseId, ORDER_TYPES);

            // 如果解锁后已经有工单了，就调用驳回再生成工单逻辑
            if (Objects.nonNull(lastWorkOrder) && lastWorkOrder.ok() && Objects.nonNull(lastWorkOrder.getData())) {
                log.info("CfUnLimitConsumer.consumeMessage.{} 解锁后已经有工单了，调用驳回再生成工单逻辑 caseId={}", caseId, caseId);
                // 解锁之后调用驳回再生成工单
                initialAuditCreateOrder.createChuci(caseId, 1, true);
            } else {
                // 如果解锁后没有工单，就直接调用创建初审工单逻辑
                log.info("CfUnLimitConsumer.consumeMessage.{} 解锁后没有工单，直接调用创建初审工单逻辑 caseId={}", caseId, caseId);
                InitialAuditCreateOrderParam orderParam = InitialAuditCreateOrderParam.builder()
                        .caseId(caseId)
                        .condition(1)
                        .first(true)
                        .to1V1(true)
                        .unLimitInitialAudit(true)
                        .checkTargetAmount(true)
                        .build();
                auditCreateOrderService.createInitialAuditOrder(orderParam);
            }
        }

        if (BlacklistCallPhaseEnum.SUBMIT_MATERIAL_REVIEW.getCode() == phase){

            CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
            //必须提交材料
            if (!(crowdfundingInfo.getStatus().equals(CrowdfundingStatus.SUBMITTED)
                    || crowdfundingInfo.getStatus().equals(CrowdfundingStatus.APPROVE_DENIED))){
                log.info("CfUnLimitConsumer.consumeMessage.{} caseId={} status={}", caseId, caseId,crowdfundingInfo.getStatus());
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            String infoUuid = crowdfundingInfo.getInfoId();
            CrowdfundingOperation crowdfundingOperation = adminCrowdfundingOperationBiz.getByInfoId(infoUuid);
            if (crowdfundingOperation.getOperation() == CrowdfundingOperationEnum.NEVER_PROCESSING.value()
                 || crowdfundingOperation.getOperation() == CrowdfundingOperationEnum.DEFER_CONTACT.value()){
                log.info("CfUnLimitConsumer.consumeMessage.{} caseId={} Operation={}",caseId, caseId,crowdfundingOperation.getOperation());
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            CailiaoConditionEvent event = new CailiaoConditionEvent(this,crowdfundingInfo.getId(), crowdfundingInfo.getInfoId());
            event.setAmount(crowdfundingInfo.getAmount());
            event.setOperation(crowdfundingOperation.getOperation());
//            adminEventPublishService.publish(event);
            event.setComment("限制收款人通过操作解锁");
            cfCailiaoService.createWorkOrder(event);
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
