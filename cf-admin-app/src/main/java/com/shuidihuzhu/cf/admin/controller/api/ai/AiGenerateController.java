package com.shuidihuzhu.cf.admin.controller.api.ai;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.admin.constant.AsyncPoolConstants;
import com.shuidihuzhu.cf.enums.ai.AiGenerateEnum;
import com.shuidihuzhu.cf.enums.ai.AiModelEnum;
import com.shuidihuzhu.cf.model.ai.AiPromptConfig;
import com.shuidihuzhu.cf.service.ai.AiGenerateServiceImpl;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.admin.model.*;
import com.shuidihuzhu.client.model.ChatChunk;
import com.shuidihuzhu.client.model.ChatStreamResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * @Description: AI生成内容
 * @Author: panghairui
 * @Date: 2024/5/29 2:17 PM
 */
@Slf4j
@RestController
@RequestMapping("/admin/cf/ai/generate")
public class AiGenerateController {

    @Resource(name = AsyncPoolConstants.AI_GENERATE_ASYNC_POOL)
    private Executor executor;

    @Resource
    private AiGenerateServiceImpl aiGenerateService;

    @ApiOperation("AI生成内容")
    @PostMapping("ai-generate-content")
    @RequiresPermission("ai:ai-generate-content")
    public Response<Void> aiGenerateContent(@RequestBody AIGenerateParam aiGenerateParam) {
        log.info("AiGenerateController AI生成内容: {}", JSONObject.toJSONString(aiGenerateParam));
        long userId = ContextUtil.getAdminLongUserId();
        aiGenerateParam.setOperatorId(userId);

        // 异步执行事件处理，不阻塞当前线程
        CompletableFuture.runAsync(() -> {
            try {
                // ai生成内容
                aiGenerateService.generate(aiGenerateParam);
            } catch (Throwable e) {
                // 处理异常，记录日志
                log.error("aiGenerateContent handle error", e);
            }
        }, executor);

        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation("AI流式生成内容")
    @PostMapping(value = "ai-stream-generate-content", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @RequiresPermission("ai:ai-generate-content")
    public Flux<ChatChunk<ChatStreamResult>> aiStreamGenerateContent(@RequestBody AIGenerateParam aiGenerateParam) {
        log.info("aiStreamGenerateContent AI生成内容: {}", JSONObject.toJSONString(aiGenerateParam));
        long userId = ContextUtil.getAdminLongUserId();
        aiGenerateParam.setOperatorId(userId);
        return aiGenerateService.streamGenerate(aiGenerateParam);
    }

    @ApiOperation("保存提示词配置")
    @PostMapping(value = "save-or-update-prompt-config")
    public Response<Void> saveOrUpdatePromptConfig(@RequestBody AiPromptConfig aiPromptConfig) {
        log.info("saveOrUpdatePromptConfig 保存提示词配置 {}", JSONObject.toJSONString(aiPromptConfig));
        aiGenerateService.saveOrUpdatePromptConfig(aiPromptConfig);
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation("查询提示词配置")
    @PostMapping(value = "query-prompt-config")
    public Response<AiPromptConfig> queryPromptConfig(@RequestParam(value = "generateType", required = false, defaultValue = "1") Integer generateType,
                                                      @RequestParam(value = "modelTypeStr") String modelTypeStr,
                                                      @RequestParam(value = "bizType", required = false, defaultValue = "0") Integer bizType) {
        log.info("queryPromptConfig 查询提示词配置 generateType:{}, modelType:{} bizType:{}", generateType, modelTypeStr, bizType);
        return NewResponseUtil.makeSuccess(aiGenerateService.queryPromptConfig(generateType, modelTypeStr, bizType));
    }

    @ApiOperation("获取ai生成内容")
    @PostMapping("get-generate-content")
    @RequiresPermission("ai:ai-generate-content")
    public Response<List<AiGenerateRecordVO>> getGenerateContent(@RequestBody QueryParam queryParam) {
        log.info("AiGenerateController 获取ai生成内容: {}", JSONObject.toJSONString(queryParam));
        return NewResponseUtil.makeSuccess(aiGenerateService.queryGenerateRecord(queryParam));
    }

    @ApiOperation("保存暂存方案")
    @PostMapping("staging-scheme")
    @RequiresPermission("ai:ai-generate-content")
    public Response<Void> stagingScheme(@RequestBody StagingParam stagingParam) {
        log.info("AiGenerateController 保存暂存方案: {}", JSONObject.toJSONString(stagingParam));
        long userId = ContextUtil.getAdminLongUserId();
        aiGenerateService.stagingScheme(stagingParam, userId);
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation("查询暂存方案")
    @PostMapping("query-scheme")
    @RequiresPermission("ai:ai-generate-content")
    public Response<StagingParam> queryScheme(@RequestBody QueryParam queryParam) {
        log.info("AiGenerateController 查询暂存方案: {}", JSONObject.toJSONString(queryParam));
        return NewResponseUtil.makeSuccess(aiGenerateService.queryStagingScheme(queryParam));
    }

    @ApiOperation("我要反馈")
    @PostMapping("user-feedback")
    @RequiresPermission("ai:ai-generate-content")
    public Response<Void> userFeedback(@RequestBody FeedbackParam feedbackParam) {
        log.info("AiGenerateController 我要反馈: {}", JSONObject.toJSONString(feedbackParam));
        long userId = ContextUtil.getAdminLongUserId();
        aiGenerateService.feedback(feedbackParam, userId);
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation("AI枚举")
    @PostMapping("get-ai-enum")
    @RequiresPermission("ai:ai-generate-content")
    public Response<Map<String, Map<Integer, String>>> getAiEnum() {

        Map<String, Map<Integer, String>> authenticityEnumMap = Maps.newHashMap();
        Map<Integer, String> aiGenerateEnum = AiGenerateEnum.getEnumMap();
        authenticityEnumMap.put("aiGenerateEnum", aiGenerateEnum);
        Map<Integer, String> aiModelEnum = AiModelEnum.getEnumMap();
        authenticityEnumMap.put("aiModelEnum", aiModelEnum);

        return NewResponseUtil.makeSuccess(authenticityEnumMap);
    }

}
