package com.shuidihuzhu.cf.admin.controller.api.report.v2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportProblemDefaultVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportProblemRaiserLabel;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportProblemVo;
import com.shuidihuzhu.cf.service.FinanceApproveService;
import com.shuidihuzhu.cf.service.crowdfunding.report.CfReportProblemService;
import com.shuidihuzhu.cf.service.crowdfunding.report.CfReportRiskService;
import com.shuidihuzhu.cf.service.crowdfunding.report.CfReportService;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.ClewCallRecordModel;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CfCaseSpecialPrePoseDetail;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * @author: fengxuan
 * @create 2019-12-10 19:45
 **/
@Api("标签树")
@RestController
@RequestMapping(path = "/admin/cf/report/v2/problem")
@Slf4j
public class CfReportProblemController {

    @Autowired
    private CfReportProblemService problemService;

    @Autowired
    private AdminCrowdfundingReportBiz adminCrowdfundingReportBiz;

    @Autowired
    private ICfReportAnswerService cfReportAnswerService;

    @Autowired
    private IReportCommunicaterListService reportCommunicaterListService;
    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Autowired
    private ICfFundraiserCommunicateService fundraiserCommunicateService;

    @Autowired
    private ICfReportCallRecordService cfReportCallRecordService;

    @Autowired
    private CfClewtrackFeignClient cfClewtrackFeignClient;
    @Resource
    private SeaAccountClientV1 seaUserAccountClientV1;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Autowired
    private FinanceApproveService financeApproveService;

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private CfReportRiskService cfReportRiskService;
    @Autowired
    private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;
    @Autowired
    private CfReportService cfReportService;
    @Autowired
    private ClewPreproseMaterialFeignClient clewPreproseMaterialFeignClient;
    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Autowired
    private MaskUtil maskUtil;


    @ApiOperation("获取标签")
    @RequestMapping(path = "list-all-label", method = RequestMethod.POST)
    @RequiresPermission("problem:list-all-label")
    public Response<List<CfReportProblemLabel>> listAllLabel(@ApiParam("第几层标签,从1开始")
                                                             @RequestParam(value = "level", required = false) Integer level,
                                                             @ApiParam("上一级标签")
                                                             @RequestParam(value = "parentId", required = false)
                                                                     Integer parentId,
                                                             @RequestParam(value = "isFundraiser", defaultValue = "false")
                                                                         boolean isFundraiser,
                                                             @RequestParam(value = "caseId", defaultValue = "0")int caseId,
                                                             @RequestParam(value = "followId", defaultValue = "0")long followId,
                                                             @RequestParam(value = "reportWorkOrderId", defaultValue = "0")
                                                                         long reportWorkOrderId) {
        int userId = ContextUtil.getAdminUserId();
        return problemService.listAllLabel(level, parentId, isFundraiser, caseId, followId, reportWorkOrderId, userId);
    }


    @ApiOperation("根据标签获取常驻问题信息")
    @RequestMapping(path = "list-direct-show-problem", method = RequestMethod.POST)
    @RequiresPermission("problem:list-direct-show-problem")
    public Response<List<CfReportProblemDefaultVo>> listDirectShowProblem(@ApiParam("第二层标签id")
                                                                          @RequestParam(value = "labelIds") String labelIds,
                                                                          @ApiParam("质疑方or筹款方")
                                                                          @RequestParam(value = "reportPage") int reportPage) {
        if (!CfReportPageEnum.REPORT_PAGE_LIST.contains(reportPage)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<Integer> labels = Splitter.on(',').splitToList(labelIds).stream().map(Integer::valueOf).collect(Collectors.toList());
        return problemService.listDirectShowProblem(labels,reportPage);
    }


    @ApiOperation("根据问题获取联动问题信息")
    @RequestMapping(path = "list-relate-problem", method = RequestMethod.POST)
    @RequiresPermission("problem:list-relate-problem")
    public Response<List<CfReportProblemVo>> listRelateProblem(@ApiParam("问题id")
                                                               @RequestParam("problemId") int problemId,
                                                               @ApiParam("问题上答案或者选项")
                                                               @RequestParam("content") String content,
                                                               @ApiParam("问题类型,文本还是下拉框")
                                                               @RequestParam("problemType") int problemType) {
        //校验如果没有content且选择了下拉框类型
        if (!CfReportProblem.noChoiceProblemType(problemType) && StringUtils.isBlank(content)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return problemService.listRelateProblem(problemId, content);
    }


    @ApiOperation("返回给发起人/筹款人的标签")
    @RequestMapping(path = "list-raiser-problem-label", method = RequestMethod.POST)
    @RequiresPermission("problem:list-raiser-problem-label")
    public Response<List<CfReportProblemRaiserLabel>> listRaiserProblemLabel(@RequestParam("caseId") int caseId) {
        return problemService.listRaiserProblemLabel(caseId);
    }


    @ApiOperation("新建筹款人问题前判断是否能生成")
    @PostMapping(path = "check-create-raiser-problem")
    @RequiresPermission("problem:check-create-raiser-problem")
    public Response<Boolean> checkCreateRaiserProblem(@RequestParam("caseId") int caseId) {
        return problemService.checkCreateRaiserProblem(caseId);
    }


    @ApiOperation("更新举报条目的处理和接通状态")
    @RequestMapping(path = "update-report-item-connect-status", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @RequiresPermission("problem:update-report-item-connect-status")
    public Response<String> updateReportItemConnectStatus(@ApiParam("举报id") @RequestParam("reportId") int reportId,
                                                          @ApiParam("案例id") @RequestParam("caseId") int caseId,
                                                          @ApiParam("接通状态 1:未接通 2:已接通") @RequestParam("connectStatus") int connectStatus){

        long adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        CrowdfundingReport report = adminCrowdfundingReportBiz.query(caseId, reportId);
        if(Objects.isNull(report)){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        if(connectStatus != CfReportConnectStatus.NO_CONNECT.getKey() && connectStatus != CfReportConnectStatus.CONNECTED.getKey()){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        int res = adminCrowdfundingReportBiz.updateHandleAndConnectStatus(reportId, CfReportHandleStatus.HANDLED.getKey(),
                connectStatus, adminUserId, CaseReportDealStatus.FINISH.getValue());

        if (res > 0) {
            //修改案例举报状态
            CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
            cfReportService.updateReportStatus(caseId,crowdfundingInfo.getInfoId());
        }

        return ResponseUtil.makeSuccess("success");
    }

    @ApiOperation("查询质疑方沟通记录")
    @RequestMapping(path = "/query-questioner-follow-record", method = RequestMethod.POST)
    @RequiresPermission("problem:query-questioner-follow-record")
    public Response<CfFollowRecordPageDO> queryReportAnswerDetail(@ApiParam("案例id") @RequestParam("caseId") int caseId,
                                                                  @ApiParam("举报id") @RequestParam(value = "reportId") int reportId){

        long adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        //质疑方跟进记录和具体举报相关，筹款方质疑记录只和案例相关
        if(reportId <= 0){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        CfReportPageEnum reportPageEnum = CfReportPageEnum.QUESTIONER;

        //质疑方跟进记录和具体举报相关
        CrowdfundingReport crowdfundingReport = adminCrowdfundingReportBiz.query(caseId, reportId);
        if(Objects.isNull(crowdfundingReport)){
            return ResponseUtil.makeResponse(AdminErrorCode.REPORT_NOT_EXIST.getCode(), AdminErrorCode.REPORT_NOT_EXIST.getMsg(), null);
        }

        List<CfReportCommunicaterDO> reportCommunicaterDOS = reportCommunicaterListService.query(caseId, reportId, reportPageEnum.getKey());
        for (CfReportCommunicaterDO communicaterDO : reportCommunicaterDOS){
            List<CfReportCallRecordDO> recordDOS = cfReportCallRecordService.query(caseId, reportId, communicaterDO.getId());
            communicaterDO.setMobileMask(maskUtil.buildByEncryptPhone(communicaterDO.getMobile()));
            communicaterDO.setMobile(null);
            communicaterDO.setCallCount(CollectionUtils.isEmpty(recordDOS) ? 0 : recordDOS.size());
        }

        List<AdminReportProblemAnswerDetail> answerDetails = cfReportAnswerService.query(caseId, reportId, reportPageEnum.getKey());

        CfFollowRecordPageDO recordPageDO = new CfFollowRecordPageDO();
        recordPageDO.setCommunicaters(reportCommunicaterDOS);
        recordPageDO.setQuestionerAnswer(answerDetails);
        recordPageDO.setImageUrls(crowdfundingReport.getImageUrls());
        //cfReportRiskService.answerLabel();
        return ResponseUtil.makeSuccess(recordPageDO);
    }

    @ApiOperation("保存质疑方或者筹款方举报问题的答案")
    @RequestMapping(path = "save-report-problem-answer", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @RequiresPermission("problem:save-report-problem-answer")
    public Response<AdminReportProblemAnswerDetail> saveReportProblemAnswer(@NotNull final String param){
        long adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        AdminReportProblemAnswerDetail answerDetailParam = JSON.parseObject(param, new TypeReference<AdminReportProblemAnswerDetail>(){});//已检查过
        if(Objects.isNull(answerDetailParam)){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        CfReportPageEnum pageEnum = CfReportPageEnum.parse(answerDetailParam.getType());
        if(Objects.isNull(pageEnum)){
            return ResponseUtil.makeResponse(AdminErrorCode.REPORT_ANSWER_PAGE_ERROR.getCode(), AdminErrorCode.REPORT_ANSWER_PAGE_ERROR.getMsg(), null);
        }

        int caseId = answerDetailParam.getCaseId();
        int reportId = answerDetailParam.getReportId();

        //质疑方跟进记录和具体举报相关，筹款方质疑记录只和案例相关
        if((pageEnum == CfReportPageEnum.QUESTIONER && reportId <= 0) || (pageEnum == CfReportPageEnum.FUNDRAISER && reportId != 0)){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        CfReportRelationEnum reportRelationEnum = CfReportRelationEnum.parse(answerDetailParam.getRelationKey());
        String reportRelation = reportRelationEnum == CfReportRelationEnum.OTHER ? answerDetailParam.getRelationValue() : reportRelationEnum.getValue();
        CfReportInfoSourceEnum infoSourceEnum = CfReportInfoSourceEnum.parse(answerDetailParam.getInfoSourceKey());
        String infoSourceValue = infoSourceEnum == CfReportInfoSourceEnum.OTHER ? answerDetailParam.getInfoSourceValue() : infoSourceEnum.getValue();
        if(StringUtils.isEmpty(reportRelation) || StringUtils.isEmpty(infoSourceValue)){
            return ResponseUtil.makeResponse(AdminErrorCode.REPORT_RELATION_TYPE_ERROR.getCode(), AdminErrorCode.REPORT_RELATION_TYPE_ERROR.getMsg(), null);
        }

        if(pageEnum == CfReportPageEnum.QUESTIONER && Objects.isNull(adminCrowdfundingReportBiz.query(caseId, reportId))){
            return ResponseUtil.makeResponse(AdminErrorCode.REPORT_NOT_EXIST.getCode(), AdminErrorCode.REPORT_NOT_EXIST.getMsg(), null);
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if(pageEnum == CfReportPageEnum.FUNDRAISER && Objects.isNull(crowdfundingInfo)){
            return ResponseUtil.makeError(ErrorCode.CF_NOT_FOUND);
        }

        String answer = answerDetailParam.getAnswerDetail();
        AdminErrorCode validate = answerValidate(answer);
        if(AdminErrorCode.SUCCESS != validate){
            return ResponseUtil.makeResponse(validate.getCode(), validate.getMsg(), null);
        }

        String operation = pageEnum == CfReportPageEnum.QUESTIONER ? "质疑人沟通列表" : "筹款方沟通列表";
        String comment = pageEnum == CfReportPageEnum.QUESTIONER ? "提交了新的质疑人沟通记录" : "提交了新的筹款方沟通记录";
        financeApproveService.addApprove(crowdfundingInfo, operation, comment, ContextUtil.getAdminUserId());

        AdminReportProblemAnswerDetail answerDetail = new AdminReportProblemAnswerDetail();
        answerDetail.setCaseId(caseId);
        answerDetail.setReportId(reportId);
        answerDetail.setType(pageEnum.getKey());
        answerDetail.setRelationKey(reportRelationEnum.getKey());
        answerDetail.setRelationValue(reportRelation);
        answerDetail.setInfoSourceKey(infoSourceEnum.getKey());
        answerDetail.setInfoSourceValue(infoSourceValue);
        answerDetail.setMemo(answerDetailParam.getMemo());
        answerDetail.setAnswerDetail(answer);
        answerDetail.setOperatorId(adminUserId);
        answerDetail.setProblemOptions(StringUtils.trimToEmpty(answerDetailParam.getProblemOptions()));
        answerDetail.setMandatoryInfo(StringUtils.trimToEmpty(answerDetailParam.getMandatoryInfo()));
        answerDetail.setNewStrategy(answerDetailParam.isNewStrategy());
        answerDetail.setConnectObject(answerDetailParam.getConnectObject());
        cfReportRiskService.saveLabel(reportId, caseId, answer);
        int res = cfReportAnswerService.insert(answerDetail);
        if(res > 0){
            return ResponseUtil.makeSuccess(answerDetail);
        }

        return ResponseUtil.makeResponse(AdminErrorCode.OPERATION_FAILED.getCode(), AdminErrorCode.OPERATION_FAILED.getMsg(), null);
    }

    private AdminErrorCode answerValidate(String answer){
        if(StringUtils.isEmpty(answer)){
            return AdminErrorCode.REPORT_ANSWER_EMPTY_ERROR;
        }

        List<AdminReportProblemAnswer> answerDetails = JSON.parseObject(answer, new TypeReference<List<AdminReportProblemAnswer>>(){});//已检查过
        if(CollectionUtils.isEmpty(answerDetails)){
            return AdminErrorCode.REPORT_ANSWER_EMPTY_ERROR;
        }

        for (AdminReportProblemAnswer problemAnswer : answerDetails){

            if(CollectionUtils.isEmpty(problemAnswer.getProblemLabelAnswers())){
                return AdminErrorCode.REPORT_ANSWER_DETAIL_EMPTY_ERROR;
            }

            for (AdminReportProblemLabelAnswer labelAnswer : problemAnswer.getProblemLabelAnswers()){

                if(CollectionUtils.isEmpty(labelAnswer.getLabelAnswers())){
                    continue;
                }

                for (List<AdminReportProblemLabel> labelAnswerList : labelAnswer.getLabelAnswers()){
                    for (AdminReportProblemLabel problemLabel : labelAnswerList){

                        CfReportProblem.ReportAnswerType answerType = CfReportProblem.ReportAnswerType.findByCode(problemLabel.getPrefixAnswerType());
                        String answerItem = problemLabel.getAnswer();
                        //0:必填
                        if(0 == problemLabel.getMustAnswer() && StringUtils.isEmpty(answerItem)){
                            return AdminErrorCode.REPORT_ANSWER_MUST_ERROR;
                        }

                        if(answerType == CfReportProblem.ReportAnswerType.shuzhi && !NumberUtils.isDigits(answerItem) && !"不知道".equals(answerItem)){
                            return AdminErrorCode.REPORT_ANSWER_TYPE_DIGTAL_ERROR;
                        }
                    }
                }
            }
        }
        return AdminErrorCode.SUCCESS;
    }


    @ApiOperation("新建筹款方跟进记录")
    @RequestMapping(path = "/when-new-query-fundraiser-page", method = RequestMethod.POST)
    @RequiresPermission("problem:when-new-query-fundraiser-page")
    public Response<CfFollowRecordPageDO> whenNewQueryFundraiserProblem(@ApiParam("案例id") @RequestParam("caseId") int caseId){

        long adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        CfReportPageEnum reportPageEnum = CfReportPageEnum.FUNDRAISER;

        List<CfReportCommunicaterDO> reportCommunicaterDOS = reportCommunicaterListService.query(caseId, 0, reportPageEnum.getKey());
        for (CfReportCommunicaterDO communicaterDO : reportCommunicaterDOS){
            List<CfReportCallRecordDO> recordDOS = cfReportCallRecordService.query(caseId, 0, communicaterDO.getId());
            communicaterDO.setMobileMask(maskUtil.buildByEncryptPhone(communicaterDO.getMobile()));
            communicaterDO.setMobile(null);
            communicaterDO.setCallCount(CollectionUtils.isEmpty(recordDOS) ? 0 : recordDOS.size());
        }

        CfFollowRecordPageDO recordPageDO = new CfFollowRecordPageDO();
        recordPageDO.setCommunicaters(reportCommunicaterDOS);

        CrowdfundingInfo info = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        recordPageDO.setPayeeMobileMask(maskUtil.buildByEncryptPhone(info.getPayeeMobile()));
        CrowdfundingInfoPayee payee = crowdfundingDelegate.getCrowdfundingInfoPayeeByInfoUuid(info.getInfoId());
        String emergencyPhone = Optional.ofNullable(payee).map(CrowdfundingInfoPayee::getEmergencyPhone).orElse("");
        recordPageDO.setEmergencyPhoneMask(maskUtil.buildByEncryptPhone(emergencyPhone));

        Response<List<CfCaseSpecialPrePoseDetail>> response = clewPreproseMaterialFeignClient.getSpecialPrePoseDetail(List.of(caseId));
        log.info("ClewPreproseMaterialFeignClient.getSpecialPrePoseDetail response:{}", JSON.toJSONString(response));
        if (response.ok() && CollectionUtils.isNotEmpty(response.getData())) {
            CfCaseSpecialPrePoseDetail cfCaseSpecialPrePoseDetail = response.getData().get(0);
            recordPageDO.setMobileMask(maskUtil.buildByDecryptPhone(cfCaseSpecialPrePoseDetail.getMobile()));
        }

        return ResponseUtil.makeSuccess(recordPageDO);
    }

    @ApiOperation("保存筹款方沟通列表记录")
    @RequestMapping(path = "save-fundraiser-communicater-record", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @RequiresPermission("problem:save-fundraiser-communicater-record")
    public Response<CfFundraiserCommunicateDO> saveFundraiserCommunicaterRecord(@ApiParam("案例id") @RequestParam("caseId") int caseId,
                                                                                @ApiParam("关系类型") @RequestParam("relationKey") int relationKey,
                                                                                @ApiParam("关系描述") @RequestParam(value = "relationValue", required = false) String relationValue,
                                                                                @ApiParam("手机号") @RequestParam("mobile") String mobile,
                                                                                @ApiParam("新建该条沟通记录的跟进记录对应的筹款方答案") @RequestParam("answerId") Integer answerId,
                                                                                @ApiParam("沟通状态") @RequestParam("connectStatus") int connectStatus,
                                                                                @ApiParam("本次沟通对象") @RequestParam(value = "connectObject", required = false) String connectObject) {
        long adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }
        if (connectStatus != CfReportConnectStatus.CONNECTED.getKey() && connectStatus != CfReportConnectStatus.NO_CONNECT.getKey()){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        CfReportRelationEnum reportRelationEnum = CfReportRelationEnum.parse(relationKey);
        String reportRelation = reportRelationEnum == CfReportRelationEnum.OTHER ? relationValue : reportRelationEnum.getValue();
        if(Objects.isNull(reportRelationEnum) || (reportRelationEnum == CfReportRelationEnum.OTHER && StringUtils.isEmpty(reportRelation)) || StringUtils.isEmpty(mobile)){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        String answerIdStr = answerId == null ? "" : String.valueOf(answerId);

        CfFundraiserCommunicateDO communicateDO = new CfFundraiserCommunicateDO();
        communicateDO.setCaseId(caseId);
        communicateDO.setRelationKey(reportRelationEnum.getKey());
        communicateDO.setRelationValue(reportRelation);
        communicateDO.setMobile(oldShuidiCipher.aesEncrypt(mobile));
        communicateDO.setConnectStatus(connectStatus);
        communicateDO.setOperatorId(adminUserId);
        communicateDO.setAnswerIds(answerIdStr);
        communicateDO.setConnectObject(connectObject);

        CfFundraiserCommunicateDO cfFundraiserCommunicateDO = fundraiserCommunicateService.getByMobileAndCaseId(caseId, oldShuidiCipher.aesEncrypt(mobile));
        if (Objects.isNull(cfFundraiserCommunicateDO)) {
            int res = fundraiserCommunicateService.insert(communicateDO);
            if (res > 0) {
                return ResponseUtil.makeSuccess(communicateDO);
            }
        } else {
            String answerIds;
            if (StringUtils.isNotBlank(cfFundraiserCommunicateDO.getAnswerIds())) {
                if (StringUtils.isNotBlank(answerIdStr)) {
                    answerIds = cfFundraiserCommunicateDO.getAnswerIds() + "," + answerIdStr;
                } else {
                    answerIds = cfFundraiserCommunicateDO.getAnswerIds();
                }
            } else {
                answerIds = answerIdStr;
            }
            int res = fundraiserCommunicateService.updateAnswer(cfFundraiserCommunicateDO.getId(), connectStatus, answerIds);
            if (res > 0) {
                return ResponseUtil.makeSuccess(communicateDO);
            }
        }

        return ResponseUtil.makeResponse(AdminErrorCode.OPERATION_FAILED.getCode(), AdminErrorCode.OPERATION_FAILED.getMsg(), null);
    }

    @ApiOperation("查询筹款方沟通列表")
    @RequestMapping(path = "query-fundraiser-communicater-list", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @RequiresPermission("problem:query-fundraiser-communicater-list")
    public Response<List<CfFundraiserCommunicateDO>> queryFundraiserCommunicaterList(@ApiParam("案例id") @RequestParam("caseId") int caseId){
        if(caseId <= 0 || Objects.isNull(crowdfundingDelegate.getCfInfoSimpleModelById(caseId))){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<CfFundraiserCommunicateDO> cfFundraiserCommunicateDOS = fundraiserCommunicateService.query(caseId);
        //明文手机号
        cfFundraiserCommunicateDOS.stream().forEach(r->{
            long operatorId = Objects.nonNull(r.getOperatorId()) ? r.getOperatorId() : 0L;
            AdminUserAccountModel accountModel = seaUserAccountClientV1.getValidUserAccountById(Integer.valueOf(String.valueOf(operatorId))).getResult();
            r.setMobileMask(maskUtil.buildByEncryptPhone(r.getMobile()));
            r.setMobile(null);
            r.setOperator(Objects.nonNull(accountModel) ? accountModel.getName() : "");
        });


        return ResponseUtil.makeSuccess(cfFundraiserCommunicateDOS);
    }

    @ApiOperation("根据筹款方跟进记录，查询对应答卷")
    @RequestMapping(path = "/query-fundraiser-follow-record", method = RequestMethod.POST)
    @RequiresPermission("problem:query-fundraiser-follow-record")
    public Response<CfFollowRecordPageDO> queryFundraiserFollowRecord(@ApiParam("案例id") @RequestParam("caseId") int caseId,
                                                                      @ApiParam("筹款方沟通列表的id") @RequestParam("followId") long followId){

        long adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        List<CfReportCommunicaterDO> reportCommunicaterDOS = reportCommunicaterListService.query(caseId, 0, CfReportPageEnum.FUNDRAISER.getKey());

        reportCommunicaterDOS.stream().forEach(r->{
            r.setMobile(shuidiCipher.decrypt(r.getMobile()));
        });

        CfFundraiserCommunicateDO communicateDO = fundraiserCommunicateService.queryById(followId);
        if (Objects.isNull(communicateDO)) {
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<AdminReportProblemAnswerDetail> answerDetails = Lists.newArrayList();
        if (StringUtils.isNotBlank(communicateDO.getAnswerIds())) {
            String answerIds = communicateDO.getAnswerIds();
            List<String> answerIdList = Lists.newArrayList(answerIds.split(","));
            List<Long> answers = Lists.newArrayList();
            for (String answerId : answerIdList) {
                if (StringUtils.isNotBlank(answerId)){
                    answers.add(Long.valueOf(answerId));
                }
            }
            answerDetails = cfReportAnswerService.queryByIds(answers);
        }

        CfFollowRecordPageDO pageDO = new CfFollowRecordPageDO();
        pageDO.setCommunicaters(reportCommunicaterDOS);
     //   problemService.checkIsNeedVerify(answerDetails);
        pageDO.setQuestionerAnswer(answerDetails);

        CrowdfundingInfo info = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        Optional.ofNullable(info.getPayeeMobile())
                        .filter(StringUtils::isNotBlank)
                                .ifPresent(r -> {
                                    pageDO.setPayeeMobile(StringUtils.EMPTY);
                                    pageDO.setPayeeMobileMask(maskUtil.buildByEncryptPhone(r));
                                });

        CrowdfundingInfoPayee payee = crowdfundingDelegate.getCrowdfundingInfoPayeeByInfoUuid(info.getInfoId());
        Optional.ofNullable(payee)
                .filter(r -> StringUtils.isNotBlank(r.getEmergencyPhone()))
                .ifPresent(r -> {
                    String emergencyPhone = r.getEmergencyPhone();
                    pageDO.setEmergencyPhone(StringUtils.EMPTY);
                    pageDO.setEmergencyPhoneMask(maskUtil.buildByEncryptPhone(emergencyPhone));
                });

        Response<List<CfCaseSpecialPrePoseDetail>> response = clewPreproseMaterialFeignClient.getSpecialPrePoseDetail(List.of(caseId));
        log.info("ClewPreproseMaterialFeignClient.getSpecialPrePoseDetail response:{}", JSON.toJSONString(response));
        if (response.ok() && CollectionUtils.isNotEmpty(response.getData())) {
            CfCaseSpecialPrePoseDetail cfCaseSpecialPrePoseDetail = response.getData().get(0);
            pageDO.setMobile(StringUtils.EMPTY);
            pageDO.setMobileMask(maskUtil.buildByDecryptPhone(cfCaseSpecialPrePoseDetail.getMobile()));
        }


        return ResponseUtil.makeSuccess(pageDO);

    }

    @ApiOperation("点击跟进记录拨打筹款方电话，未接通时，修改筹款方沟通记录的接通状态为未接通")
    @RequestMapping(path = "/update-fundraiser-follow-record-connect-status", method = RequestMethod.POST)
    @RequiresPermission("problem:update-fundraiser-follow-record-connect-status")
    public Response<String> updateFundraiserFollowRecordConnectStatus(@ApiParam("案例id") @RequestParam("caseId") int caseId,
                                                                      @ApiParam("跟进记录id") @RequestParam("followId") long followId,
                                                                      @ApiParam("1:未接通 2:已接通") @RequestParam("connectStatus") int connectStatus){

        long adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        CfFundraiserCommunicateDO communicateDO = fundraiserCommunicateService.queryByIdAndCase(followId, caseId);
        if(Objects.isNull(communicateDO)){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        if(CfReportConnectStatus.CONNECTED.getKey() != connectStatus && CfReportConnectStatus.NO_CONNECT.getKey() != connectStatus){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        int res = fundraiserCommunicateService.updateConnectStatus(followId, connectStatus);
        if(res >= 0){
            return ResponseUtil.makeSuccess("success");
        }

        return ResponseUtil.makeResponse(AdminErrorCode.OPERATION_FAILED.getCode(), AdminErrorCode.OPERATION_FAILED.getMsg(), null);
    }

    @ApiOperation("点击跟进记录拨打筹款方电话，接通电话并且保存答案时，修改筹款方沟通记录的接通状态和关联的答案")
    @RequestMapping(path = "/update-fundraiser-follow-record-answers", method = RequestMethod.POST)
    @RequiresPermission("problem:update-fundraiser-follow-record-answers")
    public Response<String> updateFundraiserFollowRecordAnswerIds(@ApiParam("案例id") @RequestParam("caseId") int caseId,
                                                                  @ApiParam("跟进记录id") @RequestParam("followId") long followId,
                                                                  @ApiParam("筹款方答案id") @RequestParam("answerId") long answerId){

        long adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        AdminReportProblemAnswerDetail answerDetail = cfReportAnswerService.queryById(answerId);
        if(Objects.isNull(answerDetail) || answerDetail.getCaseId() != caseId || answerDetail.getType() != CfReportPageEnum.FUNDRAISER.getKey()){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        CfFundraiserCommunicateDO communicateDO = fundraiserCommunicateService.queryByIdAndCase(followId, caseId);
        if(Objects.isNull(communicateDO)){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        String answerIds = StringUtils.isNotBlank(communicateDO.getAnswerIds())
                ? communicateDO.getAnswerIds() + "," + answerId : String.valueOf(answerId);

        int res = fundraiserCommunicateService.updateAnswer(followId, CfReportConnectStatus.CONNECTED.getKey(), answerIds);

        if(res >= 0){
            return ResponseUtil.makeSuccess("success");
        }

        return ResponseUtil.makeResponse(AdminErrorCode.OPERATION_FAILED.getCode(), AdminErrorCode.OPERATION_FAILED.getMsg(), null);
    }

    @ApiOperation("添加质疑方或者筹款方联系人")
    @RequestMapping(path = "add-communicater", method = RequestMethod.POST)
    @RequiresPermission("problem:add-communicater")
    public Response<String> addCommunicater(@ApiParam("案例id") @RequestParam("caseId") int caseId,
                                            @ApiParam("举报id") @RequestParam("reportId") int reportId,
                                            @ApiParam("1:质疑方沟通页 2:筹款方沟通页") @RequestParam("pageType") int pageType,
                                            @ApiParam("关系类型key") @RequestParam(value = "relativeKey") int relativeKey,
                                            @ApiParam("关系类型value") @RequestParam(value = "relativeValue", required = false) String relativeValue,
                                            @ApiParam("添加的手机号") @RequestParam("mobile") String mobile){

        long adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        CfReportPageEnum pageEnum = CfReportPageEnum.parse(pageType);
        if (caseId <= 0 || Objects.isNull(pageEnum) || StringUtils.isEmpty(mobile) || mobile.length() != 11) {
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        if((pageEnum == CfReportPageEnum.QUESTIONER && reportId <= 0) || (pageEnum == CfReportPageEnum.FUNDRAISER && reportId != 0)){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        CfReportCommunicaterDO communicater = reportCommunicaterListService.queryByMobile(caseId, reportId, oldShuidiCipher.aesEncrypt(mobile));
        if(Objects.nonNull(communicater)){
            return ResponseUtil.makeResponse(AdminErrorCode.REPORT_COMMUNICATER_REPEAT_ERROR.getCode(), AdminErrorCode.REPORT_COMMUNICATER_REPEAT_ERROR.getMsg(), null);
        }

        CfReportRelationEnum relationEnum = CfReportRelationEnum.parse(relativeKey);
        if(Objects.isNull(relationEnum) || (relationEnum == CfReportRelationEnum.OTHER && StringUtils.isEmpty(relativeValue))){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        relativeValue = relationEnum == CfReportRelationEnum.OTHER ? relativeValue : relationEnum.getValue();

        CfReportCommunicaterDO communicaterDO = new CfReportCommunicaterDO();
        communicaterDO.setCaseId(caseId);
        communicaterDO.setReportId(reportId);
        communicaterDO.setType(pageEnum.getKey());
        communicaterDO.setRelationKey(relationEnum.getKey());
        communicaterDO.setRelationValue(relativeValue);
        communicaterDO.setMobile(oldShuidiCipher.aesEncrypt(mobile));
        communicaterDO.setOperatorId(adminUserId);
        communicaterDO.setManualAdd(true);

        reportCommunicaterListService.insert(communicaterDO);

        return ResponseUtil.makeSuccess("success");
    }

    @ApiOperation("保存质疑方或者筹款方的通话记录")
    @RequestMapping(path = "save-call-record", method = RequestMethod.POST)
    @RequiresPermission("problem:save-call-record")
    public Response<String> saveCallRecord(@ApiParam("案例id") @RequestParam("caseId") int caseId,
                                           @ApiParam("举报id") @RequestParam("reportId") int reportId,
                                           @ApiParam("1:质疑方沟通页 2:筹款方沟通页") @RequestParam("pageType") int pageType,
                                           @ApiParam("沟通列表返回联系人的id") @RequestParam("addId") long addId,
                                           @ApiParam("手机号") @RequestParam("mobile") String mobile,
                                           @ApiParam("天润通话记录的唯一标识") @RequestParam("uniqueId") String uniqueId){

        long adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        CfReportPageEnum pageEnum = CfReportPageEnum.parse(pageType);
        if(Objects.isNull(pageEnum)){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        if((pageEnum == CfReportPageEnum.QUESTIONER && reportId <= 0) || (pageEnum == CfReportPageEnum.FUNDRAISER && reportId != 0)){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        CfReportCommunicaterDO communicaterDO = reportCommunicaterListService.queryById(addId, caseId, reportId);
        if(Objects.isNull(communicaterDO) || !communicaterDO.getMobile().equals(oldShuidiCipher.aesEncrypt(mobile)) || StringUtils.isEmpty(uniqueId)){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        //111****1111
        String masicMobile = mobile.substring(0, 3) + "****" + mobile.substring(7, 11);
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        String operation = pageEnum == CfReportPageEnum.QUESTIONER ? "质疑人沟通详情页" : "筹款方沟通详情页";
        String comment = pageEnum == CfReportPageEnum.QUESTIONER ? "质疑人" : communicaterDO.getRelationValue();
        financeApproveService.addApprove(crowdfundingInfo, operation, "联系了" + comment + masicMobile, ContextUtil.getAdminUserId());

        CfReportCallRecordDO recordDO = new CfReportCallRecordDO();
        recordDO.setCaseId(caseId);
        recordDO.setReportId(reportId);
        recordDO.setAddId(addId);
        recordDO.setMobile(oldShuidiCipher.aesEncrypt(mobile));
        recordDO.setUniqueId(uniqueId);
        recordDO.setOperatorId(adminUserId);

        int res = cfReportCallRecordService.insert(recordDO);

        if(res >= 0){
            return ResponseUtil.makeSuccess("success");
        }

        return ResponseUtil.makeResponse(AdminErrorCode.OPERATION_FAILED.getCode(), AdminErrorCode.OPERATION_FAILED.getMsg(), null);
    }

    @ApiOperation("查询质疑方或者筹款方的通话记录")
    @RequestMapping(path = "query-call-record", method = RequestMethod.POST)
    @RequiresPermission("problem:query-call-record")
    public Response<List<CfReportCallRecordDO>> queryCallRecord(@ApiParam("案例id") @RequestParam("caseId") int caseId,
                                                                @ApiParam("举报id") @RequestParam("reportId") int reportId,
                                                                @ApiParam("1:质疑方沟通页 2:筹款方沟通页") @RequestParam("pageType") int pageType,
                                                                @ApiParam("沟通列表返回联系人的id") @RequestParam("addId") long addId,
                                                                @ApiParam("手机号") @RequestParam("mobile") String mobile){

        CfReportPageEnum pageEnum = CfReportPageEnum.parse(pageType);
        if(Objects.isNull(pageEnum)){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        if((pageEnum == CfReportPageEnum.QUESTIONER && reportId <= 0) || (pageEnum == CfReportPageEnum.FUNDRAISER && reportId != 0)){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        CfReportCommunicaterDO communicaterDO = reportCommunicaterListService.queryById(addId, caseId, reportId);
        if(Objects.isNull(communicaterDO) || !communicaterDO.getMobile().equals(oldShuidiCipher.aesEncrypt(mobile))){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<CfReportCallRecordDO> recordDOS = cfReportCallRecordService.query(caseId, reportId, addId);
        if(CollectionUtils.isEmpty(recordDOS)){
            return ResponseUtil.makeSuccess(Lists.newArrayList());
        }

        List<String> uniqueIds = Lists.newArrayList();
        recordDOS.forEach(recordDO -> {
            uniqueIds.add(recordDO.getUniqueId());
        });

        Map<String, ClewCallRecordModel> recordModelMap = Maps.newHashMap();
        List<ClewCallRecordModel> recordModels = cfClewtrackFeignClient.getClewCallRecordsByUniqueIds(uniqueIds).getData();
        for (ClewCallRecordModel recordModel : recordModels){
            recordModelMap.put(recordModel.getUniqueId(), recordModel);
        }

        for (CfReportCallRecordDO recordDO : recordDOS){
            recordDO.setMobileMask(maskUtil.buildByEncryptPhone(recordDO.getMobile()));
            recordDO.setMobile(null);
            recordDO.setTotalDuration(recordModelMap.get(recordDO.getUniqueId()).getTotalDuration());
        }
        return ResponseUtil.makeSuccess(recordDOS);
    }

    @ApiOperation("在筹款方跟进记录页面查询同类问题的筹款方答案详情")
    @RequestMapping(path = "query-questioner-answer-detail-in-fundraiser", method = RequestMethod.POST)
    @RequiresPermission("problem:query-questioner-answer-detail-in-fundraiser")
    public Response<List<CfReportQuestionerAnswerDetail>> queryQuestionerAnswerDetailInFundraiser(@ApiParam("案例id") @RequestParam("caseId") int caseId,
                                                                                                  @ApiParam("cf_report_problem_label表一级问题的id") @RequestParam("firstId") int firstId,
                                                                                                  @ApiParam("cf_report_problem_label表二级问题的id") @RequestParam("secondId") int secondId){

        if(caseId <= 0 || firstId <= 0 || secondId <= 0){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<AdminReportProblemAnswerDetail> totalAnswerDetails = cfReportAnswerService.queryByType(caseId, CfReportPageEnum.QUESTIONER.getKey());
        if(CollectionUtils.isEmpty(totalAnswerDetails)){
            return ResponseUtil.makeSuccess(Lists.newArrayList());
        }

        List<CfReportQuestionerAnswerDetail> questionerAnswerDetails = Lists.newArrayList();

        for (AdminReportProblemAnswerDetail answerDetail : totalAnswerDetails){
            if(Objects.isNull(answerDetail) || StringUtils.isEmpty(answerDetail.getAnswerDetail())){
                continue;
            }

            List<AdminReportProblemAnswer> answerDetails = JSON.parseObject(answerDetail.getAnswerDetail(), new TypeReference<List<AdminReportProblemAnswer>>(){});//已检查过
            if(CollectionUtils.isEmpty(answerDetails)){
                continue;
            }


            for (AdminReportProblemAnswer answer : answerDetails){
                if(firstId == answer.getFirstId() && secondId == answer.getSecondId()){
                    Integer reportId = answerDetail.getReportId();
                    CrowdfundingReport report = adminCrowdfundingReportBiz.query(caseId, reportId);

                    CfReportQuestionerAnswerDetail questionerAnswerDetail = new CfReportQuestionerAnswerDetail();
                    questionerAnswerDetail.setMobile(shuidiCipher.decrypt(report.getEncryptContact()));
                    questionerAnswerDetail.setProblemAnswer(answer);
                    questionerAnswerDetails.add(questionerAnswerDetail);
                }
            }
        }

        return ResponseUtil.makeSuccess(questionerAnswerDetails);
    }


    @ApiOperation("该案例下最新一次保存的筹款人已沟通的沟通记录")
    @PostMapping(path = "get-last-fundraiser-follow-record")
    @RequiresPermission("problem:get-last-fundraiser-follow-record")
    public Response<List<AdminReportProblemAnswer>> getLastFundraiserFollowRecord(@RequestParam(value = "caseId")int caseId,
                                                                                  @RequestParam(defaultValue = "0") int followId){
        if (caseId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return problemService.getLastFundraiserFollowRecord(caseId, followId);
    }

}
