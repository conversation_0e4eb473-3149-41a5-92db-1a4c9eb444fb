package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidi.weixin.common.util.StringUtils;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.admin.util.admin.IntegerUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.BackgroundLogEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperationRecordEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfTagGroupTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingType;
import com.shuidihuzhu.cf.model.crowdfunding.CfCaseTag;
import com.shuidihuzhu.cf.model.crowdfunding.CfTagGroup;
import com.shuidihuzhu.cf.model.crowdfunding.CfTagItem;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by ahrievil on 2017/3/27.
 */
@Controller
@RequestMapping(path = "/admin/crowdfunding/tag")
public class CrowdfundingTagController {

    private static final Logger LOGGER = LoggerFactory.getLogger(CrowdfundingTagController.class);

    @Autowired
    private CfTagGroupBiz cfTagGroupBiz;
    @Autowired
    private CfTagItemBiz cfTagItemBiz;
    @Autowired
    private CfCaseTagBiz cfCaseTagBiz;
    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Autowired
    private CfAdminOperationRecordBiz cfAdminOperationRecordBiz;
	@Autowired
	private  SeaAccountClientV1 seaAccountClientV1;
	@Autowired
	private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;

	@RequiresPermission("tag:get-list")
    @RequestMapping(path = "get-list", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response getList(@RequestParam(value = "type", required = false) Integer type) {

        List<Integer> tagGroupTypes = Lists.newArrayList();
        tagGroupTypes.add(CfTagGroupTypeEnum.ALL.getValue());
        if(type != null) {
            if(type == CrowdfundingType.SERIOUS_ILLNESS.value()) {
                tagGroupTypes.add(CfTagGroupTypeEnum.ILLNESS.getValue());
            } else if(type == CrowdfundingType.DREAM.value()) {
                tagGroupTypes.add(CfTagGroupTypeEnum.DREAM.getValue());
            }
        }

        LOGGER.info("CrowdfundingTagController getList param is null");
        List<CfTagGroup> listWithItem = null;
        List<CfTagItem> cfTagItems = null;
        try {
            listWithItem = cfTagGroupBiz.getList(tagGroupTypes);
        } catch (Exception e) {
            LOGGER.error("CrowdfundingTagController getList listWithItem error!", e);
        }
        try {
            cfTagItems = cfTagItemBiz.selectAllZore();
        } catch (Exception e) {
            LOGGER.error("CrowdfundingTagController getList selectAllZore error!", e);
        }
        Map<String, Object> result = Maps.newHashMap();
        result.put("list", listWithItem);
        result.put("otherTags", cfTagItems);
        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("tag:add-to-case")
    @RequestMapping(path = "add-to-case", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response addToCase(String infoId, String tags) {
	    int userId = ContextUtil.getAdminUserId();
        LOGGER.info("CrowdfundingTagController addToCase userId:{};infoId:{};tags:{}", userId, infoId, tags);
        if (infoId == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_OPERATION_FAILED);
        }
        CrowdfundingInfo fundingInfoById = crowdfundingDelegate.getCrowdfundingInfoByInfoId(infoId);
        if (fundingInfoById == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        int crowdfundingId = fundingInfoById.getId();
        String[] tagArray = tags.split(",");
        List<CfCaseTag> tagList = Lists.newArrayList();
        if (StringUtils.isBlank(tags)) {
            cfCaseTagBiz.deleteTags(crowdfundingId);
        } else if (tagArray.length != 0) {
            for (int i = 0;i < tagArray.length; i++) {
                CfCaseTag cfCaseTag = new CfCaseTag();
                cfCaseTag.setInfoId(crowdfundingId);
                cfCaseTag.setTagId(IntegerUtil.parseInt(tagArray[i]));
                tagList.add(cfCaseTag);
            }
            try {
                cfCaseTagBiz.deleteTags(crowdfundingId);
                cfCaseTagBiz.addCaseTags(tagList);
            } catch (Exception e) {
                LOGGER.error("CrowdfundingTagController addToCase addCaseTags error!", e);
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_OPERATION_FAILED);
            }
        }
        try {
			AdminUserAccountModel adminUserAccountModel = this.seaAccountClientV1.getValidUserAccountById(userId).getResult();
			String userName = adminUserAccountModel.getName();
			this.crowdfundingOperationDelegate.saveCfOperatingRecord(infoId, userId, userName, CfOperatingRecordEnum.Type.ADD_TAG,
					CfOperatingRecordEnum.Role.OPERATOR);
		} catch (Exception e) {
			LOGGER.error("", e);
		}
        cfAdminOperationRecordBiz.addOneOperationRecord(fundingInfoById.getInfoId(), userId, CfOperationRecordEnum.ADD_CASE_TAG.value(),"");
        LOGGER.info("客服后台log：comment operationTime:{};operator:{};operationReason:{};operationType:{};infoId:{};status:{};dataStatus:{};infoStatus:{}",
                com.shuidihuzhu.common.web.util.DateUtil.formatDateTime(new Date()),userId, BackgroundLogEnum.REMARK.getMessage(),"评论",infoId,fundingInfoById.getStatus(),
                fundingInfoById.getDataStatus(),fundingInfoById.getEndTime().before(new Date())?"结束":"未结束");
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("tag:add-tag")
    @RequestMapping(path = "add-tag", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response addTag(String otherTags) {
        LOGGER.info("CrowdfundingTagController addToCase otherTags:{}", otherTags);
        if (StringUtils.isBlank(otherTags)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_OPERATION_FAILED);
        }
        HashMap<String, Object> result = Maps.newHashMap();
        if (StringUtils.isNotBlank(otherTags)) {
            String[] otherTa = otherTags.split(",");
            HashSet<String> hashSet = Sets.newHashSet(Arrays.asList(otherTa));
            List<String> items = new ArrayList<>(hashSet);
            List<String> theSame = Lists.newArrayList();
            List<CfTagItem> byTagName = cfTagItemBiz.getByTagName(items);
            for (CfTagItem cfTagItem : byTagName) {
                String trim = cfTagItem.getTagName();
                if (items.contains(trim)) {
                    items.remove(trim);
                    theSame.add(trim);
                }
            }
            if (theSame.size() > 0) {
                String msg = "添加失败重复标签：" + String.join(",", theSame);
                return NewResponseUtil.makeResponse(AdminErrorCode.ADMIN_TAG_EXISTS.getCode(),
                        msg, null);
            }
            List<CfTagItem> cfTagItemList = Lists.newArrayList();
            try {
                if (items.size() != 0) {
                    for (String tag : items) {
                        CfTagItem cfTagItem = new CfTagItem();
                        cfTagItem.setTagName(tag);
                        cfTagItemBiz.insertOne(cfTagItem);
                        cfTagItemList.add(cfTagItem);
                    }
                    List<Integer> collect = cfTagItemList.stream().mapToInt(cfTagItem -> cfTagItem.getId()).boxed().collect(
                            Collectors.toList());
                    result.put("otherIds", collect);
                }
            } catch (Exception e) {
                LOGGER.error("CrowdfundingTagController addToCase error!", e);
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_OPERATION_FAILED);
            }
        }
        return NewResponseUtil.makeSuccess(result);
    }

}
