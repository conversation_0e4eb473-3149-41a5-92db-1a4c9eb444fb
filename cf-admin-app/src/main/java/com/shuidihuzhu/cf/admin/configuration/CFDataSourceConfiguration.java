package com.shuidihuzhu.cf.admin.configuration;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

/**
 * Created by zhouyou on 2017/12/10.
 */
@Configuration
public class CFDataSourceConfiguration {

    @Bean("crowdfundingDataSource")
    @ConfigurationProperties("spring.datasource.druid.cf-admin-api-cf.cf-admin-api")
    public DataSource crowdfundingDataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean("crowdfundingSlaveDataSource")
    @ConfigurationProperties("spring.datasource.druid.cf-admin-api-cf-slave.cf-admin-api")
    public DataSource crowdfundingSlaveDataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean("cfSlave2DataSource")
    @ConfigurationProperties("spring.datasource.druid.cf-admin-api-cf-slave2.cf-admin-api")
    public DataSource cfSlave2DataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean("shuidiCfAdminDataSource")
    @ConfigurationProperties("spring.datasource.druid.cf-admin-api-cf-admin.cf-admin-api")
    public DataSource shuidiCfAdminDataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean("shuidiCfAdminSlaveDataSource")
    @ConfigurationProperties("spring.datasource.druid.cf-admin-api-cf-admin-slave.cf-admin-api")
    public DataSource shuidiCfAdminSlaveDataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean("statCfDataSource")
    @ConfigurationProperties("spring.datasource.druid.cf-admin-api-stat-cf-stat.cf-admin-api")
    public DataSource statCfDataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean("statCfSlaveDataSource")
    @ConfigurationProperties("spring.datasource.druid.shuidi-cf-stat-slave.cf-admin-api")
    public DataSource statCfSlaveDataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean("shuidiFrameDataSource")
    @ConfigurationProperties("spring.datasource.druid.cf-admin-api-frame.cf-admin-api")
    public DataSource shuidiFrameDataSource(){
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "shuidiCfTdDataSourceJdbcTemplate")
    public JdbcTemplate shuidiCfTdDataSourceJdbcTemplate(@Qualifier("shuidiCfTdDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    @Bean(CfDataSource.SHUIDI_CF_COMMENT)
    @ConfigurationProperties("spring.datasource.druid.cf-api-cf-comment.cf-admin-api")
    public DataSource cfCommentDataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean("datasource-cf-api-cf-comment-slave")
    @ConfigurationProperties("spring.datasource.druid.cf-comment-slave.cf-admin-api")
    public DataSource cfCommentSlaveDataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean("shuidiCfTdDataSource")
    @ConfigurationProperties("spring.datasource.druid.td-shuidi-crowdfunding.cf-admin-api")
    public DataSource shuidiCfTdDataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean("shuidiCfOrderTdDataSource")
    @ConfigurationProperties("spring.datasource.druid.td-shuidi-cf-order.cf-admin-api")
    public DataSource shuidiCfOrderTdDataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean("shuidiCfSpiderTdDataSource")
    @ConfigurationProperties("spring.datasource.druid.td-shuidi-cf-spider.cf-admin-api")
    public DataSource shuidiCfSpiderTdDataSource() {
        return DruidDataSourceBuilder.create().build();
    }


    @Bean("tidbCommonShuidiFundraisingDataSource")
    @ConfigurationProperties("spring.datasource.druid.tidb-common-shuidi-fundraising.cf-admin-api")
    public DataSource tidbCommonShuidiFundraisingDataSource() {
        return DruidDataSourceBuilder.create().build();
    }

}
