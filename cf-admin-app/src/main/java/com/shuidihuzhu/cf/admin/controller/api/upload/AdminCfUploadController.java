package com.shuidihuzhu.cf.admin.controller.api.upload;

import com.google.common.collect.Maps;
import com.shuidihuzhu.account.verify.client.menu.IdCardVerifyResultEnum;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.service.apollo.ApolloService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.admin.util.UploadUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingIdCaseStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingIdCase;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.crowdfunding.IdCardVerifyService;
import com.shuidihuzhu.account.verify.client.model.VerifyIdcardVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.Map;

/**
 * Created by ahrievil on 2017/5/2.
 */
@Slf4j
@RestController
@RefreshScope
@RequestMapping(path = "/admin/crowdfunding")
public class AdminCfUploadController {

    @Autowired
    private UploadUtil uploadUtil;
    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Resource
    private IdCardVerifyService idCardVerifyService;
    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Autowired
    private ApolloService apolloService;

    @RequiresPermission("cf-upload:upload")
    @RequestMapping(path = "upload", method = RequestMethod.POST)
    public Response upload(MultipartHttpServletRequest request) {
        Map<String, String> urlMap = Maps.newHashMap();
        Iterator<String> iter = request.getFileNames();
        while (iter.hasNext()) {
            String fileName = iter.next();
            MultipartFile file = request.getFile(fileName);
            if (file != null) {
                urlMap.put(fileName, apolloService.getCosSwitch() ? uploadUtil.doCosUploadV2(file) : uploadUtil.doCosUpload(file));
            }
        }
        log.info("AdminCfUploadController upload urlMap:{}", urlMap);
        return NewResponseUtil.makeSuccess(urlMap);
    }

    @RequiresPermission("cf-upload:upload-v2")
    @PostMapping("upload-v2")
    public Response uploadV2(MultipartHttpServletRequest request) {
        Map<String, String> urlMap = Maps.newHashMap();
        Iterator<String> iter = request.getFileNames();
        while (iter.hasNext()) {
            String fileName = iter.next();
            MultipartFile file = request.getFile(fileName);
            if (file != null) {
                urlMap.put(fileName, apolloService.getCosSwitch() ? uploadUtil.doCosUploadV2(file) : uploadUtil.doCosUpload(file));
            }
        }
        log.info("AdminCfUploadController uploadV2 urlMap:{}", urlMap);
        return NewResponseUtil.makeSuccess(urlMap);
    }

    @RequiresPermission("cf-upload:id-verify")
    @RequestMapping(value = "id-verify", method = RequestMethod.POST)
    public Response doIdVerify(@RequestParam("infoUuid") String infoUuid){

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if(crowdfundingInfo == null){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        CrowdfundingIdCase crowdfundingIdCase = crowdfundingDelegate.getCrowdfundingIdCaseByInfoId(crowdfundingInfo.getId());
        if(crowdfundingIdCase == null){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if(crowdfundingIdCase.getStatus() == CrowdfundingIdCaseStatusEnum.VERIFY_SUCCESS.getCode()){
            return Response.OK;
        }

        VerifyIdcardVO verifyIdcardVO = idCardVerifyService.verfiy(crowdfundingIdCase.getName(), crowdfundingIdCase.getIdCardVo(), UserRelTypeEnum.SELF, crowdfundingInfo.getUserId());
        if(verifyIdcardVO.getCode() == IdCardVerifyResultEnum.MATCH){

            crowdfundingDelegate.updateCrowdfundingIdCaseStatus(crowdfundingInfo.getId(), CrowdfundingIdCaseStatusEnum.VERIFY_SUCCESS);

            crowdfundingDelegate.updateCrowdfundingInfoStatusByInfoId(crowdfundingInfo.getInfoId(), CrowdfundingInfoDataStatusTypeEnum.ID_VERIFY.getCode(), CrowdfundingInfoStatusEnum.PASSED);

            return Response.OK;
        } else {
            return NewResponseUtil.makeError(AdminErrorCode.OPERATION_FAILED);
        }

    }
}
