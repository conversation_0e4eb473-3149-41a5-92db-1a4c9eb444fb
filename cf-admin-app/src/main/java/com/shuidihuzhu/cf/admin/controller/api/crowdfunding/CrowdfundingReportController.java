package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfRaiseMaterialClient;
import com.shuidihuzhu.cf.client.material.model.RaiseBasicInfoModel;
import com.shuidihuzhu.cf.constants.admin.GeneralConstant;
import com.shuidihuzhu.cf.delegate.EncryptDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.delegate.service.UserThirdServiceBiz;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.report.AdminCfReportLabelRiskType;
import com.shuidihuzhu.cf.model.crowdfunding.report.AdminCfReportRiskCheck;
import com.shuidihuzhu.cf.model.crowdfunding.report.AdminCfReportRiskTagLabel;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditSearchService;
import com.shuidihuzhu.cf.util.crowdfunding.CrowdfundingUtil;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.admin.util.ParamTimeRangeHandler;
import com.shuidihuzhu.cf.admin.util.lock.RedisDistributedLock;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderBiz;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.biz.crowdfunding.report.ReportCallCommentRecordBiz;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.constants.admin.PageCons;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminWorkOrderReportDao;
import com.shuidihuzhu.cf.dao.crowdfunding.ReportOperateRecordDAO;
import com.shuidihuzhu.cf.delegate.OrganizationDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.other.IWeiXinDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.enums.*;
import com.shuidihuzhu.cf.enums.ReportSourceEnum;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.approve.ApproveSourceTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.admin.CfHospitalAuditInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.*;
import com.shuidihuzhu.cf.model.report.AdminCfReportAddTrustVo;
import com.shuidihuzhu.cf.model.report.ReportCallCommentRecord;
import com.shuidihuzhu.cf.service.FinanceApproveService;
import com.shuidihuzhu.cf.service.admin.CfHospitalAuditService;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.crowdfunding.report.CfReportService;
import com.shuidihuzhu.cf.service.report.CfReportCredibleInfoService;
import com.shuidihuzhu.cf.vo.approve.CfHospitalAuditInfoNew;
import com.shuidihuzhu.cf.vo.approve.CfHospitalAuditInfoVO;
import com.shuidihuzhu.cf.vo.approve.HospitalAuditListVO;
import com.shuidihuzhu.cf.vo.approve.HospitalAuditShowVO;
import com.shuidihuzhu.cf.vo.crowdfunding.CrowdfundingApproveCommentVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CrowdfundingReportVo;
import com.shuidihuzhu.cf.vo.report.CfSendProveInfoRecord;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.baseservice.msg.v2.MsgClientV2;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.IdcardInfoExtractor;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.BusinessForwardCase;
import com.shuidihuzhu.msg.model.PushRecord;
import com.shuidihuzhu.msg.util.DateUtil;
import com.shuidihuzhu.msg.vo.rpc.MsgRecord;
import com.shuidihuzhu.msg.vo.rpc.MsgRecordBatch;
import com.shuidihuzhu.pf.common.v2.model.pagehelper.PaginationListVO;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by ahrievil on 2017/1/22.
 */
@Controller
@RequestMapping(path = "/admin/crowdfunding/report")
@Slf4j
@RefreshScope
public class CrowdfundingReportController {

    private static final Logger LOGGER = LoggerFactory.getLogger(CrowdfundingReportController.class);

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Resource
    private SeaAccountClientV1 seaAccountClientV1;
    @Autowired
    private AdminCrowdfundingReportBiz adminCrowdfundingReportBiz;
    @Autowired
    private AdminCrowdfundingOperationBiz adminCrowdfundingOperationBiz;
    @Autowired
    private CfReportFollowCommentBiz cfReportFollowCommentBiz;
    @Autowired
    private AdminCfReportAddTrustBiz adminCfReportAddTrustBiz;
    @Autowired
    private IRiskDelegate riskDelegate;
    @Resource
    private UserThirdServiceBiz userThirdServiceBiz;
    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Autowired
    private AdminCfHospitalAuditBiz cfHospitalAuditBiz;
    @Autowired
    private AdminWorkOrderReportBiz adminWorkOrderReportBiz;
    @Autowired
    private AdminWrokOrderReportRecordBiz adminWrokOrderReportRecordBiz;
    @Autowired
    private AdminWorkOrderBiz adminWorkOrderBiz;
    @Autowired
    private AdminWorkOrderRecordBiz adminWorkOrderRecordBiz;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Autowired
    private CfReportService cfReportService;

    @Autowired
    private CfHospitalAuditService cfHospitalAuditService;

    @Autowired
    private ApproveRemarkOldService approveRemarkOldService;

    @Autowired
    private AdminWorkOrderReportDao adminWorkOrderReportDao;

    @Autowired
    private OrganizationDelegate organizationDelegate;

    @Autowired
    private ReportOperateRecordDAO reportOperateRecordDAO;

    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private IAdminCredibleInfoService adminCredibleInfoService;

    @Autowired
    private IAdminCredibleEditInfoService adminCredibleEditInfoService;

    @Autowired
    private CfReportCommitmentInfoBiz cfReportCommitmentInfoBiz;

    @Autowired
    private FinanceApproveService financeApproveService;

    @Resource(name = "cf2RedissonHandler")
    private RedissonHandler cf2RedissonHandler;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private AdminCfHospitalAuditBiz adminCfHospitalAuditBiz;

    @Autowired
    private CfReportAddTrustSnapshotBiz cfReportAddTrustSnapshotBiz;

    @Autowired
    private IReportCommunicaterListService reportCommunicaterListService;

    @Autowired
    private ReportCallCommentRecordBiz reportCallCommentRecordBiz;

    @Autowired
    private CfReportCredibleInfoService cfReportCredibleInfoService;
    @Resource
    private MsgClientV2Service msgClientV2Service;
    @Resource
    private InitialAuditSearchService initialAuditSearchService;
    @Resource
    private Analytics analytics;
    @Resource
    private CfRaiseMaterialClient cfRaiseMaterialClient;
    @Resource
    private IRiskDelegate firstApproveBiz;
    @Autowired
    private MaskUtil maskUtil;

    @RequiresPermission("report:mark-report")
    @ResponseBody
    @RequestMapping(path = "/mark-report", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response<String> markReport(@ApiParam("案例id") @RequestParam(name = "caseId") int caseId,
                                       @ApiParam("举报内容") @RequestParam(name = "content") String content,
                                       @ApiParam("质疑人姓名") @RequestParam(name = "reporterName", required = false) String reporterName,
                                       @ApiParam("质疑人电话") @RequestParam(name = "reporterMobile", required = false) String reporterMobile,
                                       @ApiParam("举报类型 多个举报类型用英文逗号分割") @RequestParam(name = "reportTypes") String reportTypes,
                                       @ApiParam("是否为用户实名举报") @RequestParam(name = "realNameReport", required = false) boolean realNameReport,
                                       @ApiParam("质疑人身份证号") @RequestParam(name = "reporterIdentity", required = false, defaultValue = "") String reporterIdentity,
                                       @ApiParam("举报渠道") @RequestParam(name = "reportChannel", defaultValue = "0") int reportChannel,
                                       @ApiParam("其他举报渠道") @RequestParam(name = "reportChannelOther", defaultValue = "") String reportChannelOther,
                                       @ApiParam("举报图片") @RequestParam(name = "imageUrls", defaultValue = "") String imageUrls) {

        int adminUserId = ContextUtil.getAdminUserId();
        if (adminUserId <= 0) {
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        String lockName = "mark-report-" + adminUserId + caseId;
        RLock rLock = null;

        try {

            rLock = cf2RedissonHandler.getLock(lockName);
            if (!rLock.tryLock()) {
                return NewResponseUtil.makeResponse(AdminErrorCode.REPORT_SYSTEM_REDIS_LOCK_ERROR.getCode(), AdminErrorCode.REPORT_SYSTEM_REDIS_LOCK_ERROR.getMsg(), null);
            }

            if (StringUtils.isEmpty(content)) {
                return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
            }

            //reportTypes 格式： 1,2 | 3,4 | 5,6   1 3 5是一级类型 2 4 6是二级类型
            if (StringUtils.isEmpty(reportTypes)) {
                return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
            }

            CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
            if (Objects.isNull(crowdfundingInfo)) {
                return NewResponseUtil.makeError(ErrorCode.CF_NOT_FOUND);
            }

            if (realNameReport && (StringUtils.isEmpty(reporterName) || StringUtils.isEmpty(reporterIdentity))) {
                return NewResponseUtil.makeError(CfErrorCode.REPOERT_REAL_NAME_VALIDATE_ERROR);
            }
            cfReportService.markReport(caseId, content, reporterName, reporterMobile, reportTypes, crowdfundingInfo,
                    realNameReport, reporterIdentity, adminUserId, reportChannel, reportChannelOther, imageUrls,
                    ReportSourceEnum.ADMIN,null);
            return NewResponseUtil.makeSuccess("已举报");

        } catch (Exception e) {
            log.error("mark-report exeception.", e);
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
        } finally {
            //isLocked():查询lock 是否被任意线程所持有。
            //isHeldByCurrentThread():查询当前线程是否保持此锁定
            if (rLock.isLocked() && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }

    @RequiresPermission("report:query-user-org")
    @ResponseBody
    @RequestMapping(path = "/query-user-org", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response<SimplePersonnelVo> queryUserOrg(@ApiParam("sea后台用户userId") @RequestParam(name = "adminUserId") int adminUserId) {
        if (adminUserId <= 0) {
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        String org = organizationDelegate.getSimpleOrganization(adminUserId);
        AdminUserAccountModel adminUserInfo = seaAccountClientV1.getValidUserAccountById(adminUserId).getResult();

        SimplePersonnelVo simplePersonnelVo = new SimplePersonnelVo();
        simplePersonnelVo.setUserId(adminUserId);
        simplePersonnelVo.setOrg(org);
        simplePersonnelVo.setUserName(Objects.nonNull(adminUserInfo) ? adminUserInfo.getName() : "");
        simplePersonnelVo.setMis(Objects.nonNull(adminUserInfo) ? adminUserInfo.getMis() : "");

        return ResponseUtil.makeSuccess(simplePersonnelVo);
    }

    @RequiresPermission("report:query-report-work-order-operate-record")
    @ResponseBody
    @RequestMapping(path = "/query-report-work-order-operate-record", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response<List<ReportOperateRecordDO>> queryReportWorkOrderOperateRecord(@ApiParam("举报工单id") @RequestParam(name = "reportWorkOrderId") long reportWorkOrderId) {

        int adminUserId = ContextUtil.getAdminUserId();
        if (adminUserId <= 0) {
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        List<AdminWorkOrderReport> reportWorkOrders = adminWorkOrderReportDao.getByWorkOrderIds(Lists.newArrayList(reportWorkOrderId));
        if (CollectionUtils.isEmpty(reportWorkOrders) || Objects.isNull(reportWorkOrders.get(0))) {
            return ResponseUtil.makeResponse(AdminErrorCode.WORK_ORDER_NOT_FOUND.getCode(), AdminErrorCode.WORK_ORDER_NOT_FOUND.getMsg(), "");
        }

        List<ReportOperateRecordDO> recordDOS = reportOperateRecordDAO.queryByWorkOrderId(reportWorkOrderId);

        return ResponseUtil.makeSuccess(recordDOS);
    }

    @RequiresPermission("report:get-detail-report-list")
    @RequestMapping(path = "get-detail-report-list", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response getDetailReportList(@RequestParam(value = "infoId", defaultValue = "0") int infoId,
                                        @RequestParam(required = false, name = "pageSize", defaultValue = "" + PageCons.MAX_PAGE_SIZE) Integer pageSize,
                                        @RequestParam(required = false, name = "current", defaultValue = "1") Integer current) {
        if (infoId == 0) {
            LOGGER.info("infoid is null");
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        Map<String, Object> result = Maps.newHashMap();
        List<CrowdfundingReport> crowdfundingReportList = this.adminCrowdfundingReportBiz.getListByInfoIdAndPage(pageSize, current, infoId);
        result.put("pagination", PageUtil.transform2PageMap(crowdfundingReportList));
        if (CollectionUtils.isEmpty(crowdfundingReportList)) {
            return NewResponseUtil.makeSuccess(null);
        }
        //获取CrowdfundingReportVo列表
        List<CrowdfundingReportVo> crowdfundingReportVoList = adminCrowdfundingReportBiz.getCrowdfundingReportVo(
                crowdfundingReportList, null, 0);
        result.put("reportList", crowdfundingReportVoList);
        List<CfReportFollowComment> cfReportFollowCommentList = this.cfReportFollowCommentBiz.getCommentListByInfoId(infoId);
        if (CollectionUtils.isEmpty(cfReportFollowCommentList)) {
            result.put("comment", null);
        }
        result.put("comment", cfReportFollowCommentList);

        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("report:query-no-deal-report-workorder")
    @ResponseBody
    @RequestMapping(path = "/query-no-deal-report-workorder", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response<AdminWorkOrderReport> queryNoDealReportWorkOrder(@RequestParam(value = "caseId") int caseId) {
        int adminUserId = ContextUtil.getAdminUserId();
        if (adminUserId <= 0) {
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        CfInfoSimpleModel cfInfoSimpleModel = crowdfundingDelegate.getCfInfoSimpleModelById(caseId);
        if (Objects.isNull(cfInfoSimpleModel)) {
            return ResponseUtil.makeError(ErrorCode.CF_NOT_FOUND);
        }

        AdminWorkOrderReport workOrderReport = adminWorkOrderReportBiz.getAdminWorkOrderReportByCaseId(caseId);
        if (Objects.isNull(workOrderReport)) {
            return ResponseUtil.makeSuccess(null);
        }

        int dealResult = workOrderReport.getDealResult();
        if (dealResult != AdminWorkOrderReportConst.DealResult.DEAL_COMPLETE.getCode() && dealResult != AdminWorkOrderReportConst.DealResult.NO_DEAL.getCode()) {
            return ResponseUtil.makeSuccess(workOrderReport);
        }

        return ResponseUtil.makeSuccess(null);
    }

    //关于举报增信的流程
    @RequiresPermission("report:report-add-trust")
    @RequestMapping(path = "report-add-trust", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response reportAddTrust(@RequestParam(name = "infoId", defaultValue = "0") Integer infoId,
                                   @RequestParam(name = "operatorContent") String operatorContent,
                                   @RequestParam(name = "auditStatus") Integer auditStatus,
                                   @RequestParam(name = "mustAdd", required = false) String mustAdd,
                                   @ApiParam("1:老的举报处理详情页 2:新的举报处理详情页") @RequestParam(name = "pageSource", required = false, defaultValue = "1") int pageSource,
                                   @ApiParam("是否下发承诺书") @RequestParam(name = "issuedCommitment", defaultValue = "false") boolean issuedCommitment) {
        Integer userId = ContextUtil.getAdminUserId();
        //不允许下发增信，改用新的代录入
        if (userId >= 0 && auditStatus.equals(AddTrustAuditStatusEnum.UN_SUBMITTED.getCode())) {
            return NewResponseUtil.makeFail("无法下发增信说明，请下发举报代录入");
        }
        LOGGER.info("CrowdfundingReportController reportAddTrust infoId:{}, userId:{}, operatorContent:{},pageSource:{}", infoId, userId, operatorContent, pageSource);
        if (infoId == null || userId == null || (StringUtils.isBlank(operatorContent) && !auditStatus.equals(AddTrustAuditStatusEnum.UN_SUBMITTED.getCode()) )) {
            LOGGER.info("param is null!!!");
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        if (pageSource != BooleanEnum.TRUE_FLAG.getValue() && pageSource != BooleanEnum.FALSE_FLAG.getValue()) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(infoId);
        if (crowdfundingInfo == null) {
            LOGGER.info("infoid is error ");
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        CrowdfundingOperation crowdfundingOperation = adminCrowdfundingOperationBiz.getByInfoId(crowdfundingInfo.getInfoId());
        if (crowdfundingOperation == null) {
            LOGGER.info("crowdfundingOperation is null ");
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        if (pageSource == BooleanEnum.TRUE_FLAG.getValue() && crowdfundingOperation.getReportStatus() != CaseReportStatusEnum.HANDLEING.getValue()) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_REPORT_STATUS_ERROR);
        }

        Response<WorkOrderVO> workOrderRes = cfWorkOrderClient.getLastWorkOrderByTypes(infoId, WorkOrderType.REPORT_TYPES);
        if (pageSource == BooleanEnum.FALSE_FLAG.getValue()) {
            if (Objects.isNull(workOrderRes.getData())) {
                return NewResponseUtil.makeError(AdminErrorCode.CF_REPORT_WORK_STATUS_ERROR);
            }
            if (workOrderRes.getData().getOrderType() != WorkOrderType.lost_report.getType()
                    && workOrderRes.getData().getHandleResult() != HandleResultEnum.reach_agree.getType()) {
                return NewResponseUtil.makeError(AdminErrorCode.CF_REPORT_WORK_STATUS_ERROR);
            }
        }

        AdminUserAccountModel userAccount = this.seaAccountClientV1.getValidUserAccountById(userId).getResult();
        if (userAccount == null) {
            return NewResponseUtil.makeError(AdminErrorCode.ADMIN_ACCOUNT_NOT_EXISTS);
        }
        String comment = "";
        if (auditStatus.equals(AddTrustAuditStatusEnum.UN_SUBMITTED.getCode())) {
            //必须添加时  直接删除原有增信
            if ("must".equals(mustAdd)) {
                adminCfReportAddTrustBiz.delete(crowdfundingInfo.getInfoId());
            } else {
                //获取增信信息并进行判断此时能不能下发
                CfReportAddTrust cfReportAddTrust = adminCfReportAddTrustBiz.getByInfoUuid(crowdfundingInfo.getInfoId());
                if (cfReportAddTrust != null && (cfReportAddTrust.getAuditStatus() == AddTrustAuditStatusEnum.UN_SUBMITTED.getCode()
                        || cfReportAddTrust.getAuditStatus() == AddTrustAuditStatusEnum.REJECTED.getCode()
                        || cfReportAddTrust.getAuditStatus() == AddTrustAuditStatusEnum.SUBMITTED.getCode())) {
                    return NewResponseUtil.makeError(AdminErrorCode.ADMIN_IN_ADD_TRUST);
                }
            }
            //更新crowdfundingOperation
            crowdfundingOperation.setOperateTime(new Timestamp(System.currentTimeMillis()));
            crowdfundingOperation.setOperatorId(userId);
            comment = CaseReportFollowDealStatusEnum.ADD_TRUST.getWords() + ":" + operatorContent;
            crowdfundingOperation.setReason(comment);
            crowdfundingOperation.setReportStatus(CaseReportStatusEnum.HANDLEING.getValue());
            crowdfundingOperation.setFollowType(CaseReportFollowDealStatusEnum.ADD_TRUST.getValue());
            adminCrowdfundingOperationBiz.update(crowdfundingOperation);
            //添加跟进备注
            cfReportFollowCommentBiz.save(userId, comment, CaseReportFollowDealStatusEnum.ADD_TRUST.getValue(), infoId, userAccount.getName());
            //添加下发信息  审核状态未填写
            CfOperatingRecord cfOperatingRecord = riskDelegate.before(crowdfundingInfo.getInfoId(), userId, userAccount.getName(),
                    CfOperatingRecordEnum.Type.SEND_ADD_TRUST, CfOperatingRecordEnum.Role.OPERATOR, null);

            //下发增信信息
            long subId = adminCfReportAddTrustBiz.save(crowdfundingInfo.getInfoId(), AddTrustAuditStatusEnum.UN_SUBMITTED.getCode(),
                    operatorContent, "", "", issuedCommitment);
            //判断是否下发承诺书
            this.issuedCommitment(issuedCommitment, subId);
            //根据下发的增信信息生成新举报处理页面的可信信息列表
            adminCredibleInfoService.insertOne(crowdfundingInfo.getId(), crowdfundingInfo.getUserId(), subId, CredibleTypeEnum.SUPPLY_VERFIFY.getKey(),
                    AddTrustAuditStatusEnum.UN_SUBMITTED.getCode(), userId);

            financeApproveService.addApprove(crowdfundingInfo, "补充证明信息", "发送了补充证明信息", userId);

            riskDelegate.after(cfOperatingRecord, null);
            //更改工单增信动作
            updateWorkOrderFollowType(infoId, userId, AdminWorkOrderReportConst.FollowType.SEND_ADD_CREDIT.getCode());

            commonOperationRecordClient.create()
                    .buildBasicPlatform(crowdfundingInfo.getId(), userId, OperationActionTypeEnum.SEND_ADDITIONAL_CHANNEL)
                    .save();
            return NewResponseUtil.makeSuccess(Map.of("trustId",subId));
        } else if (auditStatus.equals(AddTrustAuditStatusEnum.REJECTED.getCode())) {
            //资料被驳回
            CfReportAddTrust cfReportAddTrust = adminCfReportAddTrustBiz.getByInfoUuid(crowdfundingInfo.getInfoId());
            if (cfReportAddTrust == null) {
                LOGGER.info("cfReportAddTrust is null! infoId is :{}", infoId);
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }
            if (cfReportAddTrust.getAuditStatus() != AddTrustAuditStatusEnum.SUBMITTED.getCode()) {
                return NewResponseUtil.makeError(AdminErrorCode.ADD_TRUST_STATUS_ERROR);
            }
            cfReportAddTrust.setAuditStatus(AddTrustAuditStatusEnum.REJECTED.getCode());
            cfReportAddTrust.setOperatorContent(operatorContent);
            //修改最新进展的内容
            comment = CaseReportFollowDealStatusEnum.ADD_TRUST.getWords() + "驳回:" + operatorContent;
            crowdfundingOperation.setReason(comment);
            crowdfundingOperation.setOperateTime(new Timestamp(System.currentTimeMillis()));
            crowdfundingOperation.setOperatorId(userId);
            adminCrowdfundingOperationBiz.update(crowdfundingOperation);
            CfOperatingRecord cfOperatingRecord = riskDelegate.before(crowdfundingInfo.getInfoId(), userId,
                    userAccount.getName(), CfOperatingRecordEnum.Type.REFUSE_ADD_TRUST,
                    CfOperatingRecordEnum.Role.OPERATOR, null);
            adminCfReportAddTrustBiz.update(cfReportAddTrust);
            //添加审核快照
            cfReportAddTrustSnapshotBiz.addSnapshot(cfReportAddTrust);
            riskDelegate.after(cfOperatingRecord, null);
            //添加跟进备注
            cfReportFollowCommentBiz.save(userId, comment, CaseReportFollowDealStatusEnum.ADD_TRUST.getValue(), infoId, userAccount.getName());
            //更改工单增信动作
            updateWorkOrderFollowType(infoId, userId, AdminWorkOrderReportConst.FollowType.REJECT_ADD_CREDIT.getCode());
            financeApproveService.addApprove(crowdfundingInfo, "补充证明信息", "审核驳回,原因:" + operatorContent, userId);

            commonOperationRecordClient.create()
                    .buildBasicPlatform(crowdfundingInfo.getId(), userId, OperationActionTypeEnum.REFUSE_ADDITIONAL_INFO)
                    .save();

            return NewResponseUtil.makeSuccess(Map.of("trustId",cfReportAddTrust.getId()));
        } else if (auditStatus.equals(AddTrustAuditStatusEnum.PASSED.getCode())) {
            //审核通过  自动发布动态
            CfReportAddTrust cfReportAddTrust = adminCfReportAddTrustBiz.getByInfoUuid(crowdfundingInfo.getInfoId());
            if (cfReportAddTrust == null) {
                LOGGER.info("cfReportAddTrust is null! infoId is :{}", infoId);
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }
            if (cfReportAddTrust.getAuditStatus() != AddTrustAuditStatusEnum.SUBMITTED.getCode()) {
                return NewResponseUtil.makeError(AdminErrorCode.ADD_TRUST_STATUS_ERROR);
            }
            //更新最新处理进展
            //comment = CaseReportFollowDealStatusEnum.ADD_TRUST.getWords() + "审核通过并发布动态";
            //crowdfundingOperation.setReason(comment);
            //crowdfundingOperation.setOperateTime(new Timestamp(System.currentTimeMillis()));
            //crowdfundingOperation.setOperatorId(userId);
            //crowdfundingOperation.setReportStatus(CaseReportStatusEnum.FINISH.getValue());
            //crowdfundingOperation.setFollowType(CaseReportFollowDealStatusEnum.NO_HANDLE.getValue());
            //adminCrowdfundingOperationBiz.update(crowdfundingOperation);
            ////查询出影响的举报
            //List<CrowdfundingReport> reportList = adminCrowdfundingReportBiz.getListByInfoId(infoId);
            //List<CrowdfundingReport> handingReportList = Lists.newArrayList();
            //List<Integer> handlingReportIds = Lists.newArrayList();
            //if (!CollectionUtils.isEmpty(reportList)) {
            //    handingReportList = reportList.stream().filter(crowdfundingReport -> crowdfundingReport.getDealStatus() == CaseReportDealStatus.HANDLEING.getValue()).
            //            collect(Collectors.toList());
            //    handlingReportIds = handingReportList.stream().map(CrowdfundingReport::getId).collect(Collectors.toList());
            //    List<Integer> noHandlingReportIds = reportList.stream().filter(crowdfundingReport -> crowdfundingReport.getDealStatus() == CaseReportDealStatus.NO_HANDLE.getValue())
            //            .map(CrowdfundingReport::getId).collect(Collectors.toList());
            //    // 批量更新举报的处理状态
            //    adminCrowdfundingReportBiz.updateReportListDealStatus(handlingReportIds, CaseReportDealStatus.FINISH, userId);
            //    //将未处理的举报  案例跟进类型重置
            //    adminCrowdfundingReportBiz.updateReportListFollowStatus(noHandlingReportIds, CaseReportFollowStatusEnum.NO_HANDLE);
            //}
            ////处理案例举报状态
            //adminApproveService.changeCaseReportStatus(crowdfundingInfo);
            //更新增信状态添加镜像
            //CfOperatingRecord cfOperatingRecord = riskDelegate.before(crowdfundingInfo.getInfoId(), userId, userAccount.getName(), CfOperatingRecordEnum.Type.PASS_ADD_TRUST,
            //        CfOperatingRecordEnum.Role.OPERATOR, handingReportList);
            cfReportAddTrust.setAuditStatus(AddTrustAuditStatusEnum.PASSED.getCode());
            adminCfReportAddTrustBiz.update(cfReportAddTrust);
            //添加审核快照
            cfReportAddTrustSnapshotBiz.addSnapshot(cfReportAddTrust);
            //riskDelegate.after(cfOperatingRecord, adminCrowdfundingReportBiz.getListByReportIds(handlingReportIds));
            //添加跟进备注
            cfReportFollowCommentBiz.save(userId, comment, CaseReportFollowDealStatusEnum.ADD_TRUST.getValue(), infoId, userAccount.getName());
            financeApproveService.addApprove(crowdfundingInfo, "补充证明信息", "审核通过", userId);
            //发布动态  通知用户
            pushUserProgress(crowdfundingInfo, cfReportAddTrust);
            //更改工单增信动作
            updateWorkOrderFollowType(infoId, userId, AdminWorkOrderReportConst.FollowType.PASS_ADD_CREDIT.getCode());
            try {
                //如果案例的举报状态为处理完成，更新工单状态为处理完成
                this.updataWorkOrderReportStatus(infoId, userId, crowdfundingInfo);
            } catch (Exception e) {
                LOGGER.error("CrowdfundingReportController reportAddTrust updateWorkOrderStatus error", e);
            }
            return NewResponseUtil.makeSuccess(Map.of("trustId",cfReportAddTrust.getId()));
        }

        return NewResponseUtil.makeSuccess(null);
    }

    private void issuedCommitment(boolean issuedCommitment, long subId) {
        if (issuedCommitment) {
            CfReportCommitmentInfo cfReportCommitmentInfo = new CfReportCommitmentInfo();
            cfReportCommitmentInfo.setIncrTrustId(subId);
            cfReportCommitmentInfo.setIllnessName("");
            cfReportCommitmentInfo.setHospital("");
            cfReportCommitmentInfo.setDetail("");
            cfReportCommitmentInfo.setTreatment("");
            cfReportCommitmentInfo.setUserName("");
            cfReportCommitmentInfo.setAddDate("");
            cfReportCommitmentInfo.setPatientName("");
            cfReportCommitmentInfoBiz.insertOne(cfReportCommitmentInfo);
        }
    }

    //更改工单状态
    private void updataWorkOrderReportStatus(Integer infoId, Integer userId, CrowdfundingInfo crowdfundingInfo) {
        CrowdfundingOperation operation = adminCrowdfundingOperationBiz.getByInfoIdMaster(crowdfundingInfo.getInfoId());
        log.info("info operation infoId :{} getReportStatus :{}", infoId, operation.getReportStatus().intValue());//已检查过
        if (operation.getReportStatus().intValue() == CaseReportStatusEnum.FINISH.getValue()) { //已检查过
            AdminWorkOrderReport adminWorkOrderReportByCaseId = adminWorkOrderReportBiz.getAdminWorkOrderReportByCaseId(infoId);
            if (null == adminWorkOrderReportByCaseId) {
                log.info("CrowdfundingReportController updataWorkOrderReportStatus No work order found,infoId:{}", infoId);
                return;
            }
            adminWorkOrderReportBiz.updateDealResultById(adminWorkOrderReportByCaseId.getId(), AdminWorkOrderReportConst.DealResult.DEAL_COMPLETE.getCode());
            AdminWorkOrderReport adminWorkOrderReport = adminWorkOrderReportBiz.getAdminWorkOrderReportByCaseId(infoId);
            AdminWorkOrderReportRecord adminWorkOrderReportRecord = AdminWorkOrderReportRecord.create(adminWorkOrderReport, adminWorkOrderReport.getWorkOrderId(), userId);
            adminWrokOrderReportRecordBiz.insertAdminWorkOrderReportRecord(adminWorkOrderReportRecord);
            adminWorkOrderBiz.updateOrderStatus(AdminWorkOrderConst.Status.FINISHED.getCode()
                    , AdminWorkOrderConst.Result.SYSTEM_COMPLETE.getCode(), adminWorkOrderReport.getWorkOrderId());
            AdminWorkOrder workOrderByCaseId = adminWorkOrderBiz.selectById(adminWorkOrderReport.getWorkOrderId());
            adminWorkOrderRecordBiz.insertOne(workOrderByCaseId);
        }
    }

    //更改工单跟进动作
    private void updateWorkOrderFollowType(Integer infoId, Integer userId, int followType) {
        AdminWorkOrderReport adminWorkOrderReport = adminWorkOrderReportBiz.getAdminWorkOrderReportByCaseId(infoId);
        if (null == adminWorkOrderReport) {
            log.info("CrowdfundingReportController updateWorkOrderFollowType No work order found,infoId:{}", infoId);
            return;
        }
        adminWorkOrderReportBiz.updateFollowTypeById(followType, adminWorkOrderReport.getId());
        AdminWorkOrderReport adminWorkOrderReportByCaseId = adminWorkOrderReportBiz.getAdminWorkOrderReportByCaseId(infoId);
        AdminWorkOrderReportRecord adminWorkOrderReportRecord = AdminWorkOrderReportRecord.create(adminWorkOrderReportByCaseId, adminWorkOrderReportByCaseId.getWorkOrderId(), userId);
        adminWrokOrderReportRecordBiz.insertAdminWorkOrderReportRecord(adminWorkOrderReportRecord);
    }

    @RequiresPermission("report:get-add-trust-info")
    @RequestMapping(path = "get-add-trust-info", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response getAddTrustInfo(@RequestParam(name = "infoUuid", defaultValue = "") String infoUuid) {
        LOGGER.info("CrowdfundingReportController getAddTrustInfo infoUuid:{}", infoUuid);
        if (StringUtils.isBlank(infoUuid)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        Map<String, Object> result = adminCrowdfundingReportBiz.getAddTrustMirror(infoUuid);
        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("report:query-credible-info-list")
    @ResponseBody
    @RequestMapping(path = "query-credible-info-list", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response<List<CfCredibleInfoDO>> queryCredibleInfoList(@RequestParam(name = "caseId") int caseId) {

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        List<CfCredibleInfoDO> cfCredibleInfoDOS = adminCredibleInfoService.queryByCaseId(caseId);
        if (CollectionUtils.isEmpty(cfCredibleInfoDOS)) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }

        List<CfCredibleInfoWorkOrderDO> credibleInfoWorkOrderDOList = adminCredibleInfoService.getByCaseId(caseId);
        Map<Long, Long> credibleInfoWorkOrderDOMap = Optional.ofNullable(credibleInfoWorkOrderDOList)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(CfCredibleInfoWorkOrderDO::getCredibleInfoId, CfCredibleInfoWorkOrderDO::getWorkOrderId, (x, y) -> y));
        for (CfCredibleInfoDO cfCredibleInfoDO : cfCredibleInfoDOS) {
            AdminUserAccountModel userAccount = seaAccountClientV1.getValidUserAccountById(cfCredibleInfoDO.getOperatorId().intValue()).getResult();//已检查过
            cfCredibleInfoDO.setMobileMask(maskUtil.buildByEncryptPhone(cfCredibleInfoDO.getMobile()));
            cfCredibleInfoDO.setMobile(null);
            cfCredibleInfoDO.setOperator(Objects.nonNull(userAccount) ? userAccount.getName() : "");
            if (CredibleTypeEnum.SUPPLY_VERFIFY.getKey() == cfCredibleInfoDO.getType()) {
                List<CfReportCredibleInfoVO> cfReportCredibleInfoVOS = cfReportAddTrustSnapshotBiz.getSnapshot(cfCredibleInfoDO.getSubId(),
                        CrowdfundingInfoStatusEnum.REJECTED.getCode());
                if (CollectionUtils.isNotEmpty(cfReportCredibleInfoVOS)) {
                    cfCredibleInfoDO.setAuditRecord(true);
                }
            }
            if (CredibleTypeEnum.HELP_PROVE.getKey() == cfCredibleInfoDO.getType()) {
                List<CfSendProveInfoRecord> cfSendProveInfoRecords = cfReportCredibleInfoService.proveInfoRecord(caseId, cfCredibleInfoDO.getSubId());
                if (CollectionUtils.isNotEmpty(cfSendProveInfoRecords)) {
                    cfCredibleInfoDO.setAuditRecord(true);
                }
            }
            cfCredibleInfoDO.setWorkOrderId(Objects.requireNonNullElse(credibleInfoWorkOrderDOMap.get(cfCredibleInfoDO.getId()), 0L));
        }

        return NewResponseUtil.makeSuccess(cfCredibleInfoDOS);
    }


    /**
     * 新的举报详情页新建可信信息
     */
    @RequiresPermission("report:save-edit-credible-info")
    @ResponseBody
    @RequestMapping(path = "save-edit-credible-info", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response<String> saveEditCredibleInfo(@RequestParam(name = "caseId") int caseId,
                                                 @ApiParam("信息来源") @RequestParam(name = "source") String source,
                                                 @ApiParam("信息内容") @RequestParam(name = "content") String content,
                                                 @ApiParam("图片信息") @RequestParam(name = "imageUrls", required = false, defaultValue = "") String imageUrls) {

        int adminUserId = ContextUtil.getAdminUserId();
        if (adminUserId <= 0) {
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        if (StringUtils.isEmpty(source) || StringUtils.isEmpty(content) || source.length() > 100 || content.length() > 500) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        CfCredibleEditInfoDO editInfoDO = new CfCredibleEditInfoDO();
        editInfoDO.setCaseId(caseId);
        editInfoDO.setSource(source);
        editInfoDO.setContent(content);
        editInfoDO.setImageUrls(imageUrls);

        int res = adminCredibleEditInfoService.insert(editInfoDO);
        if (res > 0) {
            adminCredibleInfoService.insertOne(caseId, 0, editInfoDO.getId(), CredibleTypeEnum.INTER_EDIT.getKey(), CrowdfundingInfoStatusEnum.UN_SAVE.getCode(), adminUserId);
            return NewResponseUtil.makeSuccess("success");
        }

        return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
    }

    /**
     * 查询新建可信信息
     */
    @RequiresPermission("report:query-edit-credible-info")
    @ResponseBody
    @RequestMapping(path = "query-edit-credible-info", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response<CfCredibleEditInfoDO> queryEditCredibleInfo(@RequestParam(name = "caseId") int caseId,
                                                                @RequestParam(name = "subId") long subId) {

        int adminUserId = ContextUtil.getAdminUserId();
        if (adminUserId <= 0) {
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        CfCredibleEditInfoDO editInfoDO = adminCredibleEditInfoService.queryById(subId);


        return NewResponseUtil.makeSuccess(editInfoDO);
    }

    @RequiresPermission("report:query-add-trust-info")
    @ResponseBody
    @RequestMapping(path = "query-add-trust-info", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response<CfReportAddTrustVo> queryAddTrustInfoList(@RequestParam(name = "caseId") int caseId, @RequestParam(name = "subId") long subId) {

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        AdminCfReportAddTrustVo adminCfReportAddTrustVo = adminCfReportAddTrustBiz.queryById(subId);

        return NewResponseUtil.makeSuccess(adminCfReportAddTrustVo);
    }

    @RequiresPermission("hospitalAudit:query-hospital-check-info")
    @ResponseBody
    @RequestMapping(path = "query-hospital-check-info", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response<CfHospitalAuditInfoExt> queryHospitalCheckInfoList(@RequestParam(name = "caseId") int caseId, @RequestParam(name = "subId") long subId) {

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        UserInfoModel userInfo = userInfoServiceBiz.getUserInfoByUserId(crowdfundingInfo.getUserId());


        CfHospitalAuditInfoExt hospitalAuditInfoExt = adminCfHospitalAuditBiz.getById(subId);
        String mobile = Objects.nonNull(userInfo) ? userInfo.getCryptoMobile() : "";
        hospitalAuditInfoExt.setRaiserMobileMask(maskUtil.buildByEncryptPhone(mobile));
        hospitalAuditInfoExt.setRaiserMobile(null);


        return NewResponseUtil.makeSuccess(hospitalAuditInfoExt);
    }
    @RequiresPermission("report:query-all-credible-info-list")
    @ResponseBody
    @RequestMapping(path = "query-all-credible-info-list", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response<List<CfCredibleDeailInfoDO>> queryAllCredibleInfoList(@RequestParam(name = "caseId") int caseId) {

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        List<CfCredibleInfoDO> cfCredibleInfoDOS = adminCredibleInfoService.queryByCaseId(caseId);

        List<CfCredibleDeailInfoDO> deailInfoDOS = Lists.newArrayList();
        for (CfCredibleInfoDO cfCredibleInfoDO : cfCredibleInfoDOS) {
            cfCredibleInfoDO.setMobileMask(maskUtil.buildByEncryptPhone(cfCredibleInfoDO.getMobile()));
            cfCredibleInfoDO.setMobile(null);

            CfCredibleDeailInfoDO deailInfoDO = new CfCredibleDeailInfoDO();

            BeanUtils.copyProperties(cfCredibleInfoDO, deailInfoDO);

            if (cfCredibleInfoDO.getType() == CredibleTypeEnum.SUPPLY_VERFIFY.getKey()) {
                AdminCfReportAddTrustVo adminCfReportAddTrustVo = adminCfReportAddTrustBiz.queryById(cfCredibleInfoDO.getSubId());
                CfReportAddTrustVo cfReportAddTrustVo = new CfReportAddTrustVo();
                BeanUtils.copyProperties(adminCfReportAddTrustVo, cfReportAddTrustVo);
                //检查是否下发了承诺书
                if (adminCfReportAddTrustVo.isIssuedCommitment()) {
                    CfReportCommitmentInfo cfReportCommitmentInfo = cfReportCommitmentInfoBiz.findByIncrTrustId(adminCfReportAddTrustVo.getId());
                    if (Objects.nonNull(cfReportCommitmentInfo)) {
                        cfReportAddTrustVo.setCfReportCommitmentInfo(cfReportCommitmentInfo);
                    }
                }
                deailInfoDO.setCfReportAddTrust(cfReportAddTrustVo);
            } else if (cfCredibleInfoDO.getType() == CredibleTypeEnum.HOSPITAL_CHECK.getKey()) {
                CfHospitalAuditInfoExt hospitalAuditInfoExt = adminCfHospitalAuditBiz.getById(cfCredibleInfoDO.getSubId());
                deailInfoDO.setCfHospitalAuditInfoExt(hospitalAuditInfoExt);
            } else {
                CfCredibleEditInfoDO editInfoDO = adminCredibleEditInfoService.queryById(cfCredibleInfoDO.getSubId());
                deailInfoDO.setCfCredibleEditInfoDO(editInfoDO);
            }
            deailInfoDOS.add(deailInfoDO);
        }

        return NewResponseUtil.makeSuccess(deailInfoDOS);
    }

    @ApiOperation("获取医院核实信息")
    @RequiresPermission("hospitalAudit:get-hospital-audit-info")
    @PostMapping("/get-hospital-audit-info")
    @ResponseBody
    public Response<CfHospitalAuditInfoVO> getHospitalAuditInfoV2(@RequestParam String infoUuid,
                                                                  @RequestParam(defaultValue = "0") int workOrderId) {
        CfHospitalAuditInfoExt cfHospitalAuditInfo = cfHospitalAuditBiz.getByInfoUuid(infoUuid);
        if (cfHospitalAuditInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_HOSPITAL_AUDIT_IS_NULL);
        }
        cfHospitalAuditInfo.setRaiserMobileMask(maskUtil.buildByDecryptPhone(cfHospitalAuditInfo.getRaiserMobile()));
        cfHospitalAuditInfo.setRaiserMobile(null);

        CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);

        List<CfHospitalAuditInfoTel> cfHospitalAuditInfoTels = cfHospitalAuditService.getCfHospitalAuditInfoTels(cfHospitalAuditInfo, fundingInfo, workOrderId);

        //获取医院核实信息
        UserInfoModel userInfo = userInfoServiceBiz.getUserInfoByUserId(fundingInfo.getUserId());
        CfHospitalAuditInfoVO vo = new CfHospitalAuditInfoVO();
        vo.setCfHospitalAuditInfo(cfHospitalAuditInfo);
        vo.setCfHospitalAuditInfoTels(cfHospitalAuditInfoTels);
        if(cfHospitalAuditInfo.getProvinceId() > 0 || cfHospitalAuditInfo.getCityId() > 0) {
            vo.setNew(true);
        }
        if (userInfo != null) {
            vo.setRaiserMobileMask(maskUtil.buildByEncryptPhone(userInfo.getCryptoMobile()));
        }
        return NewResponseUtil.makeSuccess(vo);
    }

    @RequiresPermission("hospitalAudit:get-hospital-audit-status")
    @ApiOperation(value = "获取医院核实状态", notes = "{0:未下发,1:已下发,2:通过,3:驳回,4:已提交}")
    @PostMapping("get-hospital-audit-status")
    @ResponseBody
    public Response<Integer> getHospitalAuditStatus(@RequestParam(name = "infoUuid", defaultValue = "") String infoUuid) {
        if (StringUtils.isBlank(infoUuid)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CfHospitalAuditInfo cfHospitalAuditInfo = cfHospitalAuditBiz.getByInfoUuid(infoUuid);
        if (cfHospitalAuditInfo == null) {
            return NewResponseUtil.makeSuccess(CrowdfundingInfoStatusEnum.UN_SAVE.getCode());
        }
        int auditStatus = cfHospitalAuditInfo.getAuditStatus();
        return NewResponseUtil.makeSuccess(auditStatus);
    }

    @RequiresPermission("hospitalAudit:hospital-submit-remark")
    @ApiOperation("医院核实提交备注")
    @PostMapping("hospital-submit-remark")
    @ResponseBody
    public Response<Void> hospitalSubmitRemark(@RequestParam String infoUuid,
                                               @ApiParam("备注内容") @RequestParam(required = false, defaultValue = "")
                                               String operatorContent) {
        CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        approveRemarkOldService.add(fundingInfo.getId(), ContextUtil.getAdminUserId(), operatorContent,
                ApproveSourceTypeEnum.HOSPITAL_AUDIT_PRIVATE);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("hospitalAudit:list-hospital-audit-remark")
    @ApiOperation("获取医院核实备注列表")
    @PostMapping("/list-hospital-audit-remark")
    @ResponseBody
    public Response<List<CrowdfundingApproveCommentVo>> listHospitalAuditRemark(@RequestParam String infoUuid) {
        CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        List<CrowdfundingApprove> approves = approveRemarkOldService.
                listByCaseIdAndSourceTypes(fundingInfo.getId(), ApproveSourceTypeEnum.Pages.HOSPITAL_AUDIT);
        List<CrowdfundingApproveCommentVo> vos = approveRemarkOldService.convert2VO(approves);

        //获取医院核实信息
        return NewResponseUtil.makeSuccess(vos);
    }

    @RequiresPermission("hospitalAudit:modify-hospital-department-tel-number")
    @ApiOperation("编辑科室座机号码")
    @PostMapping("modify-hospital-department-tel-number")
    @ResponseBody
    public Response<Void> modifyDepartmentTelNumber(@RequestParam String infoUuid,
                                                    @RequestParam String newNumber,
                                                    @RequestParam(required = false, defaultValue = "-1") int workOrderId) {
        int operatorId = ContextUtil.getAdminUserId();
        return cfReportService.modifyDepartmentTelNumber(infoUuid, newNumber, operatorId, workOrderId);
    }

    @RequiresPermission("hospitalAudit:modify-hospital-department-tel-number-v2")
    @ApiOperation("编辑科室座机号码V2")
    @PostMapping("modify-hospital-department-tel-number-v2")
    @ResponseBody
    public Response<Void> modifyDepartmentTelNumberV2(@RequestParam String infoUuid,
                                                      @RequestParam long id,
                                                      @RequestParam String areaCode,
                                                      @RequestParam String telNum,
                                                      @RequestParam String extNum,
                                                      @RequestParam(defaultValue = "0") int workOrderId) {
        int operatorId = ContextUtil.getAdminUserId();
        log.info("infoUuid:{}, id:{}, areaCode:{}, telNum:{}, extNum:{}, workOrderId:{}, operatorId:{}",
                infoUuid, id, areaCode, telNum, extNum, workOrderId, operatorId);
        if(!StringUtils.isNumeric(areaCode) || !StringUtils.isNumeric(telNum)) {
            return NewResponseUtil.makeFail("区号和座机号码必须为数字");
        }

        if(StringUtils.length(areaCode) > 5){
            return NewResponseUtil.makeFail("【座机区号】有误，不允许超过5位数");
        }

        if(StringUtils.length(telNum) > 9){
            return NewResponseUtil.makeFail("【座机号码】有误，不允许超过9位数");
        }

        if(StringUtils.length(extNum) > 20){
            return NewResponseUtil.makeFail("【分机号】有误，不允许超过20位数");
        }

        return this.cfHospitalAuditService.modifyDepartmentTelNumberV2(infoUuid, id, areaCode, telNum, extNum, operatorId, workOrderId);
    }

    @RequiresPermission("hospitalAudit:modify-hospital-info")
    @ApiOperation("编辑医院信息")
    @PostMapping("modify-hospital-info")
    @ResponseBody
    public Response<Void> modifyHospitalInfo(@RequestParam String infoUuid,
                                             @RequestParam int hospitalId,
                                             @RequestParam int provinceId,
                                             @RequestParam int cityId,
                                             @RequestParam String provinceName,
                                             @RequestParam String cityName,
                                             @RequestParam String hospitalName,
                                             @RequestParam(defaultValue = "-1") int workOrderId) {
        int operatorId = ContextUtil.getAdminUserId();
        log.info("infoUuid:{}, hospitalId:{}, provinceId:{}, cityId:{}, provinceName:{}, cityName:{}, hospitalName:{}, workOrderId:{}, operatorId:{}",
                infoUuid, hospitalId, provinceId, cityId, provinceName, cityName, hospitalName, workOrderId, operatorId);
        return this.cfHospitalAuditService.modifyHospitalInfo(infoUuid, hospitalId, provinceId, cityId,
                provinceName, cityName, hospitalName, operatorId, workOrderId);
    }

    @RequiresPermission("hospitalAudit:hospital-audit")
    @RequestMapping(path = "/hospital-audit", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    @RedisDistributedLock(key = "cf_report_controller_hospital_audit_#{infoUuid}")
    public Response hospitalAudit(String infoUuid,
                                  @ApiParam("工单id")
                                  @RequestParam(required = false, defaultValue = "0") int workOrderId,
                                  @ApiParam("工单类型")
                                  @RequestParam(required = false, defaultValue = "0") int workOrderType,
                                  @RequestParam Integer auditStatus,
                                  @ApiParam("下发原因/新增跟进备注") @RequestParam(required = false, defaultValue = "") String operatorContent,
                                  @ApiParam("下发原因补充") @RequestParam(required = false, defaultValue = "") String reasonSupplement,
                                  @ApiParam("1:默认 2:新举报处理详情页") @RequestParam(name = "pageSource", required = false, defaultValue = "1") int pageSource) {
        Integer userId = ContextUtil.getAdminUserId();
        return cfReportService.hospitalAudit(infoUuid, auditStatus, operatorContent, reasonSupplement, workOrderId, workOrderType, pageSource, userId);
    }

    @RequiresPermission("hospitalAudit:delay-process")
    @ApiOperation("医院核实延后审核")
    @PostMapping("delay-process")
    @ResponseBody
    public Response<Void> delayProcess(String infoUuid,
                                       @ApiParam("工单id")
                                       @RequestParam(required = false, defaultValue = "0") int workOrderId,
                                       @ApiParam("工单类型")
                                       @RequestParam(required = false, defaultValue = "0") int workOrderType,
                                       @ApiParam("新增跟进备注")
                                       @RequestParam(required = false, defaultValue = "") String operatorContent) {
        return cfHospitalAuditService.delayProcess(workOrderId, workOrderType, infoUuid, ContextUtil.getAdminUserId(), operatorContent);
    }

    @RequiresPermission("hospitalAudit:submit-follow-order")
    @ApiOperation("提交医院核实跟进")
    @PostMapping("submit-follow-order")
    @ResponseBody
    public Response<Void> submitFollowOrder(String infoUuid,
                                            @ApiParam("工单id")
                                            @RequestParam(required = false, defaultValue = "0") int workOrderId,
                                            @ApiParam("新增跟进备注")
                                            @RequestParam(required = false, defaultValue = "") String operatorContent) {
        return cfHospitalAuditService.submitFollowOrder(workOrderId, infoUuid, ContextUtil.getAdminUserId(), operatorContent);
    }

    @RequiresPermission("hospitalAudit:list-hospital-audit-snapshot")
    @ApiOperation("获取医院核实快照列表")
    @PostMapping("list-hospital-audit-snapshot")
    @ResponseBody
    public Response<List<CfHospitalAuditInfoNew>> listHospitalAuditSnapshot(@RequestParam String infoUuid) {
        CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        List<CfHospitalAuditInfoNew> list = cfHospitalAuditService.listHospitalAuditSnapshot(fundingInfo.getId());
        list = Lists.reverse(list);
        return NewResponseUtil.makeSuccess(list);
    }

    @RequiresPermission("hospitalAudit:get-work-order-hospital-audit-snapshot")
    @ApiOperation("获取工单医院核实快照")
    @PostMapping("get-work-order-hospital-audit-snapshot")
    @ResponseBody
    public Response<CfHospitalAuditInfoNew> getWorkOrderHospitalAuditSnapshot(
            @ApiParam("工单id") @RequestParam(required = false, defaultValue = "0") int workOrderId) {
        CfHospitalAuditInfoNew cfHospitalAuditInfoNew = cfHospitalAuditService.getWorkOrderHospitalAuditSnapshot(workOrderId);
        return NewResponseUtil.makeSuccess(cfHospitalAuditInfoNew);
    }

    @RequiresPermission("hospitalAudit:cancel-hospital-audit")
    @RequestMapping(path = "/cancel-hospital-audit", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response cancelHospitalAudit(String infoUuid, Integer userId,
                                        @ApiParam("工单id")
                                        @RequestParam(required = false, defaultValue = "0") int workOrderId,
                                        @ApiParam("工单类型")
                                        @RequestParam(required = false, defaultValue = "0") int workOrderType,
                                        @ApiParam("取消原因")
                                        @RequestParam(required = false, defaultValue = "") String reason,
                                        @ApiParam("新增跟进备注")
                                        @RequestParam(required = false, defaultValue = "") String operatorContent) {
        LOGGER.info("CrowdfundingReportController hospitalAudit infoUuid:{}, userId:{}", infoUuid, userId);
        return cfHospitalAuditService.cancelAudit(workOrderId, workOrderType, infoUuid, userId, reason, operatorContent);
    }

    @RequiresPermission("hospitalAudit:list-hospital-audit")
    @PostMapping("list-hospital-audit")
    @ResponseBody
    public Response<HospitalAuditListVO> getReportCaseList(
            @RequestParam(required = false, defaultValue = "1") int current,
            @RequestParam(required = false, defaultValue = "" + PageCons.DEFAULT_PAGE_SIZE) Integer pageSize,
            @RequestParam(required = false) Integer caseId,
            @ApiParam("材料审核状态") @RequestParam(required = false) Integer caseStatus,
            @ApiParam("医院核实下发组织是否是举报组") @RequestParam(required = false) Boolean hospitalAuditIsReportSend,
            @RequestParam(required = false, defaultValue = "-1") int hospitalAuditStatus,
            @RequestParam(required = false) Long hospitalSendBeginTime,
            @RequestParam(required = false) Long hospitalSendEndTime,
            @RequestParam(required = false) Long userSubmitBeginTime,
            @RequestParam(required = false) Long userSubmitEndTime,
            @RequestParam(required = false) Long updateBeginTime,
            @RequestParam(required = false) Long updateEndTime) {
        BooleanEnum onWorkOrderEnum = BooleanEnum.getSqlQueryByBoolean(hospitalAuditIsReportSend == null ? null : !hospitalAuditIsReportSend);

        Response checkTimeResp = ParamTimeRangeHandler.illegalTimeRangeForLong(hospitalSendBeginTime, hospitalSendEndTime);
        if (checkTimeResp.notOk()) {
            return checkTimeResp;
        }

        int count = cfHospitalAuditBiz.countHospitalAuditByStatus(CrowdfundingInfoStatusEnum.SUBMITTED.getCode());
        PaginationListVO<HospitalAuditShowVO> res = cfHospitalAuditService.listHospitalAudit(
                current, pageSize, caseId, caseStatus, onWorkOrderEnum, hospitalAuditStatus,
                hospitalSendBeginTime, hospitalSendEndTime, userSubmitBeginTime, userSubmitEndTime, updateBeginTime, updateEndTime);
        HospitalAuditListVO vo = new HospitalAuditListVO();
        vo.setPageData(res);
        vo.setTodoCount(count);
        return NewResponseUtil.makeSuccess(vo);
    }


    //举报增信审核通过下发通知
    private void pushUserProgress(CrowdfundingInfo crowdfundingInfo, CfReportAddTrust cfReportAddTrust) {

        //先发动态
        LOGGER.info(" CrowdfundingReportController addUserProgress userId:{};infoUuid:{};", cfReportAddTrust.getInfoUuid());
        String content = this.buildContent(cfReportAddTrust);
        CrowdFundingProgress crowdFundingProgress = new CrowdFundingProgress();
        crowdFundingProgress.setImageUrls(cfReportAddTrust.getImageUrls());
        crowdFundingProgress.setContent(content);
        crowdFundingProgress.setUserId(crowdfundingInfo.getUserId());
        crowdFundingProgress.setActivityId(crowdfundingInfo.getId());
        crowdFundingProgress.setTitle("");
        crowdFundingProgress.setType(CrowdFundingProgressType.PROGRESS.value());
        crowdfundingDelegate.addProgress(crowdFundingProgress);
    }

    private String buildContent(CfReportAddTrust cfReportAddTrust) {
        if (Objects.isNull(cfReportAddTrust)) {
            return "";
        }
        String content = cfReportAddTrust.getContent();
        if (!cfReportAddTrust.isIssuedCommitment()) {
            return content;
        }
        CfReportCommitmentInfo cfReportCommitmentInfo = cfReportCommitmentInfoBiz.findByIncrTrustId(cfReportAddTrust.getId());
        boolean addName = StringUtils.isNotBlank(cfReportCommitmentInfo.getPatientName());
        //有承诺书
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("承诺书<br/><br/>");
        if (addName) {
            stringBuilder.append("患者姓名 : ").append(cfReportCommitmentInfo.getPatientName()).append("<br/>");
            stringBuilder.append("患有疾病 : ").append(cfReportCommitmentInfo.getIllnessName()).append("<br/>");
        } else {
            stringBuilder.append("本人患有疾病 : ").append(cfReportCommitmentInfo.getIllnessName()).append("<br/>");
        }
        stringBuilder.append("现在治疗医院名称 : ").append(cfReportCommitmentInfo.getHospital()).append("<br/>");
        stringBuilder.append("科室名称或治疗地点 : ").append(cfReportCommitmentInfo.getDetail()).append("<br/>");
        stringBuilder.append("治疗方式 : ").append(cfReportCommitmentInfo.getTreatment()).append("<br/><br/>");
        stringBuilder.append("因");
        if (addName) {
            stringBuilder.append("患者");
        } else {
            stringBuilder.append("本人");
        }
        stringBuilder.append("家庭无力承担医疗费用，现在水滴筹平台发起求助。<br/>" +
                "本人承诺：发起求助所提交的信息、资料真实、完整、及时、合法，不存在隐瞒、夸大、伪造、隐瞒真相等情形，所筹得的赠与款项将全" +
                "部用于本人治疗，不会挪做他用，定期上传医疗花费单据，接受水滴筹平台回访，如赠与款项有剩余或根据水滴筹平台的要求，及时将赠" +
                "与款项退还水滴筹平台，再返还给赠与人。如出现任何违反本承诺书的情形，或因本人原因导致任何法律后果，均由本人承担。<br/>" +
                "特此承诺！<br/><br/>");
        stringBuilder.append("承诺人 : ").append(cfReportCommitmentInfo.getUserName()).append("<br/>");
        stringBuilder.append("填写日期 : ").append(cfReportCommitmentInfo.getAddDate()).append("<br/><br/>");
        stringBuilder.append(content);
        return stringBuilder.toString();
    }

    @RequiresPermission("report:update-report-label")
    @RequestMapping(path = "/update-report-label", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response updateReportLabel(@RequestParam("newReportLabels") String newReportLabels) {

        if (StringUtils.isBlank(newReportLabels)) {
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<CrowdfundingReportLabel> newLabelList = null;

        try {
            newLabelList = JSON.parseArray(newReportLabels, CrowdfundingReportLabel.class);//已检查过
        } catch (Exception e) {
            log.error("更新举报标签 param:{} 异常", newReportLabels, e);
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        int userId = ContextUtil.getAdminUserId();
        adminCrowdfundingReportBiz.updateReportLabel(userId, newLabelList);
        return ResponseUtil.makeSuccess("");
    }

    @RequiresPermission("report:get-report-case-list")
    @RequestMapping(path = "/get-hospital-audit-num", method = RequestMethod.POST)
    @ResponseBody
    public Response getHospitalAuditNum(@RequestParam("auditStatus") int auditStatus) {

        return ResponseUtil.makeSuccess(new AdminCfHospitalAuditBiz.HospitalAuditData(
                cfHospitalAuditBiz.countHospitalAuditByStatus(auditStatus)));

    }

    @RequiresPermission("report:has-old-report-work-order")
    @RequestMapping(path = "/has-old-report-work-order", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("查询案例是否有旧工单")
    public Response hasOldReportWorkOrder(@RequestParam("infoUuid") String infoUuid) {
        log.info("CrowdfundingReportController#hasOldReportWorkOrder infoUuid:{}", infoUuid);
        if (StringUtils.isBlank(infoUuid)) {
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (Objects.isNull(crowdfundingInfo)) {
            return ResponseUtil.makeError(ErrorCode.CF_NOT_FOUND);
        }
        AdminWorkOrderReport adminWorkOrderReport = adminWorkOrderReportBiz.getAdminWorkOrderReportByCaseId(crowdfundingInfo.getId());
        boolean has = false;
        if (Objects.nonNull(adminWorkOrderReport)) {
            has = true;
        }
        return ResponseUtil.makeSuccess(Map.of("has", has, "infoUuid", infoUuid));
    }


    @RequiresPermission("trust:get-add-trust-audit-record")
    @RequestMapping(path = "get-add-trust-audit-record", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("查询增信审核记录")
    public Response getAddTrustAuditRecord(@RequestParam(name = "subId") long subId) {
        log.info("CrowdfundingReportController.getAddTrustAuditRecord subId:{}", subId);
        if (subId < 0) {
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<CfReportCredibleInfoVO> cfReportCredibleInfoVOS = cfReportAddTrustSnapshotBiz.getSnapshot(subId,
                CrowdfundingInfoStatusEnum.REJECTED.getCode());

        return ResponseUtil.makeSuccess(cfReportCredibleInfoVOS);
    }

    @RequiresPermission("report:get-latest-handler")
    @RequestMapping(path = "get-latest-handler", method = RequestMethod.POST)
    @ResponseBody
    public Response getLatestHandler(@RequestParam(name = "caseId") int caseId) {
        log.info("CrowdfundingReportController.getLatestHandler caseId:{}", caseId);
        if (caseId <= 0) {
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        Map<String, Object> latestHandler = cfReportService.getLatestHandler(caseId);
        return ResponseUtil.makeSuccess(latestHandler);
    }

    @RequiresPermission("report:get-follow-comment")
    @RequestMapping(path = "get-follow-comment", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("查询举报跟进记录")
    public Response<CfReportFollowComment> getFollowConnent(@RequestParam(value = "infoId", defaultValue = "0") int infoId,
                                                            @RequestParam(name = "pageNum", required = false, defaultValue = "1") int pageNum,
                                                            @RequestParam(name = "pageSize", required = false,
                                                                    defaultValue = "" + PageCons.MAX_PAGE_SIZE) int pageSize) {
        log.info("CrowdfundingReportController.getFollowConnent infoId:{},pageNum:{},pageSize:{}", infoId, pageNum, pageSize);
        if (infoId <= 0) {
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        Map<String, Object> result = Maps.newHashMap();
        List<CfReportFollowComment> cfReportFollowCommentList = cfReportFollowCommentBiz.getByPage(infoId, pageNum, pageSize);
        result.put("pagination", PageUtil.transform2PageMap(cfReportFollowCommentList));
        result.put("data", cfReportFollowCommentList);
        return ResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("report:update-handle-status")
    @RequestMapping(path = "update-handle-status", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("修改举报条目处理状态")
    public Response updateHandleStatus(@RequestParam("caseId") int caseId, @RequestParam("reportId") int reportId) {
        log.info("CrowdfundingReportController.updateHandleStatus caseId:{},reportId:{}", caseId, reportId);
        if (caseId <= 0 || reportId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        int adminUserId = ContextUtil.getAdminUserId();
        long operatorId = cfReportService.getNewReportWorkOrderOperatorId(caseId, reportId);
        if (adminUserId != operatorId) {
            return NewResponseUtil.makeError(AdminErrorCode.REPORT_WORK_ORDER_CANNOT_HANDLE);
        }
        cfReportService.updateHandleStatus(caseId, adminUserId, reportId);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("report:delete-contact")
    @RequestMapping(path = "delete-contact", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("删除联系人")
    public Response deletaContact(@RequestParam("caseId") int caseId, @RequestParam("reportId") int reportId, @RequestParam("id") long id) {
        log.info("CrowdfundingReportController.deletaContact caseId:{},reportId:{},id:{}", caseId, reportId, id);
        if (caseId <= 0 || reportId < 0 || id <= 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        CfReportCommunicaterDO cfReportCommunicaterDO = reportCommunicaterListService.queryById(id, caseId, reportId);
        if (Objects.nonNull(cfReportCommunicaterDO) && !cfReportCommunicaterDO.isManualAdd()) {
            return NewResponseUtil.makeError(AdminErrorCode.REPORT_CONTACT_NOT_UPDATE);
        }
        reportCommunicaterListService.delete(id, caseId, reportId);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("report:add-call-comment-record")
    @RequestMapping(path = "add-call-comment-record", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("添加举报沟通备注记录")
    public Response addCallCommentRecrod(@RequestParam("caseId") int caseId,
                                         @RequestParam("reportId") int reportId,
                                         @RequestParam("callStatus") int callStatus,
                                         @RequestParam("comment") String comment,
                                         @RequestParam("type") int type) {
        log.info("CrowdfundingReportController.addCallCommentRecrod caseId:{},reportId:{},callStatus:{},comment:{},type:{}",
                caseId, reportId, callStatus, comment, type);
        if (caseId <= 0 || reportId < 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        ReportCallCommentRecord reportCallCommentRecord = new ReportCallCommentRecord();
        reportCallCommentRecord.setCaseId(caseId);
        reportCallCommentRecord.setReportId(reportId);
        reportCallCommentRecord.setCallStatus(callStatus);
        reportCallCommentRecord.setComment(comment);
        reportCallCommentRecord.setType(type);
        reportCallCommentRecordBiz.insertOne(reportCallCommentRecord);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("report:get-call-comment-record")
    @RequestMapping(path = "get-call-comment-record", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("查询举报沟通备注记录")
    public Response getCallCommentRecrod(@RequestParam("caseId") int caseId,
                                         @RequestParam("reportId") int reportId,
                                         @RequestParam("type") int type) {
        log.info("CrowdfundingReportController.getCallCommentRecrod caseId:{},reportId:{},type:{}", caseId, reportId, type);
        if (caseId <= 0 || reportId < 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<ReportCallCommentRecord> reportCallCommentRecords = reportCallCommentRecordBiz.getReportCallCommentRecord(caseId, reportId, type);
        return NewResponseUtil.makeSuccess(reportCallCommentRecords);
    }

    @RequiresPermission("report:modify-mark-report-types")
    @RequestMapping(path = "modify-mark-report-types", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("修改举报类型接口")
    public Response modifyMarkReportTypes(@RequestParam("reportId") int reportId,
                                          @RequestParam("reportTypes") String reportTypes,
                                          @RequestParam(value = "details", required = false, defaultValue = "") String details) {
        log.info("CrowdfundingReportController.modifyMarkReportTypes reportId:{},reportTypes:{}", reportId, reportTypes);
        if ( reportId < 0 || StringUtils.isEmpty(reportTypes)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return NewResponseUtil.makeSuccess(adminCrowdfundingReportBiz.addOrUpdateLabel(reportId, reportTypes, details));
    }

    @RequiresPermission("report:risk-check-list")
    @RequestMapping(path = "risk-check-list", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("风险核实进展列表")
    public Response riskCheckList(@RequestParam("caseId") int caseId) {
        log.info("CrowdfundingReportController.riskCheckList caseId:{}", caseId);
        if (caseId < 0 ) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<AdminCfReportRiskCheck> reportRiskCheckByCaseId = adminCrowdfundingReportBiz.getReportRiskCheckByCaseId(caseId);
        if (CollectionUtils.isEmpty(reportRiskCheckByCaseId)) {
            return NewResponseUtil.makeSuccess(reportRiskCheckByCaseId);
        }
        reportRiskCheckByCaseId.forEach(f -> {
            f.setOperatorName(initialAuditSearchService.queryOperatorName((int) f.getOperatorId()));
        });
        return NewResponseUtil.makeSuccess(reportRiskCheckByCaseId);
    }

    @RequiresPermission("report:add-risk-check")
    @RequestMapping(path = "add-risk-check", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("添加风险核实进展")
    public Response addRiskCheck(@RequestParam("caseId") int caseId, @RequestParam("content") String content) {
        log.info("CrowdfundingReportController.riskCheckList caseId:{}, content:{}", caseId, content);
        if (caseId < 0 || StringUtils.isEmpty(content)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        AdminCfReportRiskCheck adminCfReportRiskCheck = new AdminCfReportRiskCheck();
        adminCfReportRiskCheck.setCaseId(caseId);
        adminCfReportRiskCheck.setContent(content);
        adminCfReportRiskCheck.setOperatorId(ContextUtil.getAdminLongUserId());
        int i = adminCrowdfundingReportBiz.addReportRiskCheck(adminCfReportRiskCheck);
        return NewResponseUtil.makeSuccess(i);
    }

    @RequiresPermission("report:risk-tag-label")
    @RequestMapping(path = "risk-tag-label", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("风险标识-风险标签")
    public Response riskTagLabel(@RequestParam("caseId") int caseId) {
        if (caseId < 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<AdminCfReportRiskTagLabel> reportRiskTagLabel = adminCrowdfundingReportBiz.getReportRiskTagLabel(caseId);
        if (CollectionUtils.isEmpty(reportRiskTagLabel)) {
            return NewResponseUtil.makeSuccess(reportRiskTagLabel);
        }
        reportRiskTagLabel.forEach(f -> {
            f.setOperatorName(initialAuditSearchService.queryOperatorName((int) f.getOperatorId()));
        });
        return NewResponseUtil.makeSuccess(reportRiskTagLabel);
    }

    @RequiresPermission("report:fix-risk-tag-label")
    @RequestMapping(path = "fix-risk-tag-label", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("修改风险标识-风险标签")
    public Response fixRiskTagLabel(@RequestParam("caseId") int caseId,
                                    @RequestParam(value = "riskLabelType", defaultValue = "", required = false) String riskLabelType,
                                    @RequestParam("caseRiskType") int caseRiskType) {
        if (caseId < 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<String> typeList = new ArrayList<>();
        if (StringUtils.isNotEmpty(riskLabelType)) {
            typeList = Splitter.on(",").splitToList(riskLabelType)
                    .stream()
                    .map(Integer::valueOf)
                    .map(CfReportTypeEnum::getDescFromCode)
                    .collect(Collectors.toList());
        }
        AdminCfReportLabelRiskType reportLabelRiskType = AdminCfReportLabelRiskType.parse(caseRiskType);
        String riskStr = Objects.nonNull(reportLabelRiskType) ? reportLabelRiskType.getDesc() : "暂无";
        String remark = "编辑为：" + riskStr + "，标签为：" +Joiner.on(",").join(typeList);
        AdminCfReportRiskTagLabel adminCfReportRiskTagLabel = new AdminCfReportRiskTagLabel();
        adminCfReportRiskTagLabel.setCaseId(caseId);
        adminCfReportRiskTagLabel.setRiskReportType(riskLabelType);
        adminCfReportRiskTagLabel.setCaseRiskType(caseRiskType);
        adminCfReportRiskTagLabel.setRemark(remark);
        adminCfReportRiskTagLabel.setOperatorId(ContextUtil.getAdminLongUserId());
        int i = adminCrowdfundingReportBiz.addCfReportRiskTagLabel(adminCfReportRiskTagLabel);
        if (StringUtils.isEmpty(riskLabelType)) {
            return NewResponseUtil.makeSuccess(i);
        }

        List<Integer> typeCodeList = Splitter.on(",").splitToList(riskLabelType)
                .stream()
                .map(Integer::valueOf)
                .collect(Collectors.toList());
        if (caseRiskType == AdminCfReportLabelRiskType.RISK.getCode() &&
                (typeCodeList.contains(CfReportTypeEnum.commercialPromotion.getCode()) || typeCodeList.contains(CfReportTypeEnum.BUSINESS_PROMOTION.getCode()))) {
            RpcResult<RaiseBasicInfoModel> rpcResult = cfRaiseMaterialClient.selectRaiseBasicInfo(caseId);
            String diseaseName = Optional.ofNullable(rpcResult)
                    .map(RpcResult::getData)
                    .map(RaiseBasicInfoModel::getDiseaseName)
                    .orElse("");
            CfFirsApproveMaterial cfFirsApproveMaterial = firstApproveBiz.getCfFirsApproveMaterialByInfoId(caseId);
            String idCard = shuidiCipher.decrypt(cfFirsApproveMaterial.getPatientCryptoIdcard());
            IdcardInfoExtractor idcardInfoExtractor = new IdcardInfoExtractor(idCard);

            BusinessForwardCase businessForwardCase = new BusinessForwardCase();
            businessForwardCase.setCase_id(crowdfundingInfo.getInfoId());
            businessForwardCase.setInfo_id((long) caseId);
            businessForwardCase.setCreate_time(crowdfundingInfo.getCreateTime().getTime());
            businessForwardCase.setDisease_name(diseaseName);
            businessForwardCase.setPatient_encrypt_idcard(cfFirsApproveMaterial.getPatientCryptoIdcard());
            businessForwardCase.setAge((long) LocalDate.now().getYear() - idcardInfoExtractor.getYear());
            businessForwardCase.setUser_tag(String.valueOf(caseId));
            businessForwardCase.setUser_tag_type(UserTagTypeEnum.userid);
            try {
                analytics.track(businessForwardCase);
            } catch (Exception e) {
                log.info("analytics BusinessForwardCase error {}", e);
            }

        }
        return NewResponseUtil.makeSuccess(i);
    }

}
