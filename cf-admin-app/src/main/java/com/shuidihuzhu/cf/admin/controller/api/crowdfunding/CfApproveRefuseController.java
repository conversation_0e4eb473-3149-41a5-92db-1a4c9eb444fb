package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.*;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.admin.delegate.SeaUserAuthDelegate;
import com.shuidihuzhu.cf.admin.util.AdminListUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.biz.crowdfunding.materialRefuse.CfRefuseSuggestModifyBiz;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.sona.RoleEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.material.CfRefuseSuggestModify;
import com.shuidihuzhu.cf.model.crowdfunding.materialAudit.CfRefuseModifyVo;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditOperateService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditSearchService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import org.apache.kafka.common.protocol.types.Field;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by Ahrievil on 2017/7/26
 */
@RestController
@RequestMapping(path = "/admin/crowdfunding/approve/refuse")
@Slf4j
public class CfApproveRefuseController {

    private final static Logger LOGGER = LoggerFactory.getLogger(CfApproveRefuseController.class);

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CfRefuseReasonEntityBiz cfRefuseReasonEntityBiz;
    @Autowired
    private CfRefuseReasonTagBiz cfRefuseReasonTagBiz;
    @Autowired
    private CfRefuseReasonItemBiz cfRefuseReasonItemBiz;
    @Autowired
    private CfCommitVerifyItemBiz cfCommitVerifyItemBiz;
    @Autowired
    private AdminApproveService adminApproveService;
    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Autowired
    private  SeaAccountClientV1 seaAccountClientV1;
    @Resource
    private SeaUserAuthDelegate seaUserAuthDelegate;
    @Autowired
    private InitialAuditSearchService initialAuditSearchService;
    @Autowired
    private CfRefuseReasonCommonBiz reasonCommonBiz;
    @Autowired
    private CfRefuseSuggestModifyBiz suggestModifyBiz;


    @RequiresPermission("refuse:get")
    @RequestMapping(path = "/get-refuse-list-by-info-uuid", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response getRefuseList(int type, String infoUuid) {
        int userId = ContextUtil.getAdminUserId();
        LOGGER.info("CfApproveRefuseController getRefuseList type:{} infoUuid:{}, userId:{}", type, infoUuid, userId);
        if (infoUuid == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        AdminUserAccountModel validById = seaAccountClientV1.getValidUserAccountById(userId).getResult();
        if (validById == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        Map<String, Object> result = Maps.newHashMap();
        result.put("right", getRight(userId));
        result.put("initialAuditPass", initialAuditSearchService.initialPass(infoUuid));

        // 材料审核
        List<CfCommitVerifyItem> dataTypes;
        try {
            dataTypes = getCfCommitVerifyItemList(type, infoUuid);
        } catch (Exception e) {
            LOGGER.error("CfApproveRefuseController getRefuseList error!", e);
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }

        result.put("list", dataTypes);
        return NewResponseUtil.makeSuccess(result);
    }

    /**
     * 检查某userId是否拥有超级驳回权限
     * @param userId
     * @return
     */
    private boolean getRight(int userId) {
        return seaUserAuthDelegate.hasPermissionSimple(userId, RoleEnum.REFUSE_FORCE.getPermission());
    }

    @RequestMapping(path = "/get-refuse-list-by-type", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    @RequiresPermission("refuse:get-refuse-list")
    public Response adminGetRefuseList(int dataType,
                                       @RequestParam(required = false, defaultValue = "0") int useScene) {
//        LOGGER.info("CfApproveRefuseController adminGetRefuseList dataType :{}", dataType);
//        List dataList = AdminListUtil.getList(10, (start, size) -> cfCommitVerifyItemBiz.selectAllType(start, size));
//        if (!dataList.contains(dataType))
//            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
//        List<CfRefuseReasonTag> cfRefuseReasonTags = cfRefuseReasonTagBiz.selectByDataType(dataType);
//
//        this.addEntityListToTags(cfRefuseReasonTags);
//        cfRefuseReasonTags.forEach(value -> {
//            List<CfRefuseReasonEntity> entityList = value.getEntityList();
//            if (CollectionUtils.isNotEmpty(entityList)) {
//                Map<Integer, List<CfRefuseReasonItem>> reasonIdItemsMap = adminApproveService.foundEntityItems(entityList);
//                entityList.forEach(val -> val.setRefuseReasonItems(reasonIdItemsMap.get(val.getId())
//                        .stream().map(CfRefuseReasonItem::getContent).collect(Collectors.toList())));
//            }
//        });

        Map<String, Object> result = Maps.newHashMap();
        result.put("list", cfRefuseReasonEntityBiz.getRefuseListByType(dataType, useScene));
        return NewResponseUtil.makeSuccess(result);
    }

    @RequestMapping(path = "/delete-reason-entity", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    @RequiresPermission("refuse:delete-reason-entity")
    public Response deleteReasonEntity(int reasonEntityId) {
        LOGGER.info("CfApproveRefuseController deleteReasonEntity reasonEntityId:{}, userId:{}", reasonEntityId, ContextUtil.getAdminUserId());
        try {
            CfRefuseReasonEntity entity = cfRefuseReasonEntityBiz.selectById(reasonEntityId, 0);
            if (entity == null)
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            cfRefuseReasonEntityBiz.deleteOne(reasonEntityId);
            CfRefuseReasonTag cfRefuseReasonTagMap = cfRefuseReasonTagBiz.selectByTagId(entity.getTagId());
            if (cfRefuseReasonTagMap == null)
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            List<String> reasonIdList = Lists.newArrayList(Splitter.on(",").splitToList(cfRefuseReasonTagMap.getReasonIds()));
            reasonIdList.remove(String.valueOf(reasonEntityId));
            String reasonIds = String.join(",", reasonIdList);
            cfRefuseReasonTagBiz.updateReasonIds(reasonIds, cfRefuseReasonTagMap.getId());
        } catch (Exception e) {
            LOGGER.info("CfApproveRefuseController deleteReasonEntity error!", e);
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }
        return NewResponseUtil.makeSuccess(null);
    }

    @RequestMapping(path = "/add-or-update-reason-entity", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    @RequiresPermission("refuse:add-or-update-reason-entity")
    public Response<String> addOrUpdateReasonEntity(Integer id, String content, int tagId, String itemIds,
                                            @RequestParam(name = "useSceneIds", required = false) String useSceneIds,
                                                    @RequestParam(required = false) String suggestModifyIds,
                                                    @RequestParam(required = false, defaultValue = "0") int weightServiceLib,
                                                    @RequestParam(required = false, defaultValue = "false") boolean customType,
                                                    @RequestParam(required = false, defaultValue = "0") int riskLabelRelated,
                                                    @RequestParam(required = false, defaultValue = "") String riskLabelIdList) {
        try {
            CfRefuseReasonEntity entity = new CfRefuseReasonEntity(content, tagId, itemIds, suggestModifyIds, weightServiceLib, customType);
            cfRefuseReasonEntityBiz.addReasonEntity(ContextUtil.getAdminUserId(), entity, useSceneIds, riskLabelRelated, riskLabelIdList);
        } catch (Exception e) {
            LOGGER.info("添加驳回异常error!, tagId:{} itemIds:{} content:{} useSceneIds:{}",
                    tagId, itemIds, content, useSceneIds, e);
            return NewResponseUtil.makeFail(e.getMessage());
        }
        return NewResponseUtil.makeSuccess("");
    }

    @RequestMapping(path = "/get-item-info", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    @RequiresPermission("refuse:get-item-info")
    public Response getItemInfo(int dataType) {
        LOGGER.info("CfApproveRefuseController getItemInfo dataType:{}", dataType);
        List<CfRefuseReasonItem> cfRefuseReasonItems = cfRefuseReasonItemBiz.selectByType(dataType);
        Map<String, Object> result = Maps.newHashMap();

        // 对前置的资料排序
        if (dataType == InitialAuditOperateService.FIRST_APPROVE_TAG && CollectionUtils.isNotEmpty(cfRefuseReasonItems)) {
            cfRefuseReasonItems.sort(new Comparator<CfRefuseReasonItem>() {
                @Override
                public int compare(CfRefuseReasonItem o1, CfRefuseReasonItem o2) {
                    return o1.getGroupRank() - o2.getGroupRank();
                }
            });
        }

        result.put("list", cfRefuseReasonItems);
        return NewResponseUtil.makeSuccess(result);
    }

    @RequestMapping(path = "/get-tag-info", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    @RequiresPermission("refuse:get-tag-info")
    public Response getTagInfo(int dataType) {
        LOGGER.info("CfApproveRefuseController getTagInfo dataType:{}", dataType);
        List<CfRefuseReasonTag> cfRefuseReasonTags = cfRefuseReasonTagBiz.selectByDataType(dataType);
        Map<String, Object> result = Maps.newHashMap();
        result.put("list", cfRefuseReasonTags);
        return NewResponseUtil.makeSuccess(result);
    }

    @RequestMapping(path = "/add-reason-tag", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    @RequiresPermission("refuse:add-reason-tag")
    public Response<String> addReasonTag(int dataType, String describe) {
        if (StringUtils.isEmpty(describe)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_IS_NULL);
        }
        cfRefuseReasonTagBiz.addReasonTag(ContextUtil.getAdminUserId(), dataType, describe);
        return NewResponseUtil.makeSuccess("");
    }

    @RequestMapping(path = "/change-tag-rank", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("refuse:change-tag-rank")
    public Response<String> changeTagRank(int upId, int downId, int operateType) {
        try {
            cfRefuseReasonTagBiz.updateReasonTagDataStep(ContextUtil.getAdminUserId(), upId, downId, operateType);
        } catch (Exception e) {
            log.error("上移、下移理由分类错误。userId:{} upId:{} downId:{} operateType:{}",
                    ContextUtil.getAdminUserId(), upId, downId, operateType, e);
        }

        return  NewResponseUtil.makeSuccess("");
    }

    @RequestMapping(path = "/change-reason-rank", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    @RequiresPermission("refuse:change-reason-rank")
    public Response changeReasonRank(int tagId, String reasons) {
        LOGGER.info("CfApproveRefuseController changeReasonRank tagId:{}, reasons:{}, userId:{}", tagId, reasons, ContextUtil.getAdminUserId());
        try {
            cfRefuseReasonTagBiz.updateReasonIds(reasons, tagId);
        } catch (Exception e) {
            LOGGER.error("CfApproveRefuseController changeReasonRank error!", e);
        }
        return NewResponseUtil.makeSuccess(null);
    }

    private void addEntityListToTags(List<CfRefuseReasonTag> cfRefuseReasonTags) {
        LOGGER.info("CfApproveRefuseController getRefuseList before cfRefuseReasonTags :{}", cfRefuseReasonTags.toString());
//        List<Integer> tagIds = cfRefuseReasonTags.stream().map(CfRefuseReasonTag::getId).collect(Collectors.toList());
//        List<CfRefuseReasonTag> cfRefuseReasonTagList = cfRefuseReasonTagBiz.selectByTagIds(Sets.newHashSet(tagIds));
//        cfRefuseReasonTagList.forEach(value -> {
//            if (StringUtils.isNotBlank(value.getReasonIds())) {
//                List<Integer> reasonIds = Lists.transform(Splitter.on(",").splitToList(value.getReasonIds()), Integer::parseInt);
//                List<CfRefuseReasonEntity> cfRefuseReasonEntities = cfRefuseReasonEntityBiz.selectByIds(reasonIds);
//                value.setEntityList(cfRefuseReasonEntities);
//            } else {
//                value.setEntityList(Lists.newArrayList());
//            }
//        });
//        Map<Integer, List<CfRefuseReasonEntity>> collect = cfRefuseReasonTagList.stream().collect(Collectors.toMap(CfRefuseReasonTag::getId, CfRefuseReasonTag::getEntityList));
        cfRefuseReasonTags.forEach(value -> {
//            List<CfRefuseReasonEntity> list = collect.get(value.getId());
//            value.setEntityList(list);
            value.setEntityList(cfRefuseReasonEntityBiz.queryRefuseEntityByDelStatus(value.getId(), 0, CfRefuseReasonEntity.RejectOptionUseSceneEnum.MATERIAL_VERIFY.getCode(), true));
        });
        LOGGER.info("CfApproveRefuseController getRefuseList after cfRefuseReasonTags:{}", cfRefuseReasonTags.toString());
    }

    private List<CfCommitVerifyItem> getCfCommitVerifyItemList(int type, String infoUuid) {
        List<CfCommitVerifyItem> dataTypes;
        List<Integer> dataType;
        if (type == 1) {
            List<CrowdfundingInfoStatus> crowdfundingInfoStatuses = crowdfundingDelegate.getCrowdfundingInfoStatusListByInfoUuid(infoUuid);
            dataType = crowdfundingInfoStatuses.stream().map(CrowdfundingInfoStatus::getType).collect(Collectors.toList());
        } else {
            dataType = Lists.newArrayList(1);
        }
        dataTypes = cfCommitVerifyItemBiz.selectByIds(Sets.newHashSet(dataType));
        if (CollectionUtils.isEmpty(dataTypes)) return Lists.newArrayList();
        List<CfRefuseReasonTag> cfRefuseReasonTags = AdminListUtil.getList(3000, (i, j) -> cfRefuseReasonTagBiz.selectAllWithUuid(i, j, infoUuid));
        ImmutableListMultimap<Integer, CfRefuseReasonTag> index = Multimaps.index(cfRefuseReasonTags, CfRefuseReasonTag::getDataType);
        dataTypes.forEach(value -> {
            List<CfRefuseReasonTag> cfRefuseReasonTags1 = index.get(value.getId());
            value.setTags(cfRefuseReasonTags1.stream().sorted(Comparator.comparing(CfRefuseReasonTag::getDataStep)).collect(Collectors.toList()));
        });
        this.addEntityListToTags(cfRefuseReasonTags);
        CfCommitVerifyItem cfCommitVerifyItem = dataTypes.get(0);
        dataTypes.remove(0);
        dataTypes.add(cfCommitVerifyItem);
        return dataTypes;
    }

    @RequestMapping(path = "/query-all-reject-scene", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("refuse:query-all-reject-scene")
    public Response<List<CfRefuseReasonEntity.RejectOptionObject>> queryAllRejectScene(@RequestParam("dataType") int dataType) {
        return NewResponseUtil.makeSuccess(reasonCommonBiz.queryAllRejectScene(dataType));
    }

    @RequestMapping(path = "/query-operate-log", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("refuse:query-operate-log")
    public Response<List<CfRefuseReasonEntity.CfRefuseReasonOperateLog>> queryReasonLogByEntityId(@RequestParam("entityId") int entityId) {
        return NewResponseUtil.makeSuccess(reasonCommonBiz.queryReasonLogByEntityId(entityId));
    }

    // 小页签 启用、弃用

    // 编辑驳回理由的使用场景
    @Deprecated
    @RequestMapping(path = "/edit-use-scene", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("refuse:edit-use-scene")
    public Response<String> editReasonEntityUseScene(@RequestParam("entityId") int entityId,
                                                     @RequestParam("useSceneIds") String useSceneIds) {

        try {
            CfRefuseModifyVo modifyVo = new CfRefuseModifyVo();
            modifyVo.setUserId(ContextUtil.getAdminUserId());
            modifyVo.setEntityId(entityId);
            modifyVo.setUseSceneIds(useSceneIds);
            CfRefuseReasonEntity entity = cfRefuseReasonEntityBiz.selectById(entityId, null);
            if (entity == null) {
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }
            modifyVo.setItemIds(entity.getItemIds());
            modifyVo.setSuggestModifyIds(entity.getSuggestModifyIds());
            modifyVo.setWeightServiceLib(entity.getWeightServiceLib());

            cfRefuseReasonEntityBiz.editReasonEntityUseScene(modifyVo);
        } catch (Exception e) {
            log.error("编辑驳回理由的使用场景错误. userId:{} id:{}, useSceneIds:{}", ContextUtil.getAdminUserId(),
                    entityId, useSceneIds, e);
            return NewResponseUtil.makeFail(e.getMessage());
        }

        return NewResponseUtil.makeSuccess("修改成功");
    }

    @RequestMapping(path = "/edit-use-scene-v1", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("refuse:edit-use-scene-v1")
    public Response<String> editReasonEntityUseScene(@RequestBody CfRefuseModifyVo modifyVo) {

        if (StringUtils.isBlank(modifyVo.getItemIds())) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        try {
            modifyVo.setUserId(ContextUtil.getAdminUserId());
            cfRefuseReasonEntityBiz.editReasonEntityUseScene(modifyVo);
        } catch (Exception e) {
            log.error("编辑驳回理由的使用场景错误. param:{}", JSON.toJSONString(modifyVo));
            return NewResponseUtil.makeFail(e.getMessage());
        }

        return NewResponseUtil.makeSuccess("修改成功");
    }


    @RequestMapping(path = "/edit-choice-relation-id", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("refuse:edit-choice-relation-id")
    public Response<String> editChoiceRelationId(@RequestParam("entityId") int entityId,
                                                 @RequestParam("relationIds") String relationIds) {
        try {
            cfRefuseReasonEntityBiz.editChoiceRelationId(ContextUtil.getAdminUserId(), entityId, relationIds);
        } catch (Exception e) {
            log.error("编辑关联id错误. userId:{} id:{}, useSceneIds:{}", ContextUtil.getAdminUserId(), entityId, relationIds, e);
            return NewResponseUtil.makeFail(e.getMessage());
        }

        return NewResponseUtil.makeSuccess("修改成功");
    }

    @RequestMapping(path = "/query-relation-entity", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("refuse:query-relation-entity")
    public Response<CfRefuseReasonEntity> queryCanRelationEntityById(@RequestParam("sourceId") int sourceId,
                                                                     @RequestParam("relationId") int relationId) {
        CfRefuseReasonEntity reasonEntity = null;
        try {
            reasonEntity = cfRefuseReasonEntityBiz.queryCanRelationEntityById(sourceId, relationId);
        } catch(Exception e) {
            log.error("查找关联关系错误. userId:{} sourceId:{}, relationId:{}",
                    ContextUtil.getAdminUserId(), sourceId, relationId, e);
            return NewResponseUtil.makeFail(e.getMessage());
        }

        return NewResponseUtil.makeSuccess(reasonEntity);
    }

    @RequestMapping(path = "/query-all-relation-entitys", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("refuse:query-all-relation-entitys")
    public Response<List<CfRefuseReasonEntity>>  queryAllRelationEntitys(@RequestParam("entityId") int entityId) {

        return NewResponseUtil.makeSuccess(cfRefuseReasonEntityBiz.queryAllRelationEntitys(entityId));
    }

    @RequestMapping(path = "/edit-entity-delete-status", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("refuse:edit-entity-delete-status")
    public Response<String> editEntityDeleteStatus(@RequestParam("entityId") int entityId,
                                                   @RequestParam("dataStatus") int dataStatus) {

        try {
            cfRefuseReasonEntityBiz.editEntityDeleteStatus(ContextUtil.getAdminUserId(), entityId, dataStatus);
        } catch (Exception e) {
            log.error("修改驳回理由状态错误。 userId:{}, entityId:{}, deleteStatus:{}",
                    ContextUtil.getAdminUserId(), entityId, dataStatus, e);
            return NewResponseUtil.makeFail(e.getMessage());
        }

        return NewResponseUtil.makeSuccess("");
    }

    @RequestMapping(path = "/edit-entity-rank", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("refuse:edit-entity-rank")
    public Response<String> editEntityRank(@RequestParam("upId") int upId,
                                           @RequestParam("downId") int downId,
                                           @RequestParam("operateType") int operateType,
                                           @RequestParam("useScene") int useScene) {
        try {
            cfRefuseReasonEntityBiz.editEntityRank(ContextUtil.getAdminUserId(), upId, downId, operateType, useScene);
        } catch (Exception e) {
            log.error("上移下移驳回理由错误。userId:{} upId:{}  downId:{} operateType:{} useScene:{}",
                                  ContextUtil.getAdminUserId(), upId, downId, operateType, useScene, e);
            return NewResponseUtil.makeFail(e.getMessage());
        }
        return NewResponseUtil.makeSuccess("");
    }


    @RequestMapping(path = "/query-by-data-status", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("refuse:query-by-data-status")
    public Response<List<CfRefuseReasonEntity>> queryByDataStatus(@RequestParam("tagId") int tagId,
                                                                  @RequestParam("dataStatus") int dataStatus,
                                                                  @RequestParam("useScene") int useScene) {

        return  NewResponseUtil.makeSuccess(cfRefuseReasonEntityBiz.queryRefuseEntityByDelStatus(tagId, dataStatus, useScene, false));
    }

    @PostMapping(path = "/query-suggest-modify-view")
    @RequiresPermission("refuse:query-suggest-modify-view")
    public Response<List<CfRefuseSuggestModify>> queryByDataStatus(@RequestParam("dataType") int dataType) {
        return  NewResponseUtil.makeSuccess(suggestModifyBiz.selectSuggestModifyByDateTypes(Lists.newArrayList(dataType)));
    }

    @PostMapping(path = "/query-reject-reason/suggest-modify")
    @RequiresPermission("refuse:query-reject-reason/suggest-modify")
    public Response<List<CfRefuseSuggestModify>> queryByDataStatus(@RequestParam  List<Integer> entityIds) {
        return  NewResponseUtil.makeSuccess(cfRefuseReasonEntityBiz.querySuggestByEntityIds(entityIds));
    }

    /**
     * 将驳回理由和案例版本绑定
     */
    @PostMapping(path = "/add-binding-relation")
    @RequiresPermission("refuse:add-binding-relation")
    public Response<Void> addBindingRelation(@RequestParam("materialPlanId") int materialPlanId, @RequestParam("entityId") int entityId) {
        List<Integer> entities = cfRefuseReasonEntityBiz.getRefuseReasonEntitiesByMaterialPlanId(materialPlanId);
        if (entities.contains(entityId)) {
            return NewResponseUtil.makeSuccess();
        }
        boolean success = cfRefuseReasonEntityBiz.bindEntityToMaterial(entityId, materialPlanId);
        return success ? NewResponseUtil.makeSuccess() : NewResponseUtil.makeFail("驳回理由和案例材料版本绑定失败");
    }

    /**
     * 批量将驳回理由和案例版本绑定
     */
    @PostMapping(path = "/add-binding-relation-batch")
    @RequiresPermission("refuse:add-binding-relation-batch")
    public Response<Void> addBindingRelationBatch(@RequestBody Map<String, String> mapping) {
        List<String> planIds = mapping.values().stream().distinct().collect(Collectors.toList());
        if (planIds.size() != 1) {
            return NewResponseUtil.makeFail("只能给一种材料版本批量绑定关系");
        }
        List<Integer> entities = cfRefuseReasonEntityBiz.getRefuseReasonEntitiesByMaterialPlanId(Integer.parseInt(planIds.get(0)));
        for (Integer entity : entities) {
            mapping.remove(String.valueOf(entity));
        }
        boolean success = cfRefuseReasonEntityBiz.bindEntityToMaterialBatch(mapping);
        return success ? NewResponseUtil.makeSuccess() : NewResponseUtil.makeFail("驳回理由和案例材料版本批量绑定失败");
    }

    /**
     * 删除驳回理由和案例版本绑定关系
     */
    @PostMapping(path = "delete-binding-relation")
    @RequiresPermission("refuse:delete-binding-relation")
    public Response<Void> deleteBindingRelation(@RequestParam("id") int id) {
        boolean success = cfRefuseReasonEntityBiz.deleteBindingRelation(id);
        return success ? NewResponseUtil.makeSuccess() : NewResponseUtil.makeFail("驳回理由和案例材料版本绑定关系删除失败");
    }

    /**
     * 复制驳回理由绑定关系
     * @param sourcePlanId 源案例材料版本
     * @param targetPlanId 目标案例材料版本
     * @return
     */
    @PostMapping(path = "duplicate-binding-relation")
    @RequiresPermission("refuse:duplicate-binding-relation")
    public Response<Void> duplicateBindingRelation(@RequestParam("sourcePlanId") int sourcePlanId, @RequestParam("targetPlanId") int targetPlanId) {
        List<Integer> sourceEntities = cfRefuseReasonEntityBiz.getRefuseReasonEntitiesByMaterialPlanId(sourcePlanId);
        List<Integer> targetEntities = cfRefuseReasonEntityBiz.getRefuseReasonEntitiesByMaterialPlanId(targetPlanId);
        sourceEntities = sourceEntities.stream().filter(o -> !targetEntities.contains(o)).collect(Collectors.toList());
        Map<String, String> mapping = new HashMap<>();
        sourceEntities.forEach(id -> mapping.put(String.valueOf(id), String.valueOf(targetPlanId)));
        boolean success = cfRefuseReasonEntityBiz.bindEntityToMaterialBatch(mapping);
        return success ? NewResponseUtil.makeSuccess() : NewResponseUtil.makeFail("复制绑定关系失败");
    }

    /**
     * 修改驳回理由绑定关系
     * @param id
     * @param newPlanId
     * @param newEntityId
     * @return
     */
    @PostMapping(path = "update-binding-relation")
    @RequiresPermission("refuse:update-binding-relation")
    public Response<Void> updateBindingRelation(@RequestParam("id") int id, @RequestParam("planId") int newPlanId, @RequestParam("entityId") int newEntityId) {
        if (id <= 0 || newPlanId <= 0 || newEntityId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        boolean success = cfRefuseReasonEntityBiz.updateBindingRelation(id, newPlanId, newEntityId);
        return success ? NewResponseUtil.makeSuccess() : NewResponseUtil.makeFail("修改绑定关系失败");
    }

    @PostMapping(path = "query-plan-id-of-entity")
    @RequiresPermission("resfuse:query-plan-id-of-entity")
    public Response<Map<String, Integer>> queryPlanIdOfEntity(@RequestBody List<Integer> entityIds) {
        Map<String, Integer> data = cfRefuseReasonEntityBiz.getMaterialPlanIdsOfEntities(entityIds);
        return NewResponseUtil.makeSuccess(data);
    }

}
