package com.shuidihuzhu.cf.admin.controller.api.questionnaire;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.questionnaire.WxQuestionnaireAdminBiz;
import com.shuidihuzhu.cf.model.common.BaseResult;
import com.shuidihuzhu.cf.model.questionnaire.WxQuestion;
import com.shuidihuzhu.cf.model.questionnaire.WxQuestionnaire;
import com.shuidihuzhu.cf.vo.questionnaire.WxSdTagValueAdminVo;
import com.shuidihuzhu.cf.vo.questionnaire.WxSdTagAdminVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.pay.common.model.PageRequest;
import com.shuidihuzhu.pay.common.model.PageResponse;
import com.shuidihuzhu.pay.common.util.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: mhk
 * @Date: 2018/11/08
 */
@Slf4j
@RestController
@RequestMapping(path = "/admin/cf/questionaire")
public class WxQuestionnaireController {

    @Autowired
    WxQuestionnaireAdminBiz wxQuestionnaireBiz;
    @Autowired
    SeaAccountClientV1 seaAccountClientV1;
    /**
     *  分页查询
     * @return
     */
    @PostMapping(path = "/list")
    @RequiresPermission("questionaire:query")
    public Response query(
            @RequestParam(required = false, defaultValue = "") String name,
            @RequestParam(required = false, defaultValue = "") String title,
            @RequestParam(required = false, defaultValue = "") String tagName,
            @RequestParam(required = false, defaultValue = "") String createBy,
            @RequestParam("pageJson") String pageJson) {
        PageRequest pageRequest = PageUtil.parseJsonString(pageJson);

        PageResponse pageResponse = wxQuestionnaireBiz.getWxQuestionnairePage(name, title, tagName, createBy, pageRequest);
        if(null == pageResponse){
            return NewResponseUtil.makeSuccess(null);
        }
        return NewResponseUtil.makeSuccess(pageResponse);
    }

    /**
     * 添加问卷
     */
    @PostMapping(path = "/save")
    @RequiresPermission("questionaire:save")
    public Response save(
            @RequestParam(required = false,defaultValue = "0") int id,
            @RequestParam("name")  String name,
            @RequestParam("title") String title,
            @RequestParam("questionList") String questionJson){
        int userId = ContextUtil.getAdminUserId();
        AdminUserAccountModel accountModel = seaAccountClientV1.getValidUserAccountById(userId).getResult();
        BaseResult<Boolean> result;
        List<WxQuestion> questionList = JSON.parseArray(questionJson, WxQuestion.class);//已检查过
        if(id == 0){
            result = this.wxQuestionnaireBiz.addWxQuestionnaire(name, title, questionList, accountModel.getName());
        }else{
            result = this.wxQuestionnaireBiz.editWxQuestionnaire(id, name, title, questionList, accountModel.getName());
        }
        return NewResponseUtil.makeResponse(result.getCode(),result.getDesc(),result.getData());
    }

    /**
     * 查询详情
     */
    @PostMapping(path= "/detail")
    @RequiresPermission("questionaire:query")
    public Response detail(int id) {
        WxQuestionnaire wxQuestionnaire = this.wxQuestionnaireBiz.getWxQuestionnaire(id);
        return NewResponseUtil.makeSuccess(wxQuestionnaire);
    }

    /**
     * 标签列表
     */
    @PostMapping(path = "/tag/get-list")
    @RequiresPermission("questionaire:save")
    public Response getAllTags() {
        List<WxSdTagAdminVo> wxSdTagVos = this.wxQuestionnaireBiz.getAllTagList();
        return NewResponseUtil.makeSuccess(wxSdTagVos);
    }

    /**
     * 标签值
     */
    @PostMapping(path = "/tag/get-tag-values")
    @RequiresPermission("questionaire:save")
    public Response tagList(int tagId) {
        List<WxSdTagValueAdminVo> tagValues = this.wxQuestionnaireBiz.getTagValues(tagId);
        return NewResponseUtil.makeSuccess(tagValues);
    }

}
