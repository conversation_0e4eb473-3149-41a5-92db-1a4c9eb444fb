package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.admin.util.admin.IntegerUtil;
import com.shuidihuzhu.cf.delegate.commonservice.ICommonServiceDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.graytest.CfGrayTestSwitch;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by chao on 2017/6/30.
 */
@Controller
@RequestMapping(path = "/admin/cf/graytest")
public class CfGrayTestConfigController {

	@Autowired
	private ICommonServiceDelegate commonServiceDelegate;
	@Autowired
	private AlarmClient alarmClient;
	@Autowired
	SeaAccountClientV1 seaAccountClientV1;

	@RequestMapping(path = "list", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@ResponseBody
	public Response list(@RequestParam("currentPage") int currentPage, @RequestParam("size") int size) {
		Map<String, Object> result = Maps.newHashMap();

		int start = currentPage * size;
		List<CfGrayTestSwitch> data = commonServiceDelegate.getByPage(start, size);
		result.put("data", data);

		Map<String, Integer> pagination = Maps.newHashMap();
		pagination.put("current", currentPage);
		pagination.put("total", commonServiceDelegate.total());
		pagination.put("size", size);

		result.put("pagination", pagination);

		return NewResponseUtil.makeSuccess(result);
	}

	@RequestMapping(path = "updatePercentage", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@ResponseBody
	public Response update(@RequestParam("code") String code,
	                       @RequestParam("percentage") String percentage) {
		commonServiceDelegate.updateCasePercentage(code, percentage);
		return NewResponseUtil.makeSuccess(null);
	}

	@RequestMapping(path = "updateRecordResult", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@ResponseBody
	public Response updateRecordResult(@RequestParam("code") String code,
	                       @RequestParam("ifRecordResult") boolean ifRecordResult) {
		commonServiceDelegate.updateIfRecordResult(code, ifRecordResult);
		return NewResponseUtil.makeSuccess(null);
	}

	@RequestMapping(path = "update", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@ResponseBody
	public Response update(@RequestParam("code") String code,
	                       @RequestParam("percentage") String percentage,
	                       @RequestParam("ifRecordResult") boolean ifRecordResult) {
		Integer userId = ContextUtil.getAdminUserId();
		if(!checkSum(percentage)) {
			return NewResponseUtil.makeResponse(-1, "百分比之和不为100", null);
		}

		CfGrayTestSwitch oldSwitch = commonServiceDelegate.getByCode(code);
		if (null == oldSwitch) {
			return NewResponseUtil.makeResponse(-1, "code不存在", null);
		}
		if (!oldSwitch.getCasePercentage().equalsIgnoreCase(percentage)) {
			commonServiceDelegate.updateCasePercentage(code, percentage);
		}

		if (oldSwitch.isIfRecordResult() != ifRecordResult) {
			commonServiceDelegate.updateIfRecordResult(code, ifRecordResult);
		}
		if ("cf-task-publish-switch".equals(oldSwitch.getCode())) {
			AdminUserAccountModel admin = seaAccountClientV1.getValidUserAccountById(userId).getResult();
			String warnMsg = "【cf-task强制发版报警】\n状态:" + percentage + "\n操作者："+ admin.getName() + "\n操作时间：" + DateUtil.getTimeStringFromTimestamp(new Timestamp(System.currentTimeMillis()));
			alarmClient.sendByGroup("cf-server-alarm", warnMsg + "\n@刘佳伟");
			alarmClient.sendByUser(Lists.newArrayList("liujiawei"), warnMsg);
		}
		return NewResponseUtil.makeSuccess(null);
	}


	@RequestMapping(path = "delete", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@ResponseBody
	public Response delete(@RequestParam("code") String code) {
		commonServiceDelegate.delete(code);
		return NewResponseUtil.makeSuccess(null);
	}

	@RequestMapping(path = "add", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@ResponseBody
	public Response add(@RequestParam("bizType") int bizType,
	                    @RequestParam("code") String code,
	                    @RequestParam("percentage") String percentage,
	                    @RequestParam("description") String description,
	                    @RequestParam("isResultRecord") boolean isResultRecord) {
		CfGrayTestSwitch grayTestSwitch = commonServiceDelegate.getByCode(code);
		if (null != grayTestSwitch) {
			return NewResponseUtil.makeResponse(-1, "code已经存在", null);
		}

		if(!checkSum(percentage)) {
			return NewResponseUtil.makeResponse(-1, "百分比之和不为100", null);
		}

		commonServiceDelegate.add(bizType, code, percentage, description, isResultRecord);
		return NewResponseUtil.makeSuccess(null);
	}

	private boolean checkSum(String percentage) {
		// 检查百分比
		List<String> items = Splitter.on(",").splitToList(percentage);
		if(CollectionUtils.isEmpty(items)) {
			return false;
		}

		List<Integer> percentages = items.stream().map(item -> IntegerUtil.parseInt(item)).collect(Collectors.toList());
		int sum = 0;
		for(int per : percentages) {
			sum += per;
		}
		return sum == 100;
	}

	@RequestMapping(path = "/query", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@ResponseBody
	public Response getLikeCode(@RequestParam("code") String code ){
		if (StringUtils.isEmpty(code)) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}
		List<CfGrayTestSwitch> cfGrayTestSwitchList = Collections.emptyList();
		cfGrayTestSwitchList = commonServiceDelegate.getLikeCode(code);
		return NewResponseUtil.makeSuccess(cfGrayTestSwitchList);
	}
}
