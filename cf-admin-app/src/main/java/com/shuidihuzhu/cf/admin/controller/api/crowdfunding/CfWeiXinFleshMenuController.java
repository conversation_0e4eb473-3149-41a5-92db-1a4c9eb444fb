package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.GsonBuilder;
import com.shuidihuzhu.cf.admin.util.admin.IntegerUtil;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.admin.common.WxMenuExcelCodeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.WxMenuInit;
import com.shuidihuzhu.cf.model.wx.WxMiniAppMenuButton;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.wx.biz.WxMpConfigBiz;
import com.shuidihuzhu.wx.model.WxMpConfig;
import me.chanjar.weixin.common.bean.menu.WxMenu;
import me.chanjar.weixin.common.bean.menu.WxMenuButton;
import me.chanjar.weixin.common.bean.menu.WxMenuRule;
import me.chanjar.weixin.mp.api.WxMpInMemoryConfigStorage;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.io.InputStream;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 微信修改菜单
 * Author:梁洪超
 * Date:2017/12/10
 */

@Controller
@RequestMapping(path = "/admin/crowdfunding/upload")
public class CfWeiXinFleshMenuController {
    private static final String STRING_FLAG = "Mychannel=";
    private static final String FAIL = "fail";
    private static final String SUCCESS = "success";
    private static final String EMPTY = "empty";

    private static final Logger LOGGER = LoggerFactory.getLogger(CfWeiXinFleshMenuController.class);
    @Autowired
    private WxMpConfigBiz wxMpConfigBiz;

    @RequiresPermission("fleshmenu:file")
    @RequestMapping(path = "/file", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response importFormFile(MultipartHttpServletRequest request) {
        Iterator<String> iterator = request.getFileNames();
        if (!iterator.hasNext()) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        String fileName = iterator.next();
        MultipartFile multipartFile = request.getFile(fileName);
        String originalFilename = multipartFile.getOriginalFilename();
        String extensionName = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
        LOGGER.info(
                "CfWeiXinFleshMenuController importFormExcel fileName:{};originalFilename:{};extensionName:{}",
                fileName, originalFilename, extensionName);
        if (!"xlsx".equalsIgnoreCase(extensionName)) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR, "文件格式不正确，只能识别xlsx的excel");
        }
        InputStream inputStream;
        try {
            inputStream = multipartFile.getInputStream();
        } catch (Exception e) {
            LOGGER.error("CfWeiXinFleshMenuController importFormExcel 转化为input出错");
            return NewResponseUtil
                    .makeError(CfErrorCode.SYSTEM_ERROR, "CfWeiXinFleshMenuController importFormExcel 转化为input出错");
        }
        try {
            JSONObject jsonObject = this.analyzeExcelFile(inputStream);
            return NewResponseUtil.makeSuccess(jsonObject);
        } catch (Exception e) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_ERROR, "在解析数据时" + e.getMessage());
        }


    }


    private JSONObject doCreateMenuSend(Map<String, Map<Integer, WxMenuInit[]>> excelMap) {

        Map<String, List<Integer>> resultMap = Maps.newHashMap();
        resultMap.put(FAIL, Lists.newArrayList());
        resultMap.put(SUCCESS, Lists.newArrayList());
        resultMap.put(EMPTY, Lists.newArrayList());
        for (Map.Entry<String, Map<Integer, WxMenuInit[]>> stringMapEntry : excelMap.entrySet()) {

            Map<Integer, WxMenuInit[]> menusMap = stringMapEntry.getValue();

            WxMenu wxMenu = new WxMenu();

            List<WxMenuButton> onceMenuButtonList = Lists.newArrayList();
            String[] indexStrings = null;

            //构建菜单
            for (int i = 1; i < 4; i++) {
                //一级菜单
                WxMenuInit[] wxMenuInitArray = menusMap.get(i);
                if (wxMenuInitArray == null || wxMenuInitArray.length == 0) {
                    continue;
                }
                //n_0,一级菜单
                WxMenuInit onceWxMenuInit = wxMenuInitArray[0];
                if (onceWxMenuInit == null) {
                    continue;
                }
                if (onceWxMenuInit.getMatchRuleFlag() != null && null != onceWxMenuInit.getWxMenuRule()) {
                    wxMenu.setMatchRule(onceWxMenuInit.getWxMenuRule());
                }
                //是否批量修改与修改的thirdType取第一组的为准
                if (i == 1) {
                    String bachs = onceWxMenuInit.getBachs();
                    if (!StringUtils.isEmpty(bachs)) {
                        indexStrings = bachs.trim().split(",");
                    }
                }

                WxMenuButton onceWxMenuButton = onceWxMenuInit.getWxMenuButton();
                List<WxMenuButton> secondMenuButtonList = Lists.newArrayList();
                //二级菜单n_1.....
                for (int j = 1; j < 7; j++) {
                    WxMenuInit wxMenuInit = wxMenuInitArray[j];
                    if (wxMenuInit == null) {
                        continue;
                    }
                    if (wxMenuInit.getMatchRuleFlag() != null &&
                            wxMenuInit.getMatchRuleFlag().equalsIgnoreCase(WxMenuExcelCodeEnum.OK.getCodeString())) {
                        //同一组menu有多个matchrule会以最后一个为原则，其他被覆盖掉。
                        wxMenu.setMatchRule(wxMenuInit.getWxMenuRule());
                    }
                    secondMenuButtonList.add(wxMenuInit.getWxMenuButton());
                }
                onceWxMenuButton.setSubButtons(secondMenuButtonList);
                onceMenuButtonList.add(onceWxMenuButton);
            }
            wxMenu.setButtons(onceMenuButtonList);
            //批量去更新
            doBachesString(indexStrings, wxMenu, resultMap);
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("成功的公众号ThirdType", resultMap.get(SUCCESS));
        jsonObject.put("失败的公众号ThirdType", resultMap.get(FAIL));
        jsonObject.put("无公众号与ThirdType对应", resultMap.get(EMPTY));
        return jsonObject;
    }

    private void doBachesString(String[] indexStrings, WxMenu wxMenu, Map<String, List<Integer>> resultMap) {
        if (null == indexStrings) {
            return;
        }
        int statr;
        int end;
        int onlyType;
        for (String indexs : indexStrings) {
            if (StringUtils.isEmpty(indexs)) {
                continue;
            }
            if (indexs.contains("_")) {
                String[] itemIndexs = indexs.trim().split("_");
                statr = IntegerUtil.parseInt(itemIndexs[0]);
                end = IntegerUtil.parseInt(itemIndexs[1]);
                if (end < statr) {
                    end = end ^ statr;
                    statr = end ^ statr;
                    end = end ^ statr;
                }

                for (int i = statr; i <= end; i++) {
                    WxMenu itemWxMenu = this.copyWxMenu(wxMenu);
                    this.cheakoutUrl(i, itemWxMenu);
                    this.doSendCreateMenu(i, itemWxMenu, resultMap);
                }
                statr = end = 0;
            } else {
                onlyType = IntegerUtil.parseInt(indexs);
                WxMenu itemWxMenu = this.copyWxMenu(wxMenu);
                this.cheakoutUrl(onlyType, itemWxMenu);
                this.doSendCreateMenu(onlyType, itemWxMenu, resultMap);
                onlyType = 0;
            }
        }

    }

    private void doSendCreateMenu(int thirdType, WxMenu wxMenu, Map<String, List<Integer>> resultMap) {
        WxMpInMemoryConfigStorage wxMpInMemoryConfigStorage =
                this.initWxMpInMemoryConfigStorage(thirdType);
        if (wxMpInMemoryConfigStorage == null) {
            resultMap.get(EMPTY).add(thirdType);
            return;
        }
        WxMpService wxMpService = new WxMpServiceImpl();
        wxMpService.setWxMpConfigStorage(wxMpInMemoryConfigStorage);
        for (WxMenuButton button : wxMenu.getButtons()) {
            for (WxMenuButton subBtn : button.getSubButtons()) {
                subBtn.setSubButtons(null);
            }
        }
        //JSON标准化一下
        String menuJson = new GsonBuilder().disableHtmlEscaping().create().toJson(wxMenu);
        menuJson = menuJson.replace("buttons", "button")
                .replace("appId", "appid")
                .replace("pagePath", "pagepath")
                .replace("subButtons", "sub_button")
                .replace("matchRule", "matchrule")
                .replace("tagId", "tag_id");
        try {
            LOGGER.info("发送的配置信息 wxMenu:{}", wxMenu);
            LOGGER.info("发送的配置信息 menuJson:{}", menuJson);
           wxMpService.getMenuService().menuCreate(menuJson);
            resultMap.get(SUCCESS).add(thirdType);
        } catch (Exception e) {
            LOGGER.error("doCreateMenuSend 请求腾讯出错", e);
            resultMap.get(FAIL).add(thirdType);
        }
    }

    private WxMpInMemoryConfigStorage initWxMpInMemoryConfigStorage(int thirdType) {
        WxMpConfig wxMpConfig = wxMpConfigBiz.getByThirdType(thirdType);
        if (wxMpConfig == null) {
            LOGGER.debug("WxMpInMemoryConfigStorage 不存在对应的thirdType:{}", thirdType);
            return null;
        }
        WxMpInMemoryConfigStorage wxMpInMemoryConfigStorage = new WxMpInMemoryConfigStorage();
        wxMpInMemoryConfigStorage.setAppId(wxMpConfig.getAppId());
        wxMpInMemoryConfigStorage.setSecret(wxMpConfig.getSecret());
        wxMpInMemoryConfigStorage.setToken(wxMpConfig.getToken());
        wxMpInMemoryConfigStorage.setAesKey(wxMpConfig.getAesKey());
        return wxMpInMemoryConfigStorage;
    }

    private JSONObject analyzeExcelFile(InputStream inputStream) {
        try {
            Workbook workbook = WorkbookFactory.create(inputStream);
            Map<String, Map<Integer, WxMenuInit[]>> sheetMenuInitMap = Maps.newHashMap();
            for (Sheet sheet : workbook) {
                if (null == sheet) {
                    continue;
                }
                //读取一个sheet的内容
                Map<String, Map<String, String>> mapSheet = this.analyzeSheetToMap(sheet);
                //构建menu对象
                this.doCreateMenuMap(sheet, mapSheet, sheetMenuInitMap);
            }
            //发送请求
            return this.doCreateMenuSend(sheetMenuInitMap);
        } catch (Exception e) {
            LOGGER.error("analyzeExcelFile", e);
            throw new IllegalArgumentException(e);
        }

    }

    /**
     * 读取一个sheet的内容
     *
     * @param sheet
     * @return
     */
    private Map<String, Map<String, String>> analyzeSheetToMap(Sheet sheet) {
        if (sheet == null) {
            return Maps.newHashMap();
        }
        Map<String, Map<String, String>> resultSheetMap = Maps.newHashMap();
        DataFormatter dataFormatter = new DataFormatter();
        int numRows = sheet.getLastRowNum();
        //0行为列名的行
        for (int i = 1; i <= numRows; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                continue;
            }
            Map<String, String> mapRow = Maps.newHashMap();
            for (Cell cell : row) {
                if (cell == null) {
                    continue;
                }

                String cellName = sheet.getRow(0).getCell(cell.getColumnIndex()).getStringCellValue();
                String cellValue = dataFormatter.formatCellValue(cell);
                //空的单元格，不会去读取信息
                mapRow.put(cellName, cellValue);
            }
            //判断是不是空白行
            if (this.isBlockRow(mapRow)) {
                continue;
            }
            if (StringUtils.isEmpty(mapRow.get(WxMenuExcelCodeEnum.NAME.getCodeString()))) {
                //抛出异常终止
                throw new IllegalArgumentException("请检查Excel表，有一行的name为空," + "行号:" + i);
            }
            if (StringUtils.isEmpty(mapRow.get(WxMenuExcelCodeEnum.TYPE.getCodeString()))) {
                throw new IllegalArgumentException("按钮类型type不能为空" + row.getRowNum() + "行号:" + i);
            }
            if (!MapUtils.isEmpty(mapRow)) {
                resultSheetMap.put(mapRow.get(WxMenuExcelCodeEnum.NAME.getCodeString()) + i, mapRow);
            }
        }
        return resultSheetMap;
    }

    private void doCreateMenuMap(Sheet sheet, Map<String, Map<String, String>> sheetMap,
                                 Map<String, Map<Integer, WxMenuInit[]>> sheetMenuInitMap) {
        if (MapUtils.isEmpty(sheetMap) || sheet == null) {
            return;
        }
        String sheetName = sheet.getSheetName();
        //保存一页sheet的内容,得到sheet的角标名(excel不允许角标命重复)
        Map<Integer, WxMenuInit[]> mapMenu = Maps.newHashMap();

        for (Map.Entry<String, Map<String, String>> entrySheet : sheetMap.entrySet()) {

            Map<String, String> menuMap = entrySheet.getValue();

            WxMenuInit wxMenuInit = new WxMenuInit();
            String menuType = menuMap.get(WxMenuExcelCodeEnum.MENU_TYPE.getCodeString());
            if (StringUtils.isEmpty(menuType)) {
                //抛出异常，menuType设置不合法
                throw new IllegalArgumentException("menuType设置不能为空");
            }

            String[] menuTypes = menuType.trim().split("_");
            Integer fatherIndex;
            Integer subIndex;
            //为菜单的绝对位置
            try {
                fatherIndex = IntegerUtil.parseInt(menuTypes[0]);
                subIndex = IntegerUtil.parseInt(menuTypes[1]);
            } catch (Exception e) {
                throw new IllegalArgumentException("menuType设置不合法，以下划线分割_");
            }
            if (fatherIndex < 1 || fatherIndex > 3 || subIndex < 0 || subIndex > 6) {
                throw new IllegalArgumentException("menuType设置不合法，范围超出了菜单允许的范围，一级餐单1到3，二级菜单0到6");
            }

            wxMenuInit.setMenuType(menuMap.get(WxMenuExcelCodeEnum.MENU_TYPE.getCodeString()));
            wxMenuInit.setBachFlag(menuMap.get((WxMenuExcelCodeEnum.BACH_FLAG.getCodeString())));
            wxMenuInit.setBachs(menuMap.get((WxMenuExcelCodeEnum.BACHS.getCodeString())));
            wxMenuInit.setMatchRuleFlag(menuMap.get((WxMenuExcelCodeEnum.MATCHRULE.getCodeString())));
            wxMenuInit.setChannel(menuMap.get(WxMenuExcelCodeEnum.CHANNEL.getCodeString()));
            wxMenuInit.checkBach();
            //是否有匹配规则
            if (WxMenuExcelCodeEnum.OK.getCodeString().equals(menuMap.get(WxMenuExcelCodeEnum.MATCHRULE.getCodeString()))) {
                wxMenuInit.setBachFlag(WxMenuExcelCodeEnum.MATCHRULE.getCodeString());
                WxMenuRule wxMenuRule = new WxMenuRule();
                wxMenuRule.setTagId(menuMap.get(WxMenuExcelCodeEnum.TAG_ID.getCodeString()));
                wxMenuRule.setSex(menuMap.get(WxMenuExcelCodeEnum.SEX.getCodeString()));
                wxMenuRule.setCountry(menuMap.get(WxMenuExcelCodeEnum.COUNTRY.getCodeString()));
                wxMenuRule.setProvince(menuMap.get(WxMenuExcelCodeEnum.PROVINCE.getCodeString()));
                wxMenuRule.setCity(menuMap.get(WxMenuExcelCodeEnum.CITY.getCodeString()));
                wxMenuRule.setClientPlatformType(menuMap.get(WxMenuExcelCodeEnum.CLIENT_PLATFORM_TYPE.getCodeString()));
                wxMenuRule.setLanguage(menuMap.get(WxMenuExcelCodeEnum.LANGUAGE.getCodeString()));
                wxMenuInit.setWxMenuRule(wxMenuRule);
                //需要验证匹配规则合法不
                wxMenuInit.checkRuleFlag();
            }

            //建立按钮
            WxMenuButton wxMenuButton = new WxMenuButton();
            if ("miniprogram".equalsIgnoreCase(menuMap.get(WxMenuExcelCodeEnum.TYPE.getCodeString()))) {
                wxMenuButton = new WxMiniAppMenuButton();
                ((WxMiniAppMenuButton) wxMenuButton).setAppId(menuMap.get(WxMenuExcelCodeEnum.APP_ID.getCodeString()));
                ((WxMiniAppMenuButton) wxMenuButton).setPagePath(menuMap.get(WxMenuExcelCodeEnum.PAGE_PATH.getCodeString()));
            }
            wxMenuButton.setKey(menuMap.get(WxMenuExcelCodeEnum.KEY.getCodeString()));
            wxMenuButton.setName(menuMap.get(WxMenuExcelCodeEnum.NAME.getCodeString()));
            wxMenuButton.setType(menuMap.get(WxMenuExcelCodeEnum.TYPE.getCodeString()));
            wxMenuButton.setUrl(menuMap.get(WxMenuExcelCodeEnum.URL.getCodeString()));
            wxMenuButton.setMediaId(menuMap.get(WxMenuExcelCodeEnum.MEDIA_ID.getCodeString()));
            wxMenuInit.setWxMenuButton(wxMenuButton);
            //对channel进行预处理
            String url = wxMenuButton.getUrl();
            String channel = wxMenuInit.getChannel();
            if (!StringUtils.isEmpty(url) && !StringUtils.isEmpty(channel)) {
                if (!url.contains("channel=")) {
                    if (url.contains("?")) {
                        wxMenuButton.setUrl(url + "&" + STRING_FLAG + channel);
                    } else {
                        wxMenuButton.setUrl(url + "?" + STRING_FLAG + channel);
                    }
                }
            }

            //保存菜单
            WxMenuInit[] wxMenuInits = mapMenu.get(fatherIndex);
            if (wxMenuInits == null) {
                wxMenuInits = new WxMenuInit[7];
                mapMenu.put(fatherIndex, wxMenuInits);
            }
            wxMenuInits[subIndex] = wxMenuInit;
        }
        sheetMenuInitMap.put(sheetName, mapMenu);
    }

    private boolean isBlockRow(Map<String, String> map) {
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if (!StringUtils.isEmpty(entry.getValue())) {
                return false;
            }
        }
        return true;
    }

    private void cheakoutUrl(int wxMpType, WxMenu wxMenu) {
        List<WxMenuButton> list = wxMenu.getButtons();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (WxMenuButton wxMenuButton : list) {
            if (null == wxMenu) {
                continue;
            }
            String url = this.getUrlWithChannel(wxMenuButton.getUrl(), wxMpType);
            wxMenuButton.setUrl(url);
            List<WxMenuButton> sublist = wxMenuButton.getSubButtons();
            if (CollectionUtils.isEmpty(sublist)) {
                continue;
            }
            for (WxMenuButton subButton : sublist) {
                if (null == subButton) {
                    continue;
                }
                String subUrl = this.getUrlWithChannel(subButton.getUrl(), wxMpType);
                subButton.setUrl(subUrl);
            }
        }
    }

    //wxMpType 其实就是 thirdType
    private String getUrlWithChannel(String url, int wxMpType) {
        if (StringUtils.isEmpty(url)) {
            return url;
        }
        if (url.contains(STRING_FLAG)) {
            String item = url.substring(url.indexOf("?") - 1);
            String[] itemArray = item.split("&");
            int i = 0;
            String channel = "";
            for (String keyValue : itemArray) {
                if (keyValue.contains(STRING_FLAG)) {
                    String[] channels = keyValue.split("=");
                    i = channels[1].length();
                    channel = channels[1];
                    break;
                }
            }
            url = url.substring(0, url.indexOf(STRING_FLAG)) + "channel=" + channel + "_" + wxMpType +
                    url.substring(url.indexOf(channel) + i);
        }

        if (url.contains("?")) {
            return (url + "&wxMpType=" + wxMpType);
        } else {
            return (url + "?wxMpType=" + wxMpType);
        }
    }

    private WxMenu copyWxMenu(WxMenu wxMenu) {

        WxMenu resultWxMenu = new WxMenu();
        List<WxMenuButton> sourceButtons = wxMenu.getButtons();
        List<WxMenuButton> onceCopyButtons = Lists.newArrayList();

        for (WxMenuButton onceMenuButton : sourceButtons) {
            //对一级菜单的copy
            WxMenuButton onceCopyWxMenuButton = new WxMenuButton();

            onceCopyWxMenuButton.setKey(onceMenuButton.getKey());
            onceCopyWxMenuButton.setMediaId(onceMenuButton.getMediaId());
            onceCopyWxMenuButton.setName(onceMenuButton.getName());
            onceCopyWxMenuButton.setType(onceMenuButton.getType());
            onceCopyWxMenuButton.setUrl(onceMenuButton.getUrl());

            //对二级子菜单的copy
            List<WxMenuButton> subSourceButtons = onceMenuButton.getSubButtons();
            List<WxMenuButton> subCopyButtons = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(subSourceButtons)) {
                for (WxMenuButton subSourceMenuButton : subSourceButtons) {
                    //对于小程序的copy要特殊处理
                    WxMenuButton subCopyMenuButton = new WxMenuButton();
                    if ("miniprogram".equalsIgnoreCase(subSourceMenuButton.getType())) {
                        subCopyMenuButton = new WxMiniAppMenuButton();
                        ((WxMiniAppMenuButton) subCopyMenuButton).setAppId(((WxMiniAppMenuButton) subSourceMenuButton).getAppId());
                        ((WxMiniAppMenuButton) subCopyMenuButton).setPagePath(((WxMiniAppMenuButton) subSourceMenuButton).getPagePath());
                    }
                    subCopyMenuButton.setUrl(subSourceMenuButton.getUrl());
                    subCopyMenuButton.setKey(subSourceMenuButton.getKey());
                    subCopyMenuButton.setMediaId(subSourceMenuButton.getMediaId());
                    subCopyMenuButton.setName(subSourceMenuButton.getName());
                    subCopyMenuButton.setType(subSourceMenuButton.getType());
                    //保存
                    subCopyButtons.add(subCopyMenuButton);
                }
            }
            //一级菜单保存二级菜单
            onceCopyWxMenuButton.setSubButtons(subCopyButtons);
            onceCopyButtons.add(onceCopyWxMenuButton);
        }
        //保存一级菜单
        resultWxMenu.setButtons(onceCopyButtons);
        WxMenuRule wxMenuRule = wxMenu.getMatchRule();
        WxMenuRule itemWxMenuRule = null;
        if (wxMenuRule != null) {
            itemWxMenuRule = new WxMenuRule();
            itemWxMenuRule.setCity(wxMenuRule.getCity());
            itemWxMenuRule.setClientPlatformType(wxMenuRule.getClientPlatformType());
            itemWxMenuRule.setCountry(wxMenuRule.getCountry());
            itemWxMenuRule.setLanguage(wxMenuRule.getLanguage());
            itemWxMenuRule.setProvince(wxMenuRule.getProvince());
            itemWxMenuRule.setTagId(wxMenuRule.getTagId());
            itemWxMenuRule.setSex(wxMenuRule.getSex());
        }
        resultWxMenu.setMatchRule(itemWxMenuRule);
        return resultWxMenu;
    }
}
