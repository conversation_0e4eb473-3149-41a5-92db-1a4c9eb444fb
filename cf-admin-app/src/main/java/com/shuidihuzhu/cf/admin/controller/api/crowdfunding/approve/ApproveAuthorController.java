package com.shuidihuzhu.cf.admin.controller.api.crowdfunding.approve;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.account.verify.client.menu.IdCardVerifyResultEnum;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.account.verify.client.model.VerifyIdcardVO;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAuthorBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoStatusBiz;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfPayeeMaterialClient;
import com.shuidihuzhu.cf.client.material.feign.CfRaiseMaterialClient;
import com.shuidihuzhu.cf.client.material.model.CfRaiseFundUseModel;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.finance.IFinanceDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.approve.CfRaiseFundUseParam;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.crowdfunding.IdCardVerifyService;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.IdCardUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2018-09-12  19:47
 * https://wiki.shuiditech.com/pages/viewpage.action?pageId=********
 */
@RestController
@Slf4j
@RequestMapping("admin/cf/approve/author")
public class ApproveAuthorController extends AbstractApproveController {

    @Resource
    private AdminCrowdfundingAuthorBiz adminCrowdfundingAuthorBiz;

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Resource
    private SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    private IFinanceDelegate financeDelegate;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Resource
    private ApproveRemarkOldService approveRemarkOldService;

    @Resource
    private IdCardVerifyService idCardVerifyService;

    @Resource
    private CfRaiseMaterialClient cfRaiseMaterialClient;

    @Resource
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Resource
    private AdminCrowdfundingInfoStatusBiz adminCrowdfundingInfoStatusBiz;

    @Resource
    private CfPayeeMaterialClient cfPayeeMaterialClient;

    /**
     * 案例详情页编辑患者信息
     *
     * @param infoUuid
     * @param userId
     * @param name
     * @param idCard
     * @param idCardType
     * @return
     */
    @RequiresPermission("approve-author:save")
    @PostMapping("save")
    public Response<Void> save(
            @RequestParam(value = "infoUuid") String infoUuid,
            @RequestParam(value = "userId") int userId,
            @RequestParam(value = "name") String name,
            @RequestParam(value = "idCard") String idCard,
            @RequestParam(value = "idCardType") String idCardType) {
        log.info("{}, {}, {}, {}", infoUuid, name, idCard, idCardType);
        name = StringUtils.trim(name);
        idCard = StringUtils.trim(idCard);
        if (StringUtils.isEmpty(name) || StringUtils.isEmpty(infoUuid) || StringUtils.isEmpty(idCard) || StringUtils.isEmpty(idCardType)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (StringUtils.equals(idCardType, UserIdentityType.identity.name()) && StringUtils.isNotEmpty(idCard) && IdCardUtil.illegal(idCard)) {
            return NewResponseUtil.makeError(AdminErrorCode.IDCARD_VERIFY_FAILED);
        }
        if (StringUtils.equals(idCardType, UserIdentityType.identity.name())) {
            VerifyIdcardVO verifyIdcardVO = idCardVerifyService.verfiy(name, idCard, UserRelTypeEnum.SELF, userId);
            if (!Objects.equals(verifyIdcardVO.getCode(), IdCardVerifyResultEnum.MATCH)) {
                return NewResponseUtil.makeError(AdminErrorCode.IDCARD_FAILED_PATIENT);
            }
        }
        if (StringUtils.equals(idCardType, UserIdentityType.birth.name()) && idCard.length() > 50) {
            return NewResponseUtil.makeError(AdminErrorCode.BIRTH_CARD_LIMIT_FAIL);
        }

        CrowdfundingInfo fundingInfo = crowdfundingDelegate.getCrowdfundingInfoByInfoId(infoUuid);
        int caseId = fundingInfo.getId();
        AdminUserAccountModel adminUserAccountModel = seaAccountClientV1.getValidUserAccountById(userId).getResult();

        // 保存上一次收款人信息
        CfOperatingRecord before = crowdfundingDelegate.before(fundingInfo, fundingInfo.getUserId(), adminUserAccountModel.getName(),
                CfOperatingRecordEnum.Type.MODIFY_AUTHOR, CfOperatingRecordEnum.Role.OPERATOR);
        // 更新筹款人身份信息
        CrowdfundingAuthor crowdfundingAuthor = adminCrowdfundingAuthorBiz.get(caseId);
        if (Objects.isNull(crowdfundingAuthor)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        // 新增操作日志
        String approveComment = handlerApproveComment(crowdfundingAuthor, name, idCard, idCardType);
        if (StringUtils.isNotEmpty(approveComment)) {
            approveRemarkOldService.add(caseId, ContextUtil.getAdminUserId(), approveComment);
        }
        crowdfundingAuthor.setName(name);
        crowdfundingAuthor.setCryptoIdCard(oldShuidiCipher.aesEncrypt(idCard));
        crowdfundingAuthor.setIdType(UserIdentityType.valueOf(idCardType));
        adminCrowdfundingAuthorBiz.update(crowdfundingAuthor);
        crowdfundingDelegate.afterCfOperatingRecord(before);

        /**
         * 如果收款人是本人同步更新收款人信息
         * 此处与产品沟通只管更新,校验不过的运营再沟通解决
         */
        CrowdfundingRelationType relationType = fundingInfo.getRelationType();
        if (!CrowdfundingRelationType.self.equals(relationType)) {
            return NewResponseUtil.makeSuccess(null);
        }
        //draw_cash 、收款人的信息的收款人姓名更新
        Response<Void> response = financeDelegate.updatePayeeName(caseId, name, oldShuidiCipher.aesEncrypt(idCard));
        log.info("caseId:{} response:{}", caseId, JSON.toJSONString(response));
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("approve-author:use-update")
    @PostMapping("use-update")
    @ApiOperation("修改筹款用途信息")
    public Response<Void> useUpdate(@RequestBody CfRaiseFundUseParam cfRaiseFundUseParam) {
        if (Objects.isNull(cfRaiseFundUseParam)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (StringUtils.isEmpty(cfRaiseFundUseParam.getInfoUuid())) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(cfRaiseFundUseParam.getInfoUuid());
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        RpcResult<CfRaiseFundUseModel> fundUseResp = cfRaiseMaterialClient.selectFundUse(crowdfundingInfo.getId());
        CfRaiseFundUseModel fundUseRespData = Optional.ofNullable(fundUseResp)
                .map(RpcResult::getData)
                .orElse(null);
        if (Objects.isNull(fundUseRespData)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        String approveComment = handlerApproveUseUpdateComment(fundUseRespData, cfRaiseFundUseParam);
        if (StringUtils.isNotEmpty(approveComment)) {
            approveRemarkOldService.add(crowdfundingInfo.getId(), ContextUtil.getAdminUserId(), approveComment);
        }

        convertUpdateFundUseRespData(fundUseRespData, cfRaiseFundUseParam);

        RpcResult<String> stringRpcResult = cfRaiseMaterialClient.addOrUpdateFundUse(crowdfundingInfo.getId(), fundUseRespData);
        if (stringRpcResult.isFail()) {
            log.error("approveAuthorController useUpdate is error : {}, {}, {} ", JSON.toJSON(cfRaiseFundUseParam), stringRpcResult.getCode(), stringRpcResult.getMsg());
        }
        return NewResponseUtil.makeSuccess(null);
    }

}
