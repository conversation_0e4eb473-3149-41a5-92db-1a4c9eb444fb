package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfBaseInfoTemplatizeBiz;
import com.shuidihuzhu.cf.delegate.OrganizationDelegate;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplateOperatorHistory;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize;
import com.shuidihuzhu.cf.service.crowdfunding.AdminBaseInfoTemplateService;
import com.shuidihuzhu.client.model.CommonPageModel;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Ahrievil
 * @date : 2018/9/10 19:35
 */
@Slf4j
@RestController
@Api("智能发起文案的模板配置")
@RequestMapping(path = "admin/crowdfunding/base-info/template/", method = {RequestMethod.POST}, produces = "application/json;charset=UTF-8")
public class AdminBaseInfoTemplateController {

    @Autowired
    private AdminBaseInfoTemplateService templateService;
    @Resource
    private OrganizationDelegate organizationDelegate;
    @Resource
    private SeaAccountClientV1 seaAccountClientV1;
    @Resource
    private AdminCfBaseInfoTemplatizeBiz adminCfBaseInfoTemplatizeBiz;

    @ApiOperation("获取列表信息")
    @RequestMapping(path = "get-list")
    @ResponseBody
    @RequiresPermission("base-info-template:get-list")
    public Response getList(@RequestParam(required = false) String context,
                            @RequestParam(defaultValue = "0") int contentType,
                            @RequestParam(defaultValue = "0") int channelType,
                            @RequestParam(defaultValue = "0") int relationType,
                            @RequestParam(defaultValue = "1") int current,
                            @RequestParam(defaultValue = "10") int pageSize,
                            @RequestParam(required = false, defaultValue = "-1") int useScene) {
        return templateService.getList(context, contentType, channelType, relationType, current, pageSize, useScene);
    }

    @ApiOperation("获取详情")
    @RequestMapping(path = "detail")
    @ResponseBody
    @RequiresPermission("base-info-template:detail")
    public Response detail(@RequestParam long id) {
        return NewResponseUtil.makeSuccess(templateService.detail(id));
    }

    @ApiOperation("添加或者更新")
    @RequestMapping(path = "add-or-update")
    @RequiresPermission("base-info-template:add-or-update")
    public Response addOrUpdate(@RequestParam String param) {
        CfBaseInfoTemplatize cfBaseInfoTemplatize;
        try {
            cfBaseInfoTemplatize = JSON.parseObject(param, CfBaseInfoTemplatize.class);//已检查过
        } catch (Exception e) {
            log.error("AdminBaseInfoTemplateController addOrUpdate parse json error", e);
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_JSON_PARSE_ERROR);
        }
        Response response = templateService.inputParamCheck(cfBaseInfoTemplatize);
        if (Objects.nonNull(response)) {
            return response;
        }
        if (Objects.isNull(cfBaseInfoTemplatize)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        cfBaseInfoTemplatize.setOperatorId(ContextUtil.getAdminLongUserId());
        AdminUserAccountModel adminUserInfo = seaAccountClientV1.getValidUserAccountById(ContextUtil.getAdminUserId()).getResult();
        String simpleOrganization = organizationDelegate.getSimpleOrganization((int) ContextUtil.getAdminLongUserId());
        String name = Objects.nonNull(adminUserInfo) ? "-" + adminUserInfo.getName() : "";
        cfBaseInfoTemplatize.setOperatorName(simpleOrganization + name);
        response = templateService.addOrUpdate(cfBaseInfoTemplatize);
        return response;
    }

    @ApiOperation("删除")
    @RequestMapping(path = "delete")
    @ResponseBody
    @RequiresPermission("base-info-template:delete")
    public Response delete(long id) {
        return templateService.delete(id);
    }

    @ApiOperation("获取关系详情")
    @RequestMapping(path = "get-relation-info")
    @ResponseBody
    @RequiresPermission("base-info-template:get-relation-info")
    public Response getRelationInfo() {
        return templateService.getRelationInfo();
    }


    @ApiOperation("获取操作记录")
    @RequestMapping(path = "get-operator-history")
    @ResponseBody
    @RequiresPermission("base-info-template:get-operator-history")
    public Response<List<CfBaseInfoTemplateOperatorHistory>> getOperatorHistory(long cfBaseTemplateId) {
        return NewResponseUtil.makeSuccess(adminCfBaseInfoTemplatizeBiz.selectByCfBaseTemplateId(cfBaseTemplateId));
    }

    @ApiOperation("筛选1v1模板")
    @RequestMapping(path = "get-1v1-template")
    @ResponseBody
    @RequiresPermission("base-info-template:get-1v1-template")
    public Response<CommonPageModel<CfBaseInfoTemplatize>> get1v1TemplateList(@RequestParam(required = false) String context,
                                                                              @RequestParam("relationType") Integer relationType,
                                                                              @RequestParam(value = "diseaseName", required = false) String diseaseName,
                                                                              @RequestParam(value = "age", required = false) Integer age,
                                                                              @RequestParam(defaultValue = "1") int current,
                                                                              @RequestParam(defaultValue = "10") int pageSize) {
        if (Objects.isNull(relationType)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        return templateService.get1v1TemplateList(context, relationType, diseaseName, age, current, pageSize);
    }
}
