package com.shuidihuzhu.cf.admin.controller.api.crmUserManage;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crmUserManage.CrmUserDataBiz;
import com.shuidihuzhu.cf.biz.crmUserManage.CrmUserManageBiz;
import com.shuidihuzhu.cf.model.CrmUserManage.UserManage;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

@Slf4j
@RestController
@RequestMapping(path = "admin/crowdfunding/crm-user-manage/")
public class UserManageController {

    @Autowired
    private CrmUserManageBiz userManageBiz;
    @Autowired
    private CrmUserDataBiz userDataBiz;

    @RequestMapping(path = "query-user-data-by-uuid", method = RequestMethod.POST)
    @RequiresPermission("crm-user-manage:query-user-data-by-uuid")
    public Response<List<UserManage.UserData>> queryUserDataByUuid(@RequestParam("uuid") String uuid) {
        return NewResponseUtil.makeSuccess(userDataBiz.queryUserDataByUuid(uuid));
    }

    @RequestMapping(path = "query-user-data-by-clewId", method = RequestMethod.POST)
    @RequiresPermission("crm-user-manage:query-user-data-by-clewId")
    public Response<UserManage.UserData> queryUserDataByClewId(@RequestParam("clewId") long clewId) {
        UserManage.UserData userData = userDataBiz.queryUserDataByClewId(clewId);
        // 过滤不使用字段
        Optional.ofNullable(userData)
                .filter(r -> Objects.nonNull(r.getClewData()))
                .ifPresent(r -> r.getClewData().setClewPhone(StringUtils.EMPTY));
        return NewResponseUtil.makeSuccess(userData);
    }


    @RequestMapping(path = "mark-user-raise-case-quality", method = RequestMethod.POST)
    @RequiresPermission("crm-user-manage:mark-user-raise-case-quality")
    public Response<String> markUserRaiseCaseQuality(@RequestParam("uuid") String uuid,
                                                     @RequestParam("personId") String personId,
                                                     @RequestParam("raiseCaseQuality") int raiseCaseQuality) {

        userManageBiz.markRaiseCaseQuality(uuid, personId, raiseCaseQuality, ContextUtil.getAdminUserId());
        return NewResponseUtil.makeSuccess("");
    }

    @RequestMapping(path = "merge-mobiles", method = RequestMethod.POST)
    @RequiresPermission("crm-user-manage:merge-mobiles")
    public Response<String> mergePersonId(@RequestBody Set<String> mobiles) {
        log.info("手动合并多个手机号，mobiles:{} userId:{}", mobiles, ContextUtil.getAdminUserId());
        userManageBiz.mergeUuid(mobiles);
        return NewResponseUtil.makeSuccess("");
    }

}
