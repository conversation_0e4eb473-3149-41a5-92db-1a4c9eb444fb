package com.shuidihuzhu.cf.admin.controller.api.wx;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.account.model.service.MobileUserIdModel;
import com.shuidihuzhu.cf.delegate.commonservice.ICommonServiceDelegate;
import com.shuidihuzhu.cf.delegate.other.IWeiXinDelegate;
import com.shuidihuzhu.cf.delegate.service.UserThirdServiceBiz;
import com.shuidihuzhu.cf.delegate.service.WxConfigServiceBiz;
import com.shuidihuzhu.cf.delegate.service.WxGreetingConfigServiceBiz;
import com.shuidihuzhu.cf.delegate.service.WxGreetingConfigServiceBizV2;
import com.shuidihuzhu.cf.delegate.service.WxGreetingServiceBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.admin.vo.AttachHistoryVo;
import com.shuidihuzhu.cf.vo.wx.greeting.AttachKeyVO;
import com.shuidihuzhu.cf.vo.wx.greeting.GreetingVO;
import com.shuidihuzhu.cf.vo.wx.greeting.GreetingWxGroupAttachVO;
import com.shuidihuzhu.cf.vo.wx.greeting.WxConfigVO;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.grpc.account.v1.feign.SimpleUserAccountServiceClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.wx.grpc.client.common.Greeting;
import com.shuidihuzhu.wx.grpc.client.common.GreetingMatchMethod;
import com.shuidihuzhu.wx.grpc.client.common.GreetingMatchRule;
import com.shuidihuzhu.wx.grpc.model.AttacheKeyItemDto;
import com.shuidihuzhu.wx.grpc.model.GreetingWxGroupAttachV2Dto;
import com.shuidihuzhu.wx.grpc.model.QueryAttachHistoryV2Dto;
import com.shuidihuzhu.wx.grpc.model.WxConfigModel;
import com.shuidihuzhu.wx.grpc.model.WxGreetingConfigPo;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: duchao
 * @Date: 2018/5/30 上午10:54
 */
@RestController
@RequestMapping(path = "/admin/cf/wx/greeting")
public class WxGreetingConfigController {

	private final static Logger LOGGER = LoggerFactory.getLogger(WxGreetingConfigController.class);

	@Resource
	private WxConfigServiceBiz wxConfigServiceBiz;
	@Resource
	private WxGreetingConfigServiceBiz wxGreetingConfigServiceBiz;

	@Resource
	private UserThirdServiceBiz userThirdServiceBiz;

	@Autowired
	private IWeiXinDelegate weiXinDelegate;

	@Resource
	private WxGreetingServiceBiz wxGreetingServiceBiz;

	@Resource
	private WxGreetingConfigServiceBizV2 wxGreetingConfigServiceBizV2;
	@Autowired
	private SimpleUserAccountServiceClient simpleUserAccountServiceClient;
	@Autowired
	private ICommonServiceDelegate commonServiceDelegate;

	@RequiresPermission("greeting:add")
	@ApiOperation(value = "添加欢迎语")
	@PostMapping(path = "add")
	public Response addGreeting(String wxGreetingConfigsJson) {
		if (StringUtils.isBlank(wxGreetingConfigsJson)) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}
		List<WxGreetingConfigPo> wxGreetingConfigs = Lists.newArrayList();
		List<String> jsonStrings = JSON.parseArray(wxGreetingConfigsJson, String.class);//已检查过
		jsonStrings.forEach(jsonString -> {
			WxGreetingConfigPo wxGreetingConfig = JSON.parseObject(jsonString, WxGreetingConfigPo.class);//已检查过
			if (wxGreetingConfig != null) {
				wxGreetingConfigs.add(wxGreetingConfig);
			}
		});
		if (CollectionUtils.isEmpty(wxGreetingConfigs)) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_JSON_PARSE_ERROR);
		}
		Response<Boolean> response = wxGreetingConfigServiceBizV2.addGreetingV2(wxGreetingConfigs);
		if (response.getCode() == 0) {
			return NewResponseUtil.makeSuccess(null);
		} else {
			return NewResponseUtil.makeResponse(response.getCode(), response.getMsg(), null);
		}
	}

	@RequiresPermission("greeting:modify")
	@ApiOperation(value = "修改欢迎语的内容，已经关联的欢迎语不可修改")
	@PostMapping(path = "modify")
	public Response modifyGreeting(String wxGreetingConfigsJson) {
		if (StringUtils.isBlank(wxGreetingConfigsJson)) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}
		List<WxGreetingConfigPo> wxGreetingConfigs = Lists.newArrayList();
		List<String> jsonStrings = JSON.parseArray(wxGreetingConfigsJson, String.class);//已检查过
		jsonStrings.forEach(jsonString -> {
			WxGreetingConfigPo wxGreetingConfig = JSON.parseObject(jsonString, WxGreetingConfigPo.class);//已检查过
			if (wxGreetingConfig != null) {
				wxGreetingConfigs.add(wxGreetingConfig);
			}
		});
		if (CollectionUtils.isEmpty(wxGreetingConfigs)) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_JSON_PARSE_ERROR);
		}
		Response<Boolean> commonResponse = wxGreetingConfigServiceBizV2.modifyGreetingV2(wxGreetingConfigs);
		return NewResponseUtil.makeResponse(commonResponse.getCode(),
		                                 commonResponse.getMsg(),
		                                 null);
	}

	@RequiresPermission("greeting:query-by-id")
	@ApiOperation(value = "查询一个欢迎语")
	@PostMapping(path = "query-by-id")
	public Response modifyGreeting(int id) {
		Greeting greeting = wxGreetingConfigServiceBiz.queryById(id);
		return NewResponseUtil.makeSuccess(GreetingVO.fromGrpcObject(greeting));
	}

	@RequiresPermission("greeting:query-all-wxmp")
	@ApiOperation(value = "查询所有已经关联过的公众号")
	@PostMapping(path = "query-all-wxmp")
	public Response queryAllWxMp() {
		List<WxConfigModel> wxConfigs = wxGreetingConfigServiceBiz.queryWxMpList();
		List<WxConfigVO> vos = Lists.newArrayList();
		wxConfigs.forEach(item -> {
			vos.add(WxConfigVO.fromWxConfigV2(item));
		});
		return NewResponseUtil.makeSuccess(vos);
	}

	@RequiresPermission("greeting:query-all-keys")
	@ApiOperation("查询所有的事件key")
	@PostMapping(path = "query-all-keys")
	public Response queryAllKeys() {
		List<AttacheKeyItemDto> attachKeyItems = wxGreetingConfigServiceBiz.queryAttachKeys();
		List<AttachKeyVO> vos = Lists.newArrayList();
		attachKeyItems.forEach(item -> {
			vos.add(AttachKeyVO.convertV2(item));
		});
		return NewResponseUtil.makeSuccess(vos);
	}

	@RequiresPermission("greeting:list")
	@ApiOperation("分页查询所有的欢迎语")
	@PostMapping(path = "list")
	public Response listGreeting(int current, int pageSize) {
		if (current < 1) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}
		List<WxGreetingConfigPo> wxGreetingConfigPos = wxGreetingConfigServiceBizV2.listGreetingV2(current, pageSize);
		Map<String, Object> result = Maps.newHashMap();

		Map<String, Object> pagination = Maps.newHashMap();
		pagination.put("current", current);
		pagination.put("pageSize", pageSize);
		pagination.put("total", wxGreetingConfigServiceBizV2.totalCountV2());


		result.put("pagination", pagination);
		result.put("data", wxGreetingConfigPos);

		return NewResponseUtil.makeSuccess(result);
	}

	@RequiresPermission("greeting:query-by-keyword")
	@ApiOperation(value = "按关键字查欢迎语")
	@PostMapping(path = "query")
	public Response queryGreeting(int current,int pageSize, String keyword) {
		if (StringUtils.isBlank(keyword)) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}
		List<WxGreetingConfigPo> wxGreetingConfigPos = wxGreetingConfigServiceBizV2.queryGreetingV2(current, pageSize, keyword);
		Map<String, Object> result = Maps.newHashMap();

		Map<String, Object> pagination = Maps.newHashMap();
		pagination.put("current", current);
		pagination.put("pageSize", pageSize);
		pagination.put("total", wxGreetingConfigServiceBizV2.totalCountByCondition(keyword));


		result.put("pagination", pagination);
		result.put("data", wxGreetingConfigPos);

		return NewResponseUtil.makeSuccess(result);
	}

	@RequiresPermission("greeting:del")
	@ApiOperation(value = "删除一个欢迎语")
	@PostMapping(path = "del")
	public Response delGreeting(int greetingId) {
		wxGreetingConfigServiceBiz.delGreeting(greetingId);
		return NewResponseUtil.makeSuccess(null);
	}

	@RequiresPermission("greeting:attach-add")
	@ApiOperation(value = "添加欢迎语和公众号组的关联, matchRule: 0,全匹配 1,包含  2,前缀 3,后缀")
	@PostMapping(path = "attach-add")
	public Response attachWx(int wxGroupId, int contentId, String key, int matchRule, int subBizType) {
		List<GreetingMatchMethod> matchMethods = Lists.newArrayList();
		GreetingMatchMethod matchMethod1 = GreetingMatchMethod.newBuilder()
		                                                      .setKey(key)
		                                                      .setMatchRule(GreetingMatchRule.forNumber(matchRule))
		                                                      .build();
		matchMethods.add(matchMethod1);

		wxGreetingConfigServiceBizV2.addAttachV2(wxGroupId, contentId, subBizType, matchMethods);
		return NewResponseUtil.makeSuccess(null);
	}

	@RequiresPermission("greeting:attach-del")
	@ApiOperation("删除欢迎语和公众号组的关联")
	@PostMapping(path = "attach-del")
	public Response delAttach(int attachId) {
		wxGreetingConfigServiceBiz.delAttach(attachId);
		return NewResponseUtil.makeSuccess(null);
	}

	@RequiresPermission("greeting:attach-query")
	@ApiOperation(value = "查询一个公众号组关联的欢迎语")
	@PostMapping(path = "attach-query")
	public Response queryAttach(int groupId) {
		List<GreetingWxGroupAttachV2Dto> attaches = wxGreetingConfigServiceBizV2.queryAttachV2(groupId);

		List<GreetingWxGroupAttachVO> vos = Lists.newArrayList();
		attaches.forEach(item -> {
			GreetingWxGroupAttachVO vo = GreetingWxGroupAttachVO.convert(item);
			List<WxGreetingConfigPo> wxGreetingConfigPos = wxGreetingConfigServiceBizV2.queryByContentId(item.getContentId());
			if (CollectionUtils.isNotEmpty(wxGreetingConfigPos)) {
				vo.setContent(wxGreetingConfigPos.get(0).getContent());
			}
			vos.add(vo);
		});
		return NewResponseUtil.makeSuccess(vos);
	}

	@RequiresPermission("greeting:attach-history")
	@ApiOperation(value = "查询一个公众号的关联历史")
	@PostMapping(path = "attach-history")
	public Response queryAttachHistory(int current, int pageSize, @RequestParam(required = false,defaultValue = "0") Integer thirdType,
									   @RequestParam(required = false,defaultValue = "") String attachScene) {

		List<QueryAttachHistoryV2Dto> queryAttachHistoryPluses = wxGreetingConfigServiceBizV2.queryAttachHistoryV2(current, pageSize, thirdType,attachScene);
		List<AttachHistoryVo>  attachHistoryVos = Lists.newArrayList();
		if (CollectionUtils.isNotEmpty(queryAttachHistoryPluses)){
			queryAttachHistoryPluses.forEach(queryAttachHistoryPlus -> {
				AttachHistoryVo attachHistoryVo = AttachHistoryVo.convertAttachHistoryVo(queryAttachHistoryPlus);
				attachHistoryVos.add(attachHistoryVo);
			});
		}
		Map<String, Object> result = Maps.newHashMap();

		Map<String, Object> pagination = Maps.newHashMap();
		pagination.put("current", current);
		pagination.put("pageSize", pageSize);
		pagination.put("total", wxGreetingConfigServiceBizV2.totalAttachV2(thirdType,attachScene));


		result.put("pagination", pagination);
		result.put("data", attachHistoryVos);


		return NewResponseUtil.makeSuccess(result);
	}

	@RequiresPermission("greeting:get-pic-example")
	@ApiOperation(value = "获取示例图片")
	@PostMapping(path = "/get-pic-example")
	public Response getPicExample(Integer greetingType){
		String pictureUrl = "";
		return NewResponseUtil.makeSuccess(pictureUrl);
	}

	@RequiresPermission("greeting:get-pic-article-detail")
	@ApiOperation(value ="查看图文欢迎语详细")
	@PostMapping(path = "/get-pic-article-detail")
	public Response getPicArticleDetail(Integer contentId){
		if (contentId == null) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}
		List<WxGreetingConfigPo> wxGreetingConfigPos = wxGreetingConfigServiceBizV2.queryByContentId(contentId);
		if (CollectionUtils.isNotEmpty(wxGreetingConfigPos) && wxGreetingConfigPos.size() == 1) {
			WxConfigModel wxConfig = wxConfigServiceBiz.getWxConfigByAppId(wxGreetingConfigPos.get(0).getAppid());
			if (wxConfig != null) {
				wxGreetingConfigPos.get(0).setMiniProgramName(wxConfig.getName());
			}
		}
		return NewResponseUtil.makeSuccess(wxGreetingConfigPos);
	}

	@RequiresPermission("greeting:add-attach-scene")
	@ApiOperation(value ="添加关联key")
	@PostMapping(path = "/add-attach-scene")
	public Response addAttachKey(String attachKey,String attachScene){
		if (StringUtils.isBlank(attachKey) || StringUtils.isBlank(attachScene)) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}
		List<AttacheKeyItemDto> attachKeyItems = wxGreetingConfigServiceBiz.queryAttachKeys();
		List<String> keys = attachKeyItems.stream().map(AttacheKeyItemDto::getKey).collect(Collectors.toList());
		if (keys.contains(attachKey)) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR_VALID_DATA_RANGE,"此关联场景已存在,请勿重复添加");
		}
		wxGreetingConfigServiceBizV2.addAttachKeyItem(attachKey, attachScene);
		return NewResponseUtil.makeSuccess(null);
	}

	@RequiresPermission("greeting:test-greeting-second")
	@ApiOperation(value = "测试欢迎语二期")
	@PostMapping(path = "test-greeting-second")
	public 	Response testGreetingSecond(String openId,int thirdType,@RequestParam(required = false,defaultValue = "") String key){
		Boolean successTag = wxGreetingServiceBiz.sendGreetingAsync(openId, key, thirdType);
		if (successTag) {
			return NewResponseUtil.makeSuccess("发送成功");
		}
		return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
	}

	@RequiresPermission("greeting:test-greeting-mobile")
	@ApiOperation(value = "手机号测试发送")
	@PostMapping(path = "test-greeting-mobile")
	public Response testGreetingByMobile(@RequestParam String mobile, @RequestParam int thirdType, @RequestParam String keys){
		if (StringUtils.isBlank(mobile) || StringUtils.isBlank(keys) || thirdType <= 0) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}
		String[] keysArray = keys.split(",");
		MobileUserIdModel userIdByMobile = simpleUserAccountServiceClient.getUserIdByMobile(mobile);
		if (userIdByMobile == null) {
			return NewResponseUtil.makeResponse(-1, "根据手机号找不到用户", null);
		}
		UserThirdModel userThirdModel = userThirdServiceBiz.getThirdModelWithUserId(userIdByMobile.getUserId(), thirdType);
		if (userThirdModel == null) {
			return NewResponseUtil.makeResponse(-1, "根据手机号找不到用户", null);
		}
		for (int i = 0;i<keysArray.length;i++) {
			wxGreetingServiceBiz.sendGreetingAsync(userThirdModel.getOpenId(),keysArray[i],thirdType);
		}
		return NewResponseUtil.makeSuccess("发送成功");
	}

}
