package com.shuidihuzhu.cf.admin.configuration;

import brave.Tracing;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.shuidihuzhu.cf.admin.constant.AsyncPoolConstants;
import com.shuidihuzhu.cf.enhancer.subject.threadpool.annotation.Dynamic;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.*;

/**
 * @package: com.shuidihuzhu.cf.admin.configuration
 * @Author: liujiawei
 * @Date: 2019-02-17  17:25
 */
@Configuration
@EnableAsync
@Slf4j
public class AsyncPoolConfiguration {

    @Autowired
    private Tracing tracing;

    @Bean("aiCheckPhotosExecutor")
    @Dynamic
    public Executor aiCheckPhotosExecutor() {

//        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
//        executor.setCorePoolSize(10);
//        executor.setMaxPoolSize(20);
//        executor.setQueueCapacity(100);
//        executor.setKeepAliveSeconds(60);
//        executor.setThreadNamePrefix("aiCheckPhotosExecutor-");
//        executor.setRejectedExecutionHandler(new RejectedExecutionHandler() {
//            @Override
//            public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
//                log.info("The aiCheckPhotosExecutor is discarded");
//            }
//        });
//        return executor;

        return tracing.currentTraceContext().executorService(new ThreadPoolExecutor(
                5, 10, 60, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(100), new ThreadFactoryBuilder().setNameFormat("aiCheckPhotosExecutor-%d").build()));

    }


    @Bean(AsyncPoolConstants.GET_MY_ONLY_BASE_INFO_LIST_POOL)
    @Dynamic
    public Executor getMyOnlyBaseInfoListExecutor(@Value("${threadPool.getMyOnlyBaseInfoListExecutor.corePoolSize:10}") int corePoolSize,
                                                  @Value("${threadPool.getMyOnlyBaseInfoListExecutor.maximumPoolSize:40}") int maximumPoolSize) {
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("getMyOnlyBaseInfoList-%d").build();
        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 60,
                TimeUnit.SECONDS, new ArrayBlockingQueue<>(10), threadFactory, new ThreadPoolExecutor.AbortPolicy() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                log.error("The getMyOnlyBaseInfoListExecutor is rejectedExecution");
                super.rejectedExecution(r, e);
            }
        });
        return tracing.currentTraceContext().executorService(poolExecutor);
    }


    /**
     * localcache thread pool config
     */
    @Bean(AsyncPoolConstants.LOCAL_CACHE_ASYNC_POOL)
    @Dynamic
    public Executor asyncTaskThreadPoolTaskExecutor(){
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("LOCAL_CACHE_ASYNC_POOL" + "-%d").build();
        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(5, 100, 600,
                TimeUnit.SECONDS, new ArrayBlockingQueue<>(1000), threadFactory, new ThreadPoolExecutor.CallerRunsPolicy() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                log.error("The asyncTaskThreadPoolTaskExecutor is rejectedExecution");
                super.rejectedExecution(r, e);
            }
        });
        poolExecutor.allowCoreThreadTimeOut(true);
        return tracing.currentTraceContext().executorService(poolExecutor);
    }

    @Bean(AsyncPoolConstants.AI_GENERATE_ASYNC_POOL)
    public Executor asyncAiGeneratePool() {
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(AsyncPoolConstants.AI_GENERATE_ASYNC_POOL + "-%d").build();
        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(20, 20, 600,
                TimeUnit.SECONDS, new ArrayBlockingQueue<>(10), threadFactory, new ThreadPoolExecutor.AbortPolicy() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                log.error("The {} is rejectedExecution", AsyncPoolConstants.AI_GENERATE_ASYNC_POOL);
                super.rejectedExecution(r, e);
            }
        });
        return tracing.currentTraceContext().executorService(poolExecutor);
    }

    @Bean(AsyncPoolConstants.AI_GENERATE_AD_ASYNC_POOL)
    public Executor asyncAiGenerateAdPool() {
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(AsyncPoolConstants.AI_GENERATE_AD_ASYNC_POOL + "-%d").build();
        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(50, 70, 60,
                TimeUnit.SECONDS, new ArrayBlockingQueue<>(100), threadFactory, new ThreadPoolExecutor.DiscardPolicy());
        return tracing.currentTraceContext().executorService(poolExecutor);
    }

}
