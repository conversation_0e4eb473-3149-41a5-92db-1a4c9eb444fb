package com.shuidihuzhu.cf.admin.controller.api.crowdfunding.risk;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.crowdfunding.CfRiskService;
import com.shuidihuzhu.client.cf.risk.client.CfRiskClient;
import com.shuidihuzhu.client.cf.risk.client.CfRiskPlatformClient;
import com.shuidihuzhu.client.cf.risk.model.CfRiskOperateDetailParam;
import com.shuidihuzhu.client.cf.risk.model.CfRiskOperateLimitParam;
import com.shuidihuzhu.client.cf.risk.model.enums.RiskOperateSourceEnum;
import com.shuidihuzhu.client.cf.risk.model.enums.UserOperationEnum;
import com.shuidihuzhu.client.cf.risk.model.result.RiskRpcResponse;
import com.shuidihuzhu.client.param.RiskOperateLimitParam;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Objects;


/**
 * Created by sven on 18/11/2.
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/cf/risk/user-valid")
@Api("用户禁止动作相关controller")
@Slf4j
public class UserValidController {

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Autowired
    CfRiskPlatformClient cfRiskPlatformClient;

    @Autowired
    private CfRiskClient cfRiskClient;

    @Autowired
    private CfRiskService cfRiskService;

    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @ApiOperation("禁止用户在这个案例上发布动态")
    @RequestMapping(value = "/forbidden-progress", method = RequestMethod.POST)
    @RequiresPermission("risk-valid:update-progress")
    public Response updateProgress(@RequestParam(value = "infoUuid",required = true) String infoUuid, boolean enable){

        log.info("UserValidController.updateProgress param infoUuid:{}, enable:{}", infoUuid, enable);
        if(StringUtils.isEmpty(infoUuid)){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getCrowdfundingInfoByInfoId(infoUuid);
        if(null == crowdfundingInfo || crowdfundingInfo.getId() <= 0){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        int adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }
        AdminUserAccountModel model = seaAccountClientV1.getValidUserAccountById(adminUserId).getResult();
        String adminUserName = Objects.nonNull(model) ? model.getName() : null;

        CfRiskOperateDetailParam detailParam = new CfRiskOperateDetailParam();
        detailParam.setUserOperate(UserOperationEnum.PROPRESS.getCode());
        detailParam.setAction(enable);
        detailParam.setReason("材料审核详情:禁止/允许发布动态");

        CfRiskOperateLimitParam riskParam = new CfRiskOperateLimitParam();
        riskParam.setCaseId(crowdfundingInfo.getId());
        riskParam.setOperateSource(RiskOperateSourceEnum.CF_ADMIN_API.getCode());
        riskParam.setOperatorId(adminUserId);
        riskParam.setOperator(StringUtils.isEmpty(adminUserName) ? null : adminUserName);
        riskParam.setDetailParams(Lists.newArrayList(detailParam));

        Response<String> response = cfRiskClient.writeRiskOperate(riskParam);
        if(0 != response.getCode()){
            return ResponseUtil.makeResponse(response.getCode(), response.getMsg(), null);
        }

        //TODO ###请勿改动、待删除####
        Map<String, Boolean>  limits = Maps.newHashMap();
        limits.put(String.valueOf(UserOperationEnum.PROPRESS.getCode()), enable);

        RiskOperateLimitParam limitParam = new RiskOperateLimitParam();
        limitParam.setCaseId(crowdfundingInfo.getId());
        limitParam.setLimits(limits);
        limitParam.setOperateSource(RiskOperateSourceEnum.CF_ADMIN_API.getCode());

        RiskRpcResponse result = cfRiskPlatformClient.addLimit(limitParam);

        log.info("UserValidController.updateProgress result:{}", JSON.toJSONString(result));

        if(null != result && 0 != result.getCode()){
            return NewResponseUtil.makeFail(result.getMsg());
        }
        //TODO ###请勿改动、待删除####

        return ResponseUtil.makeSuccess("操作成功");
    }

    @ApiOperation("获取用户是否能在某个案例上发表动态")
    @RequestMapping(value = "/get-progress-valid", method = RequestMethod.POST)
    @RequiresPermission("risk-valid:get-progress")
    public Response getProgress(@RequestParam(value = "infoUuid",required = true) String infoUuid){
        log.info("UserValidController.getProgress param infoUuid:{}", infoUuid);

        if(StringUtils.isEmpty(infoUuid)){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getCrowdfundingInfoByInfoId(infoUuid);
        if(null == crowdfundingInfo || crowdfundingInfo.getId() <= 0){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        boolean action = cfRiskService.operatorValid(0l, crowdfundingInfo.getId(), UserOperationEnum.PROPRESS);

        log.info("UserValidController.getProgress result result:{}", action);

        if(action){
            return NewResponseUtil.makeSuccess(true, "用户能在该案例发表动态");
        } else {
            return NewResponseUtil.makeSuccess(false, "用户不能在该案例发表动态");
        }
    }
}
