package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.admin.delegate.SeaUserAuthDelegate;
import com.shuidihuzhu.cf.admin.util.AnchorPageUtils;
import com.shuidihuzhu.cf.admin.util.ParamTimeRangeHandler;
import com.shuidihuzhu.cf.admin.util.wordfilter2.SensitivewordFilter;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderBiz;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.biz.aiphoto.ImageWatermarkService;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.client.feign.CaseInfoApproveStageFeignClient;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.CfBaseInfoRiskHitVO;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.RiskControlWordCategoryDO;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordCheckContext;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResult;
import com.shuidihuzhu.cf.dao.admin.AdminWorkOrderDao;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingUserDelegate;
import com.shuidihuzhu.cf.delegate.finance.IFinanceDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.domain.CaseRaiseRiskDO;
import com.shuidihuzhu.cf.domain.caseinfo.CaseInfoApproveStageDO;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminUGCTask;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.cf.enums.sona.RoleEnum;
import com.shuidihuzhu.cf.model.admin.AdminCreditSupplement;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminSensitiveVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminUserCommentVo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.admin.dream.CfCreditSupplementService;
import com.shuidihuzhu.cf.service.crowdfunding.SensitiveWordService;
import com.shuidihuzhu.cf.service.resulthandler.ResultSensitiveHandler;
import com.shuidihuzhu.cf.vo.AnchorPageBigInt2VO;
import com.shuidihuzhu.cf.vo.crowdfunding.WorkOrderUgcBaseInfoVo;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.risk.client.UserTagClient;
import com.shuidihuzhu.client.cf.risk.model.result.UserTagHistory;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.constants.CrowdfundingCons;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.shuidihuzhu.cf.admin.constant.AsyncPoolConstants.GET_MY_ONLY_BASE_INFO_LIST_POOL;

/**
 * Created by Ahrievil on 2017/12/1
 *
 */
@Slf4j
@RefreshScope
@RestController
@RequestMapping(path = "admin/cf/sensitive")
@Api("UGC风控相关的代码")
public class SensitiveWordHandleController {

    private static final String SENSITIVE_LEADER_ROLE_ID = RoleEnum.SENSITIVE_LEADER.getPermission();

    @Resource(name = GET_MY_ONLY_BASE_INFO_LIST_POOL)
    private Executor getMyOnlyBaseInfoListExecutor;
    @Autowired
    private AdminTaskUgcBiz adminTaskUgcBiz;

    @Autowired
    private AdminWorkOrderBiz adminWorkOrderBiz;

    @Autowired
    private CfSensitiveWordRecordBiz cfSensitiveWordRecordBiz;

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Autowired
    private AdminCrowdfundingOrderBiz adminCrowdfundingOrderBiz;

    @Autowired
    private AdminCrowdFundingProgressBiz adminCrowdFundingProgressBiz;

    @Autowired
    private AdminCrowdFundingVerificationBiz adminCrowdFundingVerificationBiz;

    @Autowired
    private AdminCrowdfundingCommentBiz adminCrowdfundingCommentBiz;

    @Autowired
    private SensitiveWordService sensitiveWordService;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired
    private AdminCrowdfundingAttachmentBiz adminCrowdfundingAttachmentBiz;

    @Autowired
    private CfCreditSupplementService cfCreditSupplementService;

    @Resource
    private ICrowdfundingUserDelegate crowdfundingUserDelegate;

    @Autowired
    private IFinanceDelegate financeDelegate;

    @Autowired
    private UserCommentBiz userCommentBiz;

    @Resource
    private SeaUserAuthDelegate seaUserAuthDelegate;

    @Resource
    private IRiskDelegate riskDelegate;

    @Resource
    private UserTagClient userTagClient;

    @Autowired
    private AdminWorkOrderDao adminWorkOrderDao;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;

    @Resource
    private SeaAccountClientV1 seaAccountClientV1;


    @Autowired
    private Environment environment;

    @Value("${diff.get-my-only-baseInfo-list:false}")
    private boolean getMyOnlyBaseInfoListDiff;

    @Value("${diff.get-my-only-progress-list:false}")
    private boolean getMyOnlyProgressListDiff;

    @Value("${diff.get-my-new-list-v2:false}")
    private boolean getMyNewListV2Diff;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private ImageWatermarkService watermarkService;

    @Autowired
    private CaseInfoApproveStageFeignClient caseInfoApproveStageFeignClient;

    @RequiresPermission("sensitive:get-my-mission")
    @RequestMapping(path = "get-my-mission", method = RequestMethod.POST)
    public Response getMyMission(@RequestParam int count) {
        int userId = ContextUtil.getAdminUserId();
        log.info("SensitiveWordController getMySelfList userId:{}, count:{}", userId, count);
        if (count <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<AdminSensitiveVo> list = adminWorkOrderDao.selectUgcSensitiveByPage(AdminWorkOrderConst.Type.UGC.getCode(),
                AdminWorkOrderConst.Task.UGC_TASK_CODES,
                userId, 0, AdminUGCTask.Result.NO.getCode(), null, 0, Lists.newArrayList(), null, null);

        if (CollectionUtils.isNotEmpty(list)) {
            return NewResponseUtil.makeError(AdminErrorCode.EXIST_ORDER_NO_HANDLE);
        }

        List<AdminWorkOrder> adminWorkOrders = adminWorkOrderBiz.assigningTasks(AdminWorkOrderConst.Type.UGC,
                AdminWorkOrderConst.Task.UGC_TASKS,
                userId, count);
        if (CollectionUtils.isEmpty(adminWorkOrders)) {
            return NewResponseUtil.makeError(AdminErrorCode.NO_MORE_WORK_ORDER);
        }
        return NewResponseUtil.makeSuccess("succ");
    }

    /**
     *
     * QA线下自动化测试用，不提供线上功能
     * QA线下自动化测试用，不提供线上功能
     * QA线下自动化测试用，不提供线上功能
     */
    @RequestMapping(path = "recover-ugc-work-order", method = RequestMethod.POST)
    public Response recoverUgcWorkOrder(@RequestParam("adminUserId") int adminUserId, @ApiParam("重置工单类型 1:禁止词 2:敏感词 8:图文处理 9:动态处理") @RequestParam("orderTask") int orderTask){
        if(environment.acceptsProfiles("production")){
            return NewResponseUtil.makeFail("线上不提供重置工单功能");
        }

        if(adminUserId <= 0 || orderTask <= 0){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<Long> workOrderIds = Lists.newArrayList();

        if(AdminWorkOrderConst.Task.isUgcTasks(orderTask)){

            List<AdminSensitiveVo> adminWorkOrders = adminWorkOrderDao.selectUgcSensitiveByPage(AdminWorkOrderConst.Type.UGC.getCode(),
                    AdminWorkOrderConst.Task.UGC_TASK_CODES,
                    adminUserId, 0, AdminUGCTask.Result.NO.getCode(), null, 0, Lists.newArrayList(), null, null);

            adminWorkOrders.forEach(workOrder -> {
                workOrderIds.add(workOrder.getCaseId());
            });
        } else if(orderTask == AdminWorkOrderConst.Task.INFO_BASE_WORD.getCode()){

            List<AdminWorkOrder> adminWorkOrders = adminWorkOrderDao.listUgcBaseInfoByPage(adminUserId, AdminWorkOrderConst.Type.UGC.getCode(),
                    Lists.newArrayList(AdminWorkOrderConst.Task.INFO_BASE_WORD.getCode()),
                    null, AdminUGCTask.Result.NO.getCode(), null, null, null, null);

            adminWorkOrders.forEach(workOrder -> {
                workOrderIds.add(workOrder.getId());
            });

        } else if(orderTask == AdminWorkOrderConst.Task.PROGRESS_WORD.getCode()){

            List<AdminWorkOrder> adminWorkOrders = adminWorkOrderDao.selectUgcByPageV2(adminUserId, AdminWorkOrderConst.Type.UGC.getCode(),
                    Lists.newArrayList(AdminWorkOrderConst.Task.PROGRESS_WORD.getCode()), null, AdminUGCTask.Result.NO.getCode(), getRight(adminUserId));

            adminWorkOrders.forEach(workOrder -> {
                workOrderIds.add(workOrder.getId());
            });
        }

        if(CollectionUtils.isEmpty(workOrderIds)){
            return NewResponseUtil.makeFail("没有更多工单");
        }

        int result = adminWorkOrderBiz.resetWorkOrder(workOrderIds, 0, AdminWorkOrderConst.Status.CREATED.getCode(), AdminWorkOrderConst.TaskType.CREATED.getCode());

        return NewResponseUtil.makeSuccess(result > 0 ? "succ" : "fail");
    }



    @RequiresPermission("sensitive:getmynewlist")
    @RequestMapping(path = "get-my-new-list-v2", method = RequestMethod.POST)
    @ResultSensitiveHandler
    public Response getMyNewListV2(@RequestParam(defaultValue = "0") int anchor,
                                   @RequestParam(defaultValue = "" + CrowdfundingCons.MAX_PAGE_SIZE) int pageSize,
                                   @ApiParam("处理状态 0 未处理，1删除 2 修改 3 不处理") Integer handleResult,
                                   @ApiParam("案例id") int caseId,
                                   @ApiParam("案例标题") String title,
                                   @ApiParam("评论用户id") long commentUserId,
                                   @ApiParam("内容类型 4 订单评论 5 动态评论 6 证实评论") int contentType,
                                   @ApiParam("工单类型 0 全部 1 敏感词 2 禁止词")
                                   @RequestParam(required = false, defaultValue = "0") int taskType,
                                   @ApiParam("操作人id") @RequestParam(required = false, defaultValue = "0") int operatorId,
                                   @ApiParam("关键词") String hitWords,
                                   @ApiParam("操作时间") String startTime,
                                   @ApiParam("是否是向前翻页") @RequestParam(defaultValue = "false") boolean isPre,
                                   @ApiParam("操作时间") String endTime) {

        int userId = ContextUtil.getAdminUserId();
        log.info("SensitiveWordController getMyList userId:{}, handleResult:{}", userId, handleResult);
        //判断时间
        Response dateCheck = ParamTimeRangeHandler.illegalTimeRange(startTime, endTime);
        if (dateCheck.notOk()) {
            return dateCheck;
        }

        //案例ID、案例标题、用户ID、关键词，不限定时间
        Pair<String, String> startAndEndTime = ParamTimeRangeHandler.handleGetMyNewListV2(caseId, title, commentUserId, hitWords, startTime, endTime);
        String handledStartTime = startAndEndTime.getLeft();
        String handledEndTime = startAndEndTime.getRight();

        int selectOperatorId = promoteSelectOperatorId(operatorId);

        AnchorPageBigInt2VO<AdminSensitiveVo> result;

        if(getMyNewListV2Diff && selectOperatorId == 0){
            result = AnchorPageUtils.list(pageSize,
                    isPre,
                    realSize -> adminWorkOrderBiz.selectUgcSensitiveByAnchorFromEs(anchor,
                            realSize, isPre, selectOperatorId,
                            caseId, handleResult, title, commentUserId, contentType,
                            taskType, hitWords, handledStartTime, handledEndTime), AdminSensitiveVo::getWorkOrderId);
        }else {
            result = AnchorPageUtils.list(pageSize,
                    isPre,
                    realSize -> adminWorkOrderBiz.selectUgcSensitiveByAnchor(anchor,
                            realSize, isPre, selectOperatorId,
                            caseId, handleResult, title, commentUserId, contentType,
                            taskType, hitWords, handledStartTime, handledEndTime), AdminSensitiveVo::getWorkOrderId);
        }

        return NewResponseUtil.makeSuccess(result);
    }

    /**
     * 根据权限与是否筛选返回应查询的操作人字段
     *
     * @param targetOperatorId
     * @return
     */
    private int promoteSelectOperatorId(int targetOperatorId) {
        int currentUserId = ContextUtil.getAdminUserId();
        int hasPermission = getRight(currentUserId);

        // 无权限 仅查询当前用户
        if (hasPermission != 1) {
            return currentUserId;
        }

        // 有权限 有筛选 返回目标操作人筛选
        if (targetOperatorId != 0) {
            return targetOperatorId;
        }

        // 有权限 不需要筛选 返回所有操作人数据
        return 0;
    }

    @RequiresPermission("sensitive:list-ugc-operator")
    @Deprecated
    @PostMapping("list-ugc-operator")
    public Response listOperator() {
        return NewResponseUtil.makeSuccess(Lists.newArrayList());
    }


    @RequiresPermission("sensitive:getnewcomment")
    @RequestMapping(path = "getnewcomment", method = RequestMethod.POST)
    @ResultSensitiveHandler
    public Response getComment(long wordId,long workOrderId,int caseId,int contentType) {

        if (workOrderId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        AdminUserCommentVo commentVo = adminTaskUgcBiz.getCommentList(wordId,workOrderId,caseId,contentType);

        return NewResponseUtil.makeSuccess(commentVo);
    }


    /**
     * 检查某userId是否拥有组长权限
     *
     * @param userId
     * @return 1为拥有 其他为没有
     */
    private int getRight(int userId) {
        boolean b = seaUserAuthDelegate.hasPermissionSimple(userId, SENSITIVE_LEADER_ROLE_ID);
        return b ? 1 : 0;
    }



    @RequiresPermission("sensitive:get-my-only-baseinfo-mission")
    @RequestMapping(path = "get-my-only-baseInfo-mission", method = RequestMethod.POST)
    @ApiOperation("仅获取图文审核任务")
    public Response getMyOnlyBaseInfoMission(int userId, int count) {

        log.info("SensitiveWordController getMyBaseInfoMission userId:{}, count:{}", userId, count);

        String lockName = "work-order-get-my-only-baseInfo-mission";
        String identifier = "";

        try {
            identifier = cfRedissonHandler.tryLock(lockName, 5 * 1000, 60 * 1000);

            if (StringUtils.isNotEmpty(identifier)) {
                if (count <= 0) {
                    return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
                }

                List<AdminWorkOrder> adminWorkOrders = adminWorkOrderDao.listUgcBaseInfoByPage(userId, AdminWorkOrderConst.Type.UGC.getCode(),
                        Lists.newArrayList(AdminWorkOrderConst.Task.INFO_BASE_WORD.getCode()),
                        null, AdminUGCTask.Result.NO.getCode(), null, null, null, null);

                if (CollectionUtils.isNotEmpty(adminWorkOrders)) {
                    return NewResponseUtil.makeError(AdminErrorCode.EXIST_ORDER_NO_HANDLE);
                }

                List<AdminWorkOrder> workOrders = adminWorkOrderBiz.assigningTask(AdminWorkOrderConst.Type.UGC, AdminWorkOrderConst.Task.INFO_BASE_WORD, userId, count);

                List<UserComment> userComments = Lists.newArrayList();
                for (AdminWorkOrder workOrder : workOrders) {
                    UserComment userComment = new UserComment(UserCommentSourceEnum.UGC, workOrder.getId(), UserCommentSourceEnum.CommentType.UGC_6, userId, UserCommentSourceEnum.CommentType.UGC_6.getDesc(), "", "图文处理工单领取时间");
                    userComments.add(userComment);
                }
                userCommentBiz.addList(userComments);

                return getMyOnlyBaseInfoList(userId, 1, 10, null, null, null, null, userId, null, null);
            } else {
                log.warn("getMyOnlyBaseInfoMission:获取工单失败");
                return NewResponseUtil.makeError(AdminErrorCode.GET_WORK_ORDER_FAILED);
            }

        } catch (Exception e) {
            log.error("", e);
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        } finally {
            if(StringUtils.isNotEmpty(identifier)){
                cfRedissonHandler.unLock(lockName, identifier);
            }
        }
    }


    @RequiresPermission("sensitive:get-unhandle-baseInfo")
    @RequestMapping(path = "get-unhandle-baseInfo", method = RequestMethod.POST)
    @ApiOperation(value = "未处理的图文工单")
    public Response getUnHandleBaseInfo() {
        return NewResponseUtil.makeSuccess(adminTaskUgcBiz.countUnHandleBaseInfoOrder());
    }

    /**
     * 仅拿图文审核的
     *
     * @param userId
     * @param current
     * @param pageSize
     * @param handleResult
     * @param caseId
     * @return
     */
    @RequiresPermission("sensitive:get-my-only-baseInfo-list")
    @RequestMapping(path = "get-my-only-baseInfo-list", method = RequestMethod.POST)
    @ApiOperation(value = "获取自己名下的图文审核列表(仅图文审核，不含有动态)", response = WorkOrderUgcBaseInfoVo.class)
    @ResultSensitiveHandler
    public Response getMyOnlyBaseInfoList(int userId,
                                          @RequestParam(defaultValue = "1") int current,
                                          @RequestParam(defaultValue = "" + CrowdfundingCons.MAX_PAGE_SIZE) int pageSize,
                                          Integer handleResult,
                                          Integer caseId,
                                          @RequestParam(required = false) Integer action,
                                          @RequestParam(required = false) Integer riskLevel,
                                          @ApiParam("操作人id")
                                          @RequestParam(required = false, defaultValue = "0") int operatorId,
                                          @ApiParam("操作时间") String startTime,
                                          @ApiParam("操作时间") String endTime
    ) throws ExecutionException, InterruptedException {
        log.info("SensitiveWordController getMyBaseInfoList userId:{}, current:{}, pageSize:{}, handleResult:{}",
                userId, current, pageSize, handleResult);

        //判断时间
        Response dateCheck = ParamTimeRangeHandler.illegalTimeRange(startTime, endTime);
        if (dateCheck.notOk()) {
            return dateCheck;
        }

        Pair<String, String> startAndEndTime = ParamTimeRangeHandler.handleGetMyOnlyBaseInfoList(caseId, startTime, endTime);
        String handledStartTime = startAndEndTime.getLeft();
        String handledEndTime = startAndEndTime.getRight();

        AdminUGCTask.Result taskResult = null;

        if (handleResult != null) {
            taskResult = AdminUGCTask.Result.getByCode(handleResult);
        }

        CompletableFuture<Integer> selectOperatorIdFuture = CompletableFuture.supplyAsync(() -> promoteSelectOperatorId(operatorId), getMyOnlyBaseInfoListExecutor);
        Map<String, Object> result = Maps.newHashMap();

        CompletableFuture<Integer> countFuture = CompletableFuture.supplyAsync(() -> adminWorkOrderBiz.selectUnHandleCount(AdminWorkOrderConst.Type.UGC,
                ImmutableList.of(AdminWorkOrderConst.Task.INFO_BASE_WORD)), getMyOnlyBaseInfoListExecutor);

        CompletableFuture.allOf(selectOperatorIdFuture,countFuture).join();
        int selectOperatorId = selectOperatorIdFuture.get();
        Integer count = countFuture.get();

        result.put("unhandleCount", count == null ? 0 : count);

        List<AdminWorkOrder> adminWorkOrders = Lists.newArrayList();
        if (getMyOnlyBaseInfoListDiff) {

            Pair<Long, List<AdminWorkOrder>> pair = adminWorkOrderBiz.selectUgcBaseInfoByPageFromEs(selectOperatorId,
                    AdminWorkOrderConst.Type.UGC,
                    ImmutableList.of(AdminWorkOrderConst.Task.INFO_BASE_WORD),
                    current,
                    pageSize,
                    caseId,
                    taskResult,
                    action,
                    riskLevel,
                    handledStartTime,
                    handledEndTime);
            adminWorkOrders = pair.getRight();

            Map<String, Object> pageMap = Maps.newHashMap();
            pageMap.put("total", pair.getLeft());
            pageMap.put("current", current < 1 ? 1 : current);
            pageMap.put("pageSize", pageSize);

            result.put("pagination", pageMap);
        }else {

            adminWorkOrders = this.adminWorkOrderBiz.selectUgcBaseInfoByPage(
                    selectOperatorId,
                    AdminWorkOrderConst.Type.UGC,
                    ImmutableList.of(AdminWorkOrderConst.Task.INFO_BASE_WORD),
                    current,
                    pageSize,
                    caseId,
                    taskResult,
                    action,
                    riskLevel,
                    handledStartTime,
                    handledEndTime);
            if (CollectionUtils.isEmpty(adminWorkOrders)) {
                result.put("pagination", PageUtil.transform2PageMap(adminWorkOrders));
                result.put("data", null);
                return NewResponseUtil.makeSuccess(result);
            }

            Map<String, Object> map = PageUtil.transform2PageMap(adminWorkOrders);
            result.put("pagination", map);
        }

        List<WorkOrderUgcBaseInfoVo> workOrderUgcBaseInfoVoList = getWorkOrderUgcBaseInfoVos(adminWorkOrders);
        result.put("data", workOrderUgcBaseInfoVoList);
        return NewResponseUtil.makeSuccess(result);
    }


    private List<WorkOrderUgcBaseInfoVo> getWorkOrderUgcBaseInfoVos(List<AdminWorkOrder> adminWorkOrders) throws ExecutionException, InterruptedException {
        if (CollectionUtils.isEmpty(adminWorkOrders)) {
            return Lists.newArrayList();
        }
        List<Long> adminWorkOrderIdList = adminWorkOrders.stream().map(AdminWorkOrder::getId).collect(Collectors.toList());
        List<AdminTaskUgc> adminTaskUgcs = adminTaskUgcBiz.selectByWorkOrderIds(adminWorkOrderIdList);
        Map<Long, AdminTaskUgc> adminTaskUgcMap = adminTaskUgcs.stream().collect(Collectors.toMap(AdminTaskUgc::getWorkOrderId, Function.identity()));
        Map<Integer, List<AdminTaskUgc>> contentTypeMap = adminTaskUgcs.stream().collect(Collectors.groupingBy(AdminTaskUgc::getContentType));
        List<AdminTaskUgc> baseInfoUgcs = contentTypeMap.get(AdminUGCTask.Content.BASE_INFO.getCode());
        Map<Integer, List<CrowdfundingAttachment>> crowdfundingAttachmentMap = Collections.emptyMap();
        Map<Integer, CrowdfundingAuthor> authorMap = Maps.newHashMap();

        List<Integer> caseIds = adminTaskUgcs.stream().map(AdminTaskUgc::getCaseId).collect(Collectors.toList());
        CompletableFuture<Map<Integer, CrowdfundingInfo>> caseMapFuture = CompletableFuture.supplyAsync(() -> adminCrowdfundingInfoBiz.getMapByIds(caseIds), getMyOnlyBaseInfoListExecutor);

        CompletableFuture<Map<Integer, List<CrowdfundingAttachment>>> crowdfundingAttachmentMapFuture = null;
        CompletableFuture<Map<Integer, CrowdfundingAuthor>> authorMapFuture = null;
        if (CollectionUtils.isNotEmpty(baseInfoUgcs)) {
            crowdfundingAttachmentMapFuture = CompletableFuture.supplyAsync(() -> this.adminCrowdfundingAttachmentBiz.getMapByInfoIdListAndType(caseIds, AttachmentTypeEnum.ATTACH_CF), getMyOnlyBaseInfoListExecutor);
            authorMapFuture = CompletableFuture.supplyAsync(() -> crowdfundingUserDelegate.getByInfoIdList(caseIds), getMyOnlyBaseInfoListExecutor);
        }

        CompletableFuture<Map<Integer, CfBaseInfoRiskHitVO>> hitVOMapFuture = CompletableFuture.supplyAsync(() -> sensitiveWordService.getBaseInfoHitMapping(caseIds), getMyOnlyBaseInfoListExecutor);

        CompletableFuture<Map<Integer, CrowdFundingProgress>> crowdFundingProgressMapFuture = CompletableFuture.supplyAsync(() ->{
            List<AdminTaskUgc> progressUgcs = contentTypeMap.get(AdminUGCTask.Content.PROGRESS.getCode());
            if (CollectionUtils.isNotEmpty(progressUgcs)) {
                List<Integer> progressIds = progressUgcs.stream().map(val -> new Long(val.getExtId()).intValue()).collect(Collectors.toList());//已检查过
                return adminCrowdFundingProgressBiz.getMapByIds(progressIds);
            }
            return new HashMap<>(); }, getMyOnlyBaseInfoListExecutor);

        if (Objects.isNull(crowdfundingAttachmentMapFuture)){
            CompletableFuture.allOf(caseMapFuture, hitVOMapFuture, crowdFundingProgressMapFuture).join();

        }else {
            CompletableFuture.allOf(caseMapFuture,crowdfundingAttachmentMapFuture, authorMapFuture, hitVOMapFuture, crowdFundingProgressMapFuture).join();
            crowdfundingAttachmentMap = crowdfundingAttachmentMapFuture.get();
            authorMap = authorMapFuture.get();
        }

        Map<Integer, CrowdfundingInfo> caseMap = caseMapFuture.get();
        Map<Integer, CfBaseInfoRiskHitVO> hitVOMap = hitVOMapFuture.get();
        Map<Integer, CrowdFundingProgress> crowdFundingProgressMap = crowdFundingProgressMapFuture.get();

        Map<Integer, List<CrowdfundingAttachment>> finalCrowdfundingAttachmentMap = crowdfundingAttachmentMap;
        Map<Integer, CrowdfundingAuthor> finalAuthorMap = authorMap;

        List<CompletableFuture<WorkOrderUgcBaseInfoVo>> futureList = adminWorkOrders.stream().map(adminWorkOrder -> CompletableFuture.supplyAsync(() -> {
            AdminTaskUgc adminTaskUgc = adminTaskUgcMap.get(adminWorkOrder.getId());

            int taskUgcResult = adminTaskUgc.getResult();
            int contentType = adminTaskUgc.getContentType();
            AdminUGCTask.Content content = AdminUGCTask.Content.getByCode(contentType);
            int caseId = adminTaskUgc.getCaseId();
            CrowdfundingInfo crowdfundingInfo = caseMap.get(caseId);
            if (crowdfundingInfo == null) {
                log.warn("没找到案例信息 {}, {}, {}", adminWorkOrder.getId(), adminWorkOrder, crowdfundingInfo);
                return null;
            }

            int operatorId = adminWorkOrder.getOperatorId();

            CompletableFuture<Response<CfCapitalAccount>> cfCapitalAccountResponseFuture = CompletableFuture.supplyAsync(() ->
                    financeDelegate.getCfCapitalAccountByInfoUuid(crowdfundingInfo.getInfoId()), getMyOnlyBaseInfoListExecutor);


            CompletableFuture<String> moblieFuture = CompletableFuture.supplyAsync(() -> {
                UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(crowdfundingInfo.getUserId());
                if (userInfoModel != null && StringUtils.isNotBlank(userInfoModel.getCryptoMobile())) {
                    return shuidiCipher.decrypt(userInfoModel.getCryptoMobile());
                }
                return "";
            }, getMyOnlyBaseInfoListExecutor);

            CompletableFuture.allOf(cfCapitalAccountResponseFuture,moblieFuture).join();

            Response<CfCapitalAccount> cfCapitalAccountResponse = null;
            String moblie = "";
            try {
                cfCapitalAccountResponse = cfCapitalAccountResponseFuture.get();
                moblie = moblieFuture.get();
            } catch (InterruptedException |ExecutionException e) {
               throw new RuntimeException(e);
            }

            if (content == AdminUGCTask.Content.BASE_INFO) {
                Set<String> sensitiveWords = SensitivewordFilter.getInstance().getSensitiveWord(crowdfundingInfo
                        .getContent(), 1);
                WorkOrderUgcBaseInfoVo workOrderUgcBaseInfoVo = WorkOrderUgcBaseInfoVo.buildBaseInfo(adminWorkOrder
                                .getId(), adminTaskUgc.getId(), adminWorkOrder.getComment(), content,
                        crowdfundingInfo, finalCrowdfundingAttachmentMap.get(adminTaskUgc.getCaseId()), sensitiveWords,
                        taskUgcResult, cfCapitalAccountResponse.getData(), moblie);

                String infoUuid = crowdfundingInfo.getInfoId();
                workOrderUgcBaseInfoVo.setInfoUuId(infoUuid);
                workOrderUgcBaseInfoVo.setOperatorId(operatorId);
                workOrderUgcBaseInfoVo.setOperatorTime(new Date(adminWorkOrder.getUpdateTime().getTime()));

                //放案例的基本信息
                workOrderUgcBaseInfoVo.setCrowdfundingInfo(crowdfundingInfo);
                if (finalAuthorMap.containsKey(adminTaskUgc.getCaseId())) {
                    workOrderUgcBaseInfoVo.setCrowdfundingAuthor(finalAuthorMap.get(adminTaskUgc.getCaseId()));
                }

                // 图文风险信息
                CompletableFuture<CaseRaiseRiskDO> raiseRiskDOFuture =  CompletableFuture.supplyAsync(() -> riskDelegate.getByInfoUuid(infoUuid), getMyOnlyBaseInfoListExecutor);
                //用户标签风险级别
                CompletableFuture<UserTagHistory> userTagHistoryFuture = CompletableFuture.supplyAsync(() -> userTagClient.getOperatorValid(crowdfundingInfo.getId()), getMyOnlyBaseInfoListExecutor);
                // 审核查看暂存内容
                CompletableFuture<Response<CaseInfoApproveStageDO>> stageInfoRespFuture = CompletableFuture.supplyAsync(() ->caseInfoApproveStageFeignClient.getStageInfo(caseId), getMyOnlyBaseInfoListExecutor);

                CompletableFuture.allOf(raiseRiskDOFuture, userTagHistoryFuture, stageInfoRespFuture).join();

                CaseRaiseRiskDO raiseRiskDO;
                UserTagHistory userTagHistory;
                Response<CaseInfoApproveStageDO> stageInfoResp;
                try {
                    raiseRiskDO = raiseRiskDOFuture.get();
                    userTagHistory = userTagHistoryFuture.get();
                    stageInfoResp = stageInfoRespFuture.get();
                } catch (InterruptedException | ExecutionException e) {
                    throw new RuntimeException(e);
                }

                // 图文风险信息
                if (raiseRiskDO != null) {
                    workOrderUgcBaseInfoVo.setInfoRiskLevel(raiseRiskDO.getRiskLevel());
                    workOrderUgcBaseInfoVo.setInfoRiskData(raiseRiskDO.getRiskData());
                    workOrderUgcBaseInfoVo.setUgcAction(adminTaskUgc.getAction());
                }
                workOrderUgcBaseInfoVo.setRiskHitVO(hitVOMap.get(caseId));

                //用户标签风险级别
                if (userTagHistory != null && CollectionUtils.isNotEmpty(userTagHistory.getUnitList())) {
                    workOrderUgcBaseInfoVo.setUnits(userTagHistory.getUnitList());
                }

                // 审核查看暂存内容
                if (stageInfoResp.ok()) {
                    if (stageInfoResp.getData() != null) {
                        CaseInfoApproveStageDO stage = stageInfoResp.getData();
                        crowdfundingInfo.setContent(stage.getContent());
                        crowdfundingInfo.setTitle(stage.getTitle());
                        workOrderUgcBaseInfoVo.setContent(stage.getContent());
                        workOrderUgcBaseInfoVo.setTitle(stage.getTitle());
                        workOrderUgcBaseInfoVo.setAttachmentUrls(stage.getImages());
                    }
                } else {
                    workOrderUgcBaseInfoVo.setTitle("");
                    workOrderUgcBaseInfoVo.setContent("");
                    workOrderUgcBaseInfoVo.setAttachmentUrls("");
                }

                watermarkService.fillUgcBaseContentWatermark(caseId, workOrderUgcBaseInfoVo);
                return workOrderUgcBaseInfoVo;

            } else if (content == AdminUGCTask.Content.PROGRESS) {
                CrowdFundingProgress crowdFundingProgress = crowdFundingProgressMap.get(new Long(adminTaskUgc
                        .getExtId()).intValue());//已检查过

                CompletableFuture<Set<String>> sensitiveWordsFuture  = CompletableFuture.supplyAsync(() ->getHitWords(crowdFundingProgress), getMyOnlyBaseInfoListExecutor);
                CompletableFuture<Boolean> isSafeSingleFuture = CompletableFuture.supplyAsync(() -> riskDelegate.isSafeSingle(UgcTypeEnum.PROGRESS, crowdFundingProgress.getId(), caseId), getMyOnlyBaseInfoListExecutor);

                try {
                    CompletableFuture.allOf(sensitiveWordsFuture,isSafeSingleFuture).join();

                    WorkOrderUgcBaseInfoVo workOrderUgcBaseInfoVo = WorkOrderUgcBaseInfoVo.buildProcess(adminWorkOrder
                                .getId(), adminTaskUgc.getId(), adminWorkOrder.getComment(), content, crowdFundingProgress,
                        sensitiveWordsFuture.get(), taskUgcResult, crowdfundingInfo, cfCapitalAccountResponse.getData(), moblie);
                    workOrderUgcBaseInfoVo.setOperatorId(operatorId);
                    workOrderUgcBaseInfoVo.setOperatorTime(new Date(adminWorkOrder.getUpdateTime().getTime()));

                    // 添加是否是先审后发字段
                    workOrderUgcBaseInfoVo.setFirstTrial(!isSafeSingleFuture.get());

                    return workOrderUgcBaseInfoVo;
                } catch (InterruptedException |ExecutionException e) {
                    throw new RuntimeException(e);
                }
            }

            return null;

        }, getMyOnlyBaseInfoListExecutor)).collect(Collectors.toList());


        List<WorkOrderUgcBaseInfoVo> workOrderUgcBaseInfoVoList = futureList.stream().map(CompletableFuture::join).filter(Objects::nonNull).collect(Collectors.toList());

        //操作人
        Stream<WorkOrderUgcBaseInfoVo> resultStream = workOrderUgcBaseInfoVoList.stream();
        List<Integer> userIds = resultStream
                .map(WorkOrderUgcBaseInfoVo::getOperatorId)
                .distinct()
                .collect(Collectors.toList());
        List<AdminUserAccountModel> models = seaAccountClientV1.getUserAccountsByIds(userIds).getResult();

        Map<Integer, AdminUserAccountModel> userMap = models.stream()
                .collect(Collectors.toMap(AdminUserAccountModel::getId, Function.identity()));

        workOrderUgcBaseInfoVoList.forEach(r -> {
            String name = "";
            int user = r.getOperatorId();
            if (userMap.containsKey(user)) {
                name = userMap.get(user).getName();
            }
            r.setOperatorName(name);
        });
        return workOrderUgcBaseInfoVoList;
    }

    /**
     * 获取动态命中的风控词
     * @param crowdFundingProgress
     * @return
     */
    @NotNull
    private Set<String> getHitWords(CrowdFundingProgress crowdFundingProgress) {
        String progressContent = crowdFundingProgress.getContent();

        RiskWordCheckContext ctx = RiskWordCheckContext.builder()
                .content(progressContent)
                .useScenes(Lists.newArrayList(RiskControlWordCategoryDO.RiskWordUseScene.PUBLISH_UGC.getCode()))
                .isCheckAll(true)
                .build();
        OpResult<RiskWordResult> checkResult = riskDelegate.isHit(ctx);
        if (checkResult.isFail()) {
            return Collections.emptySet();
        }
        RiskWordResult riskWordResult = checkResult.getData();
        if (riskWordResult.isPassed()) {
            return Collections.emptySet();
        }
        return Sets.newHashSet(checkResult.getData().getHitWords());
    }

    // 只有前置审核-处理图文会被调用
    @RequiresPermission("sensitive:handle")
    @RequestMapping(path = "/first-approve/handle", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response firstApproveHandle(int handleType, @RequestParam String workOrderId,
                                      @RequestParam(required = false) String comment, @RequestParam String reason,
                                      @RequestParam(required = false) String title, @RequestParam(required = false) String content,
                                      @RequestParam(defaultValue = "") String imgUrls) {

        // 对于图文的工单 先判断工单是否已经被处理。
        AdminTaskUgc taskUgc = adminTaskUgcBiz.selectByWorkOrderId(Long.valueOf(workOrderId));
        if (taskUgc == null || AdminUGCTask.Result.getUgcHandleCode().contains(taskUgc.getResult())) {
            log.error("前置审核初的图文工单已经是终态了，不再处理。workOrderId:{} orderStatus:{}", workOrderId,
                    taskUgc == null ? null : taskUgc.getResult());
            return NewResponseUtil.makeSuccess("");
        }

        return handle( handleType, workOrderId, comment, reason, 0, title, content, imgUrls);
    }

    @RequiresPermission("sensitive:handle")
    @RequestMapping(path = "handle", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @Deprecated
    public Response handle(int handleType, @RequestParam String workOrderId,
                           @RequestParam(required = false) String comment, @RequestParam String reason,
                           @RequestParam(required = false, defaultValue = "0") int stopReasonId,
                           @RequestParam(required = false) String title, @RequestParam(required = false) String content,
                           @RequestParam(defaultValue = "") String imgUrls)
    {
        int userId = ContextUtil.getAdminUserId();
        log.info("SensitiveWordController handle userId:{}, handleType:{}, workOrderId:{}, comment:{}, reason:{}, title:{}, content:{}",
                userId, handleType, workOrderId, comment, reason, title, content);
        if (StringUtils.isBlank(workOrderId)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_IS_NULL);
        }

        return sensitiveWordService.handleUgcWorkId(handleType, workOrderId, comment, reason, stopReasonId, title, content, imgUrls, userId);
    }


    @RequiresPermission("sensitive:ugcHandle")
    @RequestMapping(path = "ugcHandle", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response ugcHandle(int handleType,
                              String reason,
                              String ugcWorkOrderId,
                              @RequestParam(required = false, value = "verifyStatus", defaultValue = "-1") Integer verifyStatus,
                              @RequestParam(required = false, value = "medicalImageUrl" )String medicalImageUrl) {
        int userId = ContextUtil.getAdminUserId();
        log.info("SensitiveWordController ugcHandle userId:{}, handleType:{},  reason:{}", userId, handleType, reason);
        if (StringUtils.isBlank(ugcWorkOrderId)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_IS_NULL);
        }
        int adminUserId = ContextUtil.getAdminUserId();
        return sensitiveWordService.handleUgcWorkId(handleType,reason,userId,ugcWorkOrderId,adminUserId,verifyStatus,medicalImageUrl);
    }

    @RequiresPermission("sensitive:handleProgress")
    @RequestMapping(path = "handleProgress", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response handleProgress(int handleType,
                                   String reason,
                                   String ugcWorkOrderId,
                                   @RequestParam(required = false) String content,
                                   @RequestParam(required = false) String imgUrls) {
        int userId = ContextUtil.getAdminUserId();
        log.info("SensitiveWordController ugcHandle userId:{}, handleType:{},  reason:{} ugcWorkOrderId={}", userId, handleType, reason,ugcWorkOrderId);
        if (StringUtils.isBlank(ugcWorkOrderId)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_IS_NULL);
        }
        return sensitiveWordService.handleProgress(handleType,reason,userId,ugcWorkOrderId,content,imgUrls);
    }

    @RequiresPermission("sensitive:handleProgressHeadImage")
    @RequestMapping(path = "handleProgressHeadImage", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response<Void> handleProgressHeadImage(int handleType, String reason, String ugcWorkOrderId) {
        int userId = ContextUtil.getAdminUserId();
        log.info("SensitiveWordController ugcHandle userId:{}, handleType:{},  reason:{} ugcWorkOrderId={}", userId, handleType, reason,ugcWorkOrderId);
        if (StringUtils.isBlank(ugcWorkOrderId)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_IS_NULL);
        }
        return sensitiveWordService.handleProgressHeadImage(handleType, reason, userId, ugcWorkOrderId);
    }


    @RequiresPermission("sensitive:get-content-info")
    @RequestMapping(path = "get-content-info", method = RequestMethod.POST)
    public Response getContentInfo(int workOrderId) {
        int userId = ContextUtil.getAdminUserId();
        log.info("SensitiveWordController getBaseInfo workOrderId:{}, userId:{}", workOrderId, userId);
        Map<String, Object> result = Maps.newHashMap();
        AdminTaskUgc adminTaskUgc = adminTaskUgcBiz.selectByWorkOrderId(workOrderId);
        if (adminTaskUgc == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CfSensitiveWordRecord cfSensitiveWordRecord = cfSensitiveWordRecordBiz.selectById(adminTaskUgc.getWordId());
        AdminUGCTask.Content contentType = AdminUGCTask.Content.getByCode(cfSensitiveWordRecord.getBizType());
        long bizId = cfSensitiveWordRecord.getBizId();
        switch (contentType) {
            case BASE_INFO:
                String infoUuid = cfSensitiveWordRecord.getInfoUuid();
                CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoUuid);
                if (crowdfundingInfo == null) {
                    return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
                }
                Map<String, Object> crowdfundingMap = Maps.newHashMap();
                long userId1 = crowdfundingInfo.getUserId();
                crowdfundingMap.put("accountUserId", userId1);
                UserInfoModel baseUserInfoByUserId = userInfoServiceBiz.getUserInfoByUserId(userId1);
                if (baseUserInfoByUserId != null) {
                    crowdfundingMap.put("nickname", baseUserInfoByUserId.getNickname());
                }
                crowdfundingMap.put("title", crowdfundingInfo.getTitle());
                crowdfundingMap.put("content", crowdfundingInfo.getContent());
                result.put("data", crowdfundingMap);
                break;
            case ORDER:
                CrowdfundingOrder crowdfundingOrder = adminCrowdfundingOrderBiz.getById(bizId);
                if (crowdfundingOrder != null) {
                    Map<String, Object> orderMap = Maps.newHashMap();
                    long userId2 = crowdfundingOrder.getUserId();
                    orderMap.put("accountUserId", userId2);
                    UserInfoModel baseUserInfoByUserId1 = userInfoServiceBiz.getUserInfoByUserId(userId2);
                    if (baseUserInfoByUserId1 != null) {
                        orderMap.put("nickname", baseUserInfoByUserId1.getNickname());
                    }
                    orderMap.put("comment", crowdfundingOrder.getComment());
                    result.put("data", orderMap);
                }
                break;
            case PROGRESS:
                CrowdFundingProgress crowdFundingProgress = adminCrowdFundingProgressBiz.getActivityProgressById(bizId);
                if (crowdFundingProgress != null) {
                    Map<String, Object> progressMap = Maps.newHashMap();
                    long userId3 = crowdFundingProgress.getUserId();
                    progressMap.put("accountUserId", userId3);
                    UserInfoModel baseUserInfoByUserId2 = userInfoServiceBiz.getUserInfoByUserId(userId3);
                    if (baseUserInfoByUserId2 != null) {
                        progressMap.put("nickname", baseUserInfoByUserId2.getNickname());
                    }
                    progressMap.put("comment", crowdFundingProgress.getContent());
                    result.put("data", progressMap);
                }
                break;
            case VERIFICATION:
                CrowdFundingVerification verification = adminCrowdFundingVerificationBiz.getById(bizId);
                if (verification != null) {
                    Map<String, Object> verificationMap = Maps.newHashMap();
                    long userId4 = verification.getVerifyUserId();
                    verificationMap.put("accountUserId", userId4);
                    UserInfoModel baseUserInfoByUserId3 = userInfoServiceBiz.getUserInfoByUserId(userId4);
                    if (baseUserInfoByUserId3 != null) {
                        verificationMap.put("nickname", baseUserInfoByUserId3.getNickname());
                    }
                    verificationMap.put("comment", verification.getDescription());
                    result.put("data", verificationMap);
                }
                break;
            case COMMENT_ORDER:
            case COMMENT_PROGRESS:
                CrowdfundingComment crowdfundingComment = adminCrowdfundingCommentBiz.getByIdNoCareDeleted(bizId,adminTaskUgc.getCaseId());
                if (crowdfundingComment != null) {
                    Map<String, Object> commentMap = Maps.newHashMap();
                    long userId5 = crowdfundingComment.getUserId();
                    commentMap.put("accountUserId", userId5);
                    UserInfoModel baseUserInfoByUserId4 = userInfoServiceBiz.getUserInfoByUserId(userId5);
                    if (baseUserInfoByUserId4 != null) {
                        commentMap.put("nickname", baseUserInfoByUserId4.getNickname());
                    }
                    commentMap.put("comment", crowdfundingComment.getContent());
                    result.put("data", commentMap);
                }
                break;
            default:
                break;
        }
        result.put("type", contentType.getCode());
        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("sensitive:get-unhaldle-list-count")
    @RequestMapping(path = "get-un-handle-list-count", method = RequestMethod.POST)
    public Response getUnHandleListCount(@RequestParam(defaultValue = "0") int type) {
        int count = 0;
        try {
            if (type == 0) {
                count = adminWorkOrderBiz.selectUnHandleCount(AdminWorkOrderConst.Type.UGC,
                        AdminWorkOrderConst.Task.UGC_TASKS);
            } else if (type == 1) {
                count = adminWorkOrderBiz.selectUnHandleCount(AdminWorkOrderConst.Type.UGC,
                        ImmutableList.of(AdminWorkOrderConst.Task.INFO_BASE_WORD, AdminWorkOrderConst.Task
                                .PROGRESS_WORD));
            }
        } catch (Exception e) {
            log.error("error msg", e);
        }
        return NewResponseUtil.makeSuccess(count);
    }

    @RequiresPermission("get-credit-supplement-info")
    @RequestMapping(path = "get-credit-supplement-info", method = RequestMethod.POST)
    public Response getCreditSupplementInfo(@ApiParam("分页每页显示的条数，最大50条") @RequestParam(defaultValue = "10", required = false) int pageSize,
                                            @ApiParam("分页的请求页数，从1开始") @RequestParam(defaultValue = "1", required = false) int current,
                                            @ApiParam("查询的案例id") @RequestParam(defaultValue = "0", required = false) int infoId,
                                            @ApiParam("房产信息") @RequestParam(defaultValue = "", required = false) String houseProperty,
                                            @ApiParam("车产信息") @RequestParam(defaultValue = "", required = false) String carProperty,
                                            @ApiParam("提交审核开始时间") @RequestParam(required = false) String startDate,
                                            @ApiParam("提交审核结束时间") @RequestParam(required = false) String endDate,
                                            @ApiParam("案例审核状态") @RequestParam(defaultValue = "4", required = false) int infoStatus) {
        log.info("getCreditSupplementInfo pageSize:{},current:{}", pageSize, current);
        if (pageSize < 0 || pageSize > 50 || current < 0 || infoStatus < 0 || infoStatus > 4) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        //判断时间
        Response dateCheck = ParamTimeRangeHandler.illegalTimeRange(startDate, endDate);
        if (dateCheck.notOk()) {
            return dateCheck;
        }

        //设置默认时间
        Pair<String, String> handledDatePair = ParamTimeRangeHandler.handGetCreditSupplementInfo(infoId, startDate, endDate);
        startDate = handledDatePair.getLeft();
        endDate = handledDatePair.getRight();

        //查询条件不为空时，走查询
        AdminCreditSupplement houseSupplement = null;
        AdminCreditSupplement carSupplement = null;
        if (StringUtils.isNotEmpty(houseProperty)) {
            houseSupplement = JSONObject.parseObject(houseProperty, AdminCreditSupplement.class);//已检查过
        }
        if (StringUtils.isNotEmpty(carProperty)) {
            carSupplement = JSONObject.parseObject(carProperty, AdminCreditSupplement.class);//已检查过
        }
        return NewResponseUtil.makeSuccess(cfCreditSupplementService.SelectByAttributes(pageSize, current, infoId,
                houseSupplement, carSupplement, startDate, endDate, infoStatus));
    }

    @RequiresPermission("get-credit-supplement-info")
    @RequestMapping(path = "update-credit-supplement-status", method = RequestMethod.POST)
    @Deprecated
    public Response updateCreditSupplementStatus(@ApiParam("操作的案例infoUuid集合") String infoUuidList,
                                                 @ApiParam("状态 2 通过， 3 驳回") int status,
                                                 @RequestParam(required = false, defaultValue = "") @ApiParam("审批评论") String commentText) {
        log.info("updateCreditSupplementStatus infoUuidList:{},status:{}", infoUuidList, status);
        int userId = ContextUtil.getAdminUserId();
        if (StringUtils.isEmpty(infoUuidList) || (status != CrowdfundingInfoStatusEnum.PASSED.getCode() &&
                status != CrowdfundingInfoStatusEnum.REJECTED.getCode()) || userId < 0) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<String> itemInfoUuidList = Arrays.asList(infoUuidList.split(","));
        return cfCreditSupplementService.updateCreditSupplementInfo(itemInfoUuidList, status, userId);
    }


    @RequiresPermission("sensitive:ugcHandle")
    @RequestMapping(path = "ugcConsultantEvaluationHandle", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response ugcConsultantEvaluationHandle(int handleType,
                                                  String reason,
                                                  String ugcWorkOrderId) {
        int userId = ContextUtil.getAdminUserId();
        log.info("SensitiveWordController ugcHandle userId:{}, handleType:{},  reason:{}", userId, handleType, reason);
        if (StringUtils.isBlank(ugcWorkOrderId)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_IS_NULL);
        }
        int adminUserId = ContextUtil.getAdminUserId();
        return sensitiveWordService.ugcConsultantEvaluationHandle(handleType,reason,userId,ugcWorkOrderId,adminUserId);
    }
}
