package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.model.service.MobileUserIdModel;
import com.shuidihuzhu.cf.admin.util.result.ResultBuilder;
import com.shuidihuzhu.cf.admin.util.result.ResultUtils;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfAdminOperationRecordBiz;
import com.shuidihuzhu.cf.constants.remark.RemarkTypeConsts;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.domain.CfCaseRiskDO;
import com.shuidihuzhu.cf.domain.RemarkDO;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperationRecordEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.risk.CaseRiskPassedEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.risk.CfCaseRiskTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.risk.CfCaseRiskVerifiedEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.risk.StatRiskHandleStatusEnum;
import com.shuidihuzhu.cf.finance.client.feign.CfFinancePauseFeignClient;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceSeaFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.enums.CfDrawCashPauseRecordEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.util.JsonUtils;
import com.shuidihuzhu.cf.vo.crowdfunding.CfCaseRiskVO;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.grpc.account.v1.feign.SimpleUserAccountServiceClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.shuidihuzhu.common.web.util.admin.PageUtil.MAX_PAGE_SIZE;

/**
 * <AUTHOR>
 * @date 2018-08-04  17:10
 */
@RestController
@Api("异常案例")
@Slf4j
@RequestMapping(path = "/admin/cf/case/risk/abnormal")
public class CfAdminAbnormalCaseController {

    @Resource
    private IRiskDelegate riskDelegate;

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Resource
    private AdminCfInfoExtBiz adminCfInfoExtBiz;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Resource
    private CfAdminOperationRecordBiz cfAdminOperationRecordBiz;

    @Resource
    private SeaAccountClientV1 seaAccountClientV1;

    @Resource
    private SimpleUserAccountServiceClient simpleUserAccountServiceClient;

    @Resource
    private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;
    @Resource
    private CfFinancePauseFeignClient cfFinancePauseFeignClient;
    @Resource
    private CfFinanceSeaFeignClient cfFinanceSeaFeignClient;
    @Autowired
    private ShuidiCipher shuidiCipher;

    private static final int OPERATION_HANDLE = CfOperationRecordEnum.CASE_RISK_HANDLE.value();

    @ApiOperation("根据各种条件 获取异常案例列表 分页")
    @PostMapping("list-by-page-condition")
    @RequiresPermission("abnormalCase:list-by-page-condition")
    public Response listByPageCondition(
            @RequestParam(required = false, defaultValue = "0") long caseId,
            @RequestParam(required = false) String mobile,
            @ApiParam("1: 材料审核, 2: 提现审核")
            @RequestParam
                    int riskType,
            @RequestParam(required = false, defaultValue = "0") int handleStatus,
            @RequestParam(name = "current") int start,
            @RequestParam(name = "pageSize") int size) {
        return createEmptyResult();

//        CfCaseRiskTypeEnum typeEnum = CfCaseRiskTypeEnum.parse(riskType);
//
//        // 单独查询infoUuid
//        if (caseId != 0) {
//            return listByCaseId(caseId, typeEnum);
//        }
//
//        if (StringUtils.isNotBlank(mobile)) {
//            return listByMobile(mobile, typeEnum, start, size);
//        }
//
//        StatRiskHandleStatusEnum statusEnum = StatRiskHandleStatusEnum.parse(handleStatus);
//
//        return listByPage(typeEnum.getType(), null, statusEnum, start, size);
    }

    private Response listByCaseId(long caseId, CfCaseRiskTypeEnum typeEnum) {
        CrowdfundingInfo fundingInfo = crowdfundingDelegate.getFundingInfoById(Math.toIntExact(caseId));//已检查过
        String infoId = fundingInfo.getInfoId();
        OpResult<CfCaseRiskDO> singleResult = riskDelegate.getByInfoUuid(infoId, typeEnum);
        if (singleResult.isFailOrNullData()) {
            return ResultUtils.transformOpResult2Response(singleResult);
        }
        CfCaseRiskDO data = singleResult.getData();
        int verified = data.getVerified();
        if (verified == CfCaseRiskVerifiedEnum.WAITING_VERIFY.getVerified()) {
            return createEmptyResult();
        }
        int passed = data.getPassed();
        if (passed == CaseRiskPassedEnum.PASSED.getValue()) {
            return createEmptyResult();
        }
        CfCaseRiskVO r = transDO2VO(data);

        ArrayList<CfCaseRiskVO> list = Lists.newArrayList(r);
        Map<String, Object> pagination = PageUtil.transform2PageMap(list);
        pagination.put("total", 1);
        pagination.put("pageSize", 10);
        return ResultBuilder.create()
                .put("list", list)
                .put("pagination", pagination)
                .buildSuccessResponse();
    }

    private Response createEmptyResult() {
        ArrayList<Object> list = Lists.newArrayList();
        return wrapListResponse(list, list);
    }

    /**
     * @param mobile
     * @param typeEnum
     * @param start
     * @param size
     * @return
     */
    private Response listByMobile(String mobile, CfCaseRiskTypeEnum typeEnum, int start, int size) {

        // 查出这个手机的用户
        MobileUserIdModel userIdByMobile = simpleUserAccountServiceClient.getUserIdByMobile(mobile);
        if (userIdByMobile == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        long userId = userIdByMobile.getUserId();

        // 查这个用户所有的案例
        List<CrowdfundingInfo> infos = crowdfundingDelegate.selectCrowdfundingInfoListByUserId(userId);
        if (CollectionUtils.isEmpty(infos)) {
            return createEmptyResult();
        }
        // 查询每个案例的风险情况
        List<CfCaseRiskVO> collect = infos.stream()
                .map(crowdfundingInfoView -> {
                    String infoId = crowdfundingInfoView.getInfoId();
                    OpResult<CfCaseRiskDO> a = riskDelegate.getByInfoUuid(infoId, typeEnum);
                    return transDO2VO(a.getData());
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return wrapListResponse(collect, infos);
    }

    @ApiOperation("异常案例 处理材料审核异常案例 设为通过")
    @PostMapping("handle-info-case")
    @RequiresPermission("abnormalCase:handle-info-case")
    public Response handleInfoCase(@RequestParam String infoUuid, @RequestParam int userId) {
        OpResult opResult = riskDelegate.handleInfoRisk(infoUuid);
        if (opResult.isSuccess()) {
            cfAdminOperationRecordBiz.addOneOperationRecord(infoUuid, userId, OPERATION_HANDLE, "手动通过");
            //  恢复风险案例暂停的打款
            CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getCrowdfundingInfoByInfoId(infoUuid);
            AdminUserAccountModel userAccount = seaAccountClientV1.getValidUserAccountById(userId).getResult();
            FeignResponse feignResponse = cfFinancePauseFeignClient.recoverBySourceType(crowdfundingInfo.getInfoId(),
                    crowdfundingInfo.getId(),
                    CfDrawCashPauseRecordEnum.PauseSourceTypeEnum.SYSTEM_RISK_CASE.getCode(),
                    CfDrawCashPauseRecordEnum.RecordStatusEnum.RECOVER.getCode(),
                    CfOperatingRecordEnum.Role.OPERATOR.getCode(), userAccount.getId(),userAccount.getName(),
                    "cf-admin-案例高风险-处理完成-异常案例 处理材料审核异常案例 设为通过");
            log.info("案例高风险处理完成\tinfoId:{}\tfeignResponse:{}",crowdfundingInfo.getId(), JSON.toJSON(feignResponse));
        }
        if (opResult.isFail()) {
            return ResultUtils.transformOpResult2Response(opResult);
        }
        cfAdminOperationRecordBiz.addOneOperationRecord(infoUuid, userId, OPERATION_HANDLE, "手动通过");
        return ResultUtils.transformOpResult2Response(opResult);
    }

    @ApiOperation("异常案例 处理提现审核异常案例 设为通过")
    @PostMapping("handle-draw-cash-case")
    @RequiresPermission("abnormalCase:handle-draw-cash-case")
    public Response handleDrawCashCase(@RequestParam String infoUuid, @RequestParam int userId) {
        OpResult opResult = riskDelegate.handleDrawCaskRisk(infoUuid);
        if (opResult.isSuccess()) {
            cfAdminOperationRecordBiz.addOneOperationRecord(infoUuid, userId, OPERATION_HANDLE, "手动通过");

            //  恢复风险案例暂停的打款
            CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getCrowdfundingInfoByInfoId(infoUuid);
            AdminUserAccountModel userAccount = seaAccountClientV1.getValidUserAccountById(userId).getResult();
            FeignResponse feignResponse = cfFinancePauseFeignClient.recoverBySourceType(crowdfundingInfo.getInfoId(),
                    crowdfundingInfo.getId(),
                    CfDrawCashPauseRecordEnum.PauseSourceTypeEnum.SYSTEM_RISK_CASE.getCode(),
                    CfDrawCashPauseRecordEnum.RecordStatusEnum.RECOVER.getCode(),
                    CfOperatingRecordEnum.Role.OPERATOR.getCode(), userAccount.getId(),userAccount.getName(),
                    "cf-admin-案例高风险-处理完成-异常案例 处理提现审核异常案例 设为通过");
            log.info("案例高风险处理完成\tinfoId:{}\tfeignResponse:{}",crowdfundingInfo.getId(), JSON.toJSON(feignResponse));
        }
        if (opResult.isFail()) {
            return ResultUtils.transformOpResult2Response(opResult);
        }
        // 自动通过提现审核工单 并公示
        cfAdminOperationRecordBiz.addOneOperationRecord(infoUuid, userId, OPERATION_HANDLE, "手动通过");
        return cfFinanceSeaFeignClient.approveDrawCashSuccess(infoUuid, userId).getData();
    }

    private CfCaseRiskVO transDO2VO(CfCaseRiskDO d) {
        CfCaseRiskVO v = null;
        try {
            if (d == null) {
                return null;
            }
            String infoUuid = d.getInfoUuid();
            CrowdfundingInfo fundingInfo = crowdfundingDelegate.getCrowdfundingInfoByInfoId(infoUuid);
            if (fundingInfo == null) {
                log.error("{}", d);
                return null;
            }

            int caseId = fundingInfo.getId();
            int riskType = d.getRiskType();
            List<Integer> remarkTypeList = getRemarkTypeListByRiskType(riskType);
            RemarkDO lastRemark = crowdfundingOperationDelegate.getLastByCaseIdAndRemarkTypes(caseId, remarkTypeList);
            Timestamp lastModified = new Timestamp(d.getUpdateTime().getTime());
            String operatorName = "";
            String lastRemarkContent = "";
            if (lastRemark != null) {
                int operatorId = Math.toIntExact(lastRemark.getOperatorId());//已检查过
                lastRemarkContent = lastRemark.getContent();
                lastModified = new Timestamp(lastRemark.getCreateTime().getTime());
                AdminUserAccountModel adminUserAccountModel = seaAccountClientV1.getValidUserAccountById(operatorId).getResult();
                if (adminUserAccountModel != null) {
                    operatorName = adminUserAccountModel.getName();
                }
            }

            UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(fundingInfo.getUserId());

            v = new CfCaseRiskVO();

            v.setHandleStatus(d.getHandleStatus());
            v.setLastRemarkContent(lastRemarkContent);

            v.setId(d.getId());
            v.setInfoUuid(d.getInfoUuid());
            v.setRisk(d.getRisk());
            v.setWaitingProcess(getWaitingProcess(d));

            v.setCaseId(fundingInfo.getId());
            v.setMobile(shuidiCipher.decrypt(userInfoModel.getCryptoMobile()));

            v.setOperatorName(operatorName);
            v.setUpdateTime(lastModified);

            v.setTitle(fundingInfo.getTitle());

            HashMap dataMap = promoteRiskData(d, infoUuid, fundingInfo);

            v.setRiskData(dataMap);

        } catch (Exception e) {
            log.error("{}, {}", d, e);
        }

        return v;
    }

    private List<Integer> getRemarkTypeListByRiskType(int riskType) {
        CfCaseRiskTypeEnum riskTypeEnum = CfCaseRiskTypeEnum.parse(riskType);
        if (riskTypeEnum == CfCaseRiskTypeEnum.CASE_INFO_RISK) {
            return Lists.newArrayList(RemarkTypeConsts.CASE_DATA_INFO_RISK,
                    RemarkTypeConsts.CASE_DATA_INFO_RISK_HANDLE);
        }
        if (riskTypeEnum == CfCaseRiskTypeEnum.DRAW_CASH_RISK) {
            return Lists.newArrayList(RemarkTypeConsts.CASE_DATA_DRAW_CASH_RISK,
                    RemarkTypeConsts.CASE_DATA_DRAW_CASH_RISK_HANDLE);
        }
        return Lists.newArrayList();
    }

    private HashMap promoteRiskData(CfCaseRiskDO d, String infoUuid, CrowdfundingInfo fundingInfo) {
        if (StringUtils.isBlank(d.getRiskData())) {
            return Maps.newHashMap();
        }
        HashMap dataMap = JsonUtils.fromJsonMaybeNull(d.getRiskData(), HashMap.class);
        if (dataMap == null) {
            return Maps.newHashMap();
        }

        dataMap.put("raisedAmount", fundingInfo.getAmount());
        dataMap.put("channel", fundingInfo.getChannel());

        CfInfoExt ext = adminCfInfoExtBiz.getByInfoUuid(infoUuid);
        if (ext != null) {
            dataMap.put("ip", ext.getClientIp());
        }
        return dataMap;
    }

    private boolean getWaitingProcess(CfCaseRiskDO d) {
        int verified = d.getVerified();
        if (CfCaseRiskVerifiedEnum.WAITING_VERIFY.getVerified() == verified) {
            log.error("{}", d);
            return false;
        }
        int passed = d.getPassed();
        return passed == CaseRiskPassedEnum.NO.getValue();
    }

    private Response listByPage(int type, Boolean passed, StatRiskHandleStatusEnum statusEnum, int current, int size) {
        if (size > MAX_PAGE_SIZE) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<CfCaseRiskDO> cfCaseRiskDOS = riskDelegate.listByConditionAll(
                CfCaseRiskVerifiedEnum.VERIFIED.getVerified(),
                passed,
                type,
                statusEnum);
        List<CfCaseRiskVO> list = cfCaseRiskDOS
                .stream()
                .map(CfAdminAbnormalCaseController.this::transDO2VO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return wrapListResponse(list, cfCaseRiskDOS);
    }

    private Response wrapListResponse(List list, List pageList) {
        return ResultBuilder.create()
                .put("list", list)
                .put("pagination", PageUtil.transform2PageMap(pageList))
                .buildSuccessResponse();
    }

}
