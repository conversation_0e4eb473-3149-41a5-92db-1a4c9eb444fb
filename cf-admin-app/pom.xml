<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<artifactId>cf-admin-api</artifactId>
	<packaging>jar</packaging>

	<parent>
		<groupId>com.shuidihuzhu.cf</groupId>
		<artifactId>cf-admin-parent</artifactId>
		<version>3.5.526-SNAPSHOT</version>
	</parent>

	<dependencies>

		<dependency>
			<groupId>com.playtika.reactivefeign</groupId>
			<artifactId>feign-reactor-spring-configuration</artifactId>
		</dependency>

		<dependency>
			<groupId>io.netty</groupId>
			<artifactId>netty-all</artifactId>
			<version>4.1.69.Final</version>
		</dependency>
		<dependency>
			<groupId>com.shuidihuzhu.ai-alps</groupId>
			<artifactId>ai-chatgpt-client</artifactId>
			<version>0.0.84</version>
		</dependency>

		<dependency>
			<groupId>com.shuidihuzhu.account</groupId>
			<artifactId>account-relation-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.shuidihuzhu.ai-alps</groupId>
			<artifactId>ai-ocean-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.shuidihuzhu.cf</groupId>
			<artifactId>cf-admin-biz</artifactId>
		</dependency>
		<dependency>
			<groupId>org.jolokia</groupId>
			<artifactId>jolokia-core</artifactId>
		</dependency>
		<dependency>
			<groupId>de.codecentric</groupId>
			<artifactId>spring-boot-admin-starter-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.shuidihuzhu.frame</groupId>
			<artifactId>frame-client</artifactId>
		</dependency>

		<dependency>
            <groupId>io.shardingjdbc</groupId>
            <artifactId>sharding-jdbc-core</artifactId>
            <version>2.0.3</version>
        </dependency>

		<dependency>
			<groupId>com.shuidihuzhu.cf</groupId>
			<artifactId>cf-client</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.shuidihuzhu.common</groupId>
					<artifactId>web-model</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.shuidihuzhu.cf</groupId>
			<artifactId>cf-finance-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.shuidihuzhu.cf</groupId>
			<artifactId>cf-finance-feign-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.shuidihuzhu.eb</groupId>
			<artifactId>schedule-mq-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
		</dependency>
		<dependency>
			<groupId>com.shuidihuzhu.cf</groupId>
			<artifactId>cf-activity-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.shuidihuzhu.pf</groupId>
			<artifactId>pf-rpc-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.shuidihuzhu.pf</groupId>
			<artifactId>pf-common-v2</artifactId>
		</dependency>
		<dependency>
			<groupId>com.shuidihuzhu.frame</groupId>
			<artifactId>charity-rpc-client</artifactId>
		</dependency>


		<dependency>
			<groupId>com.shuidihuzhu.cf</groupId>
			<artifactId>cf-common</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>xstream</artifactId>
					<groupId>com.thoughtworks.xstream</groupId>
				</exclusion>
				<exclusion>
					<artifactId>activation</artifactId>
					<groupId>javax.activation</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.shuidihuzhu.ai-algo</groupId>
			<artifactId>ai-service-client</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.shuidihuzhu.common</groupId>
					<artifactId>web-app</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- 通用权限拦截器 -->
		<dependency>
			<groupId>com.shuidihuzhu.cf</groupId>
			<artifactId>shuidi-security-plugin</artifactId>
			<!--<version>0.0.5-SNAPSHOT</version>-->
			<!--<exclusions>-->
				<!--<exclusion>-->
					<!--<groupId>com.shuidihuzhu.auth</groupId>-->
					<!--<artifactId>sea-auth-client</artifactId>-->
				<!--</exclusion>-->
			<!--</exclusions>-->
		</dependency>

		<dependency>
			<groupId>com.shuidihuzhu.cf</groupId>
			<artifactId>cf-patient-recruit-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.shuidihuzhu.common</groupId>
			<artifactId>cf-common-jdbc</artifactId>
			<version>2.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.shuidihuzhu.ai-alps</groupId>
			<artifactId>ai-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.shuidihuzhu.cf</groupId>
			<artifactId>cf-alarm-center-client</artifactId>
			<version>8.0.3</version>
		</dependency>
        <dependency>
            <groupId>com.shuidihuzhu.op</groupId>
            <artifactId>shuidi-auth-saas-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-admin-model</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>xstream</artifactId>
					<groupId>com.thoughtworks.xstream</groupId>
				</exclusion>
			</exclusions>
		</dependency>

    </dependencies>


	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>

			</plugin>
			<plugin>
				<groupId>com.shuidihuzhu.plugin</groupId>
				<artifactId>checkclass-maven-plugin</artifactId>
				<version>1.0</version>
				<configuration>
					<!--指定扫描的路径，插件扫描该路径下的.class和.jar文件-->
					<source>cf-admin-app/target/cf-admin-api-2.0.0-SNAPSHOT.jar</source>

					<!--如果插件扫描到重复类，mvn package命令会失败。-->
					<!--此时如果想强行通过打包，需要在exclusion里面配置想要暂时排除的jar包-->
					<exclusion>
						<param>xpp3_min-1.1.4c.jar</param>
						<param>xmlpull-1.1.3.1.jar</param>
						<param>weixin-java-pay-2.7.0.jar</param>
					</exclusion>

					<!--package名称扫描功能会扫描所有包含com.shuidi的jar包-->
					<!--如果想要扫描其他的jar包，可以再include里面指定-->
					<!--为include添加 param子节点即可 -->
					<include>
					</include>

					<!--指定了-Ponline时，当发现工程依赖了 SNPASHOT 包，package会失败-->
					<!--如果想让打包成功，可以配置 ignoreSnapShot为false-->
					<ignoreSnapshot>true</ignoreSnapshot>

					<!--默认的线上release的profile是 online，如果想修改此配置-->
					<!--可以在onlineProfile节点里面设置-->
					<onlineProfile>online</onlineProfile>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>check-same-class</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>1.4.1</version>
                <executions>
                    <execution>
                        <id>enforce</id>
                        <configuration>
                            <rules>
                                <!--强制项目没有重复声明的依赖项-->
                                <banDuplicatePomDependencyVersions />
                                <!--确保所有jar依赖项是同一版本-->
                                <!--<dependencyConvergence />-->
                                <executorServiceRule implementation="com.shuidihuzhu.customrule.ExecutorServiceRule">
                                    <exclude>
                                        CfTaskController.java,AdminCrowdfundingAttachmentBizImpl.java,DrawCashShutdown.java,CfAdminDrawCashApproveService.java,AdminDingdingBizImpl.java,CfScheduleAlarmService.java
                                    </exclude>
                                    <excludePath>/cf-admin-app/src/main/java</excludePath>
                                </executorServiceRule>
                                <valueRule implementation="com.shuidihuzhu.customrule.ValueRule">
									<exclude>AsyncPoolConfiguration.java,AsyncThreadPoolConfiguration.java,AdminCrowdfundingCommentBizImpl.java</exclude>
                                    <excludePath>/cf-admin-app/src/main/java</excludePath>
                                </valueRule>
                                <loggerRule implementation="com.shuidihuzhu.customrule.LoggerRule">
                                    <excludePath>/cf-admin-app/src/main/java</excludePath>
									<exclude>
										PhotoUtil.java
									</exclude>
                                </loggerRule>
								<loggerRule implementation="com.shuidihuzhu.customrule.LombokRule">
									<excludePath>/cf-admin-app/src/main/java</excludePath>
								</loggerRule>
								<cipherRule implementation="com.shuidihuzhu.rule.CipherRule">
									<exclude>
										UserInfoController.java
									</exclude>
								</cipherRule>
                            </rules>
                        </configuration>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>enforce-ban-duplicate-classes</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <!--针对jar检测不出来的class-->
                                <banDuplicateClasses>
                                    <ignoreClasses>
                                        <ignoreClass>javax.*</ignoreClass>
                                        <ignoreClass>org.junit.*</ignoreClass>
                                        <ingoreClass>org.aspectj.*</ingoreClass>
                                        <ingoreClass>org.apache.juli.*</ingoreClass>
                                        <ingoreClass>org.apache.commons.logging.*</ingoreClass>
                                        <ingoreClass>org.apache.log4j.*</ingoreClass>
                                        <ingoreClass>org.apache.tomcat.*</ingoreClass>
                                        <ingoreClass>io.netty.*</ingoreClass>
                                        <ingoreClass>net.jcip.annotations.*</ingoreClass>
                                        <ingoreClass>aj.org.objectweb.asm.*</ingoreClass>
                                        <ingoreClass>org.xmlpull.v1.*</ingoreClass>
                                        <ingoreClass>org.hibernate.validator.*</ingoreClass>
                                        <ingoreClass>org.*</ingoreClass>
                                        <ingoreClass>groovyjarjarasm.*</ingoreClass>
                                        <ingoreClass>groovyjarjarantlr.*</ingoreClass>
                                        <ingoreClass>groovyjarjarcommonscli.*</ingoreClass>
                                        <ingoreClass>groovy.*</ingoreClass>
                                        <ingoreClass>com.shuidihuzhu.cf.enums.visitconfig.*</ingoreClass>
										<ingoreClass>com.zaxxer.*</ingoreClass>
										<ingoreClass>com.dangdang.ddframe.job.event.rdb.*</ingoreClass>
										<ingoreClass>com.shuidihuzhu.msg.*</ingoreClass>
										<ingoreClass>com.shuidihuzhu.alps.feign.demo.*</ingoreClass>
										<ingoreClass>com.netflix.loadbalancer.*</ingoreClass>


                                    </ignoreClasses>
                                    <findAllDuplicates>true</findAllDuplicates>
                                </banDuplicateClasses>
                            </rules>
                            <fail>true</fail>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>extra-enforcer-rules</artifactId>
                        <version>1.0</version>
                    </dependency>
                    <dependency>
                        <groupId>com.shuidihuzhu.cf</groupId>
                        <artifactId>custom-rule</artifactId>
                        <version>1.0.23</version>
                    </dependency>
                </dependencies>
            </plugin>
			<plugin>
				<groupId>com.spotify</groupId>
				<artifactId>docker-maven-plugin</artifactId>
				<dependencies>
					<dependency>
						<groupId>javax.activation</groupId>
						<artifactId>activation</artifactId>
						<version>1.1.1</version>
					</dependency>
				</dependencies>
			</plugin>
		</plugins>
	</build>

</project>
