<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>cf-admin-biz</artifactId>
    <packaging>jar</packaging>

    <parent>
        <groupId>com.shuidihuzhu.cf</groupId>
        <artifactId>cf-admin-parent</artifactId>
        <version>3.5.526-SNAPSHOT</version>
    </parent>

    <dependencies>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-lion-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.ai-alps</groupId>
            <artifactId>ai-chatgpt-client</artifactId>
            <version>0.0.84</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>4.1.69.Final</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-spring-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.ai-alps</groupId>
            <artifactId>ai-ocean-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.ai-alps</groupId>
            <artifactId>ai-venus-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-store</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.data</groupId>
            <artifactId>servicelog-meta-cf</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.op</groupId>
            <artifactId>shuidi-auth-saas-client</artifactId>
        </dependency>

        <!--<dependency>-->
            <!--<groupId>com.shuidihuzhu.auth</groupId>-->
            <!--<artifactId>sea-auth-client</artifactId>-->
            <!--<exclusions>-->
                <!--<exclusion>-->
                    <!--<groupId>com.shuidihuzhu.common</groupId>-->
                    <!--<artifactId>web-app</artifactId>-->
                <!--</exclusion>-->
            <!--</exclusions>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.shuidihuzhu.infra</groupId>
            <artifactId>mail-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot</artifactId>
        </dependency>


        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-api-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.shuidihuzhu.common</groupId>
                    <artifactId>web-model</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuidihuzhu.common</groupId>
                    <artifactId>web-util</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>shuidi-cipher</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>3.17</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.shuidihuzhu.common</groupId>-->
<!--            <artifactId>miniapp</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.shuidihuzhu.baseservice</groupId>
            <artifactId>baseservice-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cs</groupId>
            <artifactId>cs-work-order-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baidu.aip</groupId>
            <artifactId>java-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
        </dependency>
        <dependency>
            <groupId>me.xdrop</groupId>
            <artifactId>fuzzywuzzy</artifactId>
        </dependency>

        <!--阿里云 oss-sdk-->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>

        <!--阿里云 java-core-jdk-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
        </dependency>

        <!--阿里云 cdn-jdk-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-cdn</artifactId>
        </dependency>


        <dependency>
            <groupId>com.shuidihuzhu.msg</groupId>
            <artifactId>msg-rpc-client</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.shuidihuzhu.infra</groupId>-->
<!--            <artifactId>graceful-shutdown-spring-boot-starter</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.shuidihuzhu.eb</groupId>
            <artifactId>eb-grafana-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-websocket</artifactId>
            <version>5.1.13.RELEASE</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.dataservice</groupId>
            <artifactId>dataservice-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-finance-feign-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.skyscreamer</groupId>
            <artifactId>jsonassert</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.frame</groupId>
            <artifactId>frame-client</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.data</groupId>
            <artifactId>servicelog-sdk-java</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.zookeeper</groupId>
                    <artifactId>zookeeper</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.hz</groupId>
            <artifactId>huzhu-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.eb</groupId>
            <artifactId>schedule-mq-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-admin-dal</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-material-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-event-center-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-ugc-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf-risk</groupId>
            <artifactId>cf-risk-rpc-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.algo</groupId>
            <artifactId>medicare-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-stat-client</artifactId>
        </dependency>
	    
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>markdown2image-plugin</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.frame</groupId>
            <artifactId>charity-rpc-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-api-pure-client</artifactId>
        </dependency>
	
	    <dependency>
		    <groupId>com.shuidihuzhu.cf</groupId>
		    <artifactId>cf-finance-client</artifactId>
	    </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.sdb</groupId>
            <artifactId>sdb-order-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-admin-api-pure-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.devops</groupId>
            <artifactId>cloud-app-activity-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.ai-alps</groupId>
            <artifactId>ai-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-data-platform-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
        </dependency>

    </dependencies>

</project>
