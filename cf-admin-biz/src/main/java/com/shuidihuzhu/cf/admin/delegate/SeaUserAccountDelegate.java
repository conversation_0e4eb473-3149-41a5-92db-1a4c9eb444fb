package com.shuidihuzhu.cf.admin.delegate;

import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-04-30  14:51
 * instead {@link com.shuidihuzhu.cf.delegate.SeaAccountDelegate}
 */
@Deprecated
public interface SeaUserAccountDelegate {

    /**
     *
     * @param operatorIds
     * @return key: operatorId, value: operatorName
     */
    Map<Integer, String> getByOperators(List<Integer> operatorIds);

    Map<Integer, AdminUserAccountModel> getUserByOperators(List<Integer> operatorIds);

    List<AdminUserAccountModel> getUserAccountModelByIds(List<Integer> userIds);

    //获取sea后台的组织信息 “水滴筹-研发-XXX”,如果存在多个只取一个
    String getOrg(int adminUserId);
}
