package com.shuidihuzhu.cf.admin.delegate.impl;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.admin.delegate.SeaUserAccountDelegate;
import com.shuidihuzhu.cf.biz.admin.AdminOrganizationBiz;
import com.shuidihuzhu.cf.model.admin.AdminOrganization;
import com.shuidihuzhu.cf.model.admin.AdminOrganizationUserMap;
import org.jetbrains.annotations.Nullable;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-04-30  14:51
 */
@Service
public class SeaUserAccountDelegateImpl implements SeaUserAccountDelegate {

    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    private AdminOrganizationBiz organizationBiz;

    @Override
    public Map<Integer, String> getByOperators(List<Integer> operatorIds) {
        List<AdminUserAccountModel> users = getListAuthRpcResponse(operatorIds);
        if (users == null) {
            return Maps.newHashMap();
        }
        return users.stream()
                .collect(Collectors.toMap(AdminUserAccountModel::getId,
                        AdminUserAccountModel::getName, (x, y) -> x));
    }

    @Override
    public Map<Integer, AdminUserAccountModel> getUserByOperators(List<Integer> operatorIds) {
        List<AdminUserAccountModel> users = getListAuthRpcResponse(operatorIds);
        if (users == null) {
            return Maps.newHashMap();
        }
        return users.stream()
                .collect(Collectors.toMap(AdminUserAccountModel::getId,
                        v -> v, (x, y) -> x));
    }

    @Nullable
    private List<AdminUserAccountModel> getListAuthRpcResponse(List<Integer> operatorIds) {
        AuthRpcResponse<List<AdminUserAccountModel>> rpcResponse = seaAccountClientV1.getUserAccountsByIds(operatorIds);
        if (rpcResponse == null) {
            return null;
        }
        return rpcResponse.getResult();
    }

    @Override
    public List<AdminUserAccountModel> getUserAccountModelByIds(List<Integer> userIds) {
        AuthRpcResponse<List<AdminUserAccountModel>> rpcResponse = seaAccountClientV1.getUserAccountsByIds(userIds);
        if (rpcResponse == null || CollectionUtils.isEmpty(rpcResponse.getResult())) {
            return Lists.newArrayList();
        }
        return rpcResponse.getResult();
    }

    @Override
    public String getOrg(int adminUserId) {
        String org = "";
        List<AdminOrganizationUserMap> lowestOrgByUserIds = organizationBiz.getLowestOrgByUserIds(Lists.newArrayList(adminUserId));
        Integer orgId = lowestOrgByUserIds.stream().map(AdminOrganizationUserMap::getOrgId).findFirst().orElse(null);
        if (orgId == null) {
            return org;
        }
        List<AdminOrganization> organizationList = Lists.newArrayList();
        organizationBiz.getOrgByLowestId(orgId, organizationList);
        List<String> orgNameList = organizationList.stream().map(AdminOrganization::getName).collect(Collectors.toList());
        Collections.reverse(orgNameList);
        org = Joiner.on("-").join(orgNameList);
        return org;
    }
}
