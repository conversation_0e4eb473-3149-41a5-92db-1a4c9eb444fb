package com.shuidihuzhu.cf.configuration;

import brave.Tracing;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.shuidihuzhu.cf.enhancer.subject.threadpool.annotation.Dynamic;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

import static com.shuidihuzhu.cf.admin.constant.AsyncPoolConstants.APPROVE_DETAIL_ASYNC_POOL;
import static com.shuidihuzhu.cf.admin.constant.AsyncPoolConstants.CF_SEARCH_EXECUTOR;

@Configuration
@Slf4j
public class AsyncThreadPoolConfiguration {

    @Autowired
    private Tracing tracing;

    @Bean("materialVerityHistoryExecutor")
    @Dynamic
    public Executor materialVerityHistoryExecutor() {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                5, 5, 1, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(), new ThreadFactoryBuilder().setNameFormat("material-verity-history-pool").build());
        return tracing.currentTraceContext().executorService(executor);
    }

    @Bean("crmUserManageRoleExecutor")
    @Dynamic
    public Executor crmUserManageRoleExecutor() {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                5, 5, 1, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(), new ThreadFactoryBuilder().setNameFormat("crm-user-manage-update-role-pool").build());
        return tracing.currentTraceContext().executorService(executor);
    }

    @Bean("adminLoginRecordExecutor")
    @Dynamic
    public Executor adminLoginRecordExecutor() {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                2, 2, 1, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(200), new ThreadFactoryBuilder().setNameFormat("admin-login-record-pool").build());
        return tracing.currentTraceContext().executorService(executor);
    }

    @Bean("flowOrderAutoAssignExecutor")
    @Dynamic
    public Executor flowOrderAutoAssignExecutor() {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                5, 5, 1, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(), new ThreadFactoryBuilder().setNameFormat("flow-order-auto-assign-log-pool").build());
        return tracing.currentTraceContext().executorService(executor);
    }


    @Bean(CF_SEARCH_EXECUTOR)
    @Dynamic
    public Executor cfSearchExecutor(@Value("${threadPool.cfSearchExecutor.corePoolSize:10}") int corePoolSize,
                                     @Value("${threadPool.cfSearchExecutor.maximumPoolSize:40}") int maximumPoolSize) {
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("cfSearchExecutor-%d").build();
        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 60,
                TimeUnit.SECONDS, new ArrayBlockingQueue<>(10), threadFactory, new ThreadPoolExecutor.AbortPolicy() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                log.error("The cfSearchExecutor is rejectedExecution");
                super.rejectedExecution(r, e);
            }
        });
        return tracing.currentTraceContext().executorService(poolExecutor);
    }

    @Bean(APPROVE_DETAIL_ASYNC_POOL)
    @Dynamic
    public Executor approveDetailAsyncPool() {
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("APPROVE_DETAIL_ASYNC_POOL-%d").build();
        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(20, 30, 60,
                TimeUnit.SECONDS, new ArrayBlockingQueue<>(2), threadFactory, new ThreadPoolExecutor.AbortPolicy() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                log.error("The approveDetailAsyncPool is rejectedExecution running in current thread");
                super.rejectedExecution(r, e);
            }
        });
        return tracing.currentTraceContext().executorService(poolExecutor);
    }

    @Bean("asyncSaveSnapshot")
    @Dynamic
    public Executor asyncSaveSnapshot(){
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("asyncSaveSnapshot" + "-%d").build();
        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(2, 10, 600,
                TimeUnit.SECONDS, new ArrayBlockingQueue<>(3000), threadFactory, new ThreadPoolExecutor.AbortPolicy() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                log.error("The asyncSaveSnapshot is rejectedExecution");
                super.rejectedExecution(r, e);
            }
        });
        poolExecutor.allowCoreThreadTimeOut(true);
        return tracing.currentTraceContext().executorService(poolExecutor);
    }
}
