package com.shuidihuzhu.cf.facade;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.OrganizationClientV1;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderBiz;
import com.shuidihuzhu.cf.biz.admin.common.OperationHistorySummaryBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.client.subject.caseend.CaseEndClient;
import com.shuidihuzhu.cf.delegate.AnalyticsDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.finance.IFinanceDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.BackgroundLogEnum;
import com.shuidihuzhu.cf.enums.admin.common.OperationType;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.finance.model.financestate.FinanceState;
import com.shuidihuzhu.cf.finance.model.financestate.FinanceStateEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.mq.IAdminCommonMessageHelperService;
import com.shuidihuzhu.cf.service.FinanceApproveService;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.StopFundraisingReasons;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2018-08-28  20:40
 */
@Service
@Slf4j
public class AdminApproveFacade {

    private static final Logger LOGGER = LoggerFactory.getLogger(AdminApproveFacade.class);


    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;
    @Autowired
    private OperationHistorySummaryBiz operationHistorySummaryBiz;
    @Autowired
    private CfAdminOperationRecordBiz cfAdminOperationRecordBiz;
    @Autowired
    private AdminCfInfoExtBiz adminCfInfoExtBiz;
    @Autowired
    private AdminApproveService adminApproveService;
    @Autowired
    private FinanceApproveService financeApproveService;
    @Autowired
    private AdminWorkOrderBiz adminWorkOrderBiz;
    @Autowired
    private AdminWorkOrderCaseBiz adminWorkOrderCaseBiz;
    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Autowired
    private AdminCfRepeatHandleBiz repeatHandleService;
    @Autowired
    private CaseEndClient caseEndClient;

    @Resource
    private OrganizationClientV1 organizationClientV1;

    @Autowired
    private AnalyticsDelegate analyticsDelegate;
    @Autowired
    private Analytics analytics;
    @Autowired
    private IAdminCommonMessageHelperService adminCommonMessageHelperService;
    @Autowired
    private IFinanceDelegate financeDelegate;


    public Response stopCase(Integer caseId, Integer userId, String tag, int reasonId, String commentText, String operationRemark) {
        if (null == caseId) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = this.crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeResponse(1, "无要修改的数据", null);
        }
        AdminUserAccountModel userAccount = this.seaAccountClientV1.getValidUserAccountById(userId).getResult();
        String infoId = crowdfundingInfo.getInfoId();
        CfOperatingRecord cfOperatingRecord = this.crowdfundingDelegate.before(crowdfundingInfo, userId,
                userAccount != null ? userAccount.getName() : "", CfOperatingRecordEnum.Type.PAUSE_CF, CfOperatingRecordEnum.Role.OPERATOR);

        try {
            LOGGER.info("updateOperationStatus infoId:{};userId:{};commentText:{}", infoId, userId, commentText);
            adminApproveService.updateOperationStatus(infoId, CrowdfundingOperationEnum.OPERATED, commentText, userId);
        } catch (Exception e) {
            LOGGER.error("updateOperationStatus Error!", e);
        }

        String fullComment = StringUtils.isBlank(operationRemark) ? commentText : StringUtils.join(commentText, "\n", "操作备注: ", operationRemark);
        financeApproveService.addApprove(crowdfundingInfo, tag, fullComment, userId);
        CfFinishStatus endType = CfFinishStatus.FINISH_BY_SHUIDI;
        caseEndClient.stopCase(caseId, endType, userId, tag + ":" + commentText);
        //结束案例的用户添加标签
        crowdfundingDelegate.addCrowdfundingInfoTag(crowdfundingInfo, CfFinishStatus.FINISH_BY_SHUIDI);

        operationHistorySummaryBiz.addOperationHistorySummary(OperationType.CROWDFUNDING_PAUSE, userId,
                "终止筹款：infoId=" + infoId);

        this.crowdfundingDelegate.afterCfOperatingRecord(cfOperatingRecord);
        // 操作记录入库
        cfAdminOperationRecordBiz.addOneOperationRecord(infoId, userId, CfOperationRecordEnum.PAUSE.value(), commentText);
        LOGGER.info(
                "客服后台log：doApprove-pause operationTime:{};operator:{};operationReason:{};operationType:{};infoId:{};status:{};dataStatus:{};infoStatus:{}",
                com.shuidihuzhu.common.web.util.DateUtil.formatDateTime(new Date()), userId, BackgroundLogEnum.OVER.getMessage(),
                BackgroundLogEnum.OVER.getMessage(), infoId, crowdfundingInfo.getStatus(),
                crowdfundingInfo.getDataStatus(), crowdfundingInfo.getEndTime().before(new Date()) ? "结束" : "未结束");

        adminWorkOrderCaseBiz.onCaseEnd(caseId);

        StopFundraisingReasons sfr = new StopFundraisingReasons();
        try {
            sfr.setStop_reason(commentText);
            sfr.setOperate_time(DateUtil.getCurrentDateTimeStr());
            sfr.setCase_id(StringUtils.trimToEmpty(crowdfundingInfo.getInfoId()));
            sfr.setInfo_id(Long.valueOf(crowdfundingInfo.getId()));
            sfr.setStop_reason_id(Long.valueOf(reasonId));
            sfr.setOperator_id(Optional.ofNullable(userId).map(Long::valueOf).orElse(0l));
            sfr.setOperator_name(getOperatorName(userId));
            sfr.setUser_tag(String.valueOf(userId));
            sfr.setUser_tag_type(UserTagTypeEnum.userid);

            analytics.track(sfr);
            log.info("大数据打点上报,停止筹款原因:{}", JSONObject.toJSONString(sfr));
        } catch (Exception e) {
            log.error("大数据打点上报异常,停止筹款原因，异常:{}", JSONObject.toJSONString(sfr), e);
        }

        repeatHandleService.recordFinishCase(0, caseId, endType, userId);
        return NewResponseUtil.makeSuccess(crowdfundingInfo);
    }

    private String getOperatorName(int operatorId) {
        List<AdminUserAccountModel> list = seaAccountClientV1.getUserAccountsByIds(Lists.newArrayList(operatorId)).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        AdminUserAccountModel model = list.get(0);
        String organization = getOrganization(operatorId);
        return organization + "-" + model.getName();
    }

    /**
     * 获取组织快照
     *
     * @param operatorId
     * @return
     */
    private String getOrganization(Integer operatorId) {
        String org = "";
        if (operatorId == null || operatorId <= 0) {
            return org;
        }

        org = organizationClientV1.getUserRelationOrgName(operatorId).getResult();
        if (StringUtils.isEmpty(org)) {
            return "";
        }

        return org;
    }

    public boolean ifCaseCanRecover(CfInfoExt ext) {

        if (ext == null) {
            return false;
        }

        int finishStatus = ext.getFinishStatus();
        if (finishStatus != CfFinishStatus.FINISH_BY_RAISER.getValue() && finishStatus != CfFinishStatus.FINISH_BY_SHUIDI.getValue()
                && finishStatus != CfFinishStatus.FINISH_BY_SYSTEM.getValue()) {
            return false;
        }

        return adminApproveService.isFundInitialized(ext.getCaseId());
    }

    public boolean ifCaseCanSpecialRecover(CfInfoExt ext) {
        if (Objects.isNull(ext)) {
            return false;
        }
        int finishStatus = ext.getFinishStatus();
        if (finishStatus != CfFinishStatus.FINISH_BY_RAISER.getValue()
                && finishStatus != CfFinishStatus.FINISH_BY_SHUIDI.getValue()
                && finishStatus != CfFinishStatus.FINISH_BY_SYSTEM.getValue()) {
            return false;
        }

        FinanceState financeState = financeDelegate.getFinanceState(ext.getCaseId());
        return financeState != null && financeState.getFinanceStatus() == FinanceStateEnum.FinanceStatusEnum.IN_DRAW_CASH;
    }

    public void recoverCase(CrowdfundingInfo info, CfInfoExt ext, int userId) {
        Date beginTime = null;
        if (ext.getFirstApproveStatus() == FirstApproveStatusEnum.APPLY_SUCCESS.getCode()) {
            beginTime = ext.getFirstApproveTime();
        } else {
            beginTime = ext.getDateCreated();
        }

        long mills = 0;
        if (info.getEndTime() != null) {
            mills = (new Date()).getTime() - info.getEndTime().getTime();
        }

        Date endTime = DateUtils.addDays(beginTime, 30);
        if (mills > 0) {
            endTime = DateUtils.addSeconds(endTime, (int) (mills / 1000));
        }

        crowdfundingInfoBiz.updateEndTime(info.getId(), endTime);
        adminCfInfoExtBiz.updateFinishStatus(ext.getInfoUuid(), CfFinishStatus.NOT_FINISH.getValue());
        // 发 案例恢复筹款 mq   tags：CF_RECOVER_CASE
        info.setEndTime(endTime);
        adminCommonMessageHelperService.send(adminCommonMessageHelperService.getCfRecoverCaseMsg(info));
        log.info("重置案例的初始化状态 caseId:{} userId:{} endTime:{}", ext.getCaseId(), userId, endTime);
    }
}
