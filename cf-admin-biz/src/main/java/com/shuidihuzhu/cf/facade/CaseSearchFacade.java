package com.shuidihuzhu.cf.facade;

import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.admin.util.AdminDateUtil;
import com.shuidihuzhu.cf.admin.util.BeanTrimUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoSearchBiz;
import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.SeaTransformCaseInfoVO;
import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.SeaTransformCaseSearchParam;
import com.shuidihuzhu.cf.client.base.page.v1.model.PaginationListVO;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.PreposeMaterialClient;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialSimpleModel;
import com.shuidihuzhu.cf.delegate.finance.IFinanceDelegate;
import com.shuidihuzhu.cf.model.PaginatedList;
import com.shuidihuzhu.cf.model.crowdfunding.CfCapitalAccount;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminCrowdfundingInfoView;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CaseListResponse;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CaseSearchVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoVo;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.ReportRelation;
import com.shuidihuzhu.client.cf.search.model.enums.ReportTypeEnum;
import com.shuidihuzhu.common.web.constants.CrowdfundingCons;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RefreshScope
@Slf4j
@Service
public class CaseSearchFacade {

    @Autowired
    private AdminApproveService adminApproveService;

    @Autowired
    private AdminCfInfoSearchBiz adminCfInfoSearchBiz;

    @Value("${diff.approve-search:true}")
    private boolean approveSearchDiff;

    @Value("${diff.alternate-search:false}")
    private boolean alternateSearchDiff;

    @Autowired
    private IFinanceDelegate financeDelegate;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Resource
    private ClewPreproseMaterialFeignClient clewPreproseMaterialFeignClient;

    public Response<CaseListResponse> approveSearch(CaseSearchVo caseSearchVo) {
        log.info("approveSearch search param {}", caseSearchVo);

        BeanTrimUtil.trimBean(caseSearchVo);

        String startTime = caseSearchVo.getStartTime();
        String endTime = caseSearchVo.getEndTime();

        if (AdminDateUtil.isEndTimeExtendToday(endTime)) {
            return NewResponseUtil.makeFail("结束时间不能晚于今天");
        }

        //如果不允许查询所有的数据，那么限制数据日期范围
        if (!allowQueryAll(caseSearchVo)) {
            if (StringUtils.isAllEmpty(startTime, endTime)) {
                Date now = new Date();
                caseSearchVo.setStartTime(com.shuidihuzhu.common.util.DateUtil.getDate2LStr(DateUtils.addDays(now, -7)));
                caseSearchVo.setEndTime(com.shuidihuzhu.common.util.DateUtil.getDate2LStr(now));
                log.info("案例查询查询设置默认的时间 start:{}, end:{}", startTime, endTime);
            } else if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
                if (AdminDateUtil.isTimeExtendLimit(startTime, endTime, 7)) {
                    return NewResponseUtil.makeFail("时间范围超过一周,请重新选择");
                }
            } else {
                return NewResponseUtil.makeFail("时间范围超过一周,请重新选择");
            }
        }

        //校验reportType，如果有两个值时，必须选择内部举报和外部举报
        List<Integer> reportTypes = caseSearchVo.getReportType();
        if (CollectionUtils.isNotEmpty(reportTypes)) {
            if (reportTypes.size() > 2) {
                return NewResponseUtil.makeFail("举报类型值不能多于2项");
            }
            if (reportTypes.size() == 2) {
                HashSet<Integer> reportDeRepeatSet = new HashSet<>(reportTypes);
                if (reportDeRepeatSet.size() < 2) {
                    return NewResponseUtil.makeFail("举报类型值不能相同");
                }
                for (Integer reportType : reportTypes) {
                    if (reportType != ReportTypeEnum.INTERNAL_REPORT.getCode() && reportType != ReportTypeEnum.EXTERNAL_REPORT.getCode()) {
                        return NewResponseUtil.makeFail("举报类型多指必须是外部举报和内部举报");
                    }
                }
            }
        }

        return NewResponseUtil.makeSuccess(doApproveSearch2(caseSearchVo));
    }


    private CaseListResponse doApproveSearch2(CaseSearchVo caseSearchVo) {
        CaseListResponse view = new CaseListResponse();
        Integer pageSize = caseSearchVo.getPageSize();
        if (pageSize == null || pageSize < CrowdfundingCons.MIN_PAGE_SIZE || pageSize > CrowdfundingCons.MAX_PAGE_SIZE) {
            return view;
        }
        long currentTime = System.currentTimeMillis();

        List<CrowdfundingInfoVo> crowdfundingInfoVoList = Lists.newArrayList();
        String reportMobile = caseSearchVo.getReportMobile();
        if(StringUtils.isNotBlank(reportMobile)){
            caseSearchVo.setReportMobile(oldShuidiCipher.aesEncrypt(reportMobile));
        }

        if (StringUtils.isNotBlank(caseSearchVo.getPreposeRaiseMobile())) {
            // 根据代录入发起手机号限制案例
            limitCaseByPreposeMobile(caseSearchVo);
            if (CollectionUtils.isEmpty(caseSearchVo.getExtCaseIds()) && StringUtils.isNotBlank(caseSearchVo.getPreposeRaiseMobile())) {
                PaginationListVO.PaginationProps paginationProps = new PaginationListVO.PaginationProps();
                paginationProps.setTotal(0);
                paginationProps.setCurrent(1);
                paginationProps.setPageSize(10);
                view.setPagination(paginationProps);
                view.setData(Lists.newArrayList());
                return view;
            }
        }

        if (alternateSearchDiff) {
            crowdfundingInfoVoList = adminCfInfoSearchBiz.reserveApproveSearch(caseSearchVo.getMobile(),
                    caseSearchVo.getPatientName(), caseSearchVo.getId(), caseSearchVo.getTitle(),
                    caseSearchVo.getCurrent(), pageSize);
            long total = getTotalFromPage(crowdfundingInfoVoList);
            PaginationListVO.PaginationProps paginationProps = new PaginationListVO.PaginationProps();
            paginationProps.setCurrent(caseSearchVo.getCurrent());
            paginationProps.setPageSize(caseSearchVo.getPageSize());
            paginationProps.setTotal(total);
            view.setPagination(paginationProps);
        } else if (approveSearchDiff) {
            Pair<Long, List<CrowdfundingInfoVo>> pair = adminCfInfoSearchBiz.caseSearchByEs(caseSearchVo);
            crowdfundingInfoVoList = pair.getRight();

            PaginationListVO.PaginationProps paginationProps = new PaginationListVO.PaginationProps();
            paginationProps.setTotal(pair.getLeft());
            paginationProps.setCurrent((caseSearchVo.getCurrent() == null || caseSearchVo.getCurrent() < 1) ? 1 : caseSearchVo.getCurrent());
            paginationProps.setPageSize(pageSize);
            view.setPagination(paginationProps);
        }

        log.info("crowdfundingInfoVoList size:{}", crowdfundingInfoVoList.size());
        long getCfInfoCost = System.currentTimeMillis() - currentTime;

        //获取自资金账户信息
        List<String> infoUuids = crowdfundingInfoVoList.stream().map(CrowdfundingInfoVo::getInfoId)
                .collect(Collectors.toList());
        Response<Map<String, CfCapitalAccount>> cfCapitalAccountMapResponse =
                financeDelegate.getCfCapitalAccountMapByInfoUuids(infoUuids);

        long capitalAccountGetMapCost = System.currentTimeMillis() - currentTime - getCfInfoCost;
        List<AdminCrowdfundingInfoView> crowdfundingInfoViewList = this.adminApproveService.
                getCrowdfundingInfoViewList(crowdfundingInfoVoList, cfCapitalAccountMapResponse.getData());

        long batchGetViewCost = System.currentTimeMillis() - currentTime - getCfInfoCost - capitalAccountGetMapCost;

        log.info("doApproveSearch2 getCfInfoCost:{} capitalAccountGetMapCost:{} batchGetViewCost:{}", getCfInfoCost, capitalAccountGetMapCost, batchGetViewCost);

        view.setData(crowdfundingInfoViewList);
        return view;
    }

    private void limitCaseByPreposeMobile(CaseSearchVo caseSearchVo) {

        String preposeRaiseMobile = caseSearchVo.getPreposeRaiseMobile();
        if (StringUtils.isBlank(preposeRaiseMobile)) {
            return;
        }

        // 根据代录入发起手机号查询案例id
        Response<List<ReportRelation>> resp = clewPreproseMaterialFeignClient.getPreposeByRaiseMobile(preposeRaiseMobile);
        List<ReportRelation> reportRelationList = Optional.ofNullable(resp)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Lists.newArrayList());
        if (CollectionUtils.isEmpty(reportRelationList) || reportRelationList.size() == 0) {
            return;
        }

        List<Integer> caseIds = reportRelationList.stream()
                .filter(f -> f.getCaseId() != 0)
                .map(ReportRelation::getCaseId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(caseIds) || caseIds.size() == 0) {
            return;
        }

        caseSearchVo.setExtCaseIds(caseIds);

    }

    public OperationResult<PaginationListVO<SeaTransformCaseInfoVO>> getCaseInfoListForTransform(SeaTransformCaseSearchParam param) {
        CaseSearchVo caseSearchVo = new CaseSearchVo();
        BeanUtils.copyProperties(param, caseSearchVo);
        Response<CaseListResponse> resp = approveSearch(caseSearchVo);
        if (resp.notOk()) {
            return OperationResult.fail(resp.getMsg() + resp.getCode());
        }
        CaseListResponse data = resp.getData();
        List<AdminCrowdfundingInfoView> list = data.getData();
        if (CollectionUtils.isEmpty(list)) {
            return OperationResult.success(PaginationListVO.createEmpty());
        }
        List<SeaTransformCaseInfoVO> voList = list.stream().map(this::transformCaseInfo).collect(Collectors.toList());
        return OperationResult.success(PaginationListVO.create(voList, data.getPagination()));
    }

    private SeaTransformCaseInfoVO transformCaseInfo(AdminCrowdfundingInfoView caseInfo) {
        if (caseInfo == null) {
            return null;
        }
        SeaTransformCaseInfoVO v = new SeaTransformCaseInfoVO();
        BeanUtils.copyProperties(caseInfo, v);
        v.setStatusMsg(caseInfo.getStatus().getApproveMsg());
        v.setCaseId(caseInfo.getId());
        v.setInfoUuid(caseInfo.getInfoId());
        return v;
    }

    /**
     * 是否允许检索全部数据
     * https://wiki.shuiditech.com/pages/viewpage.action?pageId=288851934
     * https://wiki.shuiditech.com/pages/viewpage.action?pageId=510526686
     * @param caseSearchVo
     * @return
     */
    private boolean allowQueryAll(CaseSearchVo caseSearchVo){
        return StringUtils.isNotEmpty(caseSearchVo.getTitle()) ||
                StringUtils.isNotEmpty(caseSearchVo.getMobile()) ||
                caseSearchVo.getId() != null ||
                StringUtils.isNotEmpty(caseSearchVo.getPatientName()) ||
                StringUtils.isNotEmpty(caseSearchVo.getCaseUserId()) ||
                caseSearchVo.getHandle() != null ||

                StringUtils.isNotEmpty(caseSearchVo.getContent()) ||
                StringUtils.isNotEmpty(caseSearchVo.getPatientIdCard()) ||
                StringUtils.isNotEmpty(caseSearchVo.getSelfRealName()) ||
                StringUtils.isNotEmpty(caseSearchVo.getSelfIdCard()) ||
                StringUtils.isNotEmpty(caseSearchVo.getSelfWechatName()) ||
                StringUtils.isNotEmpty(caseSearchVo.getPayeeName()) ||
                StringUtils.isNotEmpty(caseSearchVo.getPayeeMobile()) ||
                StringUtils.isNotEmpty(caseSearchVo.getPayeeIdCard()) ||
                StringUtils.isNotEmpty(caseSearchVo.getReportMobile()) ||
                StringUtils.isNotEmpty(caseSearchVo.getVerificationContent()) ||
                StringUtils.isNotEmpty(caseSearchVo.getTrendContent()) ||
                StringUtils.isNotEmpty(caseSearchVo.getDiseaseName()) ||
                StringUtils.isNotEmpty(caseSearchVo.getConfirmedHospital()) ||
                StringUtils.isNotEmpty(caseSearchVo.getPreposeRaiseMobile());
    }

    private long getTotalFromPage(List<?> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        Page page;
        try {
            page = (Page)list;
        } catch (Exception var3) {
            return list.size();
        }
        return page.getTotal();
    }
}
