package com.shuidihuzhu.cf.service.admin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.admin.delegate.SeaUserAccountDelegate;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfHospitalAuditBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingOperationBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.IAdminCredibleInfoService;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.constants.AdminUserIDConstants;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfHospitalAuditDao;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfHospitalAuditInfoTelDao;
import com.shuidihuzhu.cf.delegate.OrganizationDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingUserDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.BooleanEnum;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.approve.ApproveSourceTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.finance.client.feign.CfFinancePauseFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.enums.CfDrawCashPauseRecordEnum;
import com.shuidihuzhu.cf.model.admin.CfHospitalAuditInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.risk.client.admin.list.RiskListDepartmentClient;
import com.shuidihuzhu.cf.risk.client.aegis.EngineAnalysisClient;
import com.shuidihuzhu.cf.risk.model.admin.list.HospitalAuditPassDto;
import com.shuidihuzhu.cf.risk.model.aegis.RiskAnalysisBatchDto;
import com.shuidihuzhu.cf.risk.model.aegis.RiskAnalysisBatchResult;
import com.shuidihuzhu.cf.risk.model.aegis.RiskObject;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.crowdfunding.report.CfReportService;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cf.service.workorder.WorkOrderExtService;
import com.shuidihuzhu.cf.vo.approve.CfHospitalAuditInfoNew;
import com.shuidihuzhu.cf.vo.approve.HospitalAuditShowVO;
import com.shuidihuzhu.cf.vo.report.HospitalAuditRiskDtoExt;
import com.shuidihuzhu.client.baseservice.msg.v2.MsgClientV2;
import com.shuidihuzhu.client.cf.admin.model.HospitalOptSourceEnum;
import com.shuidihuzhu.client.cf.clewtrack.model.CfUserInvitedLaunchCaseRecordModel;
import com.shuidihuzhu.client.cf.clewtrack.model.ChannelRefine;
import com.shuidihuzhu.client.cf.clewtrack.param.ChannelRefineDTO;
import com.shuidihuzhu.client.cf.growthtool.client.CfChannelFeignClient;
import com.shuidihuzhu.client.cf.growthtool.client.CfHospitalNormalFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.ConfirmHospitalParam;
import com.shuidihuzhu.client.cf.workorder.CfDianhuaWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.DianhuaHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.DianhuaWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.model.hospital.CfHospitalNormal;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.CfHospitalAuditSendEvent;
import com.shuidihuzhu.msg.model.PushRecord;
import com.shuidihuzhu.msg.util.DateUtil;
import com.shuidihuzhu.pf.common.v2.model.pagehelper.PaginationListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by niejiangnan on 2017/12/18.
 */
@Slf4j
@Service
@RefreshScope
public class CfHospitalAuditService {
    @Autowired
    private AdminCfHospitalAuditBiz cfHospitalAuditBiz;
    @Autowired
    private AdminCrowdfundingOperationBiz adminCrowdfundingOperationBiz;
    @Autowired
    private IRiskDelegate riskDelegate;
    @Autowired
    private ICrowdfundingUserDelegate crowdfundingUserDelegate;
    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Resource
    private CfFinancePauseFeignClient cfFinancePauseFeignClient;

    @Autowired
    private ApproveRemarkOldService approveRemarkOldService;

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;
    @Autowired
    private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;

    @Autowired
    private CfDianhuaWorkOrderClient cfDianhuaWorkOrderClient;
    @Autowired
    private WorkOrderExtService workOrderExtService;

    @Resource
    private OrganizationDelegate organizationDelegate;

    @Autowired
    private AdminCfHospitalAuditDao cfHospitalAuditDao;

    @Autowired
    private CfChannelFeignClient cfChannelFeignClient;

    @Autowired
    private SeaUserAccountDelegate seaUserAccountDelegate;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;

    @Autowired
    private IAdminCredibleInfoService adminCredibleInfoService;
    @Autowired
    private Analytics analytics;
    @Autowired
    private CfReportService cfReportService;
    @Autowired
    private AdminCfHospitalAuditInfoTelDao adminCfHospitalAuditInfoTelDao;
    @Autowired
    private CfHospitalNormalFeignClient cfHospitalNormalFeignClient;

    @Value("${hospital-audit-to-report.switch:false}")
    private boolean hospitalAuditToReportSwitch;
    @Autowired
    private EngineAnalysisClient engineAnalysisClient;
    @Autowired
    private RiskListDepartmentClient riskListDepartmentClient;
    @Resource
    private MsgClientV2Service msgClientV2Service;


    private static final int AUTO_SEND_HOSPITAL_AUDIT_AMOUNT = 30_0000_00;

    public Response passHospitalAudit(CrowdfundingInfo crowdfundingInfo,
                                      CrowdfundingOperation crowdfundingOperation,
                                      AdminUserAccountModel userAccount,
                                      CfHospitalAuditInfo cfHospitalAuditInfo, String operatorContent, int workOrderId, int workOrderType) {
        Response r = checkOnAudit(cfHospitalAuditInfo);
        if (r.notOk()) {
            return r;
        }
        crowdfundingOperation.setOperateTime(new Timestamp(System.currentTimeMillis()));
        crowdfundingOperation.setOperatorId(userAccount.getId());
        crowdfundingOperation.setReason("医院核实信息审核通过");
        cfHospitalAuditInfo.setAuditOperatorId(userAccount.getId());
        cfHospitalAuditInfo.setAuditTime(new Date());
        adminCrowdfundingOperationBiz.update(crowdfundingOperation);
        cfHospitalAuditInfo.setAuditStatus(CrowdfundingInfoStatusEnum.PASSED.getCode());
        CfOperatingRecord cfOperatingRecord = riskDelegate.before(cfHospitalAuditInfo.getInfoUuid(), userAccount.getId(),
                userAccount.getName(), CfOperatingRecordEnum.Type.PASS_HOSPITAL_AUDIT,
                CfOperatingRecordEnum.Role.OPERATOR, null);
        //添加备注
        approveRemarkOldService.add(crowdfundingInfo.getId(), userAccount.getId(), "医院核实通过", operatorContent,
                ApproveSourceTypeEnum.HOSPITAL_AUDIT_PUBLIC);
        cfHospitalAuditBiz.update(cfHospitalAuditInfo);
        riskDelegate.after(cfOperatingRecord, null);

        List<CfHospitalAuditInfoTel> cfHospitalAuditInfoTels = this.cfHospitalAuditBiz.getByCfHospitalAuditInfoId(cfHospitalAuditInfo.getId());
        try {
            noticeRisk(crowdfundingInfo, cfHospitalAuditInfo, workOrderId, cfHospitalAuditInfoTels);
        } catch (Exception e) {
            log.error("", e);
        }

        FeignResponse feignResponse = cfFinancePauseFeignClient.recoverBySourceType(crowdfundingInfo.getInfoId(),
                crowdfundingInfo.getId(),
                CfDrawCashPauseRecordEnum.PauseSourceTypeEnum.HOSPITAL_CHECK.getCode(),
                CfDrawCashPauseRecordEnum.RecordStatusEnum.RECOVER.getCode(),
                CfOperatingRecordEnum.Role.OPERATOR.getCode(), userAccount.getId(), userAccount.getName(),
                "sea后台医院核实信息审核通过");
        log.info("sea后台医院核实信息审核通过\tinfoId:{}\tfeignResponse:{}", crowdfundingInfo.getId(), JSON.toJSON(feignResponse));

        // 存储快照
        CfHospitalAuditInfoNew cfHospitalAuditInfoNew = getCfHospitalAuditInfoNew(cfHospitalAuditInfo, crowdfundingInfo, workOrderId);
        workOrderExtService.saveHospitalAuditSnapshot(crowdfundingInfo.getId(), workOrderId, cfHospitalAuditInfoNew);

        if (crowdfundingInfo.getStatus() != CrowdfundingStatus.CROWDFUNDING_STATED) {
            return NewResponseUtil.makeError(AdminErrorCode.HOSPITAL_AUDIT_PASS_NEED_APPROVE_CASE);
        }

        return handleWorkOrderParam(workOrderId, workOrderType, userAccount.getId(), HandleResultEnum.audit_pass,
                crowdfundingInfo.getId(), cfHospitalAuditInfo);
    }

    private void noticeRisk(CrowdfundingInfo crowdfundingInfo, CfHospitalAuditInfo cfHospitalAuditInfo,
                            long workOrderId, List<CfHospitalAuditInfoTel> cfHospitalAuditInfoTels) {
        if(CollectionUtils.isEmpty(cfHospitalAuditInfoTels)) {
            return;
        }
        CfHospitalNormal cfHospitalNormal = getCfHospitalNormal(cfHospitalAuditInfo.getHospitalId());
        String hospitalCode = "";
        if (cfHospitalNormal != null) {
            hospitalCode = cfHospitalNormal.getVhospitalCode();
        }
        List<HospitalAuditPassDto.LandlineNumber> landlineNumber = Lists.newArrayList();
        for (CfHospitalAuditInfoTel cfHospitalAuditInfoTel : cfHospitalAuditInfoTels) {
            landlineNumber.add(new HospitalAuditPassDto.LandlineNumber(cfHospitalAuditInfoTel.getAreaCode(),
                    cfHospitalAuditInfoTel.getTelNum(), cfHospitalAuditInfoTel.getExtNum()));
        }
        HospitalAuditPassDto hospitalAuditPassDto = new HospitalAuditPassDto();
        hospitalAuditPassDto.setCaseId(crowdfundingInfo.getId());
        hospitalAuditPassDto.setCity(cfHospitalAuditInfo.getCityName());
        hospitalAuditPassDto.setDepartments(cfHospitalAuditInfo.getDepartment());
        hospitalAuditPassDto.setHospitalCode(hospitalCode);
        hospitalAuditPassDto.setHospitalId(cfHospitalAuditInfo.getHospitalId());
        hospitalAuditPassDto.setHospitalName(cfHospitalAuditInfo.getHospitalName());
        hospitalAuditPassDto.setProvince(cfHospitalAuditInfo.getProvinceName());
        hospitalAuditPassDto.setWorkOrderId(workOrderId);
        hospitalAuditPassDto.setLandlineNumber(landlineNumber);
        log.info("hospitalAuditPassDto:{}", JSON.toJSONString(hospitalAuditPassDto));
        this.riskListDepartmentClient.hospitalAuditPass(hospitalAuditPassDto);
    }

    public Response rejectdHospitalAudit(CrowdfundingInfo crowdfundingInfo,
                                         CrowdfundingOperation crowdfundingOperation,
                                         AdminUserAccountModel userAccount,
                                         CfHospitalAuditInfo cfHospitalAuditInfo,
                                         String operatorContent, int workOrderId, int workOrderType) {
        Response r = checkOnAudit(cfHospitalAuditInfo);
        if (r.notOk()) {
            return r;
        }
        crowdfundingOperation.setOperateTime(new Timestamp(System.currentTimeMillis()));
        crowdfundingOperation.setOperatorId(userAccount.getId());
        crowdfundingOperation.setReason("医院核实信息被驳回");
        adminCrowdfundingOperationBiz.update(crowdfundingOperation);
        cfHospitalAuditInfo.setAuditStatus(CrowdfundingInfoStatusEnum.REJECTED.getCode());
        cfHospitalAuditInfo.setOperatorContent(operatorContent);
        cfHospitalAuditInfo.setAuditOperatorId(userAccount.getId());
        cfHospitalAuditInfo.setAuditTime(new Date());
        //添加驳回医院信息镜像  以及更新  医院核实信息
        CfOperatingRecord cfOperatingRecord = riskDelegate.before(cfHospitalAuditInfo.getInfoUuid(), userAccount.getId(),
                userAccount.getName(), CfOperatingRecordEnum.Type.REFUSE_HOSPITAL_AUDIT,
                CfOperatingRecordEnum.Role.OPERATOR, null);
        //添加备注
        int operatorId = userAccount.getId();
        approveRemarkOldService.add(crowdfundingInfo.getId(), operatorId, "医院核实驳回", operatorContent,
                ApproveSourceTypeEnum.HOSPITAL_AUDIT_PRIVATE);
        cfHospitalAuditBiz.update(cfHospitalAuditInfo);
        riskDelegate.after(cfOperatingRecord, null);
        //推送消息
        pusHospitalAuditMsg(crowdfundingInfo);
        try {
            //下发医院核实任务
            crowdfundingDelegate.publishHospitalTask(crowdfundingInfo);
        } catch (Exception e) {
            log.error("下发医院核实任务失败.userId:{}", crowdfundingInfo.getUserId(), e);
        }

        commonOperationRecordClient.create()
                .buildBasicPlatform(crowdfundingInfo.getId(), operatorId, OperationActionTypeEnum.REFUSE_HOSPITAL_AUDIT)
                .save();

        // 存储快照
        int caseId = crowdfundingInfo.getId();
        CfHospitalAuditInfoNew cfHospitalAuditInfoNew = getCfHospitalAuditInfoNew(cfHospitalAuditInfo, crowdfundingInfo, workOrderId);
        workOrderExtService.saveHospitalAuditSnapshot(caseId, workOrderId, cfHospitalAuditInfoNew);

        return handleWorkOrderParam(workOrderId, workOrderType, operatorId, HandleResultEnum.audit_reject, caseId, cfHospitalAuditInfo);
    }

    private Response<Void> checkOnAudit(CfHospitalAuditInfo cfHospitalAuditInfo) {
        if (cfHospitalAuditInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_HOSPITAL_AUDIT_IS_NULL);
        }
        CrowdfundingInfoStatusEnum status = CrowdfundingInfoStatusEnum.getByCode(cfHospitalAuditInfo.getAuditStatus());
        switch (status) {
            case SUBMITTED:
                return NewResponseUtil.makeSuccess(null);
            case UN_SAVE:
                return NewResponseUtil.makeError(AdminErrorCode.CF_HOSPITAL_AUDIT_IS_CANCEL);
            case PASSED:
                return NewResponseUtil.makeError(AdminErrorCode.CF_HOSPITAL_AUDIT_IS_PASS);
            default:
                return NewResponseUtil.makeError(AdminErrorCode.ADMIN_IN_HOSPITAL);
        }
    }

    /**
     * 材料审核提交时调用
     *
     * @param caseId
     * @param workOrderId
     * @param operatorId
     * @return
     */
    public Response onCaseInfoApprove(int caseId, long workOrderId, int operatorId) {
        if (workOrderId <= 0) {
            return NewResponseUtil.makeSuccess(null);
        }
        CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        String infoUuid = fundingInfo.getInfoId();
        CrowdfundingStatus fundingInfoStatus = fundingInfo.getStatus();
        if (fundingInfoStatus != CrowdfundingStatus.CROWDFUNDING_STATED &&
                fundingInfoStatus != CrowdfundingStatus.APPROVE_DENIED) {
            return NewResponseUtil.makeSuccess(null);
        }
        CfHospitalAuditInfoExt cfHospitalAuditInfoExt = cfHospitalAuditBiz.getByInfoUuid(infoUuid);
        if (cfHospitalAuditInfoExt == null || cfHospitalAuditInfoExt.getAuditStatus() != CrowdfundingInfoStatusEnum.PASSED.getCode()) {
            return NewResponseUtil.makeSuccess(null);
        }
        return handleWorkOrderParam(workOrderId, WorkOrderType.heshi.getType(), operatorId, HandleResultEnum.audit_pass, caseId, cfHospitalAuditInfoExt);
    }

    //必须指明是不是新的举报处理详情页来的操作 2:新的举报处理详情页
    public Response sendHospitalAudit(CrowdfundingInfo crowdfundingInfo,
                                      CrowdfundingOperation crowdfundingOperation,
                                      int operatorId,
                                      String operatorContent,
                                      String reasonSupplement,
                                      CfHospitalAuditInfo cfHospitalAuditInfo,
                                      HospitalAuditTypeEnum type, int pageSource) {
        AdminUserAccountModel userAccount = this.seaAccountClientV1.getValidUserAccountById(operatorId).getResult();
        if (userAccount == null) {
            return NewResponseUtil.makeError(AdminErrorCode.ADMIN_ACCOUNT_NOT_EXISTS);
        }
        String userName = userAccount.getName();

        if (cfHospitalAuditInfo != null &&
                cfHospitalAuditInfo.getAuditStatus() != CrowdfundingInfoStatusEnum.PASSED.getCode() &&
                cfHospitalAuditInfo.getIsDelete() != 1) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_HOSPITAL_AUDIT_IS_DUPLICATE);
        }
        CrowdfundingAuthor crowdfundingAuthor = crowdfundingUserDelegate.getCrowdfundingAuthor(crowdfundingInfo.getId());
        if (crowdfundingAuthor == null || StringUtils.isBlank(crowdfundingAuthor.getName())) {
            return NewResponseUtil.makeError(AdminErrorCode.AUTHOR_NO_EXIST_ERROR);
        }

        // 检查有核实工单未完成 不许下发
        Response<List<WorkOrderVO>> listResponse = cfWorkOrderClient.listByCaseIdAndTypeAndResult(
                crowdfundingInfo.getId(),
                Lists.newArrayList(WorkOrderType.heshi.getType()),
                Lists.newArrayList(HandleResultEnum.unDoResult())
        );
        if (listResponse.notOk()) {
            return NewResponseUtil.makeResponse(listResponse.getCode(), listResponse.getMsg(), null);
        }
        List<WorkOrderVO> workOrders = listResponse.getData();
        if (CollectionUtils.isNotEmpty(workOrders)) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_HOSPITAL_AUDIT_WORK_ORDER_NOT_FINISH);
        }

        //更新crowdfundingOperation
        crowdfundingOperation.setOperateTime(new Timestamp(System.currentTimeMillis()));
        CfOperatingRecordEnum.Role role = CfOperatingRecordEnum.Role.SYSTEM;
        String prefix = "sea后台admin的job-";
        if (type == HospitalAuditTypeEnum.OPERATOR) {
            role = CfOperatingRecordEnum.Role.OPERATOR;
            prefix = "sea后台-";
        }
        crowdfundingOperation.setOperatorId(operatorId);
        crowdfundingOperation.setReason("已下发医院核实信息到用户");
        adminCrowdfundingOperationBiz.update(crowdfundingOperation);
        String infoUuid = crowdfundingOperation.getInfoId();
        commonOperationRecordClient.create()
                .buildBasicPlatform(crowdfundingInfo.getId(), operatorId, OperationActionTypeEnum.SEND_HOSPITAL_AUDIT)
                .save();

        //改为 cf-finance-api 的feign实现
        FeignResponse feignResponse = cfFinancePauseFeignClient.addPause(operatorId, infoUuid,
                crowdfundingInfo.getId(), CfDrawCashPauseRecordEnum.PauseSourceTypeEnum.HOSPITAL_CHECK.getCode(),
                Lists.newArrayList(CfDrawCashPauseRecordEnum.PauseReasonTypeEnum.HOSPITAL_CHECK.getCode()),
                role.getCode(), userName, prefix + "下发医院核实信息", false);
        log.info("下发医院核实信息\tfeignResponse:{}\tinfoId{}", JSON.toJSONString(feignResponse), crowdfundingInfo.getId());
        if (feignResponse.notOk()) {
            return NewResponseUtil.makeResponse(feignResponse.getCode(), feignResponse.getMsg(), feignResponse.getData());
        }

        cfHospitalAuditInfo = new CfHospitalAuditInfo();
        cfHospitalAuditInfo.setInfoUuid(infoUuid);
        cfHospitalAuditInfo.setAuditStatus(CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode());

        cfHospitalAuditInfo.setPatientName(crowdfundingAuthor.getName());
        cfHospitalAuditInfo.setOperatorContent(operatorContent);
        cfHospitalAuditInfo.setType(type.getCode());

        cfHospitalAuditInfo.setReason(operatorContent);
        cfHospitalAuditInfo.setReasonSupply(reasonSupplement);
        cfHospitalAuditInfo.setEasyToVerifyTime("");
        cfHospitalAuditInfo.setOperatorId(operatorId);
        String org = organizationDelegate.getSimpleOrganization(operatorId);
        cfHospitalAuditInfo.setOperatorOrg(org);

        // 举报组不走工单
        boolean isReportOrg = isReportOrg(org);
        boolean onWorkOrder = !isReportOrg;
        cfHospitalAuditInfo.setOnWorkOrder(BooleanEnum.getValueByBoolean(onWorkOrder));

        //添加下发医院信息镜像  以及医院核实信息
        CfOperatingRecord cfOperatingRecord = riskDelegate.before(infoUuid, operatorId,
                userName, CfOperatingRecordEnum.Type.SEND_HOSPITAL_AUDIT,
                CfOperatingRecordEnum.Role.OPERATOR, null);
        //添加备注
        String remarkPrefix = "";
        if (type == HospitalAuditTypeEnum.OPERATOR) {
            remarkPrefix = "【人工下发医院核实】";
        } else if (type == HospitalAuditTypeEnum.SYSTEM_AMOUNT_MORE_THAN) {
            remarkPrefix = "【系统自动下发医院核实】";
        }
        approveRemarkOldService.add(crowdfundingInfo.getId(), operatorId, remarkPrefix, operatorContent + ";" + reasonSupplement);

        cfHospitalAuditBiz.save(cfHospitalAuditInfo);

        //添加可信信息
        adminCredibleInfoService.insertOne(crowdfundingInfo.getId(), crowdfundingInfo.getUserId(), cfHospitalAuditInfo.getId(),
                CredibleTypeEnum.HOSPITAL_CHECK.getKey(), CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode(), operatorId);

        riskDelegate.after(cfOperatingRecord, null);
        // 推送消息
        pusHospitalAuditMsg(crowdfundingInfo);
        try {
            //下发医院核实任务
            crowdfundingDelegate.publishHospitalTask(crowdfundingInfo);
        } catch (Exception e) {
            log.error("下发医院核实任务失败.userId:{}", operatorId, e);
        }

        // 统计
        CfHospitalAuditSendEvent hase = new CfHospitalAuditSendEvent();
        try {
            long auditId = cfHospitalAuditInfo.getId();

            hase.setAuditor_id(auditId);
            hase.setAuditor_org(StringUtils.trimToEmpty(org));
            hase.setAudit_date(DateUtil.getCurrentDateStr());
            hase.setAudit_result(Optional.ofNullable(crowdfundingInfo.getStatus()).map(p -> p.name()).orElse(""));
            hase.setInfo_id(Long.valueOf(crowdfundingInfo.getId()));
            hase.setCase_id(StringUtils.trimToEmpty(infoUuid));
            hase.setOperate_content(operatorContent);
            hase.setOperator_id(Long.valueOf(operatorId));
            if (crowdfundingInfo.getStatus() != null) {
                hase.setCase_status_info(crowdfundingInfo.getStatus().getApproveMsg());
            } else {
                hase.setCase_status_info("");
            }
            hase.setUser_tag(String.valueOf(operatorId));
            hase.setUser_tag_type(UserTagTypeEnum.userid);

            analytics.track(hase);
            log.info("大数据打点上报,cf-hospital-audit-send-event:{}", JSONObject.toJSONString(hase));
        } catch (Exception e) {
            log.error("大数据打点上报异常,cf-hospital-audit-send-event:{}", JSONObject.toJSONString(hase), e);
        }

        if (onWorkOrder) {
            // 调用工单
            DianhuaWorkOrder order = new DianhuaWorkOrder();
            order.setCaseId(crowdfundingInfo.getId());
            order.setOrderType(WorkOrderType.genjin.getType());
            order.setYiyuanHeshiId(String.valueOf(cfHospitalAuditInfo.getId()));
            order.setYiyuanHeshiTime(DateUtil.getCurrentDateStr());
            order.setOperComment(operatorContent);
            Response<Long> response = cfDianhuaWorkOrderClient.createDianhua(order);
            log.info("DianhuaWorkOrder create order={} response={}", order, JSON.toJSONString(response));
            return response;
        } else {
            return NewResponseUtil.makeSuccess(null);
        }
    }

    /**
     * TODO 重构
     * 是否是举报组
     * 目前公共服务组没有提供接口,并且不让用角色等其他方式判断
     *
     * @param org
     * @return
     * @产品-米娜 要求临时用字符串是否包含'举报组'来判断
     */
    private boolean isReportOrg(String org) {
        return StringUtils.contains(org, "举报组");
    }


    private void pusHospitalAuditMsg(CrowdfundingInfo crowdfundingInfo) {
        // 判断案例Type
        int type = crowdfundingInfo.getType();
        long userId = crowdfundingInfo.getUserId();
        if (type == CrowdfundingType.GOODS.value()) {
            return;
        }
        //发送模板消息
        sendHospitalAuditTemplate(userId, crowdfundingInfo.getInfoId(), crowdfundingInfo.getTitle());

        sendHospitalAuditApp(userId);
    }

    private void sendHospitalAuditApp(long userId) {
        msgClientV2Service.sendAppMsg("OMI0745", Lists.newArrayList(userId));
    }

    private void sendHospitalAuditTemplate(long userId, String infoUuid, String title) {
        String modelNum0 = "templateHospital130";
        Map<Long, Map<Integer, String>> mapMap = Maps.newHashMap();
        HashMap<Integer, String> params = Maps.newHashMap();
        params.put(1, infoUuid);
        params.put(2, title);
        mapMap.put(userId, params);
        msgClientV2Service.sendWxParamsMsg(modelNum0, mapMap);
    }

    public Response<Void> delayProcess(int workOrderId, int workOrderType, String infoUuid, int operatorId, String operatorContent) {
        if (workOrderId <= 0 || workOrderType <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        CfHospitalAuditInfo info = cfHospitalAuditBiz.getByInfoUuid(infoUuid);

        int caseId = crowdfundingInfo.getId();
        Response response = handleWorkOrderParam(workOrderId, workOrderType, operatorId, HandleResultEnum.later_doing, caseId, info);

        if (response.ok()) {
            approveRemarkOldService.add(caseId, operatorId, "稍后处理", operatorContent,
                    ApproveSourceTypeEnum.HOSPITAL_AUDIT_PRIVATE);
        }
        return response;
    }

    public Response cancelAudit(int workOrderId, int workOrderType, String infoUuid, Integer userId, String reason, String operatorContent) {
        if (StringUtils.isBlank(infoUuid) || userId == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        AdminUserAccountModel userAccount = this.seaAccountClientV1.getValidUserAccountById(userId).getResult();
        if (userAccount == null) {
            return NewResponseUtil.makeError(AdminErrorCode.ADMIN_ACCOUNT_NOT_EXISTS);
        }
        CfHospitalAuditInfo cfHospitalAuditInfo = cfHospitalAuditBiz.getByInfoUuid(infoUuid);
        if (cfHospitalAuditInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (cfHospitalAuditInfo.getAuditStatus() == CrowdfundingInfoStatusEnum.PASSED.getCode()) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_HOSPITAL_AUDIT_IS_PASS);
        }
        cfHospitalAuditInfo.setAuditOperatorId(userId);
        cfHospitalAuditInfo.setAuditTime(new Date());
        cfHospitalAuditBiz.delete(cfHospitalAuditInfo);
        //如果是新举报详情页下发的医院核实，需要同步删除可信列表中的相应记录
        adminCredibleInfoService.delete(cfHospitalAuditInfo.getId(), CredibleTypeEnum.HOSPITAL_CHECK.getKey());
        //将未处理的置为不再处理

        FeignResponse feignResponse = cfFinancePauseFeignClient.recoverBySourceType(crowdfundingInfo.getInfoId(),
                crowdfundingInfo.getId(),
                CfDrawCashPauseRecordEnum.PauseSourceTypeEnum.HOSPITAL_CHECK.getCode(),
                CfDrawCashPauseRecordEnum.RecordStatusEnum.NO_DEAL.getCode(),
                CfOperatingRecordEnum.Role.OPERATOR.getCode(), userAccount.getId(), userAccount.getName(),
                "sea后台医院核实信息撤销,不再处理");
        log.info("sea后台医院核实信息撤销,不再处理\tinfoId:{}\tfeignResponse:{}", crowdfundingInfo.getId(), JSON.toJSON(feignResponse));

        crowdfundingOperationDelegate.saveCfOperatingRecord(infoUuid, userId, userAccount.getName(),
                CfOperatingRecordEnum.Type.CANCEL_HOSPITAL_AUDIT, CfOperatingRecordEnum.Role.OPERATOR);
        approveRemarkOldService.add(crowdfundingInfo.getId(), userAccount.getId(), "取消医院核实",
                reason + ";" + operatorContent, ApproveSourceTypeEnum.HOSPITAL_AUDIT_PUBLIC);


        cfHospitalAuditInfo.setAuditStatus(CrowdfundingInfoStatusEnum.UN_SAVE.getCode());
        // 存储快照
        CfHospitalAuditInfoNew cfHospitalAuditInfoNew = getCfHospitalAuditInfoNew(cfHospitalAuditInfo, crowdfundingInfo, workOrderId);
        workOrderExtService.saveHospitalAuditSnapshot(crowdfundingInfo.getId(), workOrderId, cfHospitalAuditInfoNew);

        return handleWorkOrderParam(workOrderId, workOrderType, userId, HandleResultEnum.cancel, crowdfundingInfo.getId(),
                cfHospitalAuditInfo);
    }

    @NotNull
    private Response handleWorkOrderParam(long workOrderId, int workOrderType, int operatorId, HandleResultEnum handleResult, int caseId, CfHospitalAuditInfo info) {
        if (info == null) {
            log.error("workOrderId:{}", workOrderId);
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        String yiyuanHeshiId = String.valueOf(info.getId());
        Date createTime = info.getCreateTime();

        DianhuaHandleOrderParam param = new DianhuaHandleOrderParam();
        param.setCaseId(caseId);
        param.setYiyuanHeshiId(yiyuanHeshiId);
        param.setYiyuanHeshiTime(DateUtil.getDate2LStr(createTime));

        param.setWorkOrderId(workOrderId);
        param.setHandleResult(handleResult.getType());
        param.setOrderType(workOrderType);
        param.setUserId(operatorId);
        Response response = cfDianhuaWorkOrderClient.hanldeDianhua(param);
        log.info("hanldeDianhua param={} response={}", JSON.toJSONString(param), JSON.toJSONString(response));
        return response;
    }

    public Response<Void> submitFollowOrder(int workOrderId, String infoUuid, int adminUserId, String operatorContent) {
        CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        CfHospitalAuditInfo cfHospitalAuditInfo = cfHospitalAuditBiz.getByInfoUuid(infoUuid);
        int caseId = fundingInfo.getId();

        // 备注
        approveRemarkOldService.add(caseId, adminUserId, "提交", operatorContent,
                ApproveSourceTypeEnum.HOSPITAL_AUDIT_PRIVATE);

        // 处理工单
        return handleWorkOrderParam(workOrderId, WorkOrderType.genjin.getType(), adminUserId, HandleResultEnum.done,
                caseId, cfHospitalAuditInfo);
    }

    public List<CfHospitalAuditInfoNew> listHospitalAuditSnapshot(int id) {
        return workOrderExtService.listHospitalAuditSnapshot(id);
    }

    public CfHospitalAuditInfoNew getWorkOrderHospitalAuditSnapshot(int workOrderId) {
        return workOrderExtService.getWorkOrderHospitalAuditSnapshot(workOrderId);
    }

    /**
     * 捐款触发金额达标自动下发医院核实
     *
     * @param caseId
     */
    public void onDonate(int caseId) {
        log.info("on donate caseId:{}", caseId);
        touch2AutoSend(caseId);
    }

    /**
     * 患者信息填写触发检查自动下发
     *
     * @param caseId
     */
    public void onBaseInfoSubmit(int caseId) {
        log.info("onBaseInfoSubmit caseId:{}", caseId);
        touch2AutoSend(caseId);
    }

    /**
     * 检查是否可以自动下发
     *
     * @param caseId
     */
    private void touch2AutoSend(int caseId) {
        log.info("touch2AutoSend caseId:{}", caseId);
        CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);

        // 金额达标才下发
        int amount = fundingInfo.getAmount();
        if (amount < AUTO_SEND_HOSPITAL_AUDIT_AMOUNT) {
            log.info("amount is not up to standard caseId:{}, amount:{}", caseId, amount);
            return;
        }
        String infoUuid = fundingInfo.getInfoId();
        //过滤是否有自动下发的医院核实
        CfHospitalAuditInfoExt autoSend = cfHospitalAuditDao.getAutoSendByInfoUuid(infoUuid);
        if (autoSend != null) {
            log.info("has auto send caseId:{}", caseId);
            return;
        }
        //增加开关
        if (hospitalAuditToReportSwitch) {
            cfReportService.autoReport(fundingInfo);
            return;
        }
        sendHospital(caseId, fundingInfo, infoUuid);
    }

    private void sendHospital(int caseId, CrowdfundingInfo fundingInfo, String infoUuid) {
        // 若已存在核实&核实未取消 不许下发
        CfHospitalAuditInfo cfHospitalAuditInfo = cfHospitalAuditBiz.getByInfoUuid(infoUuid);
        if (cfHospitalAuditInfo != null) {
            log.info("has normal send caseId:{}", caseId);
            return;
        }
        CrowdfundingOperation crowdfundingOperation = adminCrowdfundingOperationBiz.getByInfoId(infoUuid);
        if (crowdfundingOperation == null) {
            log.error("null operation caseId:{}", caseId);
            return;
        }
        sendHospitalAudit(fundingInfo, crowdfundingOperation, AdminUserIDConstants.SYSTEM, "30w自动下发",
                "", cfHospitalAuditInfo, HospitalAuditTypeEnum.SYSTEM_AMOUNT_MORE_THAN, CfReportSourceEnum.DEFAULT.getKey());
    }


    public PaginationListVO<HospitalAuditShowVO> listHospitalAudit(
            int current, Integer pageSize, Integer caseId, Integer caseStatus,
            BooleanEnum onWorkOrderEnum, int hospitalAuditStatus, Long hospitalSendBeginTime,
            Long hospitalSendEndTime, Long userSubmitBeginTime, Long userSubmitEndTime, Long updateBeginTime, Long updateEndTime) {


        // 此表目前数据3.3w 产品要求要总条数
        PageHelper.startPage(current, pageSize);

        List<HospitalAuditShowVO> list = cfHospitalAuditDao.listByCondition(caseId, caseStatus,
                onWorkOrderEnum.getValue(),
                hospitalAuditStatus,
                Optional.ofNullable(hospitalSendBeginTime).map(Date::new).orElse(null),
                Optional.ofNullable(hospitalSendEndTime).map(Date::new).orElse(null),
                Optional.ofNullable(userSubmitBeginTime).map(Date::new).orElse(null),
                Optional.ofNullable(userSubmitEndTime).map(Date::new).orElse(null),
                Optional.ofNullable(updateBeginTime).map(Date::new).orElse(null),
                Optional.ofNullable(updateEndTime).map(Date::new).orElse(null)
        );

        if (CollectionUtils.isEmpty(list)) {
            return PaginationListVO.createEmpty();
        }

//        获取渠道
        List<Integer> caseIds = list.stream().map(HospitalAuditShowVO::getCaseId).collect(Collectors.toList());
        List<CrowdfundingInfo> fundingInfos = crowdfundingInfoBiz.getListByIds(caseIds);
        Map<Integer, ChannelRefine.ChannelRefineResuleEnum> channelMap = getChannel(fundingInfos);

//        审核人获取组织
        List<Integer> auditOperatorIds = list.stream().map(HospitalAuditShowVO::getAuditOperatorId).collect(Collectors.toList());
        Map<Integer, String> auditOperatorOrgMap = organizationDelegate.getSimpleOrganizations(auditOperatorIds);

        // 下发人id
        List<Integer> operatorIds = list.stream().map(HospitalAuditShowVO::getOperatorId).collect(Collectors.toList());

        List<Integer> totalOperatorIds = Stream.concat(auditOperatorIds.stream(), operatorIds.stream())
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, String> totalOperatorMap = seaUserAccountDelegate.getByOperators(totalOperatorIds);

        for (HospitalAuditShowVO vo : list) {

            // 写入审核人组织
            vo.setAuditOperatorOrg(auditOperatorOrgMap.get(vo.getAuditOperatorId()));

            // 写入渠道
            vo.setChannel(channelMap.get(vo.getCaseId()).getChannelDesc());

            vo.setAuditOperatorName(totalOperatorMap.getOrDefault(vo.getAuditOperatorId(), ""));

            vo.setOperatorName(totalOperatorMap.getOrDefault(vo.getOperatorId(), ""));
        }

        return PaginationListVO.createWithList(list);
    }

    /**
     * 查询渠道
     *
     * @param caseList
     * @return
     */
    private Map<Integer, ChannelRefine.ChannelRefineResuleEnum> getChannel(List<CrowdfundingInfo> caseList) {

        //查询渠道
        List<ChannelRefineDTO> channelRefineDTOS = caseList.stream()
                .map(r -> {
                    ChannelRefineDTO dto = new ChannelRefineDTO();
                    dto.setChannel(r.getChannel());
                    dto.setInfoId((long) r.getId());
                    dto.setUserId(r.getUserId());
                    return dto;
                }).collect(Collectors.toList());

        Response<List<CfUserInvitedLaunchCaseRecordModel>> response = cfChannelFeignClient.getCfUserInvitedLaunchCaseRecordByInfoIds(channelRefineDTOS);
        log.info("getChannel refineDTO={} response={}", channelRefineDTOS, JSON.toJSONString(response));

        if (response == null || response.notOk() || CollectionUtils.isEmpty(response.getData())) {
            return Maps.newHashMap();
        }

        List<CfUserInvitedLaunchCaseRecordModel> recordModels = response.getData();

        return recordModels.stream().collect(
                Maps::newHashMap,
                (m, v) -> m.put(v.getInfoId().intValue(), ChannelRefine.ChannelRefineResuleEnum.parse(v.getChannel())),
                Map::putAll
        );
    }

    public Response<Void> modifyDepartmentTelNumberV2(String infoUuid, long id, String areaCode, String telNum, String extNum,
                                                      int operatorId, int workOrderId) {
        CrowdfundingInfo fundingInfo = this.crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (fundingInfo == null) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        CfHospitalAuditInfoExt info = cfHospitalAuditBiz.getByInfoUuid(infoUuid);
        if (info == null) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        CfHospitalAuditInfoTel oldTel = this.adminCfHospitalAuditInfoTelDao.getById(id);
        CfHospitalAuditInfoTel cfHospitalAuditInfoTel = new CfHospitalAuditInfoTel();
        cfHospitalAuditInfoTel.setId(id);
        cfHospitalAuditInfoTel.setAreaCode(areaCode);
        cfHospitalAuditInfoTel.setTelNum(telNum);
        cfHospitalAuditInfoTel.setExtNum(extNum);
        cfHospitalAuditInfoTel.setRiskLevel(0);
        cfHospitalAuditInfoTel.setRiskMsg("");
        try {
            setRiskInfo(fundingInfo, workOrderId, info, Lists.newArrayList(cfHospitalAuditInfoTel));
        } catch (Exception e) {
            log.error("", e);
        }
        this.adminCfHospitalAuditInfoTelDao.update(cfHospitalAuditInfoTel);
        String prefix = workOrderId > 0 ? String.format("【工单ID：%d】", workOrderId) : "";
        String content = String.format("%s【修改科室座机号码】\"%s\" 修改为 \"%s\"", prefix, oldTel.getFullNum(), cfHospitalAuditInfoTel.getFullNum());
        approveRemarkOldService.add(fundingInfo.getId(), operatorId, content, ApproveSourceTypeEnum.HOSPITAL_AUDIT_PRIVATE);
        return NewResponseUtil.makeSuccess(null);
    }

    private CfHospitalNormal getCfHospitalNormal(int cfHospitalId) {
        log.info("cfHospitalId:{}", cfHospitalId);
        Response<CfHospitalNormal> cfHospitalNormalResponse = this.cfHospitalNormalFeignClient.getByCfHospitalId(cfHospitalId);
        log.info("cfHospitalNormalResponse:{}", cfHospitalNormalResponse);
        return cfHospitalNormalResponse.getData();
    }

    public Response<Void> modifyHospitalInfo(String infoUuid, int hospitalId, int provinceId, int cityId,
                                             String provinceName, String cityName, String hospitalName, int operatorId, int workOrderId) {
        CfHospitalAuditInfoExt info = cfHospitalAuditBiz.getByInfoUuid(infoUuid);
        if (info == null) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        String newHospitalName = hospitalName + provinceName + cityName;
        String oldHospitalName = info.getHospitalName() + info.getProvinceName() + info.getCityName();
        info.setHospitalId(hospitalId);
        info.setProvinceId(provinceId);
        info.setCityId(cityId);
        info.setProvinceName(provinceName);
        info.setCityName(cityName);
        info.setHospitalName(hospitalName);
        cfHospitalAuditDao.updateHospitalInfo(info);
        CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        String prefix = workOrderId > 0 ? String.format("【工单ID：%d】", workOrderId) : "";
        String content = String.format("%s【修改医院信息】\"%s\" 修改为 \"%s\"", prefix, oldHospitalName, newHospitalName);
        approveRemarkOldService.add(fundingInfo.getId(), operatorId, content, ApproveSourceTypeEnum.HOSPITAL_AUDIT_PRIVATE);
        ConfirmHospitalParam confirmHospitalParam = new ConfirmHospitalParam();
        confirmHospitalParam.setCaseId(fundingInfo.getId());
        confirmHospitalParam.setAdminUserId(operatorId);
        confirmHospitalParam.setProvinceId(provinceId);
        confirmHospitalParam.setCityId(cityId);
        confirmHospitalParam.setHospitalId(hospitalId);
        confirmHospitalParam.setHospitalName(hospitalName);
        confirmHospitalParam.setHospitalEditSource(HospitalOptSourceEnum.yiyuan_heshi.getCode());
        log.info("confirmHospitalParam:{}", confirmHospitalParam);
        Response<Boolean> response = this.cfHospitalNormalFeignClient.notifyGrowthSeaConfirmHospital(confirmHospitalParam);
        log.info("notifyGrowthSeaConfirmHospital:{}", response);
        List<CfHospitalAuditInfoTel> cfHospitalAuditInfoTelRisks = this.cfHospitalAuditBiz.getByCfHospitalAuditInfoId(info.getId());
        if(CollectionUtils.isNotEmpty(cfHospitalAuditInfoTelRisks)) {
            try {
                setRiskInfo(fundingInfo, workOrderId, info, cfHospitalAuditInfoTelRisks);
                updateRisk(cfHospitalAuditInfoTelRisks);
            } catch (Exception e) {
                log.error("", e);
            }
        }
        return NewResponseUtil.makeSuccess(null);
    }

    public CfHospitalAuditInfoNew getCfHospitalAuditInfoNew(CfHospitalAuditInfo cfHospitalAuditInfo, CrowdfundingInfo fundingInfo, int workOrderId) {

        List<CfHospitalAuditInfoTel> cfHospitalAuditInfoTels = this.cfHospitalAuditBiz.getByCfHospitalAuditInfoId(cfHospitalAuditInfo.getId());

        //获取医院核实信息
        CfHospitalAuditInfoNew cfHospitalAuditInfoNew = new CfHospitalAuditInfoNew();
        BeanUtils.copyProperties(cfHospitalAuditInfo, cfHospitalAuditInfoNew);
        cfHospitalAuditInfoNew.setCfHospitalAuditInfoTels(cfHospitalAuditInfoTels);
        if(cfHospitalAuditInfo.getProvinceId() > 0 || cfHospitalAuditInfo.getCityId() > 0) {
            cfHospitalAuditInfoNew.setNew(true);
        }
        return cfHospitalAuditInfoNew;
    }

    public List<CfHospitalAuditInfoTel> getCfHospitalAuditInfoTels(CfHospitalAuditInfo cfHospitalAuditInfo, CrowdfundingInfo fundingInfo, int workOrderId) {
        List<CfHospitalAuditInfoTel> cfHospitalAuditInfoTels = this.cfHospitalAuditBiz.getByCfHospitalAuditInfoId(cfHospitalAuditInfo.getId());
        if (CollectionUtils.isEmpty(cfHospitalAuditInfoTels)) {
            return cfHospitalAuditInfoTels;
        }
        List<CfHospitalAuditInfoTel> cfHospitalAuditInfoTelRisks = Lists.newArrayList();
        for (CfHospitalAuditInfoTel cfHospitalAuditInfoTel : cfHospitalAuditInfoTels) {
            if (cfHospitalAuditInfoTel.getRiskLevel() <= 0) {
                cfHospitalAuditInfoTelRisks.add(cfHospitalAuditInfoTel);
            }
        }
        log.info("cfHospitalAuditInfoTelList:{}", JSON.toJSONString(cfHospitalAuditInfoTelRisks));
        if (CollectionUtils.isEmpty(cfHospitalAuditInfoTelRisks)) {
            return cfHospitalAuditInfoTels;
        }
        try {
            setRiskInfo(fundingInfo, workOrderId, cfHospitalAuditInfo, cfHospitalAuditInfoTelRisks);
            updateRisk(cfHospitalAuditInfoTelRisks);
        } catch (Exception e) {
            log.error("", e);
        }
        return cfHospitalAuditInfoTels;
    }

    private void updateRisk(List<CfHospitalAuditInfoTel> cfHospitalAuditInfoTelRisks) {
        for (CfHospitalAuditInfoTel cfHospitalAuditInfoTelRisk : cfHospitalAuditInfoTelRisks) {
            this.adminCfHospitalAuditInfoTelDao.updateRisk(cfHospitalAuditInfoTelRisk);
        }
    }

    private void setRiskInfo(CrowdfundingInfo fundingInfo, int workOrderId, CfHospitalAuditInfo cfHospitalAuditInfo,
                             List<CfHospitalAuditInfoTel> cfHospitalAuditInfoTelList) {
        Map<Long, CfHospitalAuditInfoTel> cfHospitalAuditInfoTelMap = cfHospitalAuditInfoTelList.stream()
                .collect(Collectors.toMap(CfHospitalAuditInfoTel::getId, Function.identity()));
        CfHospitalNormal cfHospitalNormal = getCfHospitalNormal(cfHospitalAuditInfo.getHospitalId());
        String hospitalCode = "";
        if (cfHospitalNormal != null) {
            hospitalCode = cfHospitalNormal.getVhospitalCode();
        }
        List<Object> hospitalAuditRiskDtos = Lists.newLinkedList();
        for (CfHospitalAuditInfoTel cfHospitalAuditInfoTel : cfHospitalAuditInfoTelList) {
            HospitalAuditRiskDtoExt hospitalAuditRiskDto = new HospitalAuditRiskDtoExt(fundingInfo.getId(), (long) workOrderId,
                    cfHospitalAuditInfo.getHospitalId(), hospitalCode, cfHospitalAuditInfo.getHospitalName(),
                    cfHospitalAuditInfo.getProvinceName(), cfHospitalAuditInfo.getCityName(),
                    cfHospitalAuditInfo.getDepartment(), cfHospitalAuditInfoTel.getAreaCode(),
                    cfHospitalAuditInfoTel.getTelNum(), cfHospitalAuditInfoTel.getExtNum(), new Date(),
                    cfHospitalAuditInfo.getProvinceId(), cfHospitalAuditInfo.getCityId(), cfHospitalAuditInfoTel.getId());
            hospitalAuditRiskDtos.add(hospitalAuditRiskDto);
        }
        log.info("hospitalAuditRiskDtos:{}", JSON.toJSONString(hospitalAuditRiskDtos));
        RiskAnalysisBatchDto riskAnalysisBatchDto = new RiskAnalysisBatchDto("803edce876c341b7a89911107575cba1", "", hospitalAuditRiskDtos);
        Response<List<RiskAnalysisBatchResult<?>>> riskAnalyzeResponse = this.engineAnalysisClient.analyzeBatch(riskAnalysisBatchDto);
        log.info("riskAnalyzeResponse:{}", JSON.toJSONString(riskAnalyzeResponse));
        Response<List<RiskAnalysisBatchResult<HospitalAuditRiskDtoExt>>> response = this.engineAnalysisClient
                .convertAnalyzeBatchDest(riskAnalyzeResponse, HospitalAuditRiskDtoExt.class);
        if(response == null || CollectionUtils.isEmpty(response.getData())) {
            riskAnalyzeResponse = this.engineAnalysisClient.analyzeBatch(riskAnalysisBatchDto);
            log.info("retry riskAnalyzeResponse:{}", JSON.toJSONString(riskAnalyzeResponse));
            response = this.engineAnalysisClient.convertAnalyzeBatchDest(riskAnalyzeResponse, HospitalAuditRiskDtoExt.class);
        }
        if(response == null || CollectionUtils.isEmpty(response.getData())) {
            log.error("engineAnalysisClient analyzeBatch time out");
            return;
        }
        List<RiskAnalysisBatchResult<HospitalAuditRiskDtoExt>> riskAnalysisBatchResults = response.getData();
        for (RiskAnalysisBatchResult<HospitalAuditRiskDtoExt> riskAnalysisBatchResult : riskAnalysisBatchResults) {
            HospitalAuditRiskDtoExt eventInfo = riskAnalysisBatchResult.getEventInfo();
            CfHospitalAuditInfoTel cfHospitalAuditInfoTel = cfHospitalAuditInfoTelMap.get(eventInfo.getId());
            Map<String, RiskObject> activationMap = riskAnalysisBatchResult.getActivationMap();
            RiskObject listHospitalVerification = activationMap.get("list_hospital_verification");
            JSONArray jsonArray = JSON.parseArray(listHospitalVerification.getRiskResult());
            JSONObject jsonObject = jsonArray.getJSONObject(0);
            int riskLevel = jsonObject.getInteger("level");
            String riskMsg = jsonObject.getString("msg");
            cfHospitalAuditInfoTel.setRiskLevel(riskLevel);
            cfHospitalAuditInfoTel.setRiskMsg(riskMsg);
        }
    }

}
