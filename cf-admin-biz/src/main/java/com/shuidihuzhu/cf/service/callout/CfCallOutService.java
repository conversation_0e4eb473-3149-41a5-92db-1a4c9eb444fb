package com.shuidihuzhu.cf.service.callout;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.call.CallTagBO;
import com.shuidihuzhu.cf.call.CallTagVO;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.delegate.crowdfunding.impl.CrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.model.admin.workorder.CfCallOutRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackTaskFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewCallRecordsDO;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolVolunteerFeignClient;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.config.WorkOrderHelper;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.storage.VonStorageVO;
import com.shuidihuzhu.client.cf.workorder.storage.WorkOrderStorageService;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/10/18  8:34 下午
 */
@Service
@Slf4j
public class CfCallOutService {

    @Resource
    private ShuidiCipher shuidiCipher;
    @Resource
    private CfWorkOrderClient workOrderClient;
    @Resource
    private AdminCrowdfundingInfoBiz fundingInfoBiz;
    @Resource
    private SeaAccountClientV1 accountClientV1;
    @Resource
    private CfClewtrackTaskFeignClient clewtrackTaskClient;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Resource
    private CfGrowthtoolVolunteerFeignClient cfGrowthtoolVolunteerFeignClient;
    @Resource
    private AdminCfInfoExtBiz adminCfInfoExtBiz;
    @Resource
    private CrowdfundingDelegate crowdfundingDelegate;
    @Resource
    private WorkOrderStorageService workOrderStorageService;
    @Resource
    private ClewPreproseMaterialFeignClient clewPreproseMaterialFeignClient;

    //收款人手机号
    private static final int PAYEE_MOBILE = 0B1;
    // 发起人手机号
    private static final int RAISER_MOBILE = 0B10;
    // 线下顾问
    private static final int BD_MOBILE = 0B100;
    //紧急联系人
    private static final int EMERGENCY_MOBILE = 0B1000;
    // 待录入手机号
    private static final int INPUT_MOBILE = 0B10000;

    public CfCallOutRecord selectCallRecords(long workOrderId, int caseId, long userId, long qcWorkOrderId) {
        return selectCallRecordsOption(workOrderId, caseId, userId,
                PAYEE_MOBILE + RAISER_MOBILE + BD_MOBILE + EMERGENCY_MOBILE + INPUT_MOBILE, qcWorkOrderId);
    }

    private CfCallOutRecord selectCallRecordsOption(long workOrderId, int caseId, long userId, int number, long qcWorkOrderId) {

        // 允许caseId不传 根据工单id查询
        if (caseId <= 0 || userId <= 0) {
            WorkOrderVO workOrder = queryWorkOrder(workOrderId);
            if (Objects.isNull(workOrder) || Objects.isNull(workOrder.getHandleTime())) {
                return new CfCallOutRecord().withName(StringUtils.EMPTY);
            }
            caseId = workOrder.getCaseId();
            userId = workOrder.getOperatorId();
        }

        CrowdfundingInfo crowdfundingInfo = fundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            AuthRpcResponse<AdminUserAccountModel> model = accountClientV1.getValidUserAccountByLongId(userId);
            String userName = Optional.ofNullable(model).map(AuthRpcResponse::getResult).map(AdminUserAccountModel::getName).orElse(StringUtils.EMPTY);
            return new CfCallOutRecord().withName(userName);
        }


        int infoId = crowdfundingInfo.getId();
        String payeeMobile = crowdfundingInfo.getPayeeMobile();
        long infoUserId = crowdfundingInfo.getUserId();
        String infoUuid = crowdfundingInfo.getInfoId();

        CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByCaseId(infoId);
        if (Objects.isNull(cfInfoExt)) {
            AuthRpcResponse<AdminUserAccountModel> model = accountClientV1.getValidUserAccountByLongId(userId);
            String userName = Optional.ofNullable(model).map(AuthRpcResponse::getResult).map(AdminUserAccountModel::getName).orElse(StringUtils.EMPTY);
            return new CfCallOutRecord().withName(userName);
        }

        HashSet<String> mobiles = Sets.newHashSet();

        if ((number & PAYEE_MOBILE) > 0) {
            // 收款人手机号
            if (StringUtils.isNotBlank(payeeMobile)) {
                mobiles.add(payeeMobile);
            }
        }

        if ((number & RAISER_MOBILE) > 0) {
            // 发起人手机号
            UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(infoUserId);
            if (Objects.nonNull(userInfoModel) && StringUtils.isNotBlank(userInfoModel.getCryptoMobile()) &&
                    !Objects.equals(userInfoModel.getCryptoMobile(), payeeMobile)) {
                mobiles.add(userInfoModel.getCryptoMobile());
            }
        }

        if ((number & BD_MOBILE) > 0) {
            // 线下顾问
            String volunteerUniqueCode = cfInfoExt.getVolunteerUniqueCode();
            if (StringUtils.isNotBlank(volunteerUniqueCode)) {
                CrowdfundingVolunteer volunteer = cfGrowthtoolVolunteerFeignClient.getCfVolunteerDOByUniqueCode(volunteerUniqueCode).getData();
                if (Objects.nonNull(volunteer)) {
                    mobiles.add(volunteer.getMobile());
                }
            }
        }

        if ((number & EMERGENCY_MOBILE) > 0) {
            // 紧急联系人
            CrowdfundingInfoPayee payee = crowdfundingDelegate.getCrowdfundingInfoPayeeByInfoUuid(infoUuid);
            if (Objects.nonNull(payee) && StringUtils.isNotEmpty(payee.getEmergencyPhone())) {
                mobiles.add(payee.getEmergencyPhone());
            }
        }

        if ((number & INPUT_MOBILE) > 0) {
            // 代录入手机号
            Response<PreposeMaterialModel.MaterialInfoVo> response = clewPreproseMaterialFeignClient.selectMaterialByCaseIdForQC(caseId);
            if (response.getData() != null) {
                mobiles.add(shuidiCipher.encrypt(response.getData().getRaiseMobile()));
            }
        }

        return selectCallRecords(userId, workOrderId, caseId, qcWorkOrderId, Lists.newArrayList(mobiles));
    }

    public CfCallOutRecord selectCallRecords(long userId, long workOrderId, int caseId, long qcWorkOrderId, List<String> mobiles) {

        AuthRpcResponse<AdminUserAccountModel> model = accountClientV1.getValidUserAccountByLongId(userId);
        String userName = Optional.ofNullable(model).map(AuthRpcResponse::getResult).map(AdminUserAccountModel::getName).orElse(StringUtils.EMPTY);
        String misName = Optional.ofNullable(model).map(AuthRpcResponse::getResult).map(AdminUserAccountModel::getMis).orElse(StringUtils.EMPTY);

        int totalSize = 0;
        int validSize = 0;
        int totalDuration = 0;
        List<CfCallOutRecord.CallOutDetail> outDetails = Lists.newArrayList();

        WorkOrderVO workOrder = queryWorkOrder(workOrderId);
        if (Objects.isNull(workOrder) || Objects.isNull(workOrder.getHandleTime())) {
            return new CfCallOutRecord().withName(userName);
        }

        long taskAssignTime = workOrder.getCreateTime().getTime();
        List<CfClewCallRecordsDO> recordDos = selectCallRecords(misName, taskAssignTime, mobiles);

        for (CfClewCallRecordsDO recordsDO : recordDos) {

            if (Objects.isNull(recordsDO) || recordsDO.getCaseId() != caseId ||
                    recordsDO.getCreateTime().getTime() > workOrder.getFinishTime().getTime()) {
                continue;
            }

            ++totalSize;
            if (recordsDO.getPhoneStatus() == 200) {
                ++validSize;
            }

            CfCallOutRecord.CallOutDetail outDetail = new CfCallOutRecord.CallOutDetail();
            outDetail.setCallRecordId(recordsDO.getId());
            outDetail.setMobile(shuidiCipher.decrypt(recordsDO.getEncryptCustomerNumber()));
            outDetail.setVideoUrl(recordsDO.getCosFile());
            outDetail.setPhoneStatus(recordsDO.getPhoneStatus());
            //1001:客户挂断 1000:坐席挂断
            String endReason = recordsDO.getEndReason();
            String endReasonMsg = "";
            if (StringUtils.equals(endReason, "1001")) {
                endReasonMsg = "客户挂断";
            } else if (StringUtils.equals(endReason, "1000")) {
                endReasonMsg = "坐席挂断";
            }
            outDetail.setEndReason(endReasonMsg);

            Date answerTime = recordsDO.getAnswerTime();
            long answerTimestamp = answerTime.getTime();
            long cnoEndTimestamp = recordsDO.getCnoEndTime().getTime();
            long realTotalDuration = 0;
            if (recordsDO.getPhoneStatus() == 200) {
                realTotalDuration = (cnoEndTimestamp - answerTimestamp) / 1000;
            }
            outDetail.setDuration((int) realTotalDuration);
            outDetail.setEndTime(recordsDO.getCnoEndTime());
            outDetail.setCreateTime(recordsDO.getCnoStartTime());
            outDetail.setAnswerTime(recordsDO.getAnswerTime());

            totalDuration += outDetail.getDuration();

            outDetails.add(outDetail);
        }

        // 时间正序排序
        outDetails = outDetails.stream()
                .sorted(Comparator.comparing(CfCallOutRecord.CallOutDetail::getCreateTime))
                .collect(Collectors.toList());

        CfCallOutRecord outRecord = new CfCallOutRecord();
        outRecord.setTotalSize(totalSize);
        outRecord.setValidSize(validSize);
        outRecord.setTotalDuration(totalDuration);
        outRecord.setCallOutDetails(outDetails);
        outRecord.setName(userName);
        fillCallTag(qcWorkOrderId, outRecord);

        return outRecord;
    }

    private void fillCallTag(long workOrderId, CfCallOutRecord outRecord) {

        Response<VonStorageVO> response = workOrderStorageService.getLastByType(workOrderId, WorkOrderHelper.Storage.CALL_TAG_INFO);
        VonStorageVO data = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);

        Optional<CallTagVO> callTagOptional = VonStorageVO.getByData(data, CallTagVO.class);
        if (callTagOptional.isEmpty()) {
            return;
        }
        CallTagVO callTagVO = callTagOptional.get();
        List<CallTagBO> tags = callTagVO.getTags();
        if (CollectionUtils.isEmpty(tags)) {
            return;
        }
        Map<Integer, Integer> recordIdTagMap = tags.stream()
                .collect(Collectors.toMap(CallTagBO::getCallRecordId, CallTagBO::getCallTag));
        List<CfCallOutRecord.CallOutDetail> details = outRecord.getCallOutDetails();
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        for (CfCallOutRecord.CallOutDetail e : details) {
            e.setCallTag(recordIdTagMap.getOrDefault(e.getCallRecordId(), 0));
        }
    }

    private WorkOrderVO queryWorkOrder(long workOrderId) {
        Response<WorkOrderVO> response = workOrderClient.getWorkOrderById(workOrderId);
        return Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);
    }

    private List<CfClewCallRecordsDO> selectCallRecords(String mis, long taskAssignTime, List<String> mobiles) {

        List<CfClewCallRecordsDO> allResult = Lists.newArrayList();
        if (taskAssignTime == 0) {
            return allResult;
        }

        for (String mobile : mobiles) {
            Response<List<CfClewCallRecordsDO>> response = clewtrackTaskClient.listCallRecordByMisAndPhone(mis, mobile, null, taskAssignTime);
            List<CfClewCallRecordsDO> recordsDOList = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());
            if (CollectionUtils.isNotEmpty(recordsDOList)) {
                allResult.addAll(recordsDOList);
            }
        }

        return allResult;
    }

}
