package com.shuidihuzhu.cf.service.admin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.*;
import com.google.common.primitives.Ints;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.model.service.WxUserDetailModel;
import com.shuidihuzhu.cf.admin.util.StringCountUtils;
import com.shuidihuzhu.cf.biz.admin.AdminCfOperatingRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.client.apipure.model.report.CrowdfundingReportExtDo;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.client.feign.CaseInfoApproveStageFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingInfoSlaveDao;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminWorkOrderReportDao;
import com.shuidihuzhu.cf.dao.crowdfunding.MarkReportExtDAO;
import com.shuidihuzhu.cf.delegate.EncryptDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.delegate.finance.IFinanceDelegate;
import com.shuidihuzhu.cf.delegate.other.IMiniAppDelegate;
import com.shuidihuzhu.cf.delegate.other.IWeiXinDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.delegate.service.WxUserDetailServiceBiz;
import com.shuidihuzhu.cf.domain.CaseRaiseRiskDO;
import com.shuidihuzhu.cf.domain.caseinfo.CaseInfoApproveStageDO;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.BackgroundLogEnum;
import com.shuidihuzhu.cf.enums.admin.ApproveListOrderByWhat;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceCapitalAccountFeignClient;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceFundStateFeignClient;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceReadFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.enums.CfDrawCashCommonEnum;
import com.shuidihuzhu.cf.finance.model.drawcash.CfDrawCash;
import com.shuidihuzhu.cf.finance.model.po.CfCashPauseBooleanPo;
import com.shuidihuzhu.cf.finance.model.vo.CfDrawCashApplyVo;
import com.shuidihuzhu.cf.finance.model.vo.CfLaunchRecordVo;
import com.shuidihuzhu.cf.lion.client.risk.feign.CrowdfundingReportExtFeign;
import com.shuidihuzhu.cf.model.admin.workorder.CfFundUseDetailDO;
import com.shuidihuzhu.cf.model.admin.workorder.imageContent.CPublishImageContent;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileResult;
import com.shuidihuzhu.cf.model.clew.CfUserInvitedLaunchCaseRecordVO;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.material.AuditSuggestModifyDetail;
import com.shuidihuzhu.cf.model.crowdfunding.vo.*;
import com.shuidihuzhu.cf.model.timeline.TimeLineModel;
import com.shuidihuzhu.cf.risk.client.rpc.CfReportClient;
import com.shuidihuzhu.cf.risk.model.ReportHitStrategyRecord;
import com.shuidihuzhu.cf.service.AdminEventPublishService;
import com.shuidihuzhu.cf.service.EventCenterPublishService;
import com.shuidihuzhu.cf.service.FinanceApproveService;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkService;
import com.shuidihuzhu.cf.service.cfOperatingProfile.CfOperatingProfileSettingsBiz;
import com.shuidihuzhu.cf.service.crowdfunding.CfCaseSensitiveWordService;
import com.shuidihuzhu.cf.service.crowdfunding.report.CfReportRiskService;
import com.shuidihuzhu.cf.service.message.AdminMsgClientService;
import com.shuidihuzhu.cf.service.message.SmsRecordService;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cf.service.report.CfReportCredibleInfoService;
import com.shuidihuzhu.cf.service.workorder.WorkOrderFundUseService;
import com.shuidihuzhu.cf.util.crowdfunding.AdminAppPushTemplateUtil;
import com.shuidihuzhu.cf.vo.crowdfunding.CfCapitalDetailVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CfReportLabelVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CrowdfundingApproveCommentVo;
import com.shuidihuzhu.cf.vo.crowdfunding.WorkOrderFirstApprove;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.account.v1.accountservice.WxUserDetail;
import com.shuidihuzhu.client.baseservice.msg.v2.MsgClientV2;
import com.shuidihuzhu.client.cf.api.model.MaterialVersion;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfUserInvitedLaunchCaseRecordModel;
import com.shuidihuzhu.client.cf.clewtrack.model.ChannelRefine;
import com.shuidihuzhu.client.cf.clewtrack.param.ChannelRefineDTO;
import com.shuidihuzhu.client.cf.growthtool.client.CfChannelFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.VolunteerLevelEnum;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.BasicExample;
import com.shuidihuzhu.msg.util.DateUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.toList;

/**
 * Created by ahrievil on 2017/5/17.
 */
@SuppressWarnings("ALL")
@Slf4j
@Service
@RefreshScope
public class AdminApproveService {

    @Autowired
    private SeaAccountClientV1 seaUserAccountClientV1;
    @Autowired
    private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;
    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private AdminCrowdfundingReportBiz adminCrowdfundingReportBiz;
    @Autowired
    private AdminCrowdfundingOperationBiz adminCrowdfundingOperationBiz;
    @Autowired
    private CfRepeatUserIdRecordBiz cfRepeatUserIdRecordBiz;
    @Autowired
    private AdminCrowdfundingAuthorBiz crowdfundingAuthorBiz;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired
    private CfRefuseReasonEntityBiz cfRefuseReasonEntityBiz;
    @Autowired
    private CfRefuseReasonItemBiz cfRefuseReasonItemBiz;
    @Autowired
    private AdminCrowdfundingInfoStatusBiz adminCrowdfundingInfoStatusBiz;
    @Autowired
    private AdminCfRefuseReasonMsgBiz adminCfRefuseReasonMsgBiz;
    @Autowired
    private CfAdminOperationRecordBiz cfAdminOperationRecordBiz;
    @Autowired
    private AdminMsgClientService msgClientService;
    @Autowired
    private IFinanceDelegate financeDelegate;
    @Autowired
    private IMiniAppDelegate miniAppDelegate;

    @Autowired
    private AdminCfOperatingRecordBiz adminCfOperatingRecordBiz;
    @Autowired
    private CfCaseSensitiveWordService cfCaseSensitiveWordService;
    @Autowired
    private AdminCfInfoExtBiz adminCfInfoExtBiz;
    @Autowired
    private FinanceApproveService financeApproveService;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Resource
    private WxUserDetailServiceBiz wxUserDetailServiceBiz;

    @Resource
    private IRiskDelegate riskDelegate;
    @Autowired
    private AdminCfRepeatInfoBiz cfRepeatInfoBiz;

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Autowired
    private AdminWorkOrderReportBiz workOrderReportBiz;

    @Value("${sms.modelnum.approvefail:approvefail}")
    private String modelNum;
    @Autowired
    private AdminEventPublishService adminEventPublishService;
    @Autowired
    private CfFinanceReadFeignClient cfFinanceReadFeignClient;
    @Autowired
    private MsgClientV2 msgClientV2;

    @Autowired
    private CrowdfundingVolunteerBiz volunteerBiz;

    @Autowired
    private CfClewtrackFeignClient clewtrackFeignClient;

    @Autowired
    private CfFirstApproveOperatorBiz cfFirstApproveOperatorBiz;

    @Autowired
    private IWeiXinDelegate weiXinDelegate;
    @Autowired
    private CfFinanceCapitalAccountFeignClient cfFinanceCapitalAccountFeignClient;

    @Autowired
    private AdminCrowdfundingInfoSlaveDao crowdfundingInfoSlaveDao;
    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;
    @Autowired
    private CfChannelFeignClient channelFeignClient;
    @Autowired
    private CfMaterialVerityHistoryBiz cfMaterialVerityHistoryBiz;

    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;

    @Autowired
    private ApproveRemarkService approveRemarkService;

    @Autowired
    private ApproveRemarkOldService approveRemarkOldService;

    @Autowired
    private AdminWorkOrderReportDao adminWorkOrderReportDao;

    @Autowired
    private MarkReportExtDAO markReportExtDAO;
    @Autowired
    private CfFinanceFundStateFeignClient fundStateClient;

    @Autowired
    private CaseInfoApproveStageFeignClient caseInfoApproveStageFeignClient;

    @Autowired
    CfFundUseDetailBiz detailBiz;
    @Autowired
    private AdminCfInfoMirrorRecordService adminCfInfoMirrorRecordService;
    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private CfReportRiskService cfReportRiskService;

    @Autowired
    private CfOperatingProfileSettingsBiz cfOperatingProfileSettingsBiz;

    @Autowired
    private AdminCfReportAddTrustBiz adminCfReportAddTrustBiz;

    @Autowired
    private CfRefuseReasonCommonBiz reasonCommonBiz;
    @Autowired
    private AdminCrowdfundingAttachmentBiz attachmentBiz;
    @Autowired
    private IAdminCredibleInfoService adminCredibleInfoService;
    @Autowired
    private CfReportCredibleInfoService cfReportCredibleInfoService;

    @Autowired
    private SmsRecordService smsRecordService;
    @Autowired
    private CfReportClient cfReportClient;
    @Resource
    private MsgClientV2Service msgClientV2Service;

    @Autowired
    private EncryptDelegate encryptDelegate;

    @Autowired
    private AdminCrowdfundingCityBiz adminCrowdfundingCityBiz;

    @Autowired
    private MaskUtil maskUtil;

    private static final Pattern CHINA_PATTERN = Pattern.compile("((13[0-9])|(14[0,1,4-9])|(15[0-3,5-9])|(16[2,5,6,7])|(17[0-8])|(18[0-9])|(19[0-3,5-9]))\\d{8}");


    @Autowired
    private EventCenterPublishService eventCenterPublishService;

    @Autowired
    private CrowdfundingReportExtFeign crowdfundingReportExtFeign;

    public static final String FIELD_OPERATION = "operation";
    public static final String FIELD_COMMENTS = "comments";
    public static final String FIELD_GROUP_COMMENTS = "groupedComments";

    public Map<String, Object> getCommentVoListWithGroupComments(int caseId) {
        List<CrowdfundingApprove> list = approveRemarkOldService.listByCaseId(caseId);
        Map<String, List<CrowdfundingApproveCommentVo>> map = getCommentVoList(list);
        Map<String, Object> result = Maps.newHashMap();
        result.putAll(map);
        result.put(FIELD_GROUP_COMMENTS, approveRemarkService.listCommentByCaseIdGrouped(caseId));
        return result;
    }

    public Map<String, List<CrowdfundingApproveCommentVo>> getCommentVoList(int caseId) {
        List<CrowdfundingApprove> list = approveRemarkOldService.listByCaseId(caseId);
        Map<String, List<CrowdfundingApproveCommentVo>> map = getCommentVoList(list);
        return map;
    }

    public Map<String, List<CrowdfundingApproveCommentVo>> getCommentVoList(List<CrowdfundingApprove> commentList) {
        List<CrowdfundingApproveCommentVo> comments = Lists.newArrayList();
        List<CrowdfundingApproveCommentVo> operation = Lists.newArrayList();
        Map<String, List<CrowdfundingApproveCommentVo>> result = Maps.newHashMap();
        result.put(FIELD_COMMENTS, comments);
        result.put(FIELD_OPERATION, operation);

        Map<Integer, String> accountMap = Maps.newHashMap();
        List<Integer> operatorIds = commentList.stream()
                .map(CrowdfundingApprove::getOprid).map(Long::intValue).collect(toList());//已检查过
        AuthRpcResponse<List<AdminUserAccountModel>> rpcResponse = seaUserAccountClientV1.getUserAccountsByIds(operatorIds);
        if (rpcResponse != null && rpcResponse.getResult() != null) {
            accountMap = rpcResponse.getResult().stream()
                    .collect(Collectors.toMap(AdminUserAccountModel::getId,
                            AdminUserAccountModel::getName, (x, y) -> x));
        }
        //备注
        for (int i = commentList.size() - 1; i >= 0; i--) {
            CrowdfundingApprove comment = commentList.get(i);
            if (!StringUtils.isEmpty(comment.getComment())) {
                CrowdfundingApproveCommentVo commentVo = new CrowdfundingApproveCommentVo();
                commentVo.setComment(comment.getComment());
                commentVo.setOprtime(comment.getOprtime());
                int userId = Math.toIntExact(comment.getOprid());
                commentVo.setOprid(userId);
                accountMap.computeIfAbsent(userId, i1 -> seaUserAccountClientV1.getMisByUserId(i1).getResult());
                String name = accountMap.get(userId);
                commentVo.setOrganization(comment.getOrganization());
                commentVo.setOperator(StringUtils.isBlank(name) ? "system" : name);
                if (comment.getComment().length() >= 2 && BackgroundLogEnum.REMARK.getMessage().equals(comment.getComment().substring(0,2))) {
                    comments.add(0, commentVo);
                } else {
                    operation.add(0, commentVo);
                }
            }
        }
        return result;
    }



    public int dealWithChangeOperation(String infoUuid, CrowdfundingOperationEnum operationEnum, String reason, int operatorId) {
        return dealWithChangeOperation(infoUuid, operationEnum, reason, operatorId, 0);
    }

    public int dealWithChangeOperation(String infoUuid, CrowdfundingOperationEnum operationEnum, String reason, int operatorId, int deferContactReasonType) {
        return adminCrowdfundingOperationBiz.updateOperation(infoUuid, operatorId, operationEnum.value(), reason, deferContactReasonType);
    }

    public void updateOperationStatus(String infoId, CrowdfundingOperationEnum crowdfundingOperationEnum, String commentText, int userId) {
        CrowdfundingOperation crowdfundingOperation = this.crowdfundingOperationDelegate.getByInfoId(infoId);
        if (crowdfundingOperation == null) {
            crowdfundingOperation = new CrowdfundingOperation();
            crowdfundingOperation.setInfoId(infoId);
            crowdfundingOperation.setOperatorId(userId);
            crowdfundingOperation.setOperation(crowdfundingOperationEnum.value());
            crowdfundingOperation.setFollowType(CaseReportFollowDealStatusEnum.NO_HANDLE.getValue());
            crowdfundingOperation.setReason(commentText.length() <= 255 ? commentText : commentText.substring(0, 255));
            crowdfundingOperationDelegate.addCrowdfundingOperation(crowdfundingOperation);
        }
        if (crowdfundingOperation.getOperation() > crowdfundingOperationEnum.value()) {
            return;
        }
        CrowdfundingOperation crowdfundingOperationNew = new CrowdfundingOperation();
        crowdfundingOperationNew.setInfoId(infoId);
        crowdfundingOperationNew.setOperatorId(userId);
        crowdfundingOperationNew.setOperation(crowdfundingOperationEnum.value());
        crowdfundingOperationNew.setReason(commentText.length() <= 255 ? commentText : commentText.substring(0, 255));
        crowdfundingOperationNew.setOperateTime(new Timestamp(System.currentTimeMillis()));
        crowdfundingOperationNew.setFollowType(crowdfundingOperation.getFollowType());
        this.crowdfundingOperationDelegate.updateCrowdfundingOperation(crowdfundingOperationNew);
    }

    public void changeCaseReportStatus(CrowdfundingInfo crowdfundingInfo) {
        List<CrowdfundingReport> reports = adminCrowdfundingReportBiz.getListByInfoId(crowdfundingInfo.getId());
        if (CollectionUtils.isEmpty(reports)) {
            log.info("CrowdfundingV4ReportController addReport report is empty");
        }
        String infoUuid = crowdfundingInfo.getInfoId();
        long handlingCount = reports.stream().filter(value -> value.getDealStatus() == CaseReportDealStatus.HANDLEING.getValue()).count();
        long noHandleCount = reports.stream().filter(value -> value.getDealStatus() == CaseReportDealStatus.NO_HANDLE.getValue()).count();
        log.info("handlingCount :{}, noHandleCount：{}", handlingCount, noHandleCount);
        Long operationTime=System.currentTimeMillis();
        if (handlingCount != 0) {
            int changeResult = crowdfundingOperationDelegate.updateReportStatus(CaseReportStatusEnum.HANDLEING.getValue(), infoUuid);
            if (changeResult == 1){
                this.crowdfundingOperationDelegate.saveCfOperatingRecord(infoUuid, 0, "系统", CfOperatingRecordEnum.Type.REPORT_HANDLING,
                        CfOperatingRecordEnum.Role.SYSTEM);
            }
            //需求改动之后注释
            List<CrowdfundingReport> reportList= adminCrowdfundingReportBiz.getListByInfoId(crowdfundingInfo.getId());
            if (!CollectionUtils.isEmpty(reportList)){
                List<Integer> reportUpdateNewStatusList=reportList.stream().filter(crowdfundingReport -> crowdfundingReport.getIsNewreport()==1
                        &&crowdfundingReport.getCreateTime().getTime()<operationTime).map(CrowdfundingReport::getId).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(reportUpdateNewStatusList)){
                    adminCrowdfundingReportBiz.updateReportIsNewStatus(reportUpdateNewStatusList);
                }
                reportList=reportList.stream().filter(crowdfundingReport -> crowdfundingReport.getCaseFollowStatus()==CaseReportFollowStatusEnum.NO_HANDLE.getValue()).collect(Collectors.toList());
                List<Integer> reportIds=reportList.stream().map(CrowdfundingReport::getId).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(reportIds)){
                    adminCrowdfundingReportBiz.updateReportStatusList(reportIds);
                }
            }
        } else if (noHandleCount != 0) {
            int changeResult = crowdfundingOperationDelegate.updateReportStatus(CaseReportStatusEnum.NO_HANDLE.getValue(), infoUuid);
            if (changeResult == 1){
                this.crowdfundingOperationDelegate.saveCfOperatingRecord(infoUuid, 0, "系统", CfOperatingRecordEnum.Type.REPORT_NO_HANDLE,
                        CfOperatingRecordEnum.Role.SYSTEM);
            }
        } else {
            int changeResult = crowdfundingOperationDelegate.updateReportStatus(CaseReportStatusEnum.FINISH.getValue(), infoUuid);
            if (changeResult == 1){
                this.crowdfundingOperationDelegate.saveCfOperatingRecord(infoUuid, 0, "系统", CfOperatingRecordEnum.Type.REPORT_HANDLE_FINISH,
                        CfOperatingRecordEnum.Role.SYSTEM);
            }
        }
    }

    public void reportFromCSDChangeCaseReportStatus(CrowdfundingInfo crowdfundingInfo) {
        List<CrowdfundingReport> reports = adminCrowdfundingReportBiz.getListByInfoId(crowdfundingInfo.getId());
        if (CollectionUtils.isEmpty(reports)) {
            log.info("CrowdfundingV4ReportController addReport report is empty");
        }
        String infoUuid = crowdfundingInfo.getInfoId();
        long handleIngCount = reports.stream().filter(value -> value.getDealStatus() == CaseReportDealStatus.HANDLEING.getValue()).count();
        long noHandleCount = reports.stream().filter(value -> value.getDealStatus() == CaseReportDealStatus.NO_HANDLE.getValue()).count();
        if (handleIngCount != 0) {
            int changeResult = crowdfundingOperationDelegate.updateReportStatus(CaseReportStatusEnum.HANDLEING.getValue(), infoUuid);
            if (changeResult == 1){
                this.crowdfundingOperationDelegate.saveCfOperatingRecord(infoUuid, 0, "系统", CfOperatingRecordEnum.Type.REPORT_HANDLING,
                        CfOperatingRecordEnum.Role.SYSTEM);
            }
            //需求改动之后注释
            List<CrowdfundingReport> reportList= adminCrowdfundingReportBiz.getListByInfoId(crowdfundingInfo.getId());
            if (!CollectionUtils.isEmpty(reportList)){
                reportList=reportList.stream().filter(crowdfundingReport -> crowdfundingReport.getCaseFollowStatus()==CaseReportFollowStatusEnum.NO_HANDLE.getValue()).collect(Collectors.toList());
                List<Integer> reportIds=reportList.stream().map(CrowdfundingReport::getId).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(reportIds)){
                    adminCrowdfundingReportBiz.updateReportStatusList(reportIds);
                }
            }
        } else if (noHandleCount != 0) {
            int changeResult = crowdfundingOperationDelegate.updateReportStatus(CaseReportStatusEnum.NO_HANDLE.getValue(), infoUuid);
            if (changeResult == 1){
                this.crowdfundingOperationDelegate.saveCfOperatingRecord(infoUuid, 0, "系统", CfOperatingRecordEnum.Type.REPORT_NO_HANDLE,
                        CfOperatingRecordEnum.Role.SYSTEM);
            }
        } else {
            int changeResult = crowdfundingOperationDelegate.updateReportStatus(CaseReportStatusEnum.FINISH.getValue(), infoUuid);
            if (changeResult == 1){
                this.crowdfundingOperationDelegate.saveCfOperatingRecord(infoUuid, 0, "系统", CfOperatingRecordEnum.Type.REPORT_HANDLE_FINISH,
                        CfOperatingRecordEnum.Role.SYSTEM);
            }
        }
    }

    public void handleFields(Integer handle, CrowdfundingType crowdfundingType, List<Integer> allOperationList, BasicExample basicExample, BasicExample.Criteria criteria) {
        String currentDateTimeStr = com.shuidihuzhu.common.web.util.DateUtil.getCurrentDateTimeStr();
        criteria.andEqualTo("ci.type", crowdfundingType.value());
        // criteria.andNotLike("ci.title", "%测试%",
        // BasicExample.LikeType.WITH_BOTH_WILDCARD);
        switch (CfApproveCaseHandleEnum.getByValue(handle)) {
            case OLD_REPEAT:
                List<Integer> userId = cfRepeatUserIdRecordBiz.selectUserId();
                if (userId != null && userId.size() != 0) {
                    criteria.andIn("ci.user_id", userId);
                }
                criteria.andIn("cfo.operation", allOperationList);
                criteria.andGreaterThan("end_time", currentDateTimeStr);
                break;
            case GARBAGE:
                criteria.andLessThan("target_amount", 100 * 100);
                criteria.andLessThanOrEqualTo("amount", 50 * 100);
                criteria.andGreaterThan("end_time", currentDateTimeStr);
                criteria.andIn("cfo.operation", allOperationList);
                break;
            case DELAY_APPROVE:
                criteria.andEqualTo("cfo.operation", CrowdfundingOperationEnum.DEFER_APPROVE.value());
                break;
            case DELAY_CONTACT:
                criteria.andEqualTo("cfo.operation", CrowdfundingOperationEnum.DEFER_CONTACT.value());
                break;
            case NO_LONGER_HANDLE:
                criteria.andEqualTo("cfo.operation", CrowdfundingOperationEnum.NEVER_PROCESSING.value());
                break;
            case REPEAT_CASE:
                criteria.andIn("cri.repeat_summary", AdminCfRepeatView.repeatSummaryCodeSet);
                criteria.andIn("cfo.operation", allOperationList);
                criteria.andGreaterThan("ci.end_time", currentDateTimeStr);
                break;
            case TWICE_CASE:
                criteria.andIn("cri.repeat_summary", AdminCfRepeatView.twiceSummaryCodeSet);
                criteria.andIn("cfo.operation", allOperationList);
                criteria.andGreaterThan("ci.end_time", currentDateTimeStr);
                break;
            default:
                criteria.andIn("cfo.operation", allOperationList);
                break;
        }
        basicExample.setOrderByClause("ci.user_id, " + ApproveListOrderByWhat.CREATE_TIME.getValue());
    }

    public BasicExample.Criteria findInText(BasicExample.Criteria criteria, String title, Integer id, String name,
                                            String mobile, String userId) {
        criteria.andLike("title", title, BasicExample.LikeType.WITH_BOTH_WILDCARD);
        if (id != null && id > 0) {
            criteria.andEqualTo("ci.id", id);
        }
        if (!StringUtils.isEmpty(name)) {
            List<CrowdfundingAuthor> authors = crowdfundingAuthorBiz.getByName(name);
            if (!CollectionUtils.isEmpty(authors)) {
                criteria.andIn("ci.id", authors.stream().mapToInt(CrowdfundingAuthor::getCrowdfundingId).boxed()
                        .collect(toList()));
            }
        }
        if (StringUtils.isNotBlank(userId)) {
            long caseUserId = 0;
            try {
                caseUserId = Long.valueOf(userId);
            } catch (NumberFormatException e) {
                log.warn("caseUserId valueOf generate java.lang.NumberFormatException", userId);
            } catch (Exception e){
                log.error("AdminApproveService caseUserId valueOf error",e);
            }
            if (caseUserId <= 0) {
                caseUserId = shuidiCipher.decryptUserId(userId);
            }
            criteria.andEqualTo("ci.user_id", caseUserId);
        } else if (!StringUtils.isEmpty(mobile)) {
            UserInfoModel userInfoModel = this.userInfoServiceBiz.getUserInfoByMobile(mobile);
            if (userInfoModel != null && StringUtils.isNotBlank(userInfoModel.getCryptoMobile())) {
                criteria.andEqualTo("ci.user_id", userInfoModel.getUserId());
            } else {
                criteria.andEqualTo("cie.crypto_register_mobile", oldShuidiCipher.aesEncrypt(mobile));
            }
        }
        return criteria;
    }

    /**
     * 新的驳回方法兼容旧数据和客户端逻辑
     *
     * @param reasonList 前端返回的驳回数据ID
     * @param content 填写的驳回备注
     * @param crowdfundingInfo 案例
     * @return 发短信和模板消息需要的文本
     */
    public String refuseReasonHandle(List<Integer> reasonList, String content, CrowdfundingInfo crowdfundingInfo,
                                     List<AuditSuggestModifyDetail> suggestModify) {
        List<CfRefuseReasonEntity> cfRefuseReasonEntities = cfRefuseReasonEntityBiz.selectByIds(reasonList);
        Map<Integer, String> idContentMap = cfRefuseReasonEntities.stream().collect(Collectors.toMap(CfRefuseReasonEntity::getId, CfRefuseReasonEntity::getContent));
        Map<Integer, List<CfRefuseReasonItem>> reasonItemsMap = this.foundEntityItems(cfRefuseReasonEntities);
        Set<Integer> rejectDataType = Sets.newHashSet();
        reasonItemsMap.keySet().forEach(value -> {
            List<CfRefuseReasonItem> cfRefuseReasonItems = reasonItemsMap.get(value);
            cfRefuseReasonItems.forEach(val -> rejectDataType.add(val.getType()));
        });

        //驳回发起人身份证有问题，直接在这里发消息
        if(rejectDataType.contains(CrowdfundingInfoDataStatusTypeEnum.ID_VERIFY.getCode())){
            crowdfundingDelegate.updateCrowdfundingIdCaseStatus(crowdfundingInfo.getId(), CrowdfundingIdCaseStatusEnum.VERIFY_FAIL);
            crowdfundingDelegate.sendIdCaseFailMsg(crowdfundingInfo);
        }

        // 图文驳回 打印当前图文风险log
        if(rejectDataType.contains(CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT.getCode())){
            logBasicInfoRisk(crowdfundingInfo);
        }

        // 驳回材料
        this.updateDataStatusByInfoUuidAndType(crowdfundingInfo.getInfoId(),CrowdfundingInfoStatusEnum.REJECTED, rejectDataType);

        // 创建短信和模板消息需要的文本
        StringBuilder comment = new StringBuilder();
        List<String> reasonText = cfRefuseReasonEntities.stream().map(CfRefuseReasonEntity::getContent).collect(toList());
        Set<Integer> reasonIds = Sets.newHashSet(idContentMap.keySet());
        if (StringUtils.isNotBlank(content)) {
            reasonText.add(content);
            reasonIds.add(0);
        }
        //使用次数plus one
        cfRefuseReasonEntityBiz.frequencyPlusOne(reasonIds);

        for (int i = 0; i < reasonText.size(); i++) {
            comment.append(i + 1).append(": ").append(reasonText.get(i));
            if (i != reasonText.size() - 1) {
                comment.append("\n");
            }
        }

        //驳回操作记录入库
        try {
            this.saveRefuseMsg(reasonList, crowdfundingInfo, suggestModify);
        } catch (Exception e) {
            log.error("AdminApproveService fixRefuseReasonOldData saveRefuseMsg error!", e);
        }

        return comment.toString();
    }

    /**
     * 打印图文风险日志
     * @param crowdfundingInfo
     */
    private void logBasicInfoRisk(CrowdfundingInfo crowdfundingInfo) {
        try {
            int caseId = crowdfundingInfo.getId();
            String infoUuid = crowdfundingInfo.getInfoId();
            CaseRaiseRiskDO caseRaiseRiskDO = riskDelegate.getByInfoUuid(infoUuid);
            log.info("baseInfoRefuse caseId: {}, infoUuid: {}, riskLevel: {}, title: {}, content: {}",
                    caseId,
                    infoUuid,
                    caseRaiseRiskDO.getRiskLevel(),
                    crowdfundingInfo.getTitle(),
                    crowdfundingInfo.getContent());
            TimeLineModel timeLineModel = TimeLineModel.create(caseId)
                    .description("图文驳回-风险数据")
                    .param("riskLevel", caseRaiseRiskDO.getRiskLevel())
                    .param("data", caseRaiseRiskDO)
                    .build();
            crowdfundingOperationDelegate.saveTimeLineModel(timeLineModel);
        } catch (Exception e) {
            log.error("{}, {}", crowdfundingInfo, e);
        }
    }

    public Map<Integer, List<CfRefuseReasonItem>> foundEntityItems(List<CfRefuseReasonEntity> cfRefuseReasonEntities) {
        log.info("AdminApproveService foundEntityItems cfRefuseReasonEntities:{}", cfRefuseReasonEntities);
        if (cfRefuseReasonEntities == null) {
            return Maps.newHashMap();
        }
        List<Integer> collect = cfRefuseReasonEntities.stream().map(CfRefuseReasonEntity::getId).collect(toList());
        List<CfRefuseReasonEntity> cfRefuseReasonItemMaps = cfRefuseReasonEntityBiz.selectByReasonIds(Sets.newHashSet(collect), 0);
        return cfRefuseReasonItemMaps.stream().collect(Collectors.toMap(CfRefuseReasonEntity::getId, value -> {
            List<Integer> transform = Lists.transform(Splitter.on(",").splitToList(value.getItemIds()), Integer::parseInt);
            return cfRefuseReasonItemBiz.selectByIds(Sets.newHashSet(transform));
        }));
    }

    private void saveRefuseMsg(List<Integer> reasonList, CrowdfundingInfo crowdfundingInfo, List<AuditSuggestModifyDetail> suggestModify) {
        List<CfRefuseReasonEntity> cfRefuseReasonItemMaps = cfRefuseReasonEntityBiz.selectByReasonIds(Sets.newHashSet(reasonList), 0);
        cfRefuseReasonItemMaps.forEach(value -> {
            List<Integer> transform = Lists.transform(Splitter.on(",").splitToList(value.getItemIds()), Integer::valueOf);
            List<CfRefuseReasonItem> cfRefuseReasonItems = cfRefuseReasonItemBiz.selectByIds(Sets.newHashSet(transform));
            value.setItemList(cfRefuseReasonItems);
        });
        ArrayListMultimap<Integer, Integer> multimap = ArrayListMultimap.create();
        cfRefuseReasonItemMaps.forEach(value -> multimap.putAll(value.getId(), value.getItemList().stream().map(CfRefuseReasonItem::getId).collect(toList())));
        TreeMultimap<Integer, Integer> inverse = Multimaps.invertFrom(multimap, TreeMultimap.create());
        //item -> reasonIds
        Map<Integer, Collection<Integer>> itemReasonIdMap = inverse.asMap();
        List<CfCommitVerifyItem> dataType = Lists.newArrayList();
        Set<Integer> itemIds = itemReasonIdMap.keySet();
        List<CfRefuseReasonItem> cfRefuseReasonItems = cfRefuseReasonItemBiz.selectByIds(itemIds);
        //dataType -> item
        ImmutableListMultimap<Integer, CfRefuseReasonItem> digitsByLength= Multimaps.index(cfRefuseReasonItems, CfRefuseReasonItem::getType);
        digitsByLength.keySet().forEach(value -> {
            List<CfRefuseReasonItem> cfRefuseReasonItems1 = digitsByLength.get(value);
            cfRefuseReasonItems1.forEach(val -> val.setReasonIds(Lists.newArrayList(itemReasonIdMap.get(val.getId()))));
            dataType.add(new CfCommitVerifyItem(value, cfRefuseReasonItems1));
        });
        String infoUuid = crowdfundingInfo.getInfoId();
        List<CfRefuseReasonMsg> cfRefuseReasonMsgs = Lists.newArrayList();
        CrowdfundingOperation operation = adminCrowdfundingOperationBiz.getByInfoId(crowdfundingInfo.getInfoId());
        Integer refuseCount = operation.getRefuseCount();
        Map<Integer, AuditSuggestModifyDetail> suggestMapping = getDataTypeTSuggest(suggestModify);
        dataType.forEach(value -> {
            int typeData = value.getId();
            List<CfRefuseReasonItem> cfRefuseReasonItems1 = value.getCfRefuseReasonItems();
            List<String> i = Lists.newArrayList();
            List<CfRefuseReasonItemRecord> jsonList = Lists.newArrayList();
            cfRefuseReasonItems1.forEach(val -> {
                List<Integer> reasonIds = val.getReasonIds();
                i.addAll(reasonIds.stream().map(String::valueOf).collect(Collectors.toList()));
                List<CfRefuseReasonEntity> cfRefuseReasonEntities = cfRefuseReasonEntityBiz.selectByIds(reasonIds);
                Map<Integer, String> reason = cfRefuseReasonEntities.stream().collect(Collectors.toMap(CfRefuseReasonEntity::getId, CfRefuseReasonEntity::getContent));
                jsonList.add(new CfRefuseReasonItemRecord(val.getId(), reason));
            });
            String reasonJoin = String.join(",", i);
            CfRefuseReasonMsg cfRefuseReasonMsg = new CfRefuseReasonMsg(infoUuid, typeData, reasonJoin, String.join(",", Lists.transform(Lists.newArrayList(itemReasonIdMap.keySet()), String::valueOf)), JSON.toJSONString(jsonList), getSuggestMsg(typeData, suggestMapping));
            cfRefuseReasonMsg.setRefuseCount(refuseCount + 1);
            cfRefuseReasonMsgs.add(cfRefuseReasonMsg);
        });
        adminCfRefuseReasonMsgBiz.insertList(cfRefuseReasonMsgs);
    }

    private Map<Integer, AuditSuggestModifyDetail> getDataTypeTSuggest(List<AuditSuggestModifyDetail> suggestModify) {
        Map<Integer, AuditSuggestModifyDetail> result = Maps.newHashMap();
        if (CollectionUtils.isEmpty(suggestModify)) {
            return result;
        }
        for (AuditSuggestModifyDetail modifyDetail : suggestModify) {
            result.put(modifyDetail.getMaterialId(), modifyDetail);
        }
        return result;
    }
    private String getSuggestMsg(int dataType, Map<Integer, AuditSuggestModifyDetail> suggestModify) {
        if (MapUtils.isEmpty(suggestModify)) {
            return "";
        }
        AuditSuggestModifyDetail modify = suggestModify.get(dataType);

        return modify != null ? JSON.toJSONString(modify) : "";
    }

    public static void main(String[] args) {
        System.out.println(JSON.parseObject("", AuditSuggestModifyDetail.class));
        System.out.println(JSON.parseObject(null, AuditSuggestModifyDetail.class));
    }

    public void findByRefuseCount(int refuseCountHandle, BasicExample.Criteria criteria) {
        if (refuseCountHandle == CfSimpleTrueOrFalseEnum.TRUE.value()) {
            criteria.andLessThanOrEqualTo("cfo.refuse_count", 3);
            criteria.andGreaterThanOrEqualTo("cfo.refuse_count", 1);// 驳回次数小于等于3且大于等于1
        } else if (refuseCountHandle == CfSimpleTrueOrFalseEnum.FALSE.value()) {
            criteria.andGreaterThan("cfo.refuse_count", 3);// 驳回次数大于3
        } else if (refuseCountHandle == 2) {
            criteria.andEqualTo("cfo.refuse_count", 0);
        }
    }

    public void contactHandle(int isContact, BasicExample.Criteria criteria) {
        if (isContact == CfSimpleTrueOrFalseEnum.FALSE.value()) {
            criteria.andEqualTo("cfo.call_status", CfCallOutConditionTypeEnum.DEFAULT.getValue());
        } else if (isContact == CfSimpleTrueOrFalseEnum.TRUE.value()) {
            criteria.andNotEqualTo("cfo.call_status", CfCallOutConditionTypeEnum.DEFAULT.getValue());
        }
    }

    public void finishHandle(int finished, BasicExample.Criteria criteria) {
        if (0 == finished) {
            criteria.andGreaterThan("endTime", new Date());
        } else {
            criteria.andLessThanOrEqualTo("endTime", new Date());
        }
    }

    //优化getCrowdfundingInfoView 方法主要为把循环查询剔除
    public List<AdminCrowdfundingInfoView> getCrowdfundingInfoViewList(List<CrowdfundingInfoVo> crowdfundingInfoVos,
                                                                       Map<String, CfCapitalAccount> cfCapitalAccountMap){
        //创建容器
        List<AdminCrowdfundingInfoView> crowdfundingInfoViewList = Lists.newArrayListWithCapacity(crowdfundingInfoVos.size());
        //获取一个userInfoModelMap
        List<Long> userIds = crowdfundingInfoVos.stream().map(CrowdfundingInfoVo::getUserId).collect(toList());
        log.info("getCrowdfundingInfoViewList crowdfundingInfoVos userId:{}", JSONObject.toJSONString(userIds));
        Map<Long, String> thirdTypeMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(userIds)){
            thirdTypeMap = mapFilter(userIds);
        }

        //获取AutorMap
        List<Integer> infoIds =  crowdfundingInfoVos.stream().map(CrowdfundingInfoVo::getId).collect(toList());
        log.info("getCrowdfundingInfoViewList crowdfundingInfoVos infoIds:{}", JSONObject.toJSONString(infoIds));
        Map<Integer, CrowdfundingAuthor> crowdfundingAuthorMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(infoIds)){
            crowdfundingAuthorMap = crowdfundingAuthorBiz.getByInfoIdList(infoIds);
        }

        //获取cf_admin_operating_recordMap
        List<String> infoUuids = crowdfundingInfoVos.stream().map(CrowdfundingInfoVo::getInfoId).collect(toList());
        Map<String, CfOperationRecord> cfOperationRecordMap = Maps.newHashMap();
        Map<Integer, AdminUserAccountModel> adminUserAccountModelMap = Maps.newHashMap();
        if(!CollectionUtils.isEmpty(infoUuids)){
            List<CfOperationRecord> cfOperationRecords = cfAdminOperationRecordBiz.selectOpByInfoIds(infoUuids);
            //AdminUserAccountModel
            List<Integer> operatorIds = cfOperationRecords.stream().map(CfOperationRecord::getOperatorId).collect(toList());
            if (!CollectionUtils.isEmpty(operatorIds)){
                List<AdminUserAccountModel> adminUserAccountModels = seaUserAccountClientV1.getUserAccountsByIds(operatorIds).getResult();
                if (!CollectionUtils.isEmpty(adminUserAccountModels)){
                    adminUserAccountModelMap = adminUserAccountModels.stream().collect(Collectors.
                            toMap(AdminUserAccountModel::getId, Function.identity()));
                }
            }
            cfOperationRecordMap = cfOperationRecords.stream().collect(Collectors.
                    toMap(CfOperationRecord::getInfoId, Function.identity()));
        }

        //图片map
        Map<Integer, List<CrowdfundingAttachmentVo>> crowdfundingAttachmentMap =
                crowdfundingDelegate.getByInfoIdList(infoIds, AttachmentTypeEnum.ATTACH_CF);


        //举报状态map
        Map<String, Integer> reportMap = adminCrowdfundingOperationBiz.selectUuidReportStatus(infoUuids);

        Map<Integer, CfUserInvitedLaunchCaseRecordModel> caseRecordMap = this.getCaseChannelRecordMap(infoIds);

        //Cf_Info_Stat map
        Map<Integer, CfInfoStat> cfInfoStatMap =  crowdfundingDelegate.mapByIds(infoIds);
        for (CrowdfundingInfoVo crowdfundingInfoVo : crowdfundingInfoVos){
            AdminCrowdfundingInfoView crowdfundingInfoView = new AdminCrowdfundingInfoView();
            BeanUtils.copyProperties(crowdfundingInfoVo, crowdfundingInfoView);

            /** 通过总驳回次数和用户驳回次数算审核驳回次数 */
            int refuseCount = ObjectUtils.defaultIfNull(crowdfundingInfoView.getRefuseCount(),0);
            int userRefuseCount = ObjectUtils.defaultIfNull(crowdfundingInfoView.getUserRefuseCount(),0);
            int allRefuseCount = refuseCount + userRefuseCount;
            crowdfundingInfoView.setAllRefuseCount(allRefuseCount);

            crowdfundingInfoView.setTargetAmount(crowdfundingInfoVo.getTargetAmount() / 100);
            crowdfundingInfoView.setTargetAmountInDouble(Double.parseDouble(MoneyUtil.buildBalance(crowdfundingInfoVo.getTargetAmount())));
            //写入cfCapitalAccount中的amount信息
            CfCapitalAccount cfCapitalAccount = cfCapitalAccountMap.get(crowdfundingInfoVo.getInfoId());
            if(cfCapitalAccount != null) {
                crowdfundingInfoView.setAmount(Double.valueOf(MoneyUtil.buildBalance(cfCapitalAccount.getFundsAmountInFenToSea())).intValue());
                crowdfundingInfoView.setAmountInDouble(Double.parseDouble(MoneyUtil.buildBalance(cfCapitalAccount.getFundsAmountInFenToSea())));
            } else {
                crowdfundingInfoView.setAmount(crowdfundingInfoVo.getAmount() / 100);
                crowdfundingInfoView.setAmountInDouble(Double.parseDouble(MoneyUtil.buildBalance(crowdfundingInfoVo.getAmount())));
            }
            crowdfundingInfoView.setApplicantMobile("**");
            //此处也需要优化为Map 二期做
            CrowdfundingApprove lastCrowdfundingApproveWithComment = crowdfundingOperationDelegate
                    .getLastWithCommentByCrowdfundingId(crowdfundingInfoVo.getId());
            if (null != lastCrowdfundingApproveWithComment) {
                crowdfundingInfoView.setLastComment(lastCrowdfundingApproveWithComment.getComment());
            }
            //写入发起人
            CrowdfundingAuthor crowdfundingAuthor = crowdfundingAuthorMap.get(crowdfundingInfoVo.getId());
            if (crowdfundingAuthor != null) {
                crowdfundingInfoView.setAuthorName(crowdfundingAuthor.getName());
            }
            //优化二期
            //crowdfundingInfoView.setHasApplyMultiTime(crowdfundingInfoBiz.hasApplyMultiTime(crowdfundingInfoVo));
            if (crowdfundingInfoView.getEndTime() != null && crowdfundingInfoView.getEndTime().compareTo(new Date()) < 0) {
                crowdfundingInfoView.setFinished(true);
            }
            //敏感词
            Set<String> sensitiveWords = cfCaseSensitiveWordService.get(crowdfundingInfoView.getContent() + crowdfundingInfoView.getTitle());
            log.info("sensitiveWords size:{}", cfCaseSensitiveWordService.getWordSize());
            if (!CollectionUtils.isEmpty(sensitiveWords)) {
                crowdfundingInfoView.setHasSensitiveWord(true);
                crowdfundingInfoView.setSensitiveWords(sensitiveWords);
            }
            // 判断该案例是否被举报,并给isReport字段赋值  方法体需要优化
            this.isReportOrNotByMap(crowdfundingInfoView, reportMap);
            int shareCount = getInfoFromStatByMap(crowdfundingInfoView, cfInfoStatMap);

            crowdfundingInfoView.setShareCount(shareCount);
            Integer callStatus = crowdfundingInfoVo.getCallStatus();
            if (callStatus != null) {
                if (callStatus == 0) {
                    crowdfundingInfoView.setIsContact(0);
                } else {
                    crowdfundingInfoView.setIsContact(1);
                }
            }
            CfOperationRecord cfOperationRecord = cfOperationRecordMap.get(crowdfundingInfoVo.getInfoId());
            if (cfOperationRecord != null) {
                AdminUserAccountModel adminUserAccountModel = adminUserAccountModelMap.get(cfOperationRecord.getOperatorId());
                if (adminUserAccountModel != null) {
                    crowdfundingInfoView.setOperator(adminUserAccountModel.getMis());
                }
            }
            //计算案例文案文字数量和图片数量
            List<CrowdfundingAttachmentVo> attachmentsByType = crowdfundingAttachmentMap.get(crowdfundingInfoVo.getId());
            String content = crowdfundingInfoVo.getContent();
            crowdfundingInfoView.setCharacterCount(StringCountUtils.countHanZi(content));
            crowdfundingInfoView.setImgageCount(attachmentsByType == null ? 0 :attachmentsByType.size());
            crowdfundingInfoView.setChannel(formatChannel(crowdfundingInfoVo.getChannel()));

            crowdfundingInfoView.setListGuideUserLaunchChannel(getGuideUserLaunchChannel(caseRecordMap.get(crowdfundingInfoVo.getId())));

            crowdfundingInfoView.setThirdType(thirdTypeMap.get(crowdfundingInfoView.getUserId()));

            // 案例重复或二次发起的情况
            crowdfundingInfoView.setRepeatStatusList(cfRepeatInfoBiz.selectRepeatStatusByCaseId(crowdfundingInfoView.getId(), cfCapitalAccountMap.get(crowdfundingInfoVo.getInfoId())));

            crowdfundingInfoViewList.add(crowdfundingInfoView);
        }
        return  crowdfundingInfoViewList;

    }


    public AdminCrowdfundingInfoView getCrowdfundingInfoView(CrowdfundingInfoVo crowdfundingInfoVo, CfCapitalAccount cfCapitalAccount) {

        String curContent = crowdfundingInfoVo.getContent();

        Response<CaseInfoApproveStageDO> stageInfoResp = caseInfoApproveStageFeignClient.getStageInfo(crowdfundingInfoVo.getId());
        CaseInfoApproveStageDO stageInfo = stageInfoResp.getData();
        if (stageInfo != null) {
            crowdfundingInfoVo.setTitle(stageInfo.getTitle());
            crowdfundingInfoVo.setContent(stageInfo.getContent());
        }

        AdminCrowdfundingInfoView crowdfundingInfoView = new AdminCrowdfundingInfoView();
        BeanUtils.copyProperties(crowdfundingInfoVo, crowdfundingInfoView);

        crowdfundingInfoView.setCurContent(curContent);
        crowdfundingInfoView.setAllRefuseCount(crowdfundingInfoVo.getRefuseCount() + crowdfundingInfoVo.getUserRefuseCount());

        crowdfundingInfoView.setTargetAmount(crowdfundingInfoVo.getTargetAmount() / 100);
        crowdfundingInfoView.setTargetAmountInDouble(Double.parseDouble(MoneyUtil.buildBalance(crowdfundingInfoVo.getTargetAmount())));

        if(cfCapitalAccount != null) {
            List<CfFundUseDetailDO> fundUseDetailDOList = detailBiz.listAuditDetailByCaseId(crowdfundingInfoVo.getId());
            boolean needMarkUnCommitBill = WorkOrderFundUseService.isNeedMarkUnCommitBill(fundUseDetailDOList, cfCapitalAccount);
            crowdfundingInfoView.setNeedMarkUnCommitBill(needMarkUnCommitBill);
            long sum = fundUseDetailDOList.stream().mapToLong(CfFundUseDetailDO::getBillMoney).sum();
            double unCommitBills = Double.parseDouble(MoneyUtil.buildBalance(cfCapitalAccount.getDrawCashAmount() - sum - cfCapitalAccount.getAllRefundAmount()));
            if (unCommitBills < 0) {
                unCommitBills = 0.00;
            }
            crowdfundingInfoView.setUnCommitBillAmount(unCommitBills);

            crowdfundingInfoView.setAmount(cfCapitalAccount.getFundsAmountInFenToSea() / 100);
            crowdfundingInfoView.setAmountInDouble(Double.parseDouble(MoneyUtil.buildBalance(cfCapitalAccount.getFundsAmountInFenToSea())));
            //案例有效余额
            crowdfundingInfoView.setSurplusAmountInDouble(Double.parseDouble(MoneyUtil.buildBalance(cfCapitalAccount.getSurplusAmount())));
            crowdfundingInfoView.setSuccessDrawCashAmount(MoneyUtil.buildBalance(cfCapitalAccount.getDrawCashAmount()));
        } else {
            long surplusAmount = crowdfundingInfoVo.getAmount();
            FeignResponse<CfDrawCash> response = cfFinanceReadFeignClient.getByInfoUuid(crowdfundingInfoVo.getInfoId());
            CfDrawCash cfDrawCash = response.getData();
            if (cfDrawCash != null && cfDrawCash.getDrawStatus() == CfDrawCashConstant.DrawStatus.HANDLE_SUCCESS.getCode()) {
                surplusAmount = surplusAmount - cfDrawCash.getAmount();
            }
            crowdfundingInfoView.setAmount(crowdfundingInfoVo.getAmount() / 100);
            crowdfundingInfoView.setAmountInDouble(Double.parseDouble(MoneyUtil.buildBalance(crowdfundingInfoVo.getAmount())));
            crowdfundingInfoView.setSurplusAmountInDouble(Double.parseDouble(MoneyUtil.buildBalance(surplusAmount)));
        }
        //需要优化一个userInfoModelMap
        UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(crowdfundingInfoVo.getUserId());
        if (userInfoModel != null && StringUtils.isNotBlank(userInfoModel.getCryptoMobile())) {
            crowdfundingInfoView.setApplicantMobileMask(maskUtil.buildByEncryptPhone(userInfoModel.getCryptoMobile()));
            crowdfundingInfoView.setApplicantMobile(null);
        } else {
            if(StringUtils.isNotBlank(crowdfundingInfoVo.getCryptoRegisterMobile())) {
                // 解密再加密，防止出现密文不一样的情况，影响展示
                String registerMobile = shuidiCipher.decrypt(crowdfundingInfoVo.getCryptoRegisterMobile());
                crowdfundingInfoView.setApplicantMobileMask(maskUtil.buildByDecryptPhone(registerMobile));
                crowdfundingInfoView.setApplicantMobile(null);
            }
        }
        log.info("日志脱敏crowdfundingInfoView:{}",crowdfundingInfoView);
        //此处也需要优化为Map
        CrowdfundingApprove lastCrowdfundingApproveWithComment = crowdfundingOperationDelegate
                .getLastWithCommentByCrowdfundingId(crowdfundingInfoVo.getId());
        if (null != lastCrowdfundingApproveWithComment) {
            crowdfundingInfoView.setLastComment(lastCrowdfundingApproveWithComment.getComment());
        }
        //需要优化
        CrowdfundingAuthor crowdfundingAuthor = crowdfundingAuthorBiz.get(crowdfundingInfoVo.getId());
        if (crowdfundingAuthor != null) {
            crowdfundingInfoView.setAuthorName(crowdfundingAuthor.getName());
        }
        //优化
        //crowdfundingInfoView.setHasApplyMultiTime(crowdfundingInfoBiz.hasApplyMultiTime(crowdfundingInfoVo));
        if (crowdfundingInfoView.getEndTime() != null && crowdfundingInfoView.getEndTime().compareTo(new Date()) < 0) {
            crowdfundingInfoView.setFinished(true);
        }
        //敏感词
        Set<String> sensitiveWords = cfCaseSensitiveWordService.get(crowdfundingInfoView.getContent() + crowdfundingInfoView.getTitle());
        log.info("sensitiveWords size:{}", cfCaseSensitiveWordService.getWordSize());
        if (!CollectionUtils.isEmpty(sensitiveWords)) {
            crowdfundingInfoView.setHasSensitiveWord(true);
            crowdfundingInfoView.setSensitiveWords(sensitiveWords);
        }
        // 判断该案例是否被举报,并给isReport字段赋值
        this.isReportOrNot(crowdfundingInfoView);
        int shareCount = getInfoFromStat(crowdfundingInfoView);
        crowdfundingInfoView.setShareCount(shareCount);
        Integer callStatus = crowdfundingInfoVo.getCallStatus();
        if (callStatus != null) {
            if (callStatus == 0) {
                crowdfundingInfoView.setIsContact(0);
            } else {
                crowdfundingInfoView.setIsContact(1);
            }
        }
        CfOperationRecord cfOperationRecord = cfAdminOperationRecordBiz.selectOpByInfoId(crowdfundingInfoVo.getInfoId());
        if (cfOperationRecord != null) {
            AdminUserAccountModel adminUserAccountModel = seaUserAccountClientV1.getValidUserAccountById(cfOperationRecord.getOperatorId()).getResult();
            if (adminUserAccountModel != null) {
                crowdfundingInfoView.setOperator(adminUserAccountModel.getMis());
            }
        }
        //计算案例文案文字数量和图片数量
        int id = crowdfundingInfoVo.getId();
        int imageCount = 0;
        if (stageInfo == null) {
            List<CrowdfundingAttachmentVo> attachmentsByType = crowdfundingDelegate.getAttachmentsByType(id, AttachmentTypeEnum.ATTACH_CF);
            imageCount = CollectionUtils.size(attachmentsByType);
        }else {
            String[] images = StringUtils.split(stageInfo.getImages(), ",");
            imageCount = ArrayUtils.getLength(images);
        }

        String content = crowdfundingInfoVo.getContent();
        int count = 0;
        if (!StringUtils.isBlank(content)){
            char[] chars = content.toCharArray();
            for (char aChar : chars) {
                if (Character.toString(aChar).matches("[\\u4E00-\\u9FA5]+")) {
                    count++;
                }
            }
        }
        // 不暴漏payeeMobile明文
        crowdfundingInfoView.setPayeeMobileMask(maskUtil.buildByDecryptPhoneWithOldEncrypt(crowdfundingInfoView.getPayeeMobile()));
        crowdfundingInfoView.setPayeeMobile(null);
        crowdfundingInfoView.setPayeeIdCardMask(maskUtil.buildByDecryptStrAndType(crowdfundingInfoView.getPayeeIdCard(), DesensitizeEnum.IDCARD));
        crowdfundingInfoView.setPayeeIdCard(null);
        crowdfundingInfoView.setPayeeBankCardMask(maskUtil.buildByDecryptStrAndType(crowdfundingInfoView.getPayeeBankCard(), DesensitizeEnum.BANKNO));
        crowdfundingInfoView.setPayeeBankCard(null);

        crowdfundingInfoView.setCharacterCount(count);
        crowdfundingInfoView.setImgageCount(imageCount);
        Map<Long, String> thirdTypeMap  = mapFilter(Lists.newArrayList(crowdfundingInfoView.getUserId()));
        crowdfundingInfoView.setThirdType(thirdTypeMap.get(crowdfundingInfoView.getUserId()));
        crowdfundingInfoView.setChannel(formatChannel(crowdfundingInfoView.getChannel()));
        crowdfundingInfoView.setListGuideUserLaunchChannel(getGuideUserLaunchChannel(crowdfundingInfoVo.getId()));

        //兼容老逻辑 防止上线过程中出现错误 todo 后续删除
        crowdfundingInfoView.setSourceChannel(crowdfundingInfoView.getChannel());
        // 案例重复或二次发起的情况
        crowdfundingInfoView.setRepeatStatusList(cfRepeatInfoBiz.selectRepeatStatusByCaseId(crowdfundingInfoView.getId()));

        return crowdfundingInfoView;
    }

    public int getInfoFromStat(AdminCrowdfundingView crowdfundingInfoView) {
        int shareCount = 0;
        try {
            CfInfoStat cfInfoStat = crowdfundingDelegate.getById(crowdfundingInfoView.getId());
            if (cfInfoStat != null) {
                shareCount = cfInfoStat.getShareCount();
                crowdfundingInfoView.setDonationCount(cfInfoStat.getDonationCount());
                crowdfundingInfoView.setVerificationCount(cfInfoStat.getVerifyUserCount());
            } // 填充转发次数,证实次数
        } catch (Exception e) {
            log.error("getShareCount error!", e, shareCount);
        }
        return shareCount;
    }

    public int getInfoFromStatByMap(AdminCrowdfundingView crowdfundingInfoView, Map<Integer, CfInfoStat> cfInfoStatMap) {
        int shareCount = 0;
        try {
            CfInfoStat cfInfoStat = cfInfoStatMap.get(crowdfundingInfoView.getInfoId());
            if (cfInfoStat != null) {
                shareCount = cfInfoStat.getShareCount();
                crowdfundingInfoView.setDonationCount(cfInfoStat.getDonationCount());
                crowdfundingInfoView.setVerificationCount(cfInfoStat.getVerifyUserCount());
            } // 填充转发次数,证实次数
        } catch (Exception e) {
            log.error("getShareCount error!", e, shareCount);
        }
        return shareCount;
    }

    public void isReportOrNot(AdminCrowdfundingView crowdfundingInfoView) {
        AdminCrowdfundingOperation adminCrowdfundingOperation = adminCrowdfundingOperationBiz.selectByInfoUuid(crowdfundingInfoView.getInfoId());
        Integer reportStatus = null;
        if (adminCrowdfundingOperation != null) {
            reportStatus = adminCrowdfundingOperation.getReportStatus();
        }
        if (reportStatus != null) {
            if (reportStatus == CaseReportStatusEnum.NO_REPORT.getValue()) {
            }
            crowdfundingInfoView.setIsReport(0);
        } else {
            crowdfundingInfoView.setIsReport(1);
        }
    }

    public void isReportOrNotByMap(AdminCrowdfundingView crowdfundingInfoView, Map<String, Integer> reportMap) {
        Integer reportStatus = reportMap.get(crowdfundingInfoView.getInfoId());
        if (reportStatus != null) {
            if (reportStatus == CaseReportStatusEnum.NO_REPORT.getValue()) {
                crowdfundingInfoView.setIsReport(0);
            } else {
                crowdfundingInfoView.setIsReport(1);
            }
        }
    }

    public void sendSms(String comment, long userId) {
        UserInfoModel userAccount = userInfoServiceBiz.getUserInfoByUserId(userId);
        if (userAccount != null) {
            Map<String, Map<Integer, String>> mapMap = Maps.newHashMap();
            Map<Integer, String> params = Maps.newHashMap();
            params.put(1, comment);
            mapMap.put(userAccount.getCryptoMobile(), params);
            msgClientV2Service.sendSmsParamsMsg(modelNum, mapMap, true);
        }
    }

    public boolean setCapitalData(String infoUuid, Map<String, Object> result, int crowdfundingId) {
        if (StringUtils.isBlank(infoUuid)) {
            return true;
        }

        List<CfCapitalDetailVo> list = Lists.newArrayList();
        Response<CfRefund> cfRefundResponse = financeDelegate.getCfRefundByInfoUuid(infoUuid);
        CfRefund cfRefund = cfRefundResponse.getData();
        if (cfRefund != null) {
            int applyStatus = cfRefund.getApplyStatus();
            int refundStatus = cfRefund.getRefundStatus();
            result.put("refundData", ImmutableMap.of("applyStatus", applyStatus, "status", refundStatus));
            if (applyStatus > NewCfRefundConstant.ApplyStatus.UNSUBMIT.getCode()) {
                CfCapitalDetailVo cfCapitalDetailVo = new CfCapitalDetailVo();
                cfCapitalDetailVo.setApplyByRefund(NewCfRefundConstant.ApplyStatus.SUBMIT_APPROVE_PENDING.getCode(),
                        cfRefund.getReason(), cfRefund.getApplyTime());
                list.add(cfCapitalDetailVo);
            }
            if (applyStatus == NewCfRefundConstant.ApplyStatus.APPROVE_SUCCESS.getCode()) {
                CfCapitalDetailVo cfCapitalDetailVo = new CfCapitalDetailVo();
                cfCapitalDetailVo.setApplyByRefund(NewCfRefundConstant.ApplyStatus.APPROVE_SUCCESS.getCode(),
                        cfRefund.getReason(), cfRefund.getApplyAuditTime());
                list.add(cfCapitalDetailVo);
            }
            if (refundStatus > NewCfRefundConstant.RefundStatus.HANDLING.getCode()) {
                CfCapitalDetailVo cfCapitalDetailVo = new CfCapitalDetailVo();
                cfCapitalDetailVo.setStatus(CfCapitalDetailVo.refund, refundStatus, "", cfRefund.getLastModified());
                list.add(cfCapitalDetailVo);
            }
        }
        Response<CfDrawCashApplyVo> cashApplyVoResponse = financeDelegate.getApplyInfo(crowdfundingId);
        if (cfRefundResponse.notOk() || cashApplyVoResponse.notOk()) {
            log.error("资金服务请求失败");
            return false;
        }
        CfDrawCashApplyVo drawCashApplyVo = cashApplyVoResponse.getData();
        Integer applyStatus = CfDrawCashConstant.ApplyStatus.EMPTY_VALUE.getCode();
        Integer drawStatus = CfDrawCashCommonEnum.DrawStatus.UN_BUILD.getCode();
        if(null != drawCashApplyVo) {
            applyStatus = drawCashApplyVo.getApplyStatus();
            drawStatus = drawCashApplyVo.getDrawStatus();
        }

//        案例资金处于暂停状态
        boolean pause = true;
//        案例提现打款处于暂停
        boolean drawCashPause = true;
//        案例退款处于暂停
        boolean refundPause = true;
        try {
            FeignResponse<Response<CfCashPauseBooleanPo>> feignResponse = cfFinanceReadFeignClient.getPauseStatus(infoUuid);
            if (feignResponse.ok() && feignResponse.getData().ok()
                    && null != feignResponse.getData().getData()) {
                pause = feignResponse.getData().getData().isPause();
                drawCashPause = feignResponse.getData().getData().isDrawCashPause();
                refundPause = feignResponse.getData().getData().isRefundPause();
            }
        } catch (Exception e) {
            log.error("",e);
        }

        Map drawCashDataMap = Maps.newHashMap();
        drawCashDataMap.put("applyStatus", applyStatus);
        drawCashDataMap.put("status", drawStatus);
        // todo: 这个看是怎么用的
        drawCashDataMap.put("manualStatus", 0);

        // 1 暂停 0 2  未暂停
        drawCashDataMap.put("operatorType", drawCashPause ? 1 : 0);
        drawCashDataMap.put("operatorReason", "operatorReason------在使用吗");
        drawCashDataMap.put("pause",pause);
        drawCashDataMap.put("drawCashPause", drawCashPause);
        drawCashDataMap.put("refundPause",refundPause);
        result.put("drawCashData",drawCashDataMap);

        if (drawCashApplyVo != null) {
            // 保留原代码，不知道为何 审核通过的单独写一条
            CfDrawCashApplyRecord cfDrawCashApplyRecord= bulidDrawCashApplyRecord(drawCashApplyVo);
            if (applyStatus > CfDrawCashConstant.ApplyStatus.UNSUBMIT.getCode()) {
                CfCapitalDetailVo cfCapitalDetailVo = new CfCapitalDetailVo();
                cfCapitalDetailVo.setApplyByDrawCash(
                        CfDrawCashConstant.ApplyStatus.SUBMIT_APPROVE_PENDING.getCode(), cfDrawCashApplyRecord,
                        drawCashApplyVo.getApplyTime());
                list.add(cfCapitalDetailVo);
            }
            if (applyStatus == CfDrawCashConstant.ApplyStatus.APPROVE_SUCCESS.getCode()) {
                CfCapitalDetailVo cfCapitalDetailVo = new CfCapitalDetailVo();
                cfCapitalDetailVo.setApplyByDrawCash(CfDrawCashConstant.ApplyStatus.APPROVE_SUCCESS.getCode(),
                        cfDrawCashApplyRecord, drawCashApplyVo.getApplyAuditTime());
                list.add(cfCapitalDetailVo);
            }

            if (drawStatus > CfDrawCashConstant.DrawStatus.HANDLING.getCode()) {
                String errorMsg = "";
                Response<List<CfLaunchRecordVo>> listResponse = financeDelegate.getLaunchRecord(crowdfundingId);
                List<CfLaunchRecordVo> launchRecordVoList = listResponse.getData();
                if (CollectionUtils.isNotEmpty(launchRecordVoList)) {
                    List<String> msgList = launchRecordVoList.stream()
                            .filter(item -> item.getStatus() == CfDrawCashCommonEnum.DrawStatus.HANDLE_FAILED.UN_HANDLE.getCode())
                            .map(CfLaunchRecordVo::getResMsg)
                            .collect(toList());
                    errorMsg = Joiner.on(",").join(msgList);
                }
                CfCapitalDetailVo cfCapitalDetailVo = new CfCapitalDetailVo();
                cfCapitalDetailVo.setStatus(CfCapitalDetailVo.draw, drawStatus, errorMsg,
                        drawCashApplyVo.getFinishTime());
                list.add(cfCapitalDetailVo);
            }

        }
        list.stream().sorted(comparing(CfCapitalDetailVo::getBusinessTime));
        result.put("cfCapitalDetail", list);

        return true;
    }

    // 获取已经打款成功的金额
    public boolean getSuccessDrawCashAmount(String infoUuid, Map<String, Object> result) {
        if (StringUtils.isBlank(infoUuid)) {
            result.put("successDrawCashAmount", 0);
            return true;
        }
        Response<CfCapitalAccount> capitalAccountResponse = financeDelegate.getCfCapitalAccountByInfoUuid(infoUuid);
        if (capitalAccountResponse.notOk()) {
            log.error("cfdrawcash 记录获取失败");
            return false;
        }

        CfCapitalAccount capitalAccount = capitalAccountResponse.getData();
        if (capitalAccount == null) {
            result.put("successDrawCashAmoun", 0);
            return true;
        }
        // 未考虑返还金额--- 存在问题，打款金额会比筹款金额多
        long successAmoung = capitalAccount.getDrawCashAmount();
        log.info("successAmoung:{}",successAmoung);
        result.put("successDrawCashAmount", MoneyUtil.buildBalance(successAmoung));
        return true;
    }


    public CrowdfundingAuthor aesDecryptIdcardAndMobile (CrowdfundingInfoVo crowdfundingInfoVo) {
        try {
            crowdfundingInfoVo.setPayeeIdCard(shuidiCipher.decrypt(crowdfundingInfoVo.getPayeeIdCard()));
        } catch (Exception e) {
            crowdfundingInfoVo.setPayeeIdCard(crowdfundingInfoVo.getPayeeIdCard());
        }
        try {
            crowdfundingInfoVo.setPayeeMobile(shuidiCipher.decrypt(crowdfundingInfoVo.getPayeeMobile()));
        } catch (Exception e) {
            crowdfundingInfoVo.setPayeeMobile(crowdfundingInfoVo.getPayeeMobile());
        }

        try {
            crowdfundingInfoVo.setPayeeBankCard(shuidiCipher.decrypt(crowdfundingInfoVo.getPayeeBankCard()));
        } catch (Exception e) {
            crowdfundingInfoVo.setPayeeBankCard(crowdfundingInfoVo.getPayeeBankCard());
        }
        CrowdfundingAuthor crowdfundingAuthor = crowdfundingAuthorBiz.get(crowdfundingInfoVo.getId());
        if(crowdfundingAuthor == null){
            return null;
        }
        try {
            String idCard = shuidiCipher.decrypt(crowdfundingAuthor.getCryptoIdCard());
            crowdfundingAuthor.setIdCardMask(maskUtil.buildByDecryptStrAndType(idCard, DesensitizeEnum.IDCARD));
            crowdfundingAuthor.setIdCard(idCard);
            if(crowdfundingAuthor.getIdType() != UserIdentityType.identity) {
                crowdfundingAuthor.getIdCardMask().setMaskNumber(crowdfundingAuthor.getIdCard());
            }
        } catch (Exception e) {
            crowdfundingAuthor.setIdCard(crowdfundingAuthor.getCryptoIdCard());
        }
        try {
            crowdfundingAuthor.setPhone(shuidiCipher.decrypt(crowdfundingAuthor.getCryptoPhone()));
            crowdfundingAuthor.setPhoneMask(maskUtil.buildByDecryptPhone(crowdfundingAuthor.getPhone()));
        } catch (Exception e) {
            crowdfundingAuthor.setPhone(crowdfundingAuthor.getCryptoPhone());
        }

        return crowdfundingAuthor;
    }

    public CrowdfundingOperationVo setOperatorName(CrowdfundingOperation crowdfundingOperation) {
        CrowdfundingOperationVo crowdfundingOperationVo = new CrowdfundingOperationVo();
        if (crowdfundingOperation != null) {
            BeanUtils.copyProperties(crowdfundingOperation, crowdfundingOperationVo);
            if (crowdfundingOperation.getOperatorId() != null) {
                AdminUserAccountModel adminUserAccountModel = seaUserAccountClientV1.getValidUserAccountById(crowdfundingOperation.getOperatorId()).getResult();
                if (null != adminUserAccountModel) {
                    crowdfundingOperationVo.setOperatorName(adminUserAccountModel.getMis());
                }
            }
        }
        return crowdfundingOperationVo;
    }

    public List<CfReportView> getReportList(int caseId) {
        try {
            log.info("getReportList {}", caseId);
            List<CrowdfundingReport> reportList = adminCrowdfundingReportBiz.getListByInfoId(caseId);

            return trans2CfReportView(caseId, reportList);
        } catch (Exception e) {
            log.error("no such report massage",e);
            return Lists.newArrayList();
        }
    }

    @NotNull
    public List<CfReportView> trans2CfReportView(int caseId, List<CrowdfundingReport> reportList) {
        List<CfReportView> crowdfundingReportList = Lists.newArrayList();
        if(CollectionUtils.isEmpty(reportList)){
            log.info("getReportList no report caseId={}",caseId);
            return crowdfundingReportList;
        }

        List<Integer> reportIds = reportList.stream().map(r-> r.getId()).collect(Collectors.toList());
        Map<Integer, CrowdfundingReportExtDo> reporterImageMap = Optional.ofNullable(crowdfundingReportExtFeign.getCrowdfundingReportExtByReportIds(reportIds))
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(CrowdfundingReportExtDo::getReportId, Function.identity()));
        //查询举报命中的策略
        Response<List<ReportHitStrategyRecord>> response = cfReportClient.getStrategyRecord(reportIds);

        List<CrowdfundingReportLabel> labels = adminCrowdfundingReportBiz.getReportLabels(reportIds);
        Map<Long,List<String>> map = labels.stream().collect(Collectors.groupingBy(CrowdfundingReportLabel::getReportId,Collectors.mapping(
                r -> StringUtils.isBlank(r.getReportComment()) ?
                        CfReportTypeEnum.getDescFromCode(r.getReportLabel()) : r.getReportComment(),Collectors.toList())));
        List<CrowdfundingReportLabel> modifyLabels = adminCrowdfundingReportBiz.getReportLabelsModify(reportIds);
        Map<Long,List<String>> modifyLabelsMap = modifyLabels.stream().collect(Collectors.groupingBy(CrowdfundingReportLabel::getReportId,Collectors.mapping(
                r -> CfReportTypeEnum.getDescFromCode(r.getReportLabel()),Collectors.toList())));
        Map<Long,List<Integer>> modifyLabelsCodeMap = modifyLabels.stream()
                .collect(Collectors.groupingBy(CrowdfundingReportLabel::getReportId,Collectors.mapping(
                r -> r.getReportLabel(),Collectors.toList())));
        Map<Long,List<String>> modifyLabelsCommentMap = modifyLabels.stream().collect(Collectors.groupingBy(CrowdfundingReportLabel::getReportId,Collectors.mapping(
                r -> r.getReportComment(),Collectors.toList())));
        List<CfOperatingProfileResult> cfOperatingProfileResults =
                cfOperatingProfileSettingsBiz.queryAllSettingsByType(2);
        reportList.forEach(value -> {
            AdminMarkReportExtDO reportExtDO = markReportExtDAO.queryByReportId(value.getId());
            String marker = StringUtils.EMPTY;
            if(Objects.nonNull(reportExtDO)){
                boolean flag = StringUtils.isEmpty(reportExtDO.getMarkerOrg()) && StringUtils.isEmpty(reportExtDO.getMarkerName()) && StringUtils.equals("系统", reportExtDO.getReporterName());
                if (flag){
                    marker = "--" + reportExtDO.getReporterName();
                }else{
                    marker = "--" + reportExtDO.getMarkerOrg() + ":" + reportExtDO.getMarkerName();
                }
            }


            int operatorId = value.getOperatorId();
            Map<Integer, String> accountMap = Maps.newHashMap();
            accountMap.computeIfAbsent(operatorId, i1 -> seaUserAccountClientV1.getMisByUserId(i1).getResult());
            CfReportView cfReportView = new CfReportView();
            BeanUtils.copyProperties(value, cfReportView);
            cfReportView.setContact(encryptDelegate.decrypt(value.getEncryptContact()));

            AdminWorkReportMap reportMap = workOrderReportBiz.getAdminWorkReportMapByReportId(value.getId());
            if (reportMap !=null){
                long workOrderId = reportMap.getWorkOrderId();
                List<AdminWorkOrderReport> reports = adminWorkOrderReportDao.getByWorkOrderIds(Lists.newArrayList(workOrderId));
                cfReportView.setWorkId(workOrderId);
                cfReportView.setOperator(accountMap.get(operatorId));
                cfReportView.setReportWorkOrderHandleResult(CollectionUtils.isNotEmpty(reports) ? reports.get(0).getDealResult() : 0);
            }
            if (cfReportView.getWorkId() == 0) {
                fillOperatorAndWorkId(value, cfReportView);
            }
            cfReportView.setReportTypes(map.get(Long.valueOf(value.getId())));
            cfReportView.setModifyReportTypes(modifyLabelsMap.get(Long.valueOf(value.getId())));
            cfReportView.setModifyReportCodeTypes(modifyLabelsCodeMap.get(Long.valueOf(value.getId())));
            cfReportView.setModifyReportComments(modifyLabelsCommentMap.get(Long.valueOf(value.getId())));
            cfReportView.setContent(cfReportView.getContent() + marker);
            cfReportView.setIdentity(StringUtils.isNotEmpty(value.getIdentity()) ? shuidiCipher.decrypt(value.getIdentity()) : "");
            long userId = value.getUserId();
            UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(userId);
            log.debug("userInfoModel:{}", userInfoModel);
            if (userInfoModel == null) {
                log.warn("userInfoModel is null");
            }
            cfReportView.setNickName(Optional.ofNullable(userInfoModel).map(UserInfoModel::getNickname).orElse(""));
            int reportNums = 0;
            if (userId > 0) {
                reportNums = adminCrowdfundingReportBiz.countByUserId(userId);
            }
            cfReportView.setReportNums(reportNums);
            List<CfReportLabelVo> cfReportLabelVos = cfReportRiskService.getLabelList(caseId, value, cfOperatingProfileResults);
            cfReportView.setRiskLabel(CollectionUtils.isEmpty(cfReportLabelVos) ? null :
                    JSONArray.toJSONString(cfReportRiskService.getLabelList(caseId, value, cfOperatingProfileResults)));

            if (response.ok() && CollectionUtils.isNotEmpty(response.getData())) {
                List<ReportHitStrategyRecord> reportHitStrategyRecords = response.getData();
                Set<String> strategys = reportHitStrategyRecords.stream()
                        .filter(reportHitStrategyRecord -> reportHitStrategyRecord.getReportId() == value.getId())
                        .map(ReportHitStrategyRecord::getStrategy).collect(Collectors.toSet());
                cfReportView.setHitStrategy(Lists.newArrayList(strategys));
            }

            cfReportView.setContactMask(maskUtil.buildByEncryptPhone(value.getEncryptContact()));
            cfReportView.setContact(null);

            CrowdfundingReportExtDo reporterImage = reporterImageMap.getOrDefault(value.getId(), null);

            if (reporterImage != null && StringUtils.isNotEmpty(reporterImage.getReportImage())) {
                Set<Integer> reporterImages = new HashSet<>();
                for (String s : reporterImage.getReportImage().split(",")) {
                    reporterImages.add(Integer.parseInt(s));
                }
                cfReportView.setReporterImageSet(reporterImages);
                fillVolunteerInfo(cfReportView, reporterImage);
            }
            crowdfundingReportList.add(cfReportView);
        });
        return crowdfundingReportList;
    }

    private void fillVolunteerInfo(CfReportView cfReportView, CrowdfundingReportExtDo reporterImage) {
        cfReportView.setSuspectVolunteerLevel(reporterImage.getSuspectVolunteerLevel());
        cfReportView.setSuspectVolunteerRoleDesc(Optional.ofNullable(VolunteerLevelEnum.parseByLevel(reporterImage.getSuspectVolunteerLevel())).map(VolunteerLevelEnum::getDesc).orElse(""));
        cfReportView.setInvalidVolunteerName(reporterImage.getInvalidVolunteerName());
        if (StringUtils.isNotEmpty(reporterImage.getSuspectVolunteerIdentity())){
            NumberMaskVo identityMask = new NumberMaskVo();
            identityMask.setEncryptNumber(reporterImage.getSuspectVolunteerIdentity());
            cfReportView.setSuspectVolunteerIdentity(identityMask);
        }

        if (StringUtils.isNotEmpty(reporterImage.getSuspectVolunteerMobile())) {
            NumberMaskVo mobileMask = new NumberMaskVo();
            mobileMask.setEncryptNumber(reporterImage.getSuspectVolunteerIdentity());
            cfReportView.setSuspectVolunteerMobile(mobileMask);
        }

        if (StringUtils.isNotEmpty(reporterImage.getInvalidVolunteerMobile())) {
            NumberMaskVo mobileMask = new NumberMaskVo();
            mobileMask.setEncryptNumber(reporterImage.getInvalidVolunteerMobile());
            cfReportView.setInvalidVolunteerMobile(mobileMask);
        }

        if (StringUtils.isNotEmpty(reporterImage.getInvalidVolunteerIdentity())) {
            NumberMaskVo identityMask = new NumberMaskVo();
            identityMask.setEncryptNumber(reporterImage.getInvalidVolunteerIdentity());
            cfReportView.setInvalidVolunteerIdentity(identityMask);
        }
    }

    private void fillOperatorAndWorkId(CrowdfundingReport value, CfReportView cfReportView) {
        cfReportView.setOperator("");
        final int newOperatorId = value.getNewOperatorId();
        if (newOperatorId > 0) {
            cfReportView.setOperator(getNameByOperatorId(newOperatorId));
            return;
        }
        if (value.getHandleStatus() == CfReportHandleStatus.HANDLED.getKey() && newOperatorId == 0){
            cfReportView.setOperator("系统");
            return;
        }
        final WorkOrderVO lastOrder = getLastWorkOrderByCaseId(value.getActivityId());
        if (lastOrder == null) {
            return;
        }
        cfReportView.setWorkId(lastOrder.getWorkOrderId());
        final long orderOperator = lastOrder.getOperatorId();
        if (orderOperator == 0) {
            return;
        }
        cfReportView.setOperator(getNameByOperatorId(orderOperator));
    }

    private String getNameByOperatorId(long newOperatorId) {
        AdminUserAccountModel accountModel = seaUserAccountClientV1.getValidUserAccountById(Math.toIntExact(newOperatorId)).getResult();
        if (accountModel == null) {
            return "";
        }
        return accountModel.getName();
    }

    @Nullable
    private WorkOrderVO getLastWorkOrderByCaseId(int caseId) {
        final Response<WorkOrderVO> resp = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, WorkOrderType.REPORT_TYPES);
        if (NewResponseUtil.isNotOk(resp)) {
            return null;
        }
        final WorkOrderVO lastOrder = resp.getData();
        return lastOrder;
    }

    private long getNewReportWorkOrderId(CrowdfundingReport value) {
        try {
            Response<List<WorkOrderExt>> listResponse = cfWorkOrderClient.queryExtByCase(value.getActivityId(), WorkOrderType.REPORT_TYPES,
                    OrderExtName.reportId.getName(), String.valueOf(value.getId()));
            if (listResponse.ok() && listResponse.getData() != null) {
                List<WorkOrderExt> workOrderExts = listResponse.getData();
                if (CollectionUtils.isNotEmpty(workOrderExts)) {
                    WorkOrderExt workOrderExt = workOrderExts.get(0);
                    return workOrderExt.getWorkOrderId();
                }
            }
        } catch (Exception e) {
            log.error("AdminApproveService.getNewReportWorkOrderId get new work order id eroor", e);
        }
        return 0;
    }

    public CfUserReportBrief getCfUserReportBrief(long reportUserId) {
        CfUserReportBrief cfUserReportBrief = new CfUserReportBrief();
        List<CfUserReportBrief.DetailGroupByCaseId> details = Lists.newArrayList();
        List<CrowdfundingReport> crowdfundingReports = adminCrowdfundingReportBiz.listByUserId(reportUserId);
        // 解密crowdfundingReports中的contact
        crowdfundingReports.forEach(item -> item.setEncryptContact(shuidiCipher.decrypt(item.getEncryptContact())));
        String contact = crowdfundingReports.stream().findAny().map(CrowdfundingReport::getEncryptContact).orElse("");
        long reportCrowdNums = crowdfundingReports.stream().mapToInt(CrowdfundingReport::getActivityId).distinct().count();


        //排序
        Ordering<CrowdfundingReport> ordering = Ordering.natural().nullsFirst().onResultOf(CrowdfundingReport::getCreateTime);
        Map<Integer, List<CfUserReportBrief.Detail>> sortedMap = crowdfundingReports.stream()
                .sorted(ordering)
                .map(item->{
                    CfUserReportBrief.Detail detail = CfUserReportBrief.createDetail(item);
                    detail.setMobileMask(maskUtil.buildByDecryptPhone(item.getEncryptContact()));
                    detail.setMobile(null);
                    return detail;
                })
                .collect(Collectors.groupingBy(CfUserReportBrief.Detail::getActivityId));

        List<CrowdfundingInfo> infos = crowdfundingInfoBiz.selectByCaseIdList(Lists.newArrayList(sortedMap.keySet()));
        Map<Integer, CrowdfundingInfo> infoMap = Maps.uniqueIndex(infos, CrowdfundingInfo::getId);

        crowdfundingReports.stream()
                .sorted(ordering)
                .map(CrowdfundingReport::getActivityId)
                .distinct()
                .forEach(item -> {
                    CfUserReportBrief.DetailGroupByCaseId detail = new CfUserReportBrief.DetailGroupByCaseId();
                    detail.setActivityId(item);
                    detail.setReportDetails(sortedMap.get(item));
                    String infoUuId = Optional.ofNullable(infoMap.get(item)).map(CrowdfundingInfo::getInfoId).orElse("");
                    detail.setInfoUuId(infoUuId);
                    details.add(detail);
                });


        UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(reportUserId);
        log.debug("userInfoModel:{}", userInfoModel);
        if (userInfoModel == null) {
            log.warn("userInfoModel is null");
        }
        cfUserReportBrief.setNickName(Optional.ofNullable(userInfoModel).map(UserInfoModel::getNickname).orElse(""));
        cfUserReportBrief.setReportNums(crowdfundingReports.size());
        cfUserReportBrief.setContactMask(maskUtil.buildByDecryptPhone(contact));
        cfUserReportBrief.setContact(null);
        cfUserReportBrief.setReportCrowdNums(Math.toIntExact(reportCrowdNums));
        cfUserReportBrief.setDetails(details);
        return cfUserReportBrief;
    }


    public List<AdminRefuseReasonVo> getRejectDetail(CrowdfundingInfo crowdfundingInfo) {
        if (crowdfundingInfo.getStatus() != CrowdfundingStatus.CROWDFUNDING_STATED) {
            List<CfRefuseReasonMsg> refuseReasonMsgList = adminCfRefuseReasonMsgBiz.selectByInfoUuid(crowdfundingInfo.getInfoId());
            if (CollectionUtils.isEmpty(refuseReasonMsgList)) {
                return Lists.newArrayList();
            }
            Timestamp maxTime = refuseReasonMsgList.stream().max(Comparator.comparing(CfRefuseReasonMsg::getCreateTime)).map(CfRefuseReasonMsg::getCreateTime).get();
            List<CfRefuseReasonMsg> cfRefuseReasonMsgList = refuseReasonMsgList.stream().filter(val -> val.getCreateTime().getTime() == maxTime.getTime()).collect(Collectors.toList());
            return cfRefuseReasonMsgList.stream().map(val -> {
                AdminRefuseReasonVo adminRefuseReasonVo = new AdminRefuseReasonVo();
                adminRefuseReasonVo.setType(val.getType());
                String itemReason = val.getItemReason();
                if (StringUtils.isNotBlank(itemReason)) {
                    List<CfRefuseReasonItemRecord> list = JSON.parseArray(itemReason, CfRefuseReasonItemRecord.class);
                    List<String> refuseReason = list.stream().map(value -> value.getReason().values()).flatMap(Collection::stream).distinct().collect(Collectors.toList());
                    adminRefuseReasonVo.setRefuseReason(refuseReason);
                    adminRefuseReasonVo.setReasonIds(reasonCommonBiz.getIdListSplitterByComma(val.getReasonIds()));
                    fillSuggestUserModfiy(adminRefuseReasonVo, val.getSuggestModify());
                }
                log.info("adminRefuseReasonVo:{}", adminRefuseReasonVo);
                return adminRefuseReasonVo;
            }).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    private void fillSuggestUserModfiy(AdminRefuseReasonVo adminRefuseReasonVo, String suggestModfiy) {

        try {
            if (StringUtils.isBlank(suggestModfiy)) {
                return;
            }
            AuditSuggestModifyDetail suggest = JSON.parseObject(suggestModfiy, AuditSuggestModifyDetail.class);
            if (suggest == null) {
                return;
            }

            adminRefuseReasonVo.setSuggestUserModify(suggest.getSuggestUserModify());
            adminRefuseReasonVo.setSuggestModifyIds(suggest.getAllModifyIds());

        } catch (Exception e) {
            log.warn("解析建议用户修改位置异常.param:{}", suggestModfiy, e);
        }

    }

    public void saveOperatingRecord(int userId, String infoUuid, CfOperatingRecordEnum.Type type) {
        try {
            AdminUserAccountModel adminUserAccountModel = seaUserAccountClientV1.getValidUserAccountById(userId).getResult();
            String userName = adminUserAccountModel.getName();
            this.crowdfundingOperationDelegate.saveCfOperatingRecord(infoUuid, userId, userName, type,
                    CfOperatingRecordEnum.Role.OPERATOR);
        } catch (Exception e) {
            log.error("", e);
        }
    }
    public void pushRefuseAppMsg(CrowdfundingInfo crowdfundingInfo) {
        try {
            //消息逻辑在cf-api中，发送的是modelNum=UCY8452的app消息
            miniAppDelegate.newNeedModifiedSuccess(crowdfundingInfo);
        } catch (Exception e) {
            log.error("图文/资料待修改APP推送ERROR", e);
        }
    }

    public int deleteRefuseMsgByInfoUuid(String infoUuid) {
        return adminCfRefuseReasonMsgBiz.deleteByInfoUuid(infoUuid);
    }

    public List<Integer> getNormalOperationList() {
        List<Integer> allOperationList = Arrays.stream(CrowdfundingOperationEnum.values()).map(CrowdfundingOperationEnum::value).collect(toList());
        List<Integer> operationList = Ints.asList(CrowdfundingOperationEnum.DEFER_APPROVE.value(),
                CrowdfundingOperationEnum.NEVER_PROCESSING.value(), CrowdfundingOperationEnum.DEFER_CONTACT.value());
        allOperationList.removeAll(operationList);
        return allOperationList;
    }

    public List<CfRefuseReasonEntity> getRefuseEntityList(List<Integer> refuseIdList) {
        return cfRefuseReasonEntityBiz.selectByIds(refuseIdList);
    }

    public List<CfRefuseReasonItem> getRefuseReasonItem(Set<Integer> set) {
        return cfRefuseReasonItemBiz.selectByIds(set);
    }

    public int updateDataStatusByInfoUuidAndType(String infoUuid, CrowdfundingInfoStatusEnum crowdfundingInfoStatusEnum, Set<Integer> set) {
        return adminCrowdfundingInfoStatusBiz.updateByTypes(infoUuid, crowdfundingInfoStatusEnum, set);
    }

    public int deleteRefuseByInfoUuidAndTypes(String infoUuid, Set<Integer> set) {
        return adminCfRefuseReasonMsgBiz.deleteByInfoUuidAndTypes(infoUuid, set);
    }

    public void casePassHandle(CrowdfundingInfo info) {
        this.deleteRefuseMsgByInfoUuid(info.getInfoId());
        riskDelegate.onCaseInfoPassed(info.getInfoId(), info);
    }

    public void caseRefuseHandle(CrowdfundingInfo info, String comment) {
        //材审重构
        MaterialVersion materialVersion = new MaterialVersion();
        materialVersion.setVersion(info.getMaterialPlanId());
        if(materialVersion.isMaterial100()){
            eventCenterPublishService.sendMaterialReviewOverrule(info,comment);
            this.pushRefuseAppMsg(info);
            return;
        }
        try {
            this.sendSms(comment, info.getUserId());
        } catch (Exception e) {
            log.error("sendFirstCallOutMsg error", e);
        }
        UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(info.getUserId());
        if (userInfoModel != null  && info.getType() == CrowdfundingType.SERIOUS_ILLNESS.value()) {

            long userId = info.getUserId();
            send129(comment, userInfoModel, userId);
        }
    }


    public void send129(String comment, UserInfoModel userInfoModel, long userId) {

        String modelNum0 = "template129";
        Map<Long, Map<Integer, String>> mapMap = Maps.newHashMap();

        HashMap<Integer, String> params = Maps.newHashMap();
        params.put(1, AdminAppPushTemplateUtil.limitComment(StringUtils.replace(comment, "\n", "\\n")));
        params.put(2, new SimpleDateFormat(DateUtil.YYYY_MM_DDHHMM).format(new Date()));
        mapMap.put(userInfoModel.getUserId(), params);

        msgClientV2Service.sendWxParamsMsg(modelNum0, mapMap);
    }

    public List<CrowdfundingInfoStatus> getCrowdfundingInfoStatusInfo(String infoUuid) {
        if (StringUtils.isBlank(infoUuid)) {
            return Lists.newArrayList();
        }
        return adminCrowdfundingInfoStatusBiz.getByInfoUuid(infoUuid);
    }

    /**
     * 重复案例判定标准
     * 当没有患者姓名时，判定重复的标准为：
     * 当一个用户<user_id>发起且没有结束的案例数量>=2时，则该用户所有发起且没有结束的案例都标记为重复
     * 当有患者姓名时：
     * 当同一个患者姓名发起且没有结束的案例数量>=2时，则所有用户所有发起且没有结束的案例都标记为重复
     * @param caseList 案例列表
     * @return 案例和是否重复发起的map
     */
    public Map<Integer, Boolean> isCaseRepeat(List<CrowdfundingInfo> caseList) {
        List<Integer> caseIdList = caseList.stream().map(CrowdfundingInfo::getId).distinct().collect(Collectors.toList());
        //查看所有的患者信息
        List<CrowdfundingAuthor> crowdfundingAuthors = crowdfundingAuthorBiz.selectByCaseIdList(caseIdList);
        List<Integer> haveAuthorCaseList = crowdfundingAuthors.stream().map(CrowdfundingAuthor::getCrowdfundingId).collect(Collectors.toList());
        Map<Integer, CrowdfundingAuthor> authorMap = crowdfundingAuthors.stream().collect(Collectors.toMap(CrowdfundingAuthor::getCrowdfundingId, Function.identity()));
        //分组查看案例有没有患者信息
        Map<Boolean, List<CrowdfundingInfo>> judgeHaveAuthor = caseList.stream().collect(Collectors.partitioningBy(val -> haveAuthorCaseList.contains(val.getId())));
        List<CrowdfundingInfo> notHaveAuthor = judgeHaveAuthor.get(false);
        List<CrowdfundingInfo> haveAuthor = judgeHaveAuthor.get(true);
        //没有患者信息按照userId查询案例并分组
        List<Long> userIdListNotHaveAuthor = notHaveAuthor.stream().map(CrowdfundingInfo::getUserId).collect(Collectors.toList());
        List<CrowdfundingInfo> crowdfundingInfos = crowdfundingInfoBiz.selectByUserIdWhereNotEnd(userIdListNotHaveAuthor);
        Map<Long, List<CrowdfundingInfo>> integerListMap = crowdfundingInfos.stream().collect(Collectors.groupingBy(CrowdfundingInfo::getUserId));
        Map<Integer, Boolean> result = Maps.newHashMap();
        notHaveAuthor.forEach(val -> {
            //查看该userId下面是否未结束的案例多于2个
            List<CrowdfundingInfo> crowdfundingInfos1 = integerListMap.get(val.getUserId());
            if (!CollectionUtils.isEmpty(crowdfundingInfos1) && crowdfundingInfos1.size() >= 2) {
                result.put(val.getId(), true);
            } else {
                result.put(val.getId(), false);
            }
        });
        //有患者信息查询患者姓名下面的案例
        List<String> authorNameList = crowdfundingAuthors.stream().map(CrowdfundingAuthor::getName).collect(Collectors.toList());
        List<CrowdfundingAuthor> crowdfundingAuthorListByNames = crowdfundingAuthorBiz.selectByNameList(authorNameList);
        List<Integer> caseIdListMoreAuthor = crowdfundingAuthorListByNames.stream().map(CrowdfundingAuthor::getCrowdfundingId).collect(Collectors.toList());
        List<Integer> caseIdListMoreAuthorResult = crowdfundingInfoBiz.selectByCaseIdNotEnd(caseIdListMoreAuthor);
        List<CrowdfundingAuthor> notEndAuthorList = crowdfundingAuthorListByNames.stream().filter(val -> caseIdListMoreAuthorResult.contains(val.getCrowdfundingId())).collect(Collectors.toList());
        //获取每个患者姓名下面的案例数量
        Map<String, Long> authorNameCaseCountMap = notEndAuthorList.stream().collect(Collectors.groupingBy(CrowdfundingAuthor::getName, Collectors.counting()));
        haveAuthor.forEach(val -> {
            CrowdfundingAuthor crowdfundingAuthor = authorMap.get(val.getId());
            Long caseCount = authorNameCaseCountMap.get(crowdfundingAuthor.getName());
            //查看案例数量是否大于或者等于2个
            if (caseCount != null && caseCount >= 2) {
                result.put(val.getId(), true);
            } else {
                result.put(val.getId(), false);
            }
        });
        return result;
    }


    public List<AdminCrowdfundingInfoView> getBaseListPageView(List<CrowdfundingInfoVo> crowdfundingInfos,
                                                               Map<String, CfCapitalAccount> cfCapitalAccountMap) {
        List<String> infoUuidList = crowdfundingInfos.stream().map(CrowdfundingInfoVo::getInfoId).collect(Collectors.toList());
        List<CfInfoExt> listByInfoUuids = adminCfInfoExtBiz.selectByInfoUuidList(infoUuidList);
        Map<String, CfInfoExt> infoExtMap = listByInfoUuids.stream().collect(Collectors.toMap(CfInfoExt::getInfoUuid, Function.identity()));
        crowdfundingInfos.forEach(val -> {
            CfInfoExt cfInfoExt = infoExtMap.get(val.getInfoId());
            if (cfInfoExt != null) {
                val.setCryptoRegisterMobile(cfInfoExt.getCryptoRegisterMobile());
            }
        });

        return getCrowdfundingInfoViewList(crowdfundingInfos, cfCapitalAccountMap);
    }


    public String formatChannel(String channel){

        if (StringUtils.isEmpty(channel)){
            return "其他";
        }

        if ("cf_volunteer".equals(channel)){
            return "线下渠道";
        }

        if (channel.contains("app")){
            return "APP";
        }

        if (channel.contains("toutiao") || "ad_search_feed".equals(channel)){
            return "头条信息流";
        }

        if (channel.contains("gzh")){
            return "外围号";
        }

        if ("wx_enter_nofollow".equals(channel)){
            return "我也要筹款";
        }

        if (channel.contains("wx_menu_choukuanren")){
            return "菜单发起";
        }

        return "其他";
    }

    public Map<Integer, String> getGuideUserLaunchChannelBatch(List<Integer> caseIdList) {
        Map<Integer, String> res = new HashMap<>();
        Map<Integer, CfUserInvitedLaunchCaseRecordModel> caseRecordMap = this.getCaseChannelRecordMap(caseIdList);
        caseRecordMap.forEach((caseId, obj) -> {
            String guideUserLaunchChannel = cfFirstApproveOperatorBiz.getGuideUserLaunchChannel(caseRecordMap.get(caseId) == null ? null :
                    caseRecordMap.get(caseId).getServiceUserInfo(shuidiCipher));
            res.put(caseId, guideUserLaunchChannel);
        });
        return res;
    }

    private String getGuideUserLaunchChannel(int caseId) {
        Map<Integer, CfUserInvitedLaunchCaseRecordModel> caseRecordMap = this.getCaseChannelRecordMap(Lists.newArrayList(caseId));

        return cfFirstApproveOperatorBiz.getGuideUserLaunchChannel(caseRecordMap.get(caseId) == null ? null :
                caseRecordMap.get(caseId).getServiceUserInfo(shuidiCipher));
    }

    public String getGuideUserLaunchChannel(CfUserInvitedLaunchCaseRecordModel caseRecordModel) {

        return cfFirstApproveOperatorBiz.getGuideUserLaunchChannel(caseRecordModel == null ? null :
                caseRecordModel.getServiceUserInfo(shuidiCipher));
    }


    public Map<Long, String> mapFilter(List<Long> userIds){

        Map<Long, String> map = Maps.newHashMap();
        //获取关注的公众号
        List<WxUserDetailModel> userDetailModels = wxUserDetailServiceBiz.getByUserIdBatch(Sets.newHashSet(userIds));

        if (CollectionUtils.isEmpty(userDetailModels)){
            return map;
        }

        Map<Long, List<WxUserDetailModel>> detail = userDetailModels.stream().collect(Collectors.groupingBy(WxUserDetailModel::getUserId));


        for (Long key : detail.keySet()){
            List<WxUserDetailModel> list = detail.get(key);
            String value = getThirdTypeV2(list);
            map.put(key,value);
        }
        return map;
    }


    private String getThirdType(List<WxUserDetail> list){

        String value1 = "";
        String value2 = "";
        String value3 = "";

        for (WxUserDetail userDetailModel : list){

            switch (userDetailModel.getThirdType()){
                //AccountThirdTypeEnum
                // 水滴健康保障 水滴筹 水滴健康管家 水滴筹健康 水滴筹健康生活
                case 3:
                    //关注
                    if (userDetailModel.getSubscribe() == 1){
                        value1 = "水滴筹";
                    }
                    break;
                case 17:
                case 116:
                case 115:
                case 61:
                    //关注
                    if (userDetailModel.getSubscribe() == 1){
                        value2 = "水滴筹其它";
                    }
                    break;
                default:
                    value3 = "未关注";
                    break;
            }
        }

        if (StringUtils.isNotEmpty(value1)){
            return value1;
        }

        if (StringUtils.isNotEmpty(value2)){
            return value2;
        }

        return value3;
    }

    private String getThirdTypeV2(List<WxUserDetailModel> list){

        String value1 = "";
        String value2 = "";
        String value3 = "";

        for (WxUserDetailModel userDetailModel : list){

            switch (userDetailModel.getThirdType()){
                //AccountThirdTypeEnum
                // 水滴健康保障 水滴筹 水滴健康管家 水滴筹健康 水滴筹健康生活
                case 3:
                    //关注
                    if (userDetailModel.getSubscribe() == 1){
                        value1 = "水滴筹";
                    }
                    break;
                case 17:
                case 116:
                case 115:
                case 61:
                    //关注
                    if (userDetailModel.getSubscribe() == 1){
                        value2 = "水滴筹其它";
                    }
                    break;
                default:
                    value3 = "未关注";
                    break;
            }
        }

        if (StringUtils.isNotEmpty(value1)){
            return value1;
        }

        if (StringUtils.isNotEmpty(value2)){
            return value2;
        }

        return value3;
    }

    /**
     * 重复案例数量结果
     * 当没有患者姓名时，判定重复的标准为：
     * 当一个用户<user_id>发起且没有结束的案例数量>=2时，则该用户所有发起且没有结束的案例都标记为重复
     * 当有患者姓名时：
     * 当同一个患者姓名发起且没有结束的案例数量>=2时，则所有用户所有发起且没有结束的案例都标记为重复
     * @return 案例和发起重复的数量map
     */
    public Set<CfRepeatUserIdRecord> repeatUserIdFilter() {
        //去除最近30天的案例
        List<CrowdfundingInfo> caseList = crowdfundingInfoBiz.selectByRepeatCase();
        List<Integer> caseIdList = caseList.stream().map(CrowdfundingInfo::getId).distinct().collect(Collectors.toList());
        //查看所有的患者信息
        List<CrowdfundingAuthor> crowdfundingAuthors = crowdfundingAuthorBiz.selectByCaseIdList(caseIdList);
        List<Integer> haveAuthorCaseList = crowdfundingAuthors.stream().map(CrowdfundingAuthor::getCrowdfundingId).collect(Collectors.toList());
        Map<Integer, CrowdfundingAuthor> authorMap = crowdfundingAuthors.stream().collect(Collectors.toMap(CrowdfundingAuthor::getCrowdfundingId, Function.identity()));
        //分组查看案例有没有患者信息
        Map<Boolean, List<CrowdfundingInfo>> judgeHaveAuthor = caseList.stream().collect(Collectors.partitioningBy(val -> haveAuthorCaseList.contains(val.getId())));
        List<CrowdfundingInfo> notHaveAuthor = judgeHaveAuthor.get(false);
        List<CrowdfundingInfo> haveAuthor = judgeHaveAuthor.get(true);
        //没有患者信息按照userId查询案例并分组
        List<Long> userIdListNotHaveAuthor = notHaveAuthor.stream().map(CrowdfundingInfo::getUserId).collect(Collectors.toList());
        List<CrowdfundingInfo> crowdfundingInfos = crowdfundingInfoBiz.selectByUserIdWhereNotEnd(userIdListNotHaveAuthor);
        Map<Long, List<CrowdfundingInfo>> integerListMap = crowdfundingInfos.stream().collect(Collectors.groupingBy(CrowdfundingInfo::getUserId));
        Set<CfRepeatUserIdRecord> result = Sets.newHashSet();
        notHaveAuthor.forEach(val -> {
            //查看该userId下面是否未结束的案例多于2个
            List<CrowdfundingInfo> crowdfundingInfos1 = integerListMap.get(val.getUserId());
            if (!CollectionUtils.isEmpty(crowdfundingInfos1) && crowdfundingInfos1.size() >= 2) {
                result.add(new CfRepeatUserIdRecord(val.getUserId(), crowdfundingInfos1.size()));
            }
        });
        //有患者信息查询患者姓名下面的案例
        List<String> authorNameList = crowdfundingAuthors.stream().map(CrowdfundingAuthor::getName).collect(Collectors.toList());
        List<CrowdfundingAuthor> crowdfundingAuthorListByNames = crowdfundingAuthorBiz.selectByNameList(authorNameList);
        List<Integer> caseIdListMoreAuthor = crowdfundingAuthorListByNames.stream().map(CrowdfundingAuthor::getCrowdfundingId).collect(Collectors.toList());
        List<Integer> caseIdListMoreAuthorResult = crowdfundingInfoBiz.selectByCaseIdNotEnd(caseIdListMoreAuthor);
        List<CrowdfundingAuthor> notEndAuthorList = crowdfundingAuthorListByNames.stream().filter(val -> caseIdListMoreAuthorResult.contains(val.getCrowdfundingId())).collect(Collectors.toList());
        //获取每个患者姓名下面的案例数量
        Map<String, Long> authorNameCaseCountMap = notEndAuthorList.stream().collect(Collectors.groupingBy(CrowdfundingAuthor::getName, Collectors.counting()));
        haveAuthor.forEach(val -> {
            CrowdfundingAuthor crowdfundingAuthor = authorMap.get(val.getId());
            Long caseCount = authorNameCaseCountMap.get(crowdfundingAuthor.getName());
            //查看案例数量是否大于或者等于2个
            if (caseCount != null && caseCount >= 2) {
                result.add(new CfRepeatUserIdRecord(val.getUserId(), caseCount.intValue()));
            }
        });
        return result;
    }

    // 首次工单 填充 线下渠道
    public void fillOffLineBdChannel(List<AdminCrowdfundingInfoView> crowdfundingInfoViewList) {
        if (CollectionUtils.isEmpty(crowdfundingInfoViewList)) {
            return;
        }
        List<CfInfoExt> infoExts =
                adminCfInfoExtBiz.selectByInfoUuidList(crowdfundingInfoViewList.stream().map(AdminCrowdfundingInfoView::getInfoId).collect(toList()));
        Set<String> volunteerSet = Sets.newHashSet();
        for (CfInfoExt ext : infoExts) {
            if (StringUtils.isNotEmpty(ext.getVolunteerUniqueCode())) {
                volunteerSet.add(ext.getInfoUuid());
            }
        }

        for (AdminCrowdfundingInfoView infoView : crowdfundingInfoViewList) {
            if (volunteerSet.contains(infoView.getInfoId())) {
                infoView.setChannel("线下渠道");
            }
        }
    }

    @Deprecated
    public SimpleVolunteerVo getVolunteerDetail(CfInfoExt infoExt) {

        SimpleVolunteerVo volunteerVo = new SimpleVolunteerVo();
        volunteerVo.setChannelName("其它");
        if (infoExt == null || StringUtils.isBlank(infoExt.getVolunteerUniqueCode())) {
            return volunteerVo;
        }
        List<CrowdfundingVolunteer> volunteerList = volunteerBiz.getVolunteerName(Arrays.asList(infoExt.getVolunteerUniqueCode()));
        if (CollectionUtils.isEmpty(volunteerList)) {
            return volunteerVo;
        }
        CrowdfundingVolunteer volunteer = volunteerList.get(0);

        volunteerVo.setChannelName("线下渠道");
        volunteerVo.setVolunteerName(volunteer.getVolunteerName());
        volunteerVo.setMobile(shuidiCipher.decrypt(volunteer.getMobile()));

        List<CrowdfundingCity> cityList = adminCrowdfundingCityBiz.getByIds(Arrays.asList(volunteer.getCityId()));
        if (CollectionUtils.isNotEmpty(cityList)) {
            volunteerVo.setCityName(cityList.get(0).getName());
        }

        return volunteerVo;
    }

    public String getChannelDesc(int caseId) {
        Map<Integer, CfUserInvitedLaunchCaseRecordModel> caseRecordResult = this.getCaseChannelRecordMap(Lists.newArrayList(caseId));
        CfUserInvitedLaunchCaseRecordModel caseRecordModel = caseRecordResult.get(caseId);

        return caseRecordModel == null || StringUtils.isBlank(caseRecordModel.getServiceUserInfo(shuidiCipher)) ? "其它"  : caseRecordModel.getServiceUserInfo(shuidiCipher);
    }

    public void addApprove(CrowdfundingInfo crowdfundingInfo, String operation, String comment, int userId){
        financeApproveService.addApprove(crowdfundingInfo, operation, comment, userId);
    }

    @Data
    public static class SimpleVolunteerVo {
        private String channelName;
        private String volunteerName;
        private String mobile;
        private NumberMaskVo mobileMask;
        private String cityName;
    }

    public void sendOnlineRecruitSmsWithRecord(String mobile, String message, String modelNum, int adminUserId, String defaultModelNum) {
        doSendCaseApproveSms(mobile, message, modelNum, defaultModelNum, null);
        smsRecordService.save(0, adminUserId, mobile, modelNum, message);
    }

    public void sendCaseApproveSmsWithRecord(String mobile, String message, String modelNum, int caseId, int adminUserId, Map<Integer, String> paramMap) {
        sendCaseApproveSms(mobile, message, modelNum, paramMap);
        smsRecordService.save(caseId, adminUserId, mobile, modelNum, message);
    }

    // 点击三大详情页和初审 时发送
    public void sendCaseApproveSms(String mobile, String message, String modelNum, Map<Integer, String> paramMap) {
        doSendCaseApproveSms(mobile, message, modelNum, "121duanxin1", paramMap);
    }

    private void doSendCaseApproveSms(String mobile, String message, String modelNum, String defultModelNum, Map<Integer, String> paramMap) {
        if (StringUtils.isNotBlank(message)) {
            Map<Integer, String> param = Maps.newHashMap();
            String validMessage = StringUtils.replace(message, ",", "，");
            param.put(1, validMessage);
            modelNum = defultModelNum;
            Map<String, Map<Integer, String>> mapMap = Maps.newHashMap();
            mapMap.put(mobile, param);
            msgClientV2Service.sendSmsParamsMsg(modelNum, mapMap, false);
        } else {
            if (MapUtils.isEmpty(paramMap)) {
                msgClientV2Service.sendSmsMsg(modelNum, Lists.newArrayList(mobile), false);
            } else {
                Map<String, Map<Integer, String>> mapMap = Maps.newHashMap();
                mapMap.put(mobile, paramMap);
                msgClientV2Service.sendSmsParamsMsg(modelNum, mapMap, false);
            }
        }
    }

    public boolean checkModelNum(String modelNum, String mobile, CrowdfundingInfo crowdfundingInfo) {
        CfCredibleInfoDO cfCredibleInfoDO = adminCredibleInfoService.getLastOneByCaseId(crowdfundingInfo.getId(), CredibleTypeEnum.HELP_PROVE.getKey());
        boolean result = false;
        switch (modelNum) {
            case "MHAUEC100500016":
                if (Objects.nonNull(cfCredibleInfoDO)
                        && cfCredibleInfoDO.getAuditStatus() == AddTrustAuditStatusEnum.UN_SUBMITTED.getCode()) {
                    cfReportCredibleInfoService.sendSMS(crowdfundingInfo.getInfoId(), crowdfundingInfo.getUserId(),
                            AddTrustModelNumEnum.MHAUEC100500016.name(), 2);
                }
                result = true;
                break;
            case "JFQOGT100494036":
                if (Objects.nonNull(cfCredibleInfoDO)) {
                    cfReportCredibleInfoService.sendSMS(crowdfundingInfo.getInfoId(), crowdfundingInfo.getUserId(),
                            AddTrustModelNumEnum.JFQOGT100494036.name(), 3);
                }
                result = true;
                break;
            default:
        }
        return result;
    }

    public Response addComment(String infoId, String comment, int userId, String operateMsg, BackgroundLogEnum operate) {
        if (StringUtils.isEmpty(infoId) || StringUtils.isEmpty(comment) || operate == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoSlaveDao.getFundingInfo(infoId);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        financeApproveService.addApprove(crowdfundingInfo, StringUtils.isEmpty(operateMsg) ? operate.getMessage() :
                operateMsg, comment, userId);

        try {
            log.info("updateOperationStatus infoId:{};userId:{}", infoId, userId);
            this.updateOperationStatus(infoId, CrowdfundingOperationEnum.COMMENTED, "", userId);
        } catch (Exception e) {
            log.error("updateOperationStatus Error!", e);
        }
        // 操作记录入库
        try {
            AdminUserAccountModel adminUserAccountModel = this.seaAccountClientV1.getValidUserAccountById(userId).getResult();
            String userName = (adminUserAccountModel == null ? "" : adminUserAccountModel.getName());
            this.crowdfundingOperationDelegate.saveCfOperatingRecord(infoId, userId, userName, CfOperatingRecordEnum.Type.REMARK,
                    CfOperatingRecordEnum.Role.OPERATOR);
        } catch (Exception e) {
            log.error("", e);
        }
        cfAdminOperationRecordBiz.addOneOperationRecord(infoId, userId, operate == BackgroundLogEnum.FLOW_ORDER_REMARK ?
                CfOperationRecordEnum.FLOW_ORDER_REMARK.value() : CfOperationRecordEnum.REMARK.value(), "");
        log.info(
                "客服后台log：comment operationTime:{};operator:{};operationReason:{};operationType:{};infoId:{};status:{};dataStatus:{};infoStatus:{}",
                com.shuidihuzhu.common.web.util.DateUtil.formatDateTime(new Date()), userId, operate.getMessage(),
                "评论", infoId, crowdfundingInfo.getStatus(), crowdfundingInfo.getDataStatus(),
                crowdfundingInfo.getEndTime().before(new Date()) ? "结束" : "未结束");

        // TODO：业务日志

        return NewResponseUtil.makeSuccess(this.getCommentVoList(crowdfundingInfo.getId()));
    }

    public void fillChannel(List<WorkOrderFirstApprove> firstApproveList) {

        if (CollectionUtils.isEmpty(firstApproveList))  {
            return;
        }

        List<Integer> caseIds = Lists.newArrayList();
        for (WorkOrderFirstApprove firstApprove : firstApproveList) {
            caseIds.add(firstApprove.getCaseId());
        }


        Map<Integer, CrowdfundingInfo> caseIdCfMap = this.getMapByIds(caseIds);
        Map<Integer, CfUserInvitedLaunchCaseRecordModel> channelRecordMap = getCaseChannelRecordMap(caseIds);

        for (WorkOrderFirstApprove firstApprove : firstApproveList) {

            CrowdfundingInfo cf = caseIdCfMap.get(firstApprove.getCaseId());
            firstApprove.setChannel(this.formatChannel(cf == null ? null :cf.getChannel()));

            CfUserInvitedLaunchCaseRecordModel caseRecord = channelRecordMap.get(firstApprove.getCaseId());
            firstApprove.setListGuideUserLaunchChannel(cfFirstApproveOperatorBiz.getGuideUserLaunchChannel(caseRecord == null ? null : caseRecord.getServiceUserInfo(shuidiCipher)));

        }
    }

    public Map<Integer, CrowdfundingInfo> getMapByIds(List<Integer> ids) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        List<CrowdfundingInfo> crowdfundingInfos = this.crowdfundingInfoSlaveDao.getFundingInfoByIds(ids);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(crowdfundingInfos)) {
            return Collections.emptyMap();
        }
        Map<Integer, CrowdfundingInfo> crowdfundingInfoMap = Maps.newHashMap();
        for (CrowdfundingInfo crowdfundingInfo : crowdfundingInfos) {
            crowdfundingInfoMap.put(crowdfundingInfo.getId(), crowdfundingInfo);
        }
        return crowdfundingInfoMap;
    }

    public Map<Integer, CfUserInvitedLaunchCaseRecordModel> getCaseChannelRecordMap(List<Integer> caseIds) {
        Map<Integer, CfUserInvitedLaunchCaseRecordModel> caseIdMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(caseIds)) {
            return caseIdMap;
        }

        List<CfInfoSimpleModel> infoSimpleModels = crowdfundingDelegate.getCfInfoSimpleModelListByIds(caseIds);
        if (CollectionUtils.isEmpty(infoSimpleModels)) {
            return caseIdMap;
        }

        List<ChannelRefineDTO> channelRefineDTOs = Lists.newArrayList();
        for (CfInfoSimpleModel simpleModel : infoSimpleModels) {
            ChannelRefineDTO refineDTO = new ChannelRefineDTO();
            refineDTO.setInfoId((long)simpleModel.getId());
            refineDTO.setChannel(simpleModel.getChannel());
            refineDTO.setUserId(simpleModel.getUserId());
            channelRefineDTOs.add(refineDTO);
        }

        Response<List<CfUserInvitedLaunchCaseRecordModel>> caseRecordResult = channelFeignClient.getCfUserInvitedLaunchCaseRecordByInfoIds(channelRefineDTOs);

        log.info("查询外呼的返回值:caseIds:{}, response：{}", channelRefineDTOs, JSON.toJSONString(caseRecordResult));

        if (caseRecordResult == null || CollectionUtils.isEmpty(caseRecordResult.getData())) {
            return caseIdMap;
        }

        for (CfUserInvitedLaunchCaseRecordModel caseRecordModel : caseRecordResult.getData()) {
            caseIdMap.put(Math.toIntExact((long)caseRecordModel.getInfoId()), caseRecordModel);
        }
        return caseIdMap;
    }


    public Map<Integer, CfUserInvitedLaunchCaseRecordVO> getCaseChannelRecordVOMap(List<Integer> caseIds) {
        Map<Integer, CfUserInvitedLaunchCaseRecordModel> caseIdMap = this.getCaseChannelRecordMap(caseIds);
        if(MapUtils.isEmpty(caseIdMap)){
            return Maps.newHashMap();
        }

        Map<Integer, CfUserInvitedLaunchCaseRecordVO> map = Maps.newHashMap();
        for (int caseId : caseIdMap.keySet()) {
            CfUserInvitedLaunchCaseRecordModel cfUserInvitedLaunchCaseRecordModel = caseIdMap.get(caseId);
            String serviceInfo = cfUserInvitedLaunchCaseRecordModel.getServiceUserInfo(shuidiCipher);
            CfUserInvitedLaunchCaseRecordVO vo = new CfUserInvitedLaunchCaseRecordVO(cfUserInvitedLaunchCaseRecordModel);
            try {
                Matcher matcher = CHINA_PATTERN.matcher(serviceInfo);
                if (matcher.find()) {
                    String phone = matcher.group(1);
                    vo.setPhone(phone);
                    int index = serviceInfo.indexOf(phone);
                    vo.setPrefix(serviceInfo.substring(0, index - 1));
                }
            } catch (Exception e) {
                log.error("error", e);
            }
            map.put(caseId, vo);
        }
        return map;
    }

    public Optional<CfUserInvitedLaunchCaseRecordVO> getCaseChannelRecordVO(int caseId) {
        Map<Integer, CfUserInvitedLaunchCaseRecordModel> caseIdMap = this.getCaseChannelRecordMap(Lists.newArrayList(caseId));
        if (MapUtils.isEmpty(caseIdMap) || !caseIdMap.containsKey(caseId)) {
            CfUserInvitedLaunchCaseRecordVO vo = new CfUserInvitedLaunchCaseRecordVO();
            vo.setChannel(ChannelRefine.ChannelRefineResuleEnum.YONGHU_ZIZHU.getChannelDesc());
            vo.setException(true);
            return Optional.of(vo);
        }


        CfUserInvitedLaunchCaseRecordModel cfUserInvitedLaunchCaseRecordModel = caseIdMap.get(caseId);
        String serviceInfo = cfUserInvitedLaunchCaseRecordModel.getServiceUserInfo(shuidiCipher);
        CfUserInvitedLaunchCaseRecordVO vo = new CfUserInvitedLaunchCaseRecordVO(cfUserInvitedLaunchCaseRecordModel);
        vo.setOriginData(serviceInfo);
        try {
            Matcher matcher = CHINA_PATTERN.matcher(serviceInfo);
            if (matcher.find()) {
                String phone = matcher.group(1);
                int index = serviceInfo.indexOf(phone);
                vo.setPrefix(serviceInfo.substring(0, index - 1));
                vo.setPhoneMask(maskUtil.buildByDecryptPhone(serviceInfo.substring(index)));
                vo.setPhone(null);
            } else {
                vo.setException(true);
            }
        } catch (Exception e) {
            log.info("error", e);
            vo.setException(true);
        }
        return Optional.of(vo);
    }

    public void fillRejectDetail(WorkOrderFirstApprove firstApprove) {

        List<String> rejectMsgList = Lists.newArrayList();
        // 前置审核的驳回项
        CfFirsApproveMaterial cfFirsApproveMaterial = riskDelegate.getCfFirsApproveMaterialByInfoId(firstApprove.getCaseId());
        if (cfFirsApproveMaterial != null && cfFirsApproveMaterial.getRejectReasonType() != 0) {
            switch( EditMaterialType.valueOfCode(cfFirsApproveMaterial.getRejectReasonType())) {
                case POVERTY:
                    rejectMsgList.add("前置-贫困证明");
                    break;
                case SUGGEST_END_CASE:
                    rejectMsgList.add("前置-不符合大病筹款");
                    break;
                case TARGET_AMOUNT_DESC:
                    rejectMsgList.add("前置-所需金额说明");
                    break;
                case IMAGE_URL:
                    rejectMsgList.add("前置-医疗材料");
                    break;
                case PATIENT_NAME_IDCARD:
                    rejectMsgList.add("前置-患者信息");
                    break;
                case ALL:
                    rejectMsgList.add("前置-全部驳回");
                    break;
                case DEFAULT:
                    break;
            }
        }

        // 图文的驳回项
        CfMaterialVerityHistory verityHistory = this.selectLatestMaterial(firstApprove.getCaseId(),
                CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT.getCode(),
                CfMaterialVerityHistory.REJECT_TYPE);

        if (verityHistory != null && com.shuidi.weixin.common.util.StringUtils.isNotBlank(verityHistory.getRefuseIds())) {
            String[] refuseIds = verityHistory.getRefuseIds().split(CfMaterialVerityHistory.MATERIAL_VERITY_SEPARATOR);
            List<Integer> intRefuseIds = Lists.newArrayList();
            for (String id : refuseIds) {
                intRefuseIds.add(Integer.valueOf(id));
            }
            List<CfRefuseReasonEntity> cfRefuseReasonEntities =  cfRefuseReasonEntityBiz.selectByIds(intRefuseIds);

            if (CollectionUtils.isNotEmpty(cfRefuseReasonEntities)) {
                List<CfRefuseReasonItem> reasonItems = cfRefuseReasonItemBiz.selectByIds(cfRefuseReasonEntities.stream()
                        .map(CfRefuseReasonEntity::getItemIds).collect(Collectors.toSet()));

                for (CfRefuseReasonItem reasonItem : reasonItems) {
                    rejectMsgList.add("图文-" + reasonItem.getContent());
                }
            }
        }
        firstApprove.setRejectDetail(Joiner.on(CfMaterialVerityHistory.MATERIAL_VERITY_SEPARATOR).join(rejectMsgList));

        firstApprove.setDataTypeList(this.getCrowdfundingInfoStatusInfo(firstApprove.getInfoUuid()));
    }

    public CfMaterialVerityHistory selectLatestMaterial(int caseId, int materialId, int handleType) {
        return cfMaterialVerityHistoryBiz.selectLatestMaterial(caseId, materialId, handleType);
    }

    public boolean isFundInitialized(int caseId) {
        FeignResponse<Boolean> result = fundStateClient.isInitialized(caseId);
        log.info("初始化案例时、判断资金的状态.caseId:{} result:{}", caseId, JSON.toJSONString(result));

        return result.ok() && result.getData() != null ? result.getData() : false;
    }

    private CfDrawCashApplyRecord bulidDrawCashApplyRecord(CfDrawCashApplyVo drawCashApplyVo) {
        if (null == drawCashApplyVo) {
            return null;
        }
        CfDrawCashApplyRecord cfDrawCashApplyRecord = new CfDrawCashApplyRecord();

        cfDrawCashApplyRecord.setStopCfReason(drawCashApplyVo.getStopCfReason());
        cfDrawCashApplyRecord.setUseOfFunds(drawCashApplyVo.getUseOfFunds());
        cfDrawCashApplyRecord.setPatientConditionNow(drawCashApplyVo.getPatientConditionNow());
        cfDrawCashApplyRecord.setDateCreated(drawCashApplyVo.getApplyAuditTime());
        cfDrawCashApplyRecord.setLastModified(drawCashApplyVo.getApplyTime());
        cfDrawCashApplyRecord.setPatientRegion(drawCashApplyVo.getPatientRegion());
        cfDrawCashApplyRecord.setPatientAddress(drawCashApplyVo.getPatientAddress());
        cfDrawCashApplyRecord.setEmergencyContactName(drawCashApplyVo.getEmergencyContactName());
        cfDrawCashApplyRecord.setEmergencyContactPhone(drawCashApplyVo.getEmergencyContactPhone());
        cfDrawCashApplyRecord.setEmergencyContactRelation(drawCashApplyVo.getEmergencyContactRelation());
        return cfDrawCashApplyRecord;
    }

    public CPublishImageContent queryPublishImageContent(int caseId) {

        CPublishImageContent imageContent = new CPublishImageContent();

        Response<List<WorkOrderVO>> orderVoResult = cfWorkOrderClient.listByCaseIdAndTypeAndResult(caseId,
                Lists.newArrayList(WorkOrderType.content.getType()),
                Lists.newArrayList(HandleResultEnum.undoing.getType(), HandleResultEnum.doing.getType()));
        if (orderVoResult == null || CollectionUtils.isEmpty(orderVoResult.getData())) {
            return imageContent;
        }
        CrowdfundingInfo info = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (info == null) {
            return imageContent;
        }

        imageContent.setNoFinishImageOrder(1);

        imageContent.setTitle(info.getTitle());
        imageContent.setContent(info.getContent());
        List<CrowdfundingAttachmentVo> attachments = crowdfundingDelegate.getAttachmentsByType(caseId,
                AttachmentTypeEnum.ATTACH_CF);
        if (CollectionUtils.isNotEmpty(attachments)) {
            List<Integer> ids = attachments.stream().map(CrowdfundingAttachmentVo::getId).collect(toList());
            Map<Integer, Integer> watermarks = attachmentBiz.getImageWatermarkByIds(ids);

            for (CrowdfundingAttachmentVo attachment : attachments) {
                attachment.setWatermark(Objects.requireNonNullElse(watermarks.get(attachment.getId()), 0));
            }
        }
        imageContent.setAttachmentVos(attachments);


        return imageContent;
    }

    public boolean patientAutoPass(int caseId, List<Integer> passIds, List<Integer> refuseIds) {

        if (CollectionUtils.isNotEmpty(passIds)
                && passIds.contains(CrowdfundingInfoDataStatusTypeEnum.PATIENT_INFO_SUBMIT.getCode())) {
            return false;
        }

        if (CollectionUtils.isNotEmpty(refuseIds)
                && refuseIds.contains(CrowdfundingInfoDataStatusTypeEnum.PATIENT_INFO_SUBMIT.getCode())) {
            return false;
        }

        return crowdfundingAuthorBiz.isFaceIdSuccess(caseId);
    }
}
