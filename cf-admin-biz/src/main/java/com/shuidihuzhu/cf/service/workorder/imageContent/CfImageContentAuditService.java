package com.shuidihuzhu.cf.service.workorder.imageContent;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.admin.river.impl.RiverDiBaoFacadeImpl;
import com.shuidihuzhu.cf.admin.util.lock.RedisDistributedLock;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.client.adminpure.enums.WorkOrderExtContentTypeEnum;
import com.shuidihuzhu.cf.client.feign.CaseInfoApproveStageFeignClient;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.CfBaseInfoRiskHitVO;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecordDTO;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.client.ugc.service.RiskControlWordManageClient;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.dao.workorder.CfImageContentAuditDAO;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.impl.CrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.BackgroundLogEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.model.admin.workorder.imageContent.*;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfRefuseReasonItemRecord;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.ai.AiImageMaskServiceImpl;
import com.shuidihuzhu.cf.service.crowdfunding.CfContentImageService;
import com.shuidihuzhu.cf.service.crowdfunding.SensitiveWordService;
import com.shuidihuzhu.cf.service.record.CfCrowdfundingAttachmentRecordService;
import com.shuidihuzhu.cf.service.workorder.WorkOrderExtService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditSearchService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.cf.vo.approve.InitialAuditAdditionInfoVO;
import com.shuidihuzhu.cf.vo.approve.RiverSendSmsParamVO;
import com.shuidihuzhu.cf.vo.v5.MaterialRejectPositionType;
import com.shuidihuzhu.client.cf.admin.model.ImageContentPassModel;
import com.shuidihuzhu.client.cf.growthtool.model.CfCaseSpecialPrePoseDetail;
import com.shuidihuzhu.client.cf.workorder.CfTwWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.TwHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.constant.CfClientMQTopicCons;
import com.shuidihuzhu.client.model.enums.ImageMaskBizEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.msg.model.SmsTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class CfImageContentAuditService {

    @Autowired
    private InitialAuditSearchService initialSearchService;
    @Autowired
    private WorkOrderExtService orderExtService;
    @Autowired
    private ICrowdfundingDelegate fundingDelegate;
    @Autowired
    private RiverDiBaoFacadeImpl riverDiBaoFacade;
    @Autowired
    private CfMaterialVerityHistoryBiz verityHistoryBiz;
    @Autowired
    private CfImageContentAuditDAO imageContentAuditDAO;
    @Autowired
    private AdminApproveService approveService;
    @Autowired
    private AdminCrowdfundingInfoBiz fundingInfoBiz;
    @Autowired
    private CfContentImageService cfContentImageService;
    @Autowired
    private IRiskDelegate riskDelegate;
    @Autowired
    private CaseInfoApproveStageFeignClient stageFeignClient;
    @Autowired
    private RiskControlWordManageClient riskWordClient;
    @Autowired
    private AdminCrowdfundingInfoStatusBiz infoStatusBiz;
    @Autowired
    private SensitiveWordService senstiveWorkService;
    @Autowired
    private CommonOperationRecordClient commonOperateClient;
    @Autowired
    private CfRefuseReasonEntityBiz reasonEntityBiz;
    @Autowired
    private CfRefuseReasonItemBiz reasonItemBiz;
    @Autowired
    private CfTwWorkOrderClient twWorkOrderClient;
    @Autowired
    private AdminCfInfoExtBiz adminCfInfoExtBiz;
    @Autowired
    private AdminCfRefuseReasonMsgBiz reasonMsgBiz;
    @Autowired
    private CfCrowdfundingAttachmentRecordService cfCrowdfundingAttachmentRecordService;

    @Autowired
    private CfWorkOrderClient workOrderClient;

    @Resource
    private SeaAccountClientV1 seaAccountClientV1;
    @Resource
    private CrowdfundingDelegate crowdfundingDelegate;
    @Resource
    private CaseInfoApproveStageFeignClient caseInfoApproveStageFeignClient;
    @Resource
    private AdminCrowdfundingDetailSendMsgTemplateBiz sendMsgTemplateBiz;
    @Resource
    private AdminTwModifyService adminTwModifyService;
    @Resource
    private AiImageMaskServiceImpl aiImageMaskService;
    @Resource
    private Producer producer;

    @Resource
    private MaskUtil maskUtil;


    private List<Integer> IMAGE_REJECT_POSITION_IDS = Lists.newArrayList();
    private List<Integer> CONTENT_REJECT_POSITION_IDS = Lists.newArrayList();

    @PostConstruct
    public void initialRejectPositionIds() {
        IMAGE_REJECT_POSITION_IDS.addAll(getReasonPositionIds("展示图片"));
        CONTENT_REJECT_POSITION_IDS.addAll(getReasonPositionIds("标题"));
        CONTENT_REJECT_POSITION_IDS.addAll(getReasonPositionIds("求助说明"));
    }

    private List<Integer> getReasonPositionIds(String content) {
        List<CfRefuseReasonItem> items = reasonItemBiz.selectByContent(content);
        List<Integer> ids = Lists.newArrayList();
        if (CollectionUtils.isEmpty(items)) {
            return ids;
        }
        for (CfRefuseReasonItem reasonItem : items) {
            ids.add(reasonItem.getId());
        }

        return ids;
    }

    public CfImageContentAuditView queryImageContentView(int caseId, long workOrderId) {

        CfImageContentAuditView auditView = orderExtService.getByClazz(workOrderId,
                WorkOrderExtContentTypeEnum.IMAGE_CONTENT_AUDIT, CfImageContentAuditView.class);
        if (auditView != null) {
            return auditView;
        }

        CrowdfundingInfo cf = fundingInfoBiz.getFundingInfoById(caseId);
        if (cf == null) {
            log.warn("通过案例id不能找到caseId:{}", caseId);
            return null;
        }
        auditView = new CfImageContentAuditView();
        // 当前公示的图文
        auditView.setPublicCaseBaseInfo(getPublicCaseInfo(cf));
        // 图文
        auditView.setCaseBaseInfo(initialSearchService.getBaseInfo(cf));
        // 前置
        InitialAuditCaseDetail.FirstApproveCaseInfo firstApproveCaseInfo = initialSearchService.getFirstApproveCaseInfo(cf);
        auditView.setFirstApproveCaseInfo(firstApproveCaseInfo);
        Optional.ofNullable(firstApproveCaseInfo)
                .filter(r -> Objects.nonNull(r.getSpecialPrePoseDetail()) && StringUtils.isNotBlank(r.getSpecialPrePoseDetail().getMobile()))
                        .ifPresent(r -> {
                            CfCaseSpecialPrePoseDetail specialPrePoseDetail = r.getSpecialPrePoseDetail();
//                            specialPrePoseDetail.setMobileMask(maskUtil.buildByDecryptPhone(specialPrePoseDetail.getMobile()));
                            specialPrePoseDetail.setMobile(StringUtils.EMPTY);
                        });
        // 增信
        auditView.setCreditInfo(initialSearchService.getInitialCreditInfo(caseId));
        // 低保
        InitialAuditAdditionInfoVO initial = riverDiBaoFacade.getInfo(caseId);
        if (initial != null) {
            auditView.setAdditionInfo(initial.getDiBaoAndPinKunInfo());
        }
        auditView.setFinishContent(getFinishContent(cf));

        // 图文敏感词
        Response<List<CfBaseInfoRiskHitVO>> result = riskWordClient.selectHitVoByCaseIds(Lists.newArrayList(caseId));
        auditView.setRiskHitVO(result != null && CollectionUtils.isNotEmpty(result.getData()) ?
                result.getData().get(0) : null);

        // 上次已经通过的项
        auditView.setPassTypes(getLastPassIds(cf.getInfoId()));

        //      查询是否是初审高风险
        boolean initialAuditHighRisk = false;
        Response<WorkOrderVO> lastHighRiskOrder = workOrderClient.getLastWorkOrderByTypes(caseId, Collections.singletonList(WorkOrderType.highriskshenhe.getType()));
        if (Objects.nonNull(lastHighRiskOrder) && lastHighRiskOrder.ok() && Objects.nonNull(lastHighRiskOrder.getData())) {
            initialAuditHighRisk = true;
        }
        auditView.setInitialAuditHighRisk(initialAuditHighRisk);


        return auditView;
    }

    private Set<Integer> getLastPassIds(String infoUuid) {

        CfRefuseReasonMsg lastMsg = reasonMsgBiz.selectByInfoIdAndType(infoUuid, CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT.getCode());
        if (lastMsg == null || StringUtils.isBlank(lastMsg.getItemReason())) {
            return Sets.newHashSet();
        }

        List<CfRefuseReasonItemRecord> recordList = Lists.newArrayList();
        try {
            recordList = JSON.parseArray(lastMsg.getItemReason(), CfRefuseReasonItemRecord.class);
        } catch (Exception e) {
            log.info("解析驳回理由异常:infoUuid:{}", infoUuid, e);
        }
        Set<Integer> passIds = Sets.newHashSet(CfImageContentAuditInfo.IMAGE,
                CfImageContentAuditInfo.CONTENT);
        for (CfRefuseReasonItemRecord record : recordList) {
            if (record.getItemIds() == MaterialRejectPositionType.CASE_IMAGE.getCode()) {
                passIds.remove(CfImageContentAuditInfo.IMAGE);
            }

            if (record.getItemIds() == MaterialRejectPositionType.CASE_TITLE.getCode() ||
                    record.getItemIds() == MaterialRejectPositionType.CASE_CONTENT.getCode() ) {
                passIds.remove(CfImageContentAuditInfo.CONTENT);
            }
        }
        return passIds;
    }

    private InitialAuditCaseDetail.CaseBaseInfo getPublicCaseInfo(CrowdfundingInfo cf) {
        InitialAuditCaseDetail.CaseBaseInfo publicCaseInfo = new InitialAuditCaseDetail.CaseBaseInfo();
        publicCaseInfo.setTitle(cf.getTitle());
        publicCaseInfo.setContent(cf.getContent());
        return publicCaseInfo;
    }

    private String getFinishContent(CrowdfundingInfo cf) {
        CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByCaseId(cf.getId());
        if (cfInfoExt == null) {
            return "";
        }

        int finishStatus = cfInfoExt.getFinishStatus();
        for (CfFinishStatus cfFinishStatus : CfFinishStatus.values()) {
            if (cfFinishStatus.getValue() == finishStatus) {
                if (cf.getEndTime().getTime() <= System.currentTimeMillis()
                        && finishStatus == CfFinishStatus.NOT_FINISH.getValue()) {
                    return CfFinishStatus.EXPIRE.getDescription();
                } else {
                    return cfFinishStatus.getDescription();
                }
            }
        }
        return "";
    }

    // 审核～ 通过或驳回～
    @RedisDistributedLock(key = "image_content_handle_image_text_audit_#{handleParam.caseId}")
    public void handleImageTextAudit(CfImageContentHandleParam handleParam) {

        // 判断参数问题
        CrowdfundingInfo cf = fundingInfoBiz.getFundingInfoById(handleParam.getCaseId());
        if (cf == null) {

            // 抛异常
            throw new RuntimeException("通过案例id不能找到caseId:" + handleParam.getCaseId());
        }

        validateParam(handleParam);

        // 更新工单的状态
        handleWorkOrder(handleParam);

        // 更新案例的状态 发驳回消息
        updateInitialStatus(handleParam, cf);

        // 记录日志 快照保存
        saveSnapshot(handleParam);
    }

    private void validateParam(CfImageContentHandleParam handleVo) {

    }

    private void handleWorkOrder(CfImageContentHandleParam handleVo) {
        TwHandleOrderParam param = new TwHandleOrderParam();
        param.setCaseId(handleVo.getCaseId());
        param.setWorkOrderId(handleVo.getWorkOrderId());
        param.setUserId(handleVo.getUserId());
        param.setOperComment(handleVo.getHandleComment());
        param.setHandleResult(getHandleResult(handleVo));
        param.setOrderType(WorkOrderType.content.getType());
        param.setCallStatus(handleVo.getCallStatus()+"");
        Response result = twWorkOrderClient.handleTwOrder(param);
        log.info("图文审核调用工单系统 param:{} result:{}", JSON.toJSONString(param), JSON.toJSONString(result));
    }

    private int getHandleResult(CfImageContentHandleParam handleVo) {
        return CfImageContentAuditInfo.ImageContextStatus.getAuditStatus(handleVo.getPassTypes()) ==
                CfImageContentAuditInfo.ImageContextStatus.PASS.getCode() ?
                HandleResultEnum.audit_pass.getType() :
                HandleResultEnum.audit_reject.getType();
    }

    private void updateInitialStatus(CfImageContentHandleParam param, CrowdfundingInfo info) {
        CfImageContentAuditInfo auditInfo = new CfImageContentAuditInfo();
        auditInfo.setCaseId(param.getCaseId());
        auditInfo.setWorkOrderId(param.getWorkOrderId());
        auditInfo.setOperatorId(param.getUserId());
        auditInfo.setOperatorDetail(verityHistoryBiz.queryOperateDetail((int)param.getUserId()));
        int auditStatus = CfImageContentAuditInfo.ImageContextStatus.getAuditStatus(param.getPassTypes());
        auditInfo.setInfoStatus(auditStatus);

        imageContentAuditDAO.addImageContentInfo(auditInfo);

        param.getCurrImageAuditView().setPassTypes(param.getPassTypes());
        param.setInfoUuid(info.getInfoId());


        if (auditStatus == CfImageContentAuditInfo.ImageContextStatus.PASS.getCode()) {
            handleImageTextPass(info, param.getUserId(), param.getWorkOrderId());
        } else {
            handleImageTextReject(param, info);
        }

        recordAuditLog(param, auditStatus);
    }

    private void handleImageTextPass(CrowdfundingInfo info, long userId, long workOrderId) {
        log.info("图文通过将图文的数据写到案例表。caseId:{} userId:{} workOrderId:{}", info.getId(), userId, workOrderId);
        stageFeignClient.commitStage(info.getId());
        // 图文审核通过，发送掩码通知消息
        log.info("AiImageMaskServiceImpl sendImageMaskMq {} {}", info.getId(), ImageMaskBizEnum.CF_DETAIL_IMAGE.getDesc());
        aiImageMaskService.sendImageMaskMq(info.getId(), ImageMaskBizEnum.CF_DETAIL_IMAGE.getCode());

        // 原有图文通过的逻辑
        riskDelegate.handleRiskPassed(info.getInfoId(), Math.toIntExact(userId));

        cfContentImageService.close(userId, info);

        // 图文通过，更新图文修改记录表
        adminTwModifyService.updateTwModifyFlag(workOrderId, info);

        // 发送图文审核通过消息
        sendImageContentPassMq(info.getId());
    }

    private void sendImageContentPassMq(Integer caseId) {
        Message msg =  new Message(CfClientMQTopicCons.CF, CfClientMQTagCons.CF_IMAGE_CONTENT_PASS,
                "" + System.currentTimeMillis(),
                ImageContentPassModel.builder().caseId(caseId).build());
        MessageResult result = producer.send(msg);
        log.info("图文审核通过消息发送 msg:{} result:{}", msg, result);
    }

    private void handleImageTextReject(CfImageContentHandleParam param, CrowdfundingInfo info) {
        // 如果是图文驳回 将驳回的信息写到材料审核的图文材料上
        String rejectMsgs = approveService.refuseReasonHandle(Lists.newArrayList(param.getRejectIds()), "", info, Lists.newArrayList());
        approveService.caseRefuseHandle(info, rejectMsgs);
        // 需要更新
        infoStatusBiz.updateByInfoId(info.getInfoId(), CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT.getCode(),
                CrowdfundingInfoStatusEnum.REJECTED);
        if (info.getStatus() != CrowdfundingStatus.APPROVE_DENIED) {
            String msg = String.format("将案例的材料审核的状态更新为修改中 caseId:%s，案例当前的状态是:%s",
                    info.getId(), info.getStatus());
            log.info(msg);
            commonOperateClient.create()
                    .buildBasicPlatform(param.getWorkOrderId(), Math.toIntExact(param.getUserId()),
                            OperationActionTypeEnum.IMAGE_CONTENT_AUDIT_CHANGE_STATUS)
                    .buildCaseId(param.getCaseId())
                    .buildRemark(msg)
                    .save();

            fundingInfoBiz.updateStatus(info.getId(), CrowdfundingStatus.APPROVE_DENIED.value(), info.getStatus().value());
        }
    }

    private void recordAuditLog(CfImageContentHandleParam param, int auditStatus) {
        CfImageOperateLog.OperateDetail operateDetail = new CfImageOperateLog.OperateDetail();

        String result = auditStatus == CfImageContentAuditInfo.ImageContextStatus.PASS.getCode()
                ? "审核通过" : "审核驳回";
        operateDetail.setAuditResult(result);

        String auditComment = getAuditComment(param);
        operateDetail.setHandleNotes(auditComment);
        operateDetail.setComment(param.getHandleComment());

        commonOperateClient.create()
                .buildBasicPlatform(param.getWorkOrderId(), Math.toIntExact(param.getUserId()),
                        OperationActionTypeEnum.IMAGE_CONTENT_AUDIT)
                .buildCaseId(param.getCaseId())
                .buildRemark(JSON.toJSONString(operateDetail))
                .save();

        // 详情页的备注
        approveService.addComment(param.getInfoUuid(), auditStatus + " : " + auditComment + "【评论】" + param.getHandleComment() + "\n",
                Math.toIntExact(param.getUserId()),"图文审核", BackgroundLogEnum.IMAGE_CONTENT_HANDLE);

        if (CollectionUtils.isNotEmpty(param.getRejectIds())) {
            verityHistoryBiz.recordVerityHistory(new CfMaterialVerityHistory.CfMaterialVerityHistoryRecord()
                    .buildCaseId(param.getCaseId())
                    .buildInfoId(param.getInfoUuid())
                    .buildPassIds(Lists.newArrayList())
                    .buildRejectIds(Lists.newArrayList(param.getRejectIds()))
                    .buildComment(param.getHandleComment())
                    .buildUserId(Math.toIntExact(param.getUserId())), param.getWorkOrderId(), 0);
        }

    }

    private String getAuditComment(CfImageContentHandleParam param) {

        StringBuilder comment = new StringBuilder("");
        if (param.getCallStatus() != 0 ) {
            comment.append("呼通状态：");
            comment.append(param.getCallStatus() == 1 ? "呼通" : "未呼通").append("\n");
        }

        comment.append("【文章】").append(getRejectMsg(param, CfImageContentAuditInfo.CONTENT)).append("\n");
        comment.append("【图片】").append(getRejectMsg(param, CfImageContentAuditInfo.IMAGE)).append("\n");
        return comment.toString();
    }

    private String getRejectMsg(CfImageContentHandleParam param, int type) {
        if (CollectionUtils.isNotEmpty(param.getPassTypes()) && param.getPassTypes().contains(type)) {
            return "审核通过;\n";
        }

        StringBuilder msg = new StringBuilder("审核驳回；");
        List<CfRefuseReasonEntity> allReason = reasonEntityBiz.selectByReasonIds(param.getRejectIds(), null);
        Map<Integer, String> rejectDetails = Maps.newHashMap();
        int line = 0;
        for (CfRefuseReasonEntity reason : allReason) {
            if (isSameRejectType(reason, type)) {
                msg.append(++line + ": ").append(reason.getContent()).append("  ");
            }
            rejectDetails.put(reason.getId(), reason.getContent());
        }

        param.getCurrImageAuditView().setRejectDetails(rejectDetails);
        return msg.toString();
    }

    private boolean isSameRejectType(CfRefuseReasonEntity reason, int type) {

        List<Integer> itemIds = AdminWorkOrderFlow.splitterFollowTagViews(reason.getItemIds());

        List<Integer> allItemIds = type == CfImageContentAuditInfo.IMAGE ? IMAGE_REJECT_POSITION_IDS :
                CONTENT_REJECT_POSITION_IDS;

        for (Integer id : itemIds) {
            if (allItemIds.contains(id)) {
                return true;
            }
        }

        return false;
    }

    private void saveSnapshot(CfImageContentHandleParam handleVo) {

        handleVo.getCurrImageAuditView().setCallStatus(handleVo.getCallStatus());

        orderExtService.save(handleVo.getCaseId(), handleVo.getWorkOrderId(),
                WorkOrderExtContentTypeEnum.IMAGE_CONTENT_AUDIT, handleVo.getCurrImageAuditView());
    }

    public InitialAuditCaseDetail.CaseBaseInfo editImageContent(CfImageContentEditParam param) {

        log.info("图文工单的图文修改msg:{}", JSON.toJSONString(param));

        CrowdfundingInfo cf = fundingInfoBiz.getFundingInfoById(param.getCaseId());
        if (cf == null) {
            log.error("图文工单修改案例，不能找到案例 caseId:{}",param.getCaseId());
            return null;
        }

        //添加记录
        cfCrowdfundingAttachmentRecordService.insertBatch(param);

        log.info("editImageContent updateContent crowdfundingInfo:{}, title:{}, content:{}, imgUrls:{}, userId:{}", cf, param.getTitle(), param.getContent(), param.getImgUrls(), param.getUserId());

        AdminUserAccountModel userAccount = seaAccountClientV1.getValidUserAccountById(param.getUserId()).getResult();
        CfOperatingRecord cfOperatingRecord = crowdfundingDelegate.before(cf, param.getUserId(), userAccount.getName(), CfOperatingRecordEnum.Type.MODIFY_CONTENT, CfOperatingRecordEnum.Role.OPERATOR);

        if (StringUtils.isNotBlank(param.getTitle()) && StringUtils.isNotBlank(param.getContent()) && param.getImgUrls() != null) {
            caseInfoApproveStageFeignClient.saveStage(param.getCaseId(), param.getTitle(), param.getContent(), param.getImgUrls());
        }

        crowdfundingDelegate.afterCfOperatingRecord(cfOperatingRecord);

        recordEditLog(param, cf);

        return initialSearchService.getBaseInfo(cf);
    }

    private void recordEditLog(CfImageContentEditParam param, CrowdfundingInfo cf) {

        CfImageOperateLog.OperateDetail operateDetail = new CfImageOperateLog.OperateDetail();
        operateDetail.setAuditResult("修改图文");
        operateDetail.setHandleNotes(param.getChangeModify());
        operateDetail.setComment(param.getReason());

        commonOperateClient.create()
                .buildBasicPlatform(param.getWorkOrderId(), Math.toIntExact(param.getUserId()),
                        OperationActionTypeEnum.IMAGE_CONTENT_AUDIT)
                .buildCaseId(param.getCaseId())
                .buildRemark(JSON.toJSONString(operateDetail))
                .save();

        // 详情页的备注
        approveService.addComment(cf.getInfoId(), param.getChangeModify() + (StringUtils.isNotEmpty(param.getReason()) ? "\n" + param.getReason() : ""),
                param.getUserId(),"图文审核", BackgroundLogEnum.IMAGE_CONTENT_HANDLE);
    }

    public List<CfImageOperateLog> queryOperateLog(int caseId) {

        List<CfImageOperateLog> allLogs = Lists.newArrayList();
        List<OperationRecordDTO> allRecords = commonOperateClient.listByCaseIdAndActionTypes(caseId,
                Lists.newArrayList(OperationActionTypeEnum.IMAGE_CONTENT_AUDIT.getValue()));

        if (CollectionUtils.isEmpty(allRecords)) {
            return allLogs;
        }

        for (OperationRecordDTO record : allRecords) {
            CfImageOperateLog log = new CfImageOperateLog();
            log.setWorkOrderId(record.getBizId());
            log.setOperator(record.getNameWithOrg());
            log.setOperateTime(record.getActionTime().getTime());
            log.setOperateDetail(JSON.parseObject(record.getRemark(), CfImageOperateLog.OperateDetail.class));
            allLogs.add(log);
        }

        return allLogs;
    }


    public void sendSms(RiverSendSmsParamVO param){

        CrowdfundingInfo cf = fundingInfoBiz.getFundingInfoById(param.getCaseId());
        if (cf == null) {
            log.warn("通过案例id不能找到caseId:{}", param.getCaseId());
            return ;
        }

        // 发短信
        approveService.sendCaseApproveSmsWithRecord(param.getMobile(), param.getContent(), param.getModelNum(),
                param.getCaseId(), param.getOperatorId(), param.getParamMap());

        String smsMsg = getSmsContent(param);
        if (MapUtils.isNotEmpty(param.getParamMap())) {
            for (Map.Entry<Integer, String> entry : param.getParamMap().entrySet()) {
                smsMsg = smsMsg.replace("{" + entry.getKey() + "}", entry.getValue());
            }
        }
        CfImageOperateLog.OperateDetail operateDetail = new CfImageOperateLog.OperateDetail("", "", "发送短信：<br>短信内容: " + smsMsg
                + "<br>发送手机号：【" + param.getMobile() + "】");
        commonOperateClient.create()
                .buildBasicPlatform(param.getWorkOrderId(), Math.toIntExact(param.getOperatorId()),
                        OperationActionTypeEnum.IMAGE_CONTENT_AUDIT)
                .buildCaseId(param.getCaseId())
                .buildRemark(JSON.toJSONString(operateDetail))
                .save();

        // 详情页的备注
        approveService.addComment(cf.getInfoId(), param.getMobile() + "  " + param.getContent(),
                param.getOperatorId(),"图文审核", BackgroundLogEnum.IMAGE_CONTENT_HANDLE);

    }

    private String getSmsContent(RiverSendSmsParamVO param) {
        if (StringUtils.isNotBlank(param.getContent())) {
            return param.getContent();
        }
        List<SmsTemplate> smsTemplates = sendMsgTemplateBiz.getTemplateByModelNum(param.getModelNum());

        return (org.apache.commons.collections.CollectionUtils.isEmpty(smsTemplates) || smsTemplates.size() > 1) ? "" : smsTemplates.get(0).getText();
    }



}
