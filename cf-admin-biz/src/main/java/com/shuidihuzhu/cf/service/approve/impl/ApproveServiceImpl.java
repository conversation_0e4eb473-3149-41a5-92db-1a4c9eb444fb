package com.shuidihuzhu.cf.service.approve.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.admin.constant.AsyncPoolConstants;
import com.shuidihuzhu.cf.admin.river.impl.RiverCreditFacadeImpl;
import com.shuidihuzhu.cf.admin.river.impl.RiverDiBaoFacadeImpl;
import com.shuidihuzhu.cf.admin.river.impl.RiverPinKunFacadeImpl;
import com.shuidihuzhu.cf.biz.admin.impl.AdminCfInitialAuditCheckInfoService;
import com.shuidihuzhu.cf.biz.aiphoto.ImageWatermarkService;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.biz.crowdfunding.report.AdminCfInfoLostContactService;
import com.shuidihuzhu.cf.biz.risk.IDishonestService;
import com.shuidihuzhu.cf.client.adminpure.model.initial.CfVolunteerRiskRecord;
import com.shuidihuzhu.cf.client.apipure.feign.dp.DpClewFeignClient;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.feign.CfFirstApproveFeignClient;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.client.material.feign.CfRaiseMaterialClient;
import com.shuidihuzhu.cf.client.material.model.CfBasicLivingGuardModel;
import com.shuidihuzhu.cf.client.material.model.CfRaiseFundUseModel;
import com.shuidihuzhu.cf.constant.ResultFieldConstants;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingUserDelegate;
import com.shuidihuzhu.cf.delegate.finance.IFinanceDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.domain.CfCaseRiskDO;
import com.shuidihuzhu.cf.domain.cf.AdminCfInitialAuditCheckInfoDO;
import com.shuidihuzhu.cf.dto.CfMultipleCaseRiskDto;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.enums.crowdfunding.risk.CaseRiskPassedEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.risk.CfCaseRiskTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.risk.CfCaseRiskVerifiedEnum;
import com.shuidihuzhu.cf.facade.AdminApproveFacade;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceCapitalAccountFeignClient;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceDrawCashFeignClient;
import com.shuidihuzhu.cf.finance.client.feign.CfServiceChargeConfigFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.financestate.FinanceState;
import com.shuidihuzhu.cf.finance.model.vo.AdminServiceChargeVo;
import com.shuidihuzhu.cf.finance.model.vo.draw.CfAmountInfoVo;
import com.shuidihuzhu.cf.model.SimilarCaseRecord;
import com.shuidihuzhu.cf.model.admin.PhotoStatus;
import com.shuidihuzhu.cf.model.clew.CfUserInvitedLaunchCaseRecordVO;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.material.credit.CreditSupplementModel;
import com.shuidihuzhu.cf.model.crowdfunding.vo.*;
import com.shuidihuzhu.cf.model.risk.Dishonest;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.admin.CaseInfoApproveSubItemService;
import com.shuidihuzhu.cf.service.approve.ApproveService;
import com.shuidihuzhu.cf.service.approve.CfMultipleCaseRiskService;
import com.shuidihuzhu.cf.service.crowdfunding.*;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditSearchService;
import com.shuidihuzhu.cf.util.crowdfunding.CrowdfundingUtil;
import com.shuidihuzhu.cf.vo.CfFirsApproveMaterialVO;
import com.shuidihuzhu.cf.vo.admin.CfFirstApproveExt;
import com.shuidihuzhu.cf.vo.admin.initialAudit.AdminCfInitialAuditCheckInfoVO;
import com.shuidihuzhu.cf.vo.approve.InitialAuditAdditionInfoVO;
import com.shuidihuzhu.cf.vo.approve.MultipleCaseImagesVo;
import com.shuidihuzhu.cf.vo.approve.RiverDetailVO;
import com.shuidihuzhu.cf.vo.approve.TreatmentVO;
import com.shuidihuzhu.cf.vo.crowdfunding.CfProgressVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CfSupplyProgressVo;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CfCaseSpecialPrePoseDetail;
import com.shuidihuzhu.client.cf.growthtool.model.VolunteerLevelEnum;
import com.shuidihuzhu.client.cf.risk.client.UserTagClient;
import com.shuidihuzhu.client.cf.risk.model.result.UserTagHistory;
import com.shuidihuzhu.client.cf.search.client.CfSearchClient;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.BasicExample;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.proxy.InvocationHandler;
import org.springframework.cglib.proxy.Proxy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Future;

import static java.util.stream.Collectors.toList;

/**
 * 代码抽取
 * <AUTHOR>
 */
@Slf4j
@Service
public class ApproveServiceImpl implements ApproveService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApproveServiceImpl.class);
    @Autowired
    private CfFinanceDrawCashFeignClient cfFinanceDrawCashFeignClient;
    @Autowired
    private AdminCrowdFundingProgressBiz adminCrowdFundingProgressBiz;
    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CfAdminOperationRecordBiz cfAdminOperationRecordBiz;
    @Autowired
    private AdminCfInfoExtBiz adminCfInfoExtBiz;
    @Autowired
    private CfTagItemBiz cfTagItemBiz;
    @Autowired
    private ICrowdfundingUserDelegate crowdfundingUserDelegate;
    @Autowired
    private AdminCrowdfundingOperationBiz adminCrowdfundingOperationBiz;
    @Autowired
    private AdminApproveService adminApproveService;
    @Autowired
    private CfFinanceCapitalAccountFeignClient cfFinanceCapitalAccountFeignClient;
    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Autowired
    private AdminWorkOrderReportBiz adminWorkOrderReportBiz;
    @Autowired
    private CfPhotoAiBiz cfPhotoAiBiz;
    @Resource
    private AdminCfInfoLostContactService adminCfInfoLostContactService;
    @Autowired
    private AdminTaskUgcBiz adminTaskUgcBiz;
    @Resource
    private IRiskDelegate riskDelegate;
    @Resource
    private AdminApproveFacade adminApproveFacade;
    @Autowired
    private AdminCfRepeatInfoBiz repeatInfoBizService;
    @Resource
    private UserTagClient userTagClient;
    @Autowired
    private AdminCrowdfundingAuthorBiz adminCrowdfundingAuthorBiz;
    @Autowired
    private CfMaterialVerityHistoryBiz verityHistoryBiz;
    @Resource
    private CaseInfoApproveSubItemService caseInfoApproveSubItemService;
    @Autowired
    private IFinanceDelegate iFinanceDelegate;
    @Autowired
    private ImageWatermarkService watermarkService;
    @Autowired
    private InitialAuditSearchService initialAuditSearchService;

    @Autowired
    private FirstApproveService firstApproveService;
    @Autowired
    private CfFirstApproveFeignClient firstApproveClient;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Resource
    private AdminCfInitialAuditCheckInfoService checkInfoService;

    @Autowired
    private IFinanceDelegate financeDelegate;
    @Autowired
    private CfSupplyProgressService cfSupplyProgressService;
    @Autowired
    private RiverCreditFacadeImpl riverCreditAdapter;

    @Autowired
    private RiverPinKunFacadeImpl riverPinKunFacade;

    @Autowired
    private RiverDiBaoFacadeImpl riverDiBaoFacade;

    @Autowired
    private CfRaiseMaterialClient cfRaiseMaterialClient;

    @Autowired
    private ClewPreproseMaterialFeignClient clewPreproseMaterialFeignClient;

    @Autowired
    private IDishonestService dishonestService;
    @Autowired
    private CfCailiaoService cailiaoService;
    @Resource(name = AsyncPoolConstants.APPROVE_DETAIL_ASYNC_POOL)
    private Executor approveDetailAsyncExecutor;

    @Resource
    private CfServiceChargeConfigFeignClient cfServiceChargeConfigFeignClient;
    @Autowired
    private CfVolunteerRiskRecordBiz cfVolunteerRiskRecordBiz;
    @Resource
    private DpClewFeignClient dpClewFeignClient;

    @Autowired
    private MaskUtil maskUtil;
    @Autowired
    private AdminCrowdfundingInfoPayeeBiz adminCrowdfundingInfoPayeeBiz;
    @Autowired
    private CfSearchClient cfSearchClient;
    @Autowired
    private CfMultipleCaseRiskService cfMultipleCaseRiskService;
    @Autowired
    private CfMultipleCaseRiskServiceImpl cfMultipleCaseRiskServiceImpl;

    @Override
    public Response<Map<String, Object>> detail(String infoUuid, Integer reportWorkOrderId) {
        if (null == infoUuid) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        CompletableFuture<List<CfReportView>> reportFuture = CompletableFuture.supplyAsync(()->{
            return adminApproveService.getReportList(crowdfundingInfo.getId());
        }, approveDetailAsyncExecutor);

        CompletableFuture<CfAmountInfoVo> amountFuture = CompletableFuture.supplyAsync(()->{
            return financeDelegate.getFundraiserAmountInfo(infoUuid);
        }, approveDetailAsyncExecutor);


        CrowdfundingInfoVo crowdfundingInfoVo = crowdfundingInfoBiz.getFundingInfoVoByInfoUuid(infoUuid);
        int crowdfundingId = crowdfundingInfoVo.getId();
        String payeeIdCardEncrypt = crowdfundingInfoVo.getPayeeIdCard();

        Map<String, Object> result = Maps.newHashMap();

        boolean AIPhotoExist = cfPhotoAiBiz.checkAIPhotoExist(crowdfundingId);
        result.put("aiPhotoExist", AIPhotoExist);

        CrowdfundingAuthor crowdfundingAuthor = adminApproveService.aesDecryptIdcardAndMobile(crowdfundingInfoVo);

        String patientIdCardEnc = Optional.ofNullable(crowdfundingAuthor)
                .map(CrowdfundingAuthor::getCryptoIdCard)
                .orElse(StringUtils.EMPTY);
        if(crowdfundingAuthor != null){
            PhotoStatus photoStatus = cfPhotoAiBiz.selectPhotoStatus(crowdfundingId);
            if (AIPhotoExist && Objects.nonNull(photoStatus)) {
                String description = Optional.ofNullable(photoStatus.getDescription())
                        .orElse("");
                photoStatus.setDescription(description);
                result.put("photoStatus", photoStatus);
                result.put("nameCheck", photoStatus.getName().equals(crowdfundingAuthor.getName()));
                result.put("idCardCheck", idCardCheckJudge(photoStatus, crowdfundingAuthor));
            }
            crowdfundingAuthor.setCryptoIdCard(null);
            crowdfundingAuthor.setCryptoPhone(null);
            crowdfundingAuthor.setIdCard(null);
            crowdfundingAuthor.setPhone(null);
        }

        CrowdfundingOperation crowdfundingOperation = this.adminCrowdfundingOperationBiz.getByInfoId(infoUuid);

        CrowdfundingOperationVo crowdfundingOperationVo = adminApproveService.setOperatorName(crowdfundingOperation);

        int caseId = crowdfundingInfo.getId();
        List<CfTagItem> tagList = cfTagItemBiz.getByInfoId(caseId);
        if (tagList != null && tagList.size() != 0) {
            result.put("tagList", tagList);
        }
        CfOperationRecord contactRecord = cfAdminOperationRecordBiz.selectContact(infoUuid);
        if (contactRecord != null) {
            result.put("contactRecord", contactRecord);
        }

        //此处的逻辑为了兼容老逻辑、暂时保留，以后使用下面的逻辑

        List<CfCreditSupplement> cfCreditSupplements = crowdfundingUserDelegate.selectByInfoUuid(infoUuid);
        if (cfCreditSupplements != Collections.EMPTY_LIST) {
            result.put("cfCreditSupplements", cfCreditSupplements);
        }
        //此处的逻辑为了兼容老逻辑、暂时保留，以后使用下面的逻辑

        OpResult<CreditSupplementModel> creditSupplementModel = crowdfundingUserDelegate.getCreditSupplementByCaseId(crowdfundingId);

        if (creditSupplementModel.isSuccess()) {
            result.put("creditSupplementModel", creditSupplementModel.getData());
        }
        // 获取平台服务费配置
        CompletableFuture<AdminServiceChargeVo> serviceChargeConfigFuture = CompletableFuture.supplyAsync(() -> {
            FeignResponse<AdminServiceChargeVo> resp = cfServiceChargeConfigFeignClient.getByCaseId(caseId);
            if (Objects.isNull(resp) || resp.notOk()) {
                return new AdminServiceChargeVo();
            }
            return resp.getData();
        }, approveDetailAsyncExecutor);

        //获取案例需要审核的材料列表
        List<CrowdfundingInfoDataStatusTypeEnum> requiredDataTypes = CrowdfundingUtil.getRequiredCaseList(adminCfInfoExtBiz.getByInfoUuid(infoUuid));
        List<Integer> requiredCodeList = requiredDataTypes.stream().map(CrowdfundingInfoDataStatusTypeEnum::getCode).collect(toList());
        result.put("requiredCodeList", requiredCodeList);
        result.put("idCaseData", crowdfundingDelegate.getCrowdfundingIdCaseByInfoId(crowdfundingId));

        result.put("rejectDetails", adminApproveService.getRejectDetail(crowdfundingInfo));
        result.put("dataTypeList", adminApproveService.getCrowdfundingInfoStatusInfo(infoUuid));
        result.put("crowdfundingAuthor", crowdfundingAuthor);
        result.put("crowdfundingAttachmentList", repeatInfoBizService.getFundingAttachmentWithRepeatInfo(crowdfundingId));
        TreatmentVO treatmentVO = crowdfundingUserDelegate.getCrowdfundingTreatmentVO(crowdfundingId);
        result.put("crowdfundingTreatment", treatmentVO);
        result.put("comments", adminApproveService.getCommentVoListWithGroupComments(crowdfundingId));
        CfCapitalAccount cfCapitalAccount = null;
        try {
            FeignResponse<CfCapitalAccount> response = cfFinanceCapitalAccountFeignClient.capitalAccountGetByInfoUuid(infoUuid);
            if (response == null || response.notOk()) {
                log.error("finance服务异常");
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
            }
            cfCapitalAccount = response.getData();
        } catch (Exception e) {
            log.error("finance服务异常", e);
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }

        // 平台服务费
        if (Objects.nonNull(cfCapitalAccount)) {
            // 是否收取平台服务费
            AdminServiceChargeVo adminServiceChargeVo = getResult(serviceChargeConfigFuture);
            result.put("adminServiceCharge", adminServiceChargeVo);
        }
        // 收款人是否失信被执行人
        AdminCrowdfundingInfoView crowdfundingInfoView = adminApproveService.getCrowdfundingInfoView(crowdfundingInfoVo, cfCapitalAccount);
        Dishonest dishonestInfo = dishonestService.getDishonestInfo(caseId, 1,crowdfundingInfoView.getPayeeName());
        crowdfundingInfoView.setPayeeDithonest(dishonestInfo != null);
        // 不暴漏敏感信息明文
        crowdfundingInfoView.setPayeeMobile(null);
        crowdfundingInfoView.setPayeeIdCard(null);
        crowdfundingInfoView.setPayeeBankCard(null);
        crowdfundingInfoView.setPayeeRepeatWarn(this.getPayeeRepeat(payeeIdCardEncrypt, crowdfundingId, patientIdCardEnc));
        result.put("crowdfundingInfo", crowdfundingInfoView);
        if (crowdfundingOperationVo.getOperation() == CrowdfundingOperationEnum.OPERATED.value()) {
            CfOperationRecord cfOperationRecord = cfAdminOperationRecordBiz.selectOpByInfoId(infoUuid);
            if (cfOperationRecord != null) {
                result.put("lastOperation", cfOperationRecord.getOperation());
            }
        }
        boolean isOldCrowdFunding = CrowdfundingUtil.decideIsOldCrowdFunding(adminCfInfoExtBiz.getByInfoUuid(infoUuid));
        result.put("isOldCrowdFunding", isOldCrowdFunding);
        result.put("crowdfundingOperation", crowdfundingOperationVo);

        AdminCrowdfundingInfoView v = (AdminCrowdfundingInfoView) result.get("crowdfundingInfo");

        CrowdfundingInfoPayee payee = crowdfundingDelegate.getCrowdfundingInfoPayeeByInfoUuid(infoUuid);
        CrowdfundingRelationType relationType = crowdfundingInfo.getRelationType();
        boolean publicPay = relationType == CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT || relationType == CrowdfundingRelationType.charitable_organization;

        if (payee != null) {
            String emergencyPhone = payee.getEmergencyPhone();
            if (StringUtils.isNotEmpty(emergencyPhone)) {
                payee.setEmergencyPhoneMask(maskUtil.buildByEncryptPhone(emergencyPhone));
                payee.setEmergencyPhone(null);
            }
            result.put("crowdfundingInfoPayee", payee);
            // 标记是否收款人是否内部顾问
            if (!publicPay && payee.getIdType() == UserIdentityType.identity.getCode()) {
                List<CfVolunteerRiskRecord> riskRecords = cfVolunteerRiskRecordBiz.getByCaseId(caseId);
                if (CollectionUtils.isNotEmpty(riskRecords) && riskRecords.stream().anyMatch(r -> r.getPatientIdCard().equals(payee.getIdCard()))) {
                    CfVolunteerRiskRecord cfVolunteerRiskRecord = riskRecords.stream().filter(r -> r.getPatientIdCard().equals(payee.getIdCard())).findFirst().orElse(new CfVolunteerRiskRecord());
                    int riskType = cfVolunteerRiskRecord.getRiskType();
                    result.put("payeeRiskTypeStr", Optional.ofNullable(VolunteerLevelEnum.parseByLevel(riskType)).map(VolunteerLevelEnum::getDesc).orElse(StringUtils.EMPTY));
                }
            }
        }

        // 添加对公打款的信息
        CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee = crowdfundingDelegate
                .getCrowdfundingInfoHospitalPayeeByInfoUuid(infoUuid);
        if (crowdfundingInfoHospitalPayee == null) {
            crowdfundingInfoHospitalPayee = new CrowdfundingInfoHospitalPayee();
        } else{
            crowdfundingInfoHospitalPayee.setHospitalBankCardMask(
                    maskUtil.buildByDecryptStrAndType(crowdfundingInfoHospitalPayee.getHospitalBankCard(), DesensitizeEnum.BANKNO));
            crowdfundingInfoHospitalPayee.setHospitalBankCard(null);
            crowdfundingInfoHospitalPayee.setHospitalEmergencyPhoneMask(
                    maskUtil.buildByEncryptPhone(crowdfundingInfoHospitalPayee.getHospitalEmergencyPhone()));
            crowdfundingInfoHospitalPayee.setHospitalEmergencyPhone(null);
        }
        result.put("crowdfundingHospitalPayee", crowdfundingInfoHospitalPayee);

        //添加慈善组织
        CfCharityPayee cfCharityPayee = crowdfundingDelegate.getCfCharityPayeeByUUid(infoUuid);
        if (cfCharityPayee != null) {
            cfCharityPayee.setOrgMobile(shuidiCipher.decrypt(cfCharityPayee.getOrgMobile()));
            cfCharityPayee.setOrgBankCard(shuidiCipher.decrypt(cfCharityPayee.getOrgBankCard()));
            List<CrowdfundingAttachmentVo> attachmentVos = crowdfundingDelegate.getAttachmentsByType(crowdfundingId, AttachmentTypeEnum.ATTACH_CHARITY);
            StringBuilder sb = new StringBuilder();
            attachmentVos.stream().forEach(r -> {
                sb.append(r.getUrl()).append(",");
            });
            cfCharityPayee.setOrgPic(sb.substring(0, sb.length() - 1));
            if (CrowdfundingRelationType.charitable_organization.equals(v.getRelationType())) {
                v.setRelationType(CrowdfundingRelationType.charitable_organization);
            }
            result.put("cfCharityPayee", cfCharityPayee);
        }

        CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByInfoUuid(infoUuid);
        result.put("cfInfoExt", cfInfoExt);

        Map<String, Object> firstApprove = Maps.newHashMap();
        if (cfInfoExt != null) {
            int finishStatus = cfInfoExt.getFinishStatus();
            for (CfFinishStatus cfFinishStatus : CfFinishStatus.values()) {
                if (cfFinishStatus.getValue() == finishStatus) {
                    if (crowdfundingInfo.getEndTime().getTime() <= System.currentTimeMillis()
                            && finishStatus == CfFinishStatus.NOT_FINISH.getValue()) {
                        result.put("finishMsg", CfFinishStatus.EXPIRE.getDescription());
                    } else {
                        result.put("finishMsg", cfFinishStatus.getDescription());
                    }
                }
            }
            firstApprove.put("firstApproveStatus", cfInfoExt.getFirstApproveStatus());

        }
        CfFaceIdLivingVerifyInfo cfFaceIdLivingVerifyInfo = riskDelegate.getCfFaceIdLivingVerifyInfoByInfoUuid(infoUuid);
        if (cfFaceIdLivingVerifyInfo != null) {
            result.put("faceId", cfFaceIdLivingVerifyInfo.getPhotoValid() == CfSimpleTrueOrFalseEnum.TRUE.value());
        }

        CfBaseInfoTemplateRecord cfBaseInfoTemplateRecord = crowdfundingDelegate.selectByInfoUuid(infoUuid);
        boolean isAuthorNameSame = true;
        if (cfBaseInfoTemplateRecord != null && crowdfundingAuthor != null && StringUtils.isNotBlank(cfBaseInfoTemplateRecord.getAuthorName())) {
            String name = crowdfundingAuthor.getName();
            isAuthorNameSame = StringUtils.equals(cfBaseInfoTemplateRecord.getAuthorName(), name);
        }
        result.put("isAuthorNameSame", isAuthorNameSame);

        UserTagHistory userTagHistory = userTagClient.getOperatorValid(crowdfundingId);
        if (userTagHistory != null && CollectionUtils.isNotEmpty(userTagHistory.getUnitList())) {
            result.put("userTagHistoryUnits", userTagHistory.getUnitList());
        }

        // 设置资金信息
        try {
            boolean getFinanceDetailResult = adminApproveService.setCapitalData(infoUuid, result, crowdfundingId);
            if (!getFinanceDetailResult) {
                log.error("无法获取资金操作记录");
                return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, null);
            }
            //TODO：老版资金状态也在内部，逐步干掉
            result.putAll(iFinanceDelegate.getCapitalData(infoUuid));

            //兼容版资金状态展示
            result.put("cfCapitalStatusDesc", "无");
            FinanceState financeState = iFinanceDelegate.getFinanceState(caseId);
            if (financeState != null) {
                result.put("cfCapitalStatusDesc", financeState.getSubStatus().getDesc());
            }

            adminApproveService.getSuccessDrawCashAmount(infoUuid, result);
        } catch (Exception e) {
            LOGGER.error("getCapitalData error:e", e);
        }

        // 镜像信息
//        adminApproveService.setMirrorData(infoUuid, result);
        int highRiskStatus = 0;
        if (reportWorkOrderId != null && reportWorkOrderId > 0) {
            AdminWorkOrderReport adminWorkOrderReport = adminWorkOrderReportBiz.selectWorkOrderReportById(reportWorkOrderId);
            highRiskStatus = adminWorkOrderReport == null ? 0 : adminWorkOrderReport.getCaseRisk();
        }
        result.put("highRiskStatus", highRiskStatus);

        // 收款人照片信息 OCR识别结果
        PhotoStatus selectPhotoStatus = cfPhotoAiBiz.selectPhotoStatus(crowdfundingId, 100);
        if (Objects.nonNull(selectPhotoStatus)) {
            result.put("payeePhotoStatus", selectPhotoStatus);
        }

        // 是否失联
        boolean hasLost = adminCfInfoLostContactService.hasLost(infoUuid);
        result.put(ResultFieldConstants.CONTACT_HAS_LOST, hasLost);

        CfFirsApproveMaterial material = riskDelegate.getCfFirsApproveMaterialByInfoId(crowdfundingId);
        // 前置审核添加目标金额
        CfFirsApproveMaterialVO materialVO = firstApproveService.buildWithMaterial(material);
        materialVO.setTargetAmount(crowdfundingInfo.getTargetAmount());
        CfFirstApproveExt ext = watermarkService.getFirstApproveExt(caseId, materialVO);
        firstApprove.put("firstApprove", watermarkService.getFirstApproveExt(caseId, materialVO));
        String comment = adminTaskUgcBiz.selectFirstCommentByCaseId(crowdfundingId);
        firstApprove.put("firstApproveComment", comment);
        // 获取前置审核材料名称校验结果
        List<AdminCfInitialAuditCheckInfoDO> checkInfoDOS = checkInfoService.listByCaseIdOrderByCheckType(caseId);
        if (CollectionUtils.isNotEmpty(checkInfoDOS)) {
            List<AdminCfInitialAuditCheckInfoVO> voList = Lists.newArrayList();
            checkInfoDOS.forEach(infoDO -> voList.add(new AdminCfInitialAuditCheckInfoVO(infoDO)));
            firstApprove.put("checkInfo", voList);
        }
        // 获取患者是否是失信被执行人
        Dishonest info = dishonestService.getDishonestInfo(caseId, 0, ext.getPatientRealName());
        if (info != null){
            firstApprove.put("patientDishonest", true);
        }else{
            firstApprove.put("patientDishonest", false);
        }
        result.put("firstApprove", firstApprove);


        // 案例数据审核通过状态
        // 审核数据状态
        result.put("caseInfoRiskStatus", getRiskStatus(infoUuid, CfCaseRiskTypeEnum.CASE_INFO_RISK));
        // 打款数据状态
        result.put("caseDrawCashRiskStatus", getRiskStatus(infoUuid, CfCaseRiskTypeEnum.DRAW_CASH_RISK));
        // 志愿者的信息
        result.put("volunteer", adminApproveService.getVolunteerDetail(cfInfoExt));
        result.put("detailGuideUserLaunchChannel", adminApproveService.getChannelDesc(caseId));
        Optional<CfUserInvitedLaunchCaseRecordVO> optional = adminApproveService.getCaseChannelRecordVO(caseId);
        if (!optional.isEmpty()) {
            result.put("CfUserInvitedLaunchCaseRecordVO", optional.get());
        }

        // 填充材料审核子项审核信息
        result.put("subApproveInfo", caseInfoApproveSubItemService.getSubApproveItemStatus(caseId));

        // 审核记录总览信息 包含每种类型审核记录总数与最新一次审核记录详情
        result.put("approveHistoryOverview", verityHistoryBiz.getHistoryOverview(infoUuid));
        // 资金是否是初始化状态
        result.put("fundInitialized", adminApproveService.isFundInitialized(caseId));
        result.put("isShowSpecialRecoverButton", adminApproveFacade.ifCaseCanSpecialRecover(cfInfoExt));

        // 新增信信息
        result.put("creditInfo", initialAuditSearchService.getCreditInfoVO(caseId));

        // 前置报备
        result.put("prepose", initialAuditSearchService.getLastPrepose(caseId));

        // 增信操作信息
        result.put("creditHandleInfo", riverCreditAdapter.getHandleInfo(caseId));

        // 低保贫困信息
        CfBasicLivingGuardModel diBaoAndPinKunInfo = Optional.ofNullable(riverDiBaoFacade.getDetail(caseId, 0))
                .map(RiverDetailVO::getInfo)
                .map(InitialAuditAdditionInfoVO::getDiBaoAndPinKunInfo)
                .orElse(null);
        result.put("diBaoAndPinKunInfo", watermarkService.getLivingGuardView(caseId, diBaoAndPinKunInfo));

        // 低保操作信息
        result.put("diBaoHandleInfo", riverDiBaoFacade.getHandleInfo(caseId));

        // 贫困操作信息
        result.put("pinKunHandleInfo", riverPinKunFacade.getHandleInfo(caseId));


        //展示下发动态按钮信息
        CfSupplyProgressVo.SupplyProgressButtonInfo supplyButtonInfo = cfSupplyProgressService.getSupplyButtonInfo(caseId);
        if (supplyButtonInfo != null) {
            result.put("supplyProgressButtonInfo", supplyButtonInfo);
        }

        RpcResult<CfRaiseFundUseModel> fundUseResp = cfRaiseMaterialClient.selectFundUse(caseId);
        CfRaiseFundUseModel fundUseRespData = Optional.ofNullable(fundUseResp).map(RpcResult::getData).orElse(null);
        // 资金用户信息
        result.put("fundUseInfo", fundUseRespData);
        List<CfReportView> crowdfundingReportList = getResult(reportFuture);
        // 证件号掩码
        if (CollectionUtils.isNotEmpty(crowdfundingReportList)) {
            crowdfundingReportList
                    .stream()
                    .filter(r -> StringUtils.isNotBlank(r.getIdentity()))
                    .forEach(r -> {
                        String identity = r.getIdentity();
                        NumberMaskVo identifyMask = maskUtil.buildByDecryptStrAndType(identity, DesensitizeEnum.IDCARD);
                        r.setIdentifyMask(identifyMask);
                        r.setIdentity(StringUtils.EMPTY);
                    });
        }
        result.put("crowdfundingReportList", crowdfundingReportList);
        // 筹款人看到的金额
        CfAmountInfoVo cfAmountInfoVo = getResult(amountFuture);
        result.put("fundraiserAmountInfo", cfAmountInfoVo);

        //代录入手机号
        Response<List<CfCaseSpecialPrePoseDetail>> response = clewPreproseMaterialFeignClient.getSpecialPrePoseDetail(Lists.newArrayList(caseId));
        List<CfCaseSpecialPrePoseDetail> l = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(l)) {
            NumberMaskVo prepostMobileMask = l.stream()
                    .filter(r -> StringUtils.isNotEmpty(r.getMobile()))
                    .map(r -> maskUtil.buildByDecryptPhoneWithOldEncrypt(r.getMobile()))
                    .findAny().orElse(null);
            result.put("prePostMobileMask", prepostMobileMask);
            result.put("prePostMobile", null);
        } else {
            result.put("prePostMobile", "");
            result.put("prePostMobileMask", null);
        }


        result.put("supplyMap", setSupplyMap(treatmentVO, crowdfundingAuthor, materialVO, crowdfundingInfoVo, payee, crowdfundingInfoHospitalPayee, cfAmountInfoVo));

        result.put("markMaterialType", cailiaoService.isNeedMarkMaterialType());
        OperationResult<Boolean> checkFangPinCase = dpClewFeignClient.checkFangPinCase(caseId);
        Boolean aBoolean = Optional.ofNullable(checkFangPinCase)
                .map(OperationResult::getData)
                .orElse(false);
        result.put("checkFangPinCase", aBoolean);

        return NewResponseUtil.makeSuccess(result);
    }

    /**
     * 获取案例材审详情页的票据归纳
     * @param workOrderId
     * @return
     */
    @Override
    public Response<MultipleCaseImagesVo> getMultipleCaseImages(long workOrderId, int caseId) {
        if (workOrderId <= 0 && caseId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        MultipleCaseImagesVo res = new MultipleCaseImagesVo();
        CfMultipleCaseRiskDto multipleCaseRisk = null;
        if (workOrderId > 0) {
            multipleCaseRisk = cfMultipleCaseRiskService.getRecordByWorkOrderId(workOrderId);
        } else {
            multipleCaseRisk = cfMultipleCaseRiskService.getLatestRecordByCaseId(caseId);
        }
        if (multipleCaseRisk == null) {
            return NewResponseUtil.makeSuccess(res);
        }
        res.setFirstApproveTime(multipleCaseRisk.getFirstApproveTime());
        res.setReceivedMoneyInFen(multipleCaseRisk.getAmount());
        res.setHaveMultipleCaseRisk(multipleCaseRisk.getHaveAmountRisk());

        int currentCaseId = multipleCaseRisk.getCaseId();
        // 当前案例的动态图片
        MultipleCaseImagesVo.CaseImages currentCase = new MultipleCaseImagesVo.CaseImages();
        currentCase.setCaseId(currentCaseId);
        currentCase.setCfProgressVoList(this.getProgressVosByCaseId(currentCaseId));
        res.getCaseImages().add(currentCase);

        List<CrowdfundingInfo> sameIdCardCaseList = new ArrayList<>();
        List<SimilarCaseRecord> similarCaseRecordList = JSON.parseArray(multipleCaseRisk.getSimilarCase(), SimilarCaseRecord.class);
        List<Integer> caseIdList = similarCaseRecordList == null ? new ArrayList<>() : similarCaseRecordList.stream().map(SimilarCaseRecord::getCaseId).collect(toList());
        caseIdList.add(currentCaseId);
        sameIdCardCaseList = crowdfundingInfoBiz.getListByIds(caseIdList);

        CrowdfundingInfo currentCaseInfo = sameIdCardCaseList.stream().filter(crowdfundingInfo -> crowdfundingInfo.getId() == currentCaseId).findFirst().orElse(null);
        if (currentCaseInfo == null) {
            return NewResponseUtil.makeSuccess(res);
        }

        sameIdCardCaseList = sameIdCardCaseList.stream().filter(crowdfundingInfo -> crowdfundingInfo.getCreateTime().before(currentCaseInfo.getCreateTime())).collect(toList());
        // 获取同身份证历史案例的材料图片
        if (CollectionUtils.isNotEmpty(sameIdCardCaseList)) {
            searchMaterialImages(sameIdCardCaseList, res);
        }

        return NewResponseUtil.makeSuccess(res);
    }


    private void searchMaterialImages(List<CrowdfundingInfo> sameIdCardCaseList, MultipleCaseImagesVo res) {
        if (CollectionUtils.isEmpty(sameIdCardCaseList)) {
            return;
        }
        // 疾病诊断证明
        Set<Integer> hospitalAuditAttachmentType = Sets.newHashSet(AdminAttachmentTypeEnum.ATTACH_MEDICAL_RECORD_HOME.value(),
                AdminAttachmentTypeEnum.ATTACH_PASS_HOSPITAL.value(), AdminAttachmentTypeEnum.ATTACH_TREATMENT_NOTE.value(),
                AdminAttachmentTypeEnum.ATTACH_INSPECTION_REPORT.value(), AdminAttachmentTypeEnum.ATTACH_EXTRA.value(),
                AdminAttachmentTypeEnum.ATTACH_TREATMENT.value(), AdminAttachmentTypeEnum.ATTACH_IN_HOSPITAL.value(),
                AdminAttachmentTypeEnum.ATTACH_LEAVE_HOSPITAL.value());
        Map<Integer, List<CrowdfundingAttachmentVo>> attachmentMap = repeatInfoBizService.getFundingAttachmentWithRepeatInfoBatch(sameIdCardCaseList.stream().map(CrowdfundingInfo::getId).collect(toList()));
        if (MapUtils.isEmpty(attachmentMap)) {
            return;
        }
        for (Map.Entry <Integer, List<CrowdfundingAttachmentVo>> entry: attachmentMap.entrySet()) {
            MultipleCaseImagesVo.CaseImages caseImage  = new MultipleCaseImagesVo.CaseImages();
            int historyCaseId = entry.getKey();
            caseImage.setCaseId(historyCaseId);
            caseImage.setCfProgressVoList(this.getProgressVosByCaseId(historyCaseId));
            caseImage.setPreAuditImageUrl(this.getPreAuditImage(historyCaseId));
            List<CrowdfundingAttachmentVo> attachmentVoList = entry.getValue();
            caseImage.setHospitalAttachmentList(attachmentVoList.stream().filter(crowdfundingAttachmentVo -> hospitalAuditAttachmentType.contains(crowdfundingAttachmentVo.getType()))
                    .sorted((o1, o2) -> o2.getCreatetime() > o1.getCreatetime()? 1 : -1).collect(toList()));
            caseImage.setImageAuditAttachmentList(attachmentVoList.stream().filter(crowdfundingAttachmentVo -> crowdfundingAttachmentVo.getType() == AdminAttachmentTypeEnum.ATTACH_CF.value())
                    .sorted((o1, o2) -> o2.getCreatetime() > o1.getCreatetime()? 1 : -1).collect(toList()));
            res.getCaseImages().add(caseImage);
        }
        res.getCaseImages().sort(Comparator.comparing(MultipleCaseImagesVo.CaseImages::getCaseId).reversed());
    }

    private String getPreAuditImage(int caseId) {
        CfFirsApproveMaterial material = riskDelegate.getCfFirsApproveMaterialByInfoId(caseId);
        return material == null? "" : material.getImageUrl();
    }
    /**
     * 获取案例的动态图片
     * @param caseId
     * @return
     */
    private List<CfProgressVo> getProgressVosByCaseId(int caseId) {
        BasicExample basicExample = new BasicExample();
        BasicExample.Criteria criteria = basicExample.or();
        criteria.andEqualTo("crowdfunding_id", caseId);
        List<CrowdFundingProgress> crowdFundingProgresses = adminCrowdFundingProgressBiz.queryAllByBasicExample(basicExample);
        return adminCrowdFundingProgressBiz.getProgressOfActivity(crowdFundingProgresses).stream().sorted(Comparator.comparing(CrowdFundingProgress::getId).reversed()).collect(toList());
    }

    private boolean idCardCheckJudge(PhotoStatus photoStatus, CrowdfundingAuthor crowdfundingAuthor) {

        String cryptoIdCard = crowdfundingAuthor.getCryptoIdCard();
        if (StringUtils.isEmpty(cryptoIdCard)) {
            return false;
        }

        String realIdCard = shuidiCipher.decrypt(cryptoIdCard);

        if (StringUtils.isBlank(photoStatus.getIdCardNumber())) {
            return StringUtils.isNotBlank(photoStatus.getIdNumber()) && photoStatus.getIdNumber().equals(realIdCard);
        }

        if (StringUtils.isNotBlank(photoStatus.getIdCardNumber()) && !"无法识别".equals(photoStatus.getIdCardNumber())) {
            String realNumber = shuidiCipher.decrypt(photoStatus.getIdCardNumber());
            return StringUtils.isNotBlank(realNumber) && realNumber.equals(realIdCard);
        }

        return false;
    }

    private boolean getPayeeRepeat(String currentPayeeIdCardEncrypt, int currentCaseId, String currentPatientIdCardEncrypt) {
        // 当前案例是否存在收款人信息
        if (StringUtils.isAnyBlank(currentPayeeIdCardEncrypt, currentPatientIdCardEncrypt) || currentCaseId <= 0) {
            return false;
        }
        // 查询收款人卡号相同的案例
        List<Integer> otherCaseIds = adminCrowdfundingInfoPayeeBiz.selectCaseIdsByPayeeIdCard(currentPayeeIdCardEncrypt)
                .stream()
                .filter(r -> r != currentCaseId)
                .collect(toList());
        if (CollectionUtils.isEmpty(otherCaseIds)) {
            return false;
        }
        // 如果存在，再判断这些案例中是否存在患者身份证号与当前案例不一致的数据
        for (List<Integer> item : Lists.partition(otherCaseIds, 200)) {
            List<CrowdfundingAuthor> crowdfundingAuthors = adminCrowdfundingAuthorBiz.selectByCaseIdList(item);
            if (CollectionUtils.isNotEmpty(crowdfundingAuthors) &&
                    crowdfundingAuthors.stream().anyMatch(r -> !StringUtils.equals(currentPatientIdCardEncrypt, r.getCryptoIdCard()))) {
                return true;
            }
        }
        return false;
    }

    private static Map<String, Object> bindMap(Map<String, Object> sourceMap) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        return (Map<String, Object>) Proxy.newProxyInstance(sourceMap.getClass().getClassLoader(),
                sourceMap.getClass().getInterfaces(), new InvocationHandler() {
                    @Override
                    public Object invoke(Object o, Method method, Object[] objects) throws Throwable {
                        stopWatch.stop();
                        log.info("ApproveServiceImpl map invoke {}, time {}", JSON.toJSONString(objects), stopWatch.getTime());
                        Object invoke = method.invoke(sourceMap, objects);
                        stopWatch.reset();
                        stopWatch.start();
                        return invoke;
                    }
                });
    }


    /**
     * 0 未验证 1 通过 2 异常
     *
     * @param infoUuid
     * @param type
     * @return
     */
    private int getRiskStatus(String infoUuid, CfCaseRiskTypeEnum type) {
        OpResult<CfCaseRiskDO> infoRisk = riskDelegate.getByInfoUuid(infoUuid, type);
        if (infoRisk.isFailOrNullData()) {
            return 0;
        }
        CfCaseRiskDO data = infoRisk.getData();
        int verified = data.getVerified();
        if (verified == CfCaseRiskVerifiedEnum.WAITING_VERIFY.getVerified()) {
            return 0;
        }
        int passed = data.getPassed();
        return CaseRiskPassedEnum.isPassed(passed) ? 1 : 2;
    }


    private <T> T getResult(Future<T> future) {
        if (future == null) {
            return null;
        }

        try {
            return future.get();
        } catch (Throwable e) {
            log.error("获取异步结果失败.future:{}", future, e);
        }
        return null;
    }


    private Map<String, String> setSupplyMap(TreatmentVO treatmentVO,
                                             CrowdfundingAuthor crowdfundingAuthor,
                                             CfFirsApproveMaterialVO materialVO,
                                             CrowdfundingInfoVo crowdfundingInfoVo,
                                             CrowdfundingInfoPayee payee,
                                             CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee,
                                             CfAmountInfoVo cfAmountInfoVo) {
        Map<String, String> supplyMap = Maps.newHashMap();

        supplyMap.put("诊断中疾病", Optional.ofNullable(treatmentVO).map(TreatmentVO::getDiseaseName).orElse(""));
        Optional<String> name = Optional.ofNullable(crowdfundingAuthor).map(CrowdfundingAuthor::getName).filter(StringUtils::isNotEmpty);
        if (!name.isPresent()) {
            name = Optional.ofNullable(materialVO).map(CfFirsApproveMaterialVO::getPatientRealName);
        }
        supplyMap.put("患者姓名", name.orElse(""));

        supplyMap.put("收款人与患者关系", Optional.ofNullable(crowdfundingInfoVo)
                .map(CrowdfundingInfoVo::getRelationType)
                .filter(r -> r != CrowdfundingRelationType.charitable_organization || r != CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT)
                .map(CrowdfundingRelationType::getDescription).orElse(""));
        String payeeName = Optional.ofNullable(payee).map(CrowdfundingInfoPayee::getName).orElse("");
        supplyMap.put("收款人姓名", payeeName);
        supplyMap.put("收款账户", payeeName);

        supplyMap.put("发起人姓名", Optional.ofNullable(materialVO).map(r -> {
            if (r.getRelType() == UserRelTypeEnum.SELF) {
                return r.getPatientRealName();
            } else {
                return r.getSelfRealName();
            }
        }).orElse(""));

        supplyMap.put("修改前目标金额", Optional.ofNullable(materialVO).map(r -> r.getTargetAmount() / 100).map(String::valueOf).orElse(""));

        supplyMap.put("已提现金额", Optional.ofNullable(cfAmountInfoVo).map(r -> r.getAlreadyDrawCashAmountInFen() / 100).map(String::valueOf).orElse(""));

        if (Optional.ofNullable(crowdfundingInfoVo).filter(r -> r.getRelationType() == CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT).isPresent()) {
            supplyMap.put("医院名称", Optional.ofNullable(crowdfundingInfoHospitalPayee).map(CrowdfundingInfoHospitalPayee::getHospitalAccountName).orElse(""));
            supplyMap.put("科室名称", Optional.ofNullable(crowdfundingInfoHospitalPayee).map(CrowdfundingInfoHospitalPayee::getDepartment).orElse(""));
        } else {
            supplyMap.put("医院名称", Optional.ofNullable(treatmentVO).map(TreatmentVO::getHospitalName).orElse(""));
        }

        return supplyMap;
    }

}
