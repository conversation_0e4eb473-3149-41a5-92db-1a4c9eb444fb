package com.shuidihuzhu.cf.service;

import brave.Tracing;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.shuidihuzhu.cf.dao.admin.CfScheduleAlarmDao;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.domain.cf.CfScheduleAlarm;
import com.shuidihuzhu.cf.enhancer.subject.alarm.AlarmHelper;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.markdown.Html2ImageWrapperUtils;
import com.shuidihuzhu.cf.markdown.MarkDown2HtmlWrapper;
import com.shuidihuzhu.cf.markdown.entity.MarkdownEntity;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.format.DateTimeFormat;
import org.quartz.CronExpression;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.sql.DataSource;
import java.awt.image.BufferedImage;
import java.sql.ResultSetMetaData;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalUnit;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/7/20
 */

@Slf4j
@Service
public class CfScheduleAlarmService {

    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @Resource
    private CfScheduleAlarmDao cfScheduleAlarmDao;

    @Resource
    private Tracing tracing;

    private static ExecutorService executorService;
//    private Map<String, JdbcTemplate> jdbcTemplateMap = Maps.newHashMap();

    private static final String defaultDatasource = "shuidiCfTdDataSource";


    private static final DateTimeFormatter ymd = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Resource
    private Environment environment;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private CfScheduleAlarmDataSourceService cfScheduleAlarmDataSourceService;

    @PostConstruct
    public void init() {
        executorService = tracing.currentTraceContext().executorService(new ThreadPoolExecutor(
                5, 10, 1, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000), new ThreadFactoryBuilder().setNameFormat("cf-schedule-alarm-pool").build()));

//        Map<String, DataSource> dataSourceMap = applicationContext.getBeansOfType(DataSource.class);
//        dataSourceMap.remove("multipleDataSource");
//
//        dataSourceMap.entrySet().stream().forEach(entry -> {
//            jdbcTemplateMap.put(entry.getKey(), new JdbcTemplate(entry.getValue()));
//        });

    }

    public void run(Date now) {

        long anchor = 0;
        int pageSize = 100;
        while (true) {
            List<CfScheduleAlarm> scheduleAlarmList = cfScheduleAlarmDao.getAllByAnchor(anchor, pageSize, false);
            if (CollectionUtils.isEmpty(scheduleAlarmList)) {
                break;
            }

            scheduleAlarmList.parallelStream().forEach(cfScheduleAlarm -> {
                try {
                    CronExpression cronExpression = new CronExpression(cfScheduleAlarm.getCrontab());
                    boolean isSatisfied = cronExpression.isSatisfiedBy(now);
                    if (!isSatisfied) {
                        return;
                    }
                    executorService.submit(new CfScheduleAlarmInnerJob(cronExpression, cfScheduleAlarm));
                } catch (Exception e) {
                    log.error("校验cronExpression异常，cfScheduleAlarm:{}", cfScheduleAlarm, e);
                }

            });

            if (scheduleAlarmList.size() < pageSize) {
                break;
            }
            anchor = scheduleAlarmList.get(scheduleAlarmList.size() - 1).getId();
        }
    }

    public boolean insert(CfScheduleAlarm cfScheduleAlarm) {
        if (StringUtils.isBlank(cfScheduleAlarm.getDataSource())) {
            cfScheduleAlarm.setDataSource(defaultDatasource);
        }
        int rows = cfScheduleAlarmDao.insert(cfScheduleAlarm);
        return rows > 0;
    }

    public boolean update(CfScheduleAlarm cfScheduleAlarm) {
        if (StringUtils.isBlank(cfScheduleAlarm.getDataSource())) {
            cfScheduleAlarm.setDataSource(defaultDatasource);
        }
        int rows = cfScheduleAlarmDao.update(cfScheduleAlarm);
        return rows > 0;
    }

    public boolean delete(long id, long operatorId) {
        int rows = cfScheduleAlarmDao.delete(id, operatorId);
        return rows > 0;
    }

    public void trigger(long id) {
        CfScheduleAlarm cfScheduleAlarm = cfScheduleAlarmDao.getById(id);
        if (Objects.isNull(cfScheduleAlarm)) {
            log.info("CfScheduleAlarmService trigger cfScheduleAlarm is null");
            return;
        }

        executorService.submit(new CfScheduleAlarmInnerJob(null, cfScheduleAlarm));
    }

    public CfScheduleAlarm getById(long id) {
        return cfScheduleAlarmDao.getById(id);
    }

    public void triggerByTitle(String title) {
        List<CfScheduleAlarm> cfScheduleAlarms = cfScheduleAlarmDao.getByTitle(title);
        if (CollectionUtils.isEmpty(cfScheduleAlarms) || cfScheduleAlarms.size() > 1) {
            log.info("CfScheduleAlarmService triggerByTitle cfScheduleAlarm is null or more than one ");
            return;
        }

        executorService.submit(new CfScheduleAlarmInnerJob(null, cfScheduleAlarms.get(0)));
    }

//    public Map<String, String> getDataSource() {
//        if (MapUtils.isEmpty(jdbcTemplateMap)) {
//            return Maps.newHashMap();
//        }
//
//        return jdbcTemplateMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getKey));
//    }


    public List<CfScheduleAlarm> selectByAnchor(String title, String desc, String cronTab, String robotKey, Integer contentType, String dataSource, int pageSize, boolean isPre, long anchor, String feishuRobotKey, Set<String> signSet) {
        if (CollectionUtils.isEmpty(signSet)) {
            return Lists.newArrayList();
        }
        return cfScheduleAlarmDao.getListByAnchor(title, desc, cronTab, robotKey, contentType, dataSource, anchor, pageSize, isPre, feishuRobotKey, signSet);
    }

    class CfScheduleAlarmInnerJob implements Runnable{
        private CronExpression  cronExpression;
        private CfScheduleAlarm cfScheduleAlarm;

        public CfScheduleAlarmInnerJob(CfScheduleAlarm cfScheduleAlarm) {
            this.cfScheduleAlarm = cfScheduleAlarm;
        }

        public CfScheduleAlarmInnerJob(CronExpression cronExpression, CfScheduleAlarm cfScheduleAlarm) {
            this.cronExpression = cronExpression;
            this.cfScheduleAlarm = cfScheduleAlarm;
        }

        @Override
        public void run() {
            if (cfScheduleAlarm == null || StringUtils.isAnyBlank(cfScheduleAlarm.getExecuteSql(), cfScheduleAlarm.getFeishuRobotKey())) {
                return;
            }

            List<Map<String, Object>> dataResult = null;
            try {
                dataResult = queryResult(cfScheduleAlarm);
            } catch (Throwable e) {
                String operatorName = "";
                AuthRpcResponse<AdminUserAccountModel> account = seaAccountClientV1.getValidUserAccountById(cfScheduleAlarm.getOperatorId().intValue());
                if (account.isSuccess()) {
                    AdminUserAccountModel userAccountModel = account.getResult();
                    if (Objects.nonNull(userAccountModel)){
                        operatorName = userAccountModel.getName();
                    }
                }

                log.error("CfScheduleAlarmService error operatorName: {} , cfScheduleAlarm: {}", operatorName, cfScheduleAlarm, e);
            }

            if (CollectionUtils.isEmpty(dataResult)) {
                return;
            }

            log.info("schedule id:{} title:{} dataResult:{}", cfScheduleAlarm.getId(), cfScheduleAlarm.getTitle(), JSON.toJSONString(dataResult));

            StringBuilder stringBuilder = new StringBuilder();
            if (!environment.acceptsProfiles(Profiles.of("production"))) {
                stringBuilder.append("## ").append("测试环境").append("\n\n");
            }
            stringBuilder.append("## ").append(replacePlaceholder(cfScheduleAlarm.getTitle())).append("\n\n");

            AtomicBoolean firstRow = new AtomicBoolean(true);
            dataResult.forEach(map -> {
                Set<String> keySet = map.keySet();
                if (firstRow.get()) {
                    keySet.forEach(key -> stringBuilder.append("|").append(key));
                    stringBuilder.append("|").append("\n");
                    keySet.forEach(key -> stringBuilder.append("|:-:"));
                    stringBuilder.append("|").append("\n");

                    firstRow.set(false);
                }
                keySet.forEach(key -> stringBuilder.append("|").append(map.get(key)));
                stringBuilder.append("|").append("\n");
            });


            try {
                String robotKey = cfScheduleAlarm.getRobotKey();
                String feishuRobotKey = cfScheduleAlarm.getFeishuRobotKey();
                switch (cfScheduleAlarm.getContentType()) {
                    case 0:
                        AlarmBotService.sentText(feishuRobotKey, stringBuilder.toString(), null, null);
                        AlarmBotService.sentText(robotKey, stringBuilder.toString(), null, null);
                        break;
                    case 1:
                        AlarmBotService.sentMarkDown(robotKey, stringBuilder.toString());
                        AlarmBotService.sentMarkDown(feishuRobotKey, stringBuilder.toString());
                        break;
                    case 2:
                        BufferedImage bf = Html2ImageWrapperUtils.fromMarkdownStr(stringBuilder.toString());
                        AlarmBotService.sentImage(robotKey, bf);
                        AlarmBotService.sentImage(feishuRobotKey, bf);
                        break;
                    default:
                        break;
                }
            } catch (Exception e) {
                String operatorName = "";
                AuthRpcResponse<AdminUserAccountModel> account = seaAccountClientV1.getValidUserAccountById(cfScheduleAlarm.getOperatorId().intValue());
                if (account.isSuccess()) {
                    AdminUserAccountModel userAccountModel = account.getResult();
                    if (Objects.nonNull(userAccountModel)){
                        operatorName = userAccountModel.getName();
                    }
                }

                log.error("机器人发消息异常. {}  cfScheduleAlarm:{}", operatorName, cfScheduleAlarm, e);
            }

        }


        private List<Map<String, Object>> queryResult(CfScheduleAlarm cfScheduleAlarm) {
            String sql = cfScheduleAlarm.getExecuteSql();
            if (StringUtils.isAnyBlank(sql)) {
                return Lists.newArrayList();
            }

            JdbcTemplate jdbcTemplate = cfScheduleAlarmDataSourceService.getJdbcTemplate(cfScheduleAlarm.getDataSource());

            if (jdbcTemplate == null) {
                return Lists.newArrayList();
            }

            return jdbcTemplate.query(sql, resultSet -> {
                List<Map<String, Object>> list = Lists.newArrayList();
                ResultSetMetaData md = resultSet.getMetaData();//获取键名
                int columnCount = md.getColumnCount();//获取行的数量

                while (resultSet.next()) {
                    Map<String, Object> rowData = Maps.newLinkedHashMap();
                    for (int i = 1; i <= columnCount; i++) {
                        rowData.put(md.getColumnLabel(i), resultSet.getObject(i));
                    }
                    list.add(rowData);
                }
                return list;
            });
        }
    }

    /**
     * 处理标题中的展示的符
     * @param title
     * @return
     */
    private static String replacePlaceholder(String title) {
        //截取表达式
        String[] expressionArray = StringUtils.substringsBetween(title, "${", "}");
        if (expressionArray == null){
            return title;
        }
        List<String> expressionList = Lists.newArrayList(expressionArray);
        if (CollectionUtils.isEmpty(expressionList)) {
            return title;
        }
        //表达式规则 ${xxx} 每个xxx是一个表达式，xxx中参数以:分割，首位为类型，后续为自定义参数
        for (String expression : expressionList) {
            try {
                title = realDealExpression(expression, title);
            } catch (Exception e) {
                log.error("e", e);
            }
        }
        return title;

    }

    private static String realDealExpression(String expression, String title) {
        //拆分表达式
        List<String> splitters = Splitter.on(":").splitToList(expression);
        if (CollectionUtils.isEmpty(splitters)){
            return title ;
        }
        //临时处理
        if (splitters.get(0).equals("date")) {
            //日期的第二个参数为需要修改的天数
            LocalDate now = LocalDate.now();
            if (splitters.size() > 1 && StringUtils.isNotBlank(splitters.get(1))) {
                now = now.plusDays(Long.parseLong(splitters.get(1)));
            }
            title = StringUtils.replace(title, "${" + expression + "}", now.format(ymd));
        }
        return title;
    }

    public static void main(String[] args) {
        System.out.println(replacePlaceholder("${date}招募营收播报"));
    }
}
