package com.shuidihuzhu.cf.service.record;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAttachmentBiz;
import com.shuidihuzhu.cf.dao.record.CfAllOperateRecordDao;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.enums.record.RecordOperateTypeEnum;
import com.shuidihuzhu.cf.enums.record.RecordPageTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.model.record.CfAllOperateRecordDo;
import com.shuidihuzhu.cf.model.record.CfAllOperateRecordVo;
import com.shuidihuzhu.cf.service.crowdfunding.AdminUserOrgService;
import com.shuidihuzhu.client.cf.api.client.CfImageMaskFeignClient;
import com.shuidihuzhu.client.model.MaskAttachmentVo;
import com.shuidihuzhu.client.model.enums.ImageMaskBizEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.msg.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/9/1  3:59 下午
 */
@Service
@Slf4j
public class CfAllOperateRecordService {
    @Resource
    private CfAllOperateRecordDao cfAllOperateRecordDao;

    @Autowired
    private AdminCrowdfundingAttachmentBiz adminCrowdfundingAttachmentBiz;

    @Autowired
    private SeaAccountDelegate seaAccountDelegate;

    @Autowired
    private AdminUserOrgService adminUserOrgService;
    @Resource
    private CfImageMaskFeignClient cfImageMaskFeignClient;

    private final static List<Integer> ugcTypes = Lists.newArrayList(ImageMaskBizEnum.CF_DETAIL_IMAGE.getCode(), ImageMaskBizEnum.CF_MEDICAL_IMAGE.getCode());

    public int insert(CfAllOperateRecordDo cfAllOperateRecordDo){
        return cfAllOperateRecordDao.insert(cfAllOperateRecordDo);
    }

    public void insertBatch(int adminUserId, int caseId, int bizType, String bizId, int operateType, int pageType, String content) {
        AdminUserAccountModel adminUserAccountModel = seaAccountDelegate.getAccountModel(adminUserId);
        String adminUserName = Objects.isNull(adminUserAccountModel) ? "" : adminUserAccountModel.getName();

        String organization = adminUserOrgService.getOrganization(adminUserId);

        List<CfAllOperateRecordDo> cfAllOperateRecordDoList = cfAllOperateRecordDao.getListByCaseIdAndBizType(caseId, bizType);
        Set<Long> bizIds = cfAllOperateRecordDoList.stream().map(CfAllOperateRecordDo::getBizId).collect(Collectors.toSet());

        List<String> list = Splitter.on(",").splitToList(bizId);

        List<CfAllOperateRecordDo> cfAllOperateRecordDos = Lists.newArrayList();
        for (String l : Sets.newHashSet(list)) {
            if (StringUtils.equals(l, "0")) {
                List<CrowdfundingAttachment> attachments = adminCrowdfundingAttachmentBiz.getAttachmentsByCaseIdsWithDelete(Lists.newArrayList(caseId), Lists.newArrayList(AttachmentTypeEnum.ATTACH_CF.value()));
                if (CollectionUtils.isEmpty(attachments)) {
                    continue;
                }
                for (CrowdfundingAttachment attachment : attachments) {
                    if (bizIds.contains((long) attachment.getId()) || attachment.getIsDelete() == 0) {
                        continue;
                    }
                    CfAllOperateRecordDo cfAllOperateRecordDo = CfAllOperateRecordDo
                            .build(operateType, bizType, attachment.getId(), caseId, content, adminUserId, adminUserName, pageType, organization);
                    cfAllOperateRecordDos.add(cfAllOperateRecordDo);
                }
            } else {
                CfAllOperateRecordDo cfAllOperateRecordDo = CfAllOperateRecordDo
                        .build(operateType, bizType, Long.parseLong(l), caseId, content, adminUserId, adminUserName, pageType, organization);
                cfAllOperateRecordDos.add(cfAllOperateRecordDo);
            }
        }

        if (CollectionUtils.isEmpty(cfAllOperateRecordDos)) {
            return;
        }

        cfAllOperateRecordDao.insertBatch(cfAllOperateRecordDos);
    }

    public List<CfAllOperateRecordVo> getListByCaseId(int caseId, int bizType, long bizId) {

        List<CfAllOperateRecordVo> cfAllOperateRecordVos = Lists.newArrayList();

        List<CfAllOperateRecordDo> cfAllOperateRecordDos = cfAllOperateRecordDao.getListByCaseId(caseId, bizType, bizId);
        if(CollectionUtils.isEmpty(cfAllOperateRecordDos)){
            return cfAllOperateRecordVos;
        }

        List<CrowdfundingAttachment> attachments = adminCrowdfundingAttachmentBiz.getAttachmentsByCaseIdsWithDelete(Lists.newArrayList(caseId), Lists.newArrayList(AttachmentTypeEnum.ATTACH_CF.value()));
        List<Integer> ids = attachments.stream().filter(v -> v.getIsDelete() == 0).map(CrowdfundingAttachment::getId).collect(Collectors.toList());

        // 如果是查掩码图片的操作记录
        if (bizType == 2) {
            List<Long> bizIds = attachments.stream().map(CrowdfundingAttachment::getId).map(Integer::longValue).collect(Collectors.toList());
            Response<List<MaskAttachmentVo>> response = cfImageMaskFeignClient.queryMaskImage(bizIds, ugcTypes);
            List<MaskAttachmentVo> maskAttachmentVos = Optional.ofNullable(response)
                    .filter(Response::ok)
                    .map(Response::getData)
                    .orElse(Lists.newArrayList());
            ids = maskAttachmentVos.stream().filter(v -> v.getIsDelete() == 0).map(MaskAttachmentVo::getBizId).map(Math::toIntExact).collect(Collectors.toList());
        }
        
        for (CfAllOperateRecordDo c : cfAllOperateRecordDos) {
            if (ids.contains((int) c.getBizId())) {
                continue;
            }
            CfAllOperateRecordVo cfAllOperateRecordVo = CfAllOperateRecordVo.builder()
                    .bizId(c.getBizId())
                    .content(c.getContent())
                    .caseId(c.getCaseId())
                    .department(c.getDepartment())
                    .operator(c.getOperator())
                    .operatorId(c.getOperatorId())
                    .bizType(c.getBizType())
                    .pageDesc(Objects.isNull(RecordPageTypeEnum.parse(c.getPageType())) ? "" : Objects.requireNonNull(RecordPageTypeEnum.parse(c.getPageType())).getDesc())
                    .operateDesc(Objects.isNull(RecordOperateTypeEnum.parse(c.getOperateType())) ? "" : Objects.requireNonNull(RecordOperateTypeEnum.parse(c.getOperateType())).getDesc())
                    .operatorTime(DateUtil.getDate2LStr(c.getCreateTime()))
                    .build();
            cfAllOperateRecordVos.add(cfAllOperateRecordVo);
        }

        return cfAllOperateRecordVos;
    }

    public List<CfAllOperateRecordDo> getListByCaseIdAndBizType(int caseId, int bizType) {
        return cfAllOperateRecordDao.getListByCaseIdAndBizType(caseId, bizType);
    }
}
