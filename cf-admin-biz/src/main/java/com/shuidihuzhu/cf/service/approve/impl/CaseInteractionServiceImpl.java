package com.shuidihuzhu.cf.service.approve.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimaps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.admin.util.TianRuCallRecordUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.feign.CrowdfundingOrderFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.client.ugc.caseprocessstatus.model.CaseProcessStatusEnum;
import com.shuidihuzhu.cf.client.ugc.caseprocessstatus.model.CaseProcessStatusRecordDO;
import com.shuidihuzhu.cf.client.ugc.caseprocessstatus.service.CaseProcessStatusClient;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationConstants;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecordDTO;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICfInfoXXXRecordDelegate;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.finance.client.enums.CaseInteractionEnum;
import com.shuidihuzhu.cf.finance.client.feign.FinanceInteractionFeignClient;
import com.shuidihuzhu.cf.finance.client.model.CaseInteractionVo;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrderModelResult;
import com.shuidihuzhu.cf.service.approve.CaseInteractionService;
import com.shuidihuzhu.cf.vo.approve.CaseInteractionDetailVO;
import com.shuidihuzhu.cf.vo.approve.InteractionCountNodeVO;
import com.shuidihuzhu.cf.vo.approve.InteractionDataVO;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewBaseInfoDO;
import com.shuidihuzhu.client.cf.clewtrack.model.ClewCallRecordModel;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.MobileUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CaseInteractionServiceImpl implements CaseInteractionService {

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Autowired
    private SeaAccountDelegate seaAccountDelegate;

    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;

    @Autowired
    private CaseProcessStatusClient caseProcessStatusClient;

    @Autowired
    private CfClewtrackFeignClient cfClewtrackFeignClient;

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Autowired
    private ICfInfoXXXRecordDelegate cfInfoXXXRecordDelegate;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Resource
    private CfWorkOrderClient cfWorkOrderClient;

    @Resource
    private CrowdfundingOrderFeignClient crowdfundingOrderFeignClient;

    @Resource
    private FinanceInteractionFeignClient financeInteractionFeignClient;

    @Resource
    private SeaAccountClientV1 seaAccountClientV1;

    @Override
    public Response<CaseInteractionDetailVO> detail(int caseId) {
        CaseInteractionDetailVO vo = new CaseInteractionDetailVO();

        // 查询当前案例处理状态列表
        List<CaseProcessStatusRecordDO> sourceCaseProcessStatusList = caseProcessStatusClient.list(caseId);
        Map<Integer, CaseProcessStatusRecordDO> caseProcessStatusMap = sourceCaseProcessStatusList
                .stream().collect(Collectors.toMap(CaseProcessStatusRecordDO::getCaseProcessStatus, Function.identity(), (v1, v2) -> v1));

        // 去重后的list
        final List<CaseProcessStatusRecordDO> caseProcessStatusList = Lists.newArrayList(caseProcessStatusMap.values());

        ArrayList<InteractionDataVO> dataList = Lists.newArrayList();
        vo.setDataList(dataList);

        // 各个环节操作记录
        List<OperationRecordDTO> records = commonOperationRecordClient
                .listByBizIdAndActionTypes(caseId, OperationActionTypeEnum.INTERACTION_TYPES);
        List<InteractionDataVO> interactionDetailList = records.stream()
                .map(v -> {
                    InteractionDataVO dataVO = new InteractionDataVO();
                    dataVO.setActionTime(v.getActionTime());
                    dataVO.setCaseProcessStatus(processStatus(v.getCaseProcessStatus()));
                    if (v.getOperatorId() > 0) {
                        String name = seaAccountDelegate.getNameByUserId(v.getOperatorId());
                        dataVO.setOperatorNameWithOrg(seaAccountDelegate.getNameWithOrg(name, v.getOrganization()));
                    }
                    OperationActionTypeEnum actionType = v.getActionType();
                    dataVO.setActionType(actionType);
                    dataVO.setActorIdentify(v.getActorIdentify());
                    dataVO.setDisplayCaseProcessStatus(getDisplayCaseProcessStatus(v.getCaseProcessStatus(), caseProcessStatusList, caseProcessStatusMap));
                    if (actionType == OperationActionTypeEnum.PLATFORM_CALL_OUT ||
                            actionType == OperationActionTypeEnum.USER_CALL_IN) {
                        String callUniqueId = v.getExtMap().get(OperationConstants.ExtName.CALL_UNIQUE_ID_TIAN_RUN);
                        ClewCallRecordModel callRecord = getByUniqueId(callUniqueId);
                        if (callRecord != null) {
                            dataVO.setCallStartTime(callRecord.getCnoStartTime());
                            dataVO.setCallEndTime(callRecord.getCnoEndTime());
                            dataVO.setCallDuration(callRecord.getTotalDuration());
                            dataVO.setCallAudioRecordUrl(TianRuCallRecordUtil.processAudioUrl(callRecord.getRecordFile()));
                            dataVO.setCallConnected(callRecord.getSipCause() == 200);
                            dataVO.setMobile(MobileUtil.mask(shuidiCipher.decrypt(callRecord.getEncryptCustomerNumber())));
                            dataVO.setOperatorNameWithOrg(seaAccountDelegate.getNameWithOrg(callRecord.getClientName(), callRecord.getOrgName()));
                        }
                    }
                    return dataVO;
                })
                .sorted(Comparator.comparing(InteractionDataVO::getActionTime))
                .collect(Collectors.toList());

        vo.setDataList(interactionDetailList);

        // 每个环节互动计数
        List<InteractionCountNodeVO> countList = Lists.newArrayList();
        ImmutableListMultimap<CaseProcessStatusEnum, InteractionDataVO> groupedMap = Multimaps.index(interactionDetailList, InteractionDataVO::getCaseProcessStatus);

        // 添加尾节点
        List<CaseProcessStatusRecordDO> statusListWithEnd = addEndNode(caseId, caseProcessStatusList);

        Date clewTime = getClewTime(caseId);
        if (clewTime != null) {
            CaseProcessStatusRecordDO clewNode = new CaseProcessStatusRecordDO();
            clewNode.setCaseId(caseId);
            clewNode.setCaseProcessStatus(CaseProcessStatusEnum.CREATE_CLEW.getValue());
            clewNode.setCreateTime(clewTime);
            statusListWithEnd.add(0, clewNode);
        }else {
            log.info("clew time null caseId:{}", caseId);
        }

        statusListWithEnd
                .stream()
                .filter(v -> v.getCaseProcessStatus() != CaseProcessStatusEnum.UNKNOWN.getValue())
                .sorted(Comparator.comparing(CaseProcessStatusRecordDO::getCaseProcessStatus))
                .forEachOrdered(caseProcessStatusRecordDO -> {
                    InteractionCountNodeVO v = new InteractionCountNodeVO();
                    CaseProcessStatusEnum status = CaseProcessStatusEnum.parse(caseProcessStatusRecordDO.getCaseProcessStatus());
                    v.setActionTime(status == CaseProcessStatusEnum.UNKNOWN ?
                            clewTime :
                            caseProcessStatusRecordDO.getCreateTime()
                    );
                    v.setCaseProcessStatus(processStatus(status));
                    v.setActionCount(groupedMap.get(status).size());
                    countList.add(v);
                });
        vo.setCountList(countList);

        return NewResponseUtil.makeSuccess(vo);
    }

    @Override
    public List<CaseInteractionVo> getCaseInteractionList(int caseId) {


        // [用户操作提现][打款成功/失败]节点
        com.shuidihuzhu.cf.finance.client.response.FeignResponse<List<CaseInteractionVo>> response = financeInteractionFeignClient.selectFinanceInteraction(caseId);
        List<CaseInteractionVo> caseInteractionVoList = Optional.ofNullable(response)
                .filter(com.shuidihuzhu.cf.finance.client.response.FeignResponse::ok)
                .map(com.shuidihuzhu.cf.finance.client.response.FeignResponse::getData)
                .orElse(Lists.newArrayList());

        // [案例发起]节点
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            return Lists.newArrayList();
        }
        CaseInteractionVo caseInteractionVo = CaseInteractionVo.builder()
                .caseInteractionEnum(CaseInteractionEnum.LAUNCHED_NODE)
                .actionTime(crowdfundingInfo.getCreateTime())
                .build();
        caseInteractionVoList.add(caseInteractionVo);

        // [首次转发]节点
        List<CfInfoShareRecord> records = cfInfoXXXRecordDelegate.getByInfoId(
                caseId,
                crowdfundingInfo.getCreateTime().getTime(),
                System.currentTimeMillis(),
                0,
                1);
        Date firstForwardTime = Optional.ofNullable(records)
                .filter(CollectionUtils::isNotEmpty)
                .map(r -> r.get(0))
                .map(CfInfoShareRecord::getDateCreated)
                .orElse(null);
        if (Objects.nonNull(firstForwardTime)) {
            CaseInteractionVo forwardCaseVo = CaseInteractionVo.builder()
                    .caseInteractionEnum(CaseInteractionEnum.FIRST_FORWARD_NODE)
                    .actionTime(firstForwardTime)
                    .build();
            caseInteractionVoList.add(forwardCaseVo);
        }

        Response<List<WorkOrderVO>> workOrderVOResponse = cfWorkOrderClient.queryByCaseAndTypes(caseId, Lists.newArrayList(WorkOrderType.cailiao_4.getType(), WorkOrderType.cailiao_5.getType(), WorkOrderType.cailiao_fuwu.getType()));
        List<WorkOrderVO> workOrderVOs = Optional.ofNullable(workOrderVOResponse)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Lists.newArrayList());
        // [用户提交材审]节点
        if (CollectionUtils.isNotEmpty(workOrderVOs) && workOrderVOs.size() > 0) {
            WorkOrderVO workOrderVO = workOrderVOs.get(0);
            CaseInteractionVo fuwuCaseVo = CaseInteractionVo.builder()
                    .caseInteractionEnum(CaseInteractionEnum.FU_WU_WORK_CREATE_NODE)
                    .actionTime(workOrderVO.getCreateTime())
                    .build();
            caseInteractionVoList.add(fuwuCaseVo);
        }

        // [可提现]节点
        workOrderVOs = workOrderVOs.stream()
                .filter(f -> f.getHandleResult() == HandleResultEnum.audit_pass.getType()
                        || f.getHandleResult() == HandleResultEnum.audit_reject.getType())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(workOrderVOs) && workOrderVOs.size() > 0) {
            WorkOrderVO workOrderVO = workOrderVOs.get(0);

            AuthRpcResponse<AdminUserAccountModel> account = seaAccountClientV1.getValidUserAccountById((int) workOrderVO.getOperatorId());
            String name = Optional.ofNullable(account)
                    .map(AuthRpcResponse::getResult)
                    .map(AdminUserAccountModel::getName)
                    .orElse("");

            CaseInteractionVo fuwuHandleVo = CaseInteractionVo.builder()
                    .caseInteractionEnum(CaseInteractionEnum.FU_WU_WORK_HANDLE_NODE)
                    .actionTime(workOrderVO.getFinishTime())
                    .successTag(workOrderVO.getHandleResult() == HandleResultEnum.audit_pass.getType() ? 1 : 0)
                    .operatorName(name)
                    .build();
            caseInteractionVoList.add(fuwuHandleVo);
        }

        // [首次获捐]节点
        FeignResponse<CrowdfundingOrderModelResult> feignResponse = crowdfundingOrderFeignClient.selectByUserIdAndThirdType(null, caseId, null, 1, 1);
        List<CrowdfundingOrder> crowdfundingOrders = Optional.ofNullable(feignResponse)
                .filter(FeignResponse::ok)
                .map(FeignResponse::getData)
                .map(CrowdfundingOrderModelResult::getCrowdfundingOrders)
                .orElse(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(crowdfundingOrders)) {
            CrowdfundingOrder crowdfundingOrder = crowdfundingOrders.get(0);
            CaseInteractionVo firstDonteVo = CaseInteractionVo.builder()
                    .caseInteractionEnum(CaseInteractionEnum.FIRST_DONATE_NODE)
                    .actionTime(crowdfundingOrder.getPayTime())
                    .build();
            caseInteractionVoList.add(firstDonteVo);
        }

        // 根据优先级排序
        return caseInteractionVoList.stream()
                .sorted(Comparator.comparingInt(vo -> vo.getCaseInteractionEnum().getPriority()))
                .collect(Collectors.toList());
    }

    private CaseProcessStatusEnum processStatus(CaseProcessStatusEnum status){
        if (status == CaseProcessStatusEnum.UNKNOWN) {
            return CaseProcessStatusEnum.CREATE_CLEW;
        }
        return status;
    }

    private Date getClewTime(int caseId) {
        CrowdfundingInfo fundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        long raiserId = fundingInfo.getUserId();

        String mobile = Optional.ofNullable(userInfoServiceBiz.getUserInfoByUserId(raiserId))
                .map(r -> shuidiCipher.decrypt(r.getCryptoMobile()))
                .orElse(null);

        if (StringUtils.isBlank(mobile)){
            return null;
        }

        List<CfClewBaseInfoDO> clews = cfClewtrackFeignClient.getClewBaseInfoByMobile(mobile).getData();
        if (CollectionUtils.isEmpty(clews)) {
            return null;
        }
        return clews.stream()
                .map(CfClewBaseInfoDO::getCreateTime)
                .filter(v -> v.before(fundingInfo.getCreateTime()))
                .max(Comparator.naturalOrder())
                .orElse(null);
    }

    private List<CaseProcessStatusRecordDO> addEndNode(int caseId, List<CaseProcessStatusRecordDO> caseProcessStatusList) {
        if (CollectionUtils.isEmpty(caseProcessStatusList)) {
            return caseProcessStatusList;
        }
        CaseProcessStatusRecordDO v = new CaseProcessStatusRecordDO();
        v.setCaseId(caseId);

        CaseProcessStatusEnum lastStatus = CaseProcessStatusEnum.parse(
                caseProcessStatusList.get(caseProcessStatusList.size() - 1).getCaseProcessStatus()
        );
        CaseProcessStatusEnum nextStatus = lastStatus.getNextStatus();
        if (nextStatus != null) {
            v.setCaseProcessStatus(nextStatus.getValue());
            caseProcessStatusList.add(v);
        }
        return caseProcessStatusList;
    }

    private String getDisplayCaseProcessStatus(CaseProcessStatusEnum caseProcessStatus,
                                               List<CaseProcessStatusRecordDO> caseProcessStatusList,
                                               Map<Integer, CaseProcessStatusRecordDO> caseProcessStatusMap) {
        caseProcessStatus = processStatus(caseProcessStatus);
        CaseProcessStatusRecordDO caseProcessStatusRecordDO = caseProcessStatusMap.get(caseProcessStatus.getValue());
        int index = caseProcessStatusList.indexOf(caseProcessStatusRecordDO);
        // 未知在list中找不到 下一个status为结束
        if (index < 0) {
            return CaseProcessStatusEnum.CREATE_CLEW.getName() + "-" + CaseProcessStatusEnum.CREATE_CLEW.getNextName();
        }
        int size = caseProcessStatusList.size();
        // 如果是list最后一个条目 则取正常流程下的nextStatus
        if (index == size - 1) {
            return caseProcessStatus.getName() + "-" + caseProcessStatus.getNextName();
        }
        // 如果不是最后一个条目 则取list中下一个status
        String nextName = CaseProcessStatusEnum.parse(caseProcessStatusList.get(index + 1).getCaseProcessStatus()).getName();
        return caseProcessStatus.getName() + "-" + nextName;
    }


    @Nullable
    private ClewCallRecordModel getByUniqueId(String callUniqueId) {
        Response<List<ClewCallRecordModel>> listResp =
                cfClewtrackFeignClient.getClewCallRecordsByUniqueIds(Lists.newArrayList(callUniqueId));
        if (listResp == null ||
                listResp.getCode() != 0 ||
                listResp.getData() == null ||
                CollectionUtils.isEmpty(listResp.getData())
        ) {
            log.error("getByUniqueId resp error callUniqueId:{}, response:{}", callUniqueId, JSON.toJSON(listResp));
            return null;
        }
        return listResp.getData().get(0);
    }

}
