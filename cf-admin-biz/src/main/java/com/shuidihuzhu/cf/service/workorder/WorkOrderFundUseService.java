package com.shuidihuzhu.cf.service.workorder;

import com.google.common.collect.Lists;
import com.google.common.collect.Ordering;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdFundingProgressBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAuthorBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingOperationBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingReportBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfFundUseDetailBiz;
import com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTaskWorkOrder;
import com.shuidihuzhu.cf.client.feign.CfFirstApproveFeignClient;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.dao.amount.CfAmountReasonableTaskWorkOrderDao;
import com.shuidihuzhu.cf.dao.crowdfunding.NewAdminCfFundUseAuditDao;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.crowdfunding.CaseReportDealStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.CaseReportStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfFinishStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.CfTaskEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdFundingProgressType;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceCapitalAccountFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.CfCapitalAccountRecord;
import com.shuidihuzhu.cf.model.admin.FundUseCaseDetailVo;
import com.shuidihuzhu.cf.model.admin.FundUseWorkOrderInfo;
import com.shuidihuzhu.cf.model.admin.FundUseWorkOrderVO;
import com.shuidihuzhu.cf.model.admin.workorder.CfFundUseDetailDO;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileSettings;
import com.shuidihuzhu.cf.model.crowdfunding.CfCapitalAccount;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport;
import com.shuidihuzhu.cf.service.EventCenterPublishService;
import com.shuidihuzhu.cf.service.ai.AiImageMaskServiceImpl;
import com.shuidihuzhu.cf.service.cfOperatingProfile.CfOperatingCaseLabelBiz;
import com.shuidihuzhu.cf.service.workorder.delayfinance.IDelayFinanceWorkOrderService;
import com.shuidihuzhu.cf.service.workorder.promoteBill.CfPromoteBillService;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminCrowdfundingProgress;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.workorder.CfFundUseWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCoreFeignClient;
import com.shuidihuzhu.client.cf.workorder.ext.WorkOrderExtFeignClient;
import com.shuidihuzhu.client.cf.workorder.ext.WorkOrderExtVO;
import com.shuidihuzhu.client.cf.workorder.model.FundUseHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.FundUseWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.model.enums.ImageMaskBizEnum;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * @author: fengxuan
 * @create 2019-12-19 17:04
 **/
@Service
@Slf4j
@RefreshScope
public class WorkOrderFundUseService {

    @Autowired
    CfFundUseDetailBiz detailBiz;

    @Autowired
    CfFundUseWorkOrderClient fundUseWorkOrderClient;

    @Autowired
    CfFinanceCapitalAccountFeignClient accountFeignClient;
    @Autowired
    private ShuidiCipher shuidiCipher;


    @Autowired
    AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Autowired
    private AdminCfInfoExtBiz adminCfInfoExtBiz;

    @Autowired
    AdminCrowdfundingOperationBiz operationBiz;

    @Autowired(required = false)
    private Producer producer;

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Autowired
    AdminCrowdFundingProgressBiz adminCrowdFundingProgressBiz;

    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;

    @Autowired
    NewAdminCfFundUseAuditDao adminCfFundUseAuditDao;

    @Autowired
    CfOperatingCaseLabelBiz caseLabelBiz;

    @Resource
    private SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    private EventCenterPublishService eventCenterPublishService;

    @Autowired
    private AdminCrowdfundingAuthorBiz authorBiz;

    @Autowired
    private AdminCrowdfundingReportBiz adminCrowdfundingReportBiz;

    @Autowired
    private WorkOrderExtFeignClient workOrderExtFeignClient;
    @Autowired
    private CfFirstApproveFeignClient cfFirstApproveFeignClient;
    @Autowired
    private CfPromoteBillService promoteBillService;
    @Resource
    private WorkOrderCoreFeignClient workOrderCoreFeignClient;
    @Resource
    private CfAmountReasonableTaskWorkOrderDao cfAmountReasonableTaskWorkOrderDao;
    @Resource
    private AiImageMaskServiceImpl aiImageMaskService;
    @Resource
    private IDelayFinanceWorkOrderService delayFinanceWorkOrderService;
    @Autowired
    private MaskUtil maskUtil;

    @Value("${fund.use.work.order.patient.name:true}")
    private boolean fund_use_work_order_patient_name;

    private static final List<Integer> fundUseType = Lists.newArrayList(
            WorkOrderType.funduseshenhe.getType(),
            WorkOrderType.funduserisk.getType()
    );


    public Response<PageResult<FundUseWorkOrderVO>> fundUseOrderList(WorkOrderListParam workOrderListParam) {
        PageResult<FundUseWorkOrderVO> pageResult = new PageResult<>();
        Response<PageResult<WorkOrderVO>> fundUseOrderList = fundUseWorkOrderClient.funduseOrderlist(workOrderListParam);

        if (fundUseOrderList.notOk() || Objects.isNull(fundUseOrderList.getData()) || CollectionUtils.isEmpty(fundUseOrderList.getData().getPageList())) {
            return NewResponseUtil.makeSuccess(pageResult);
        }
        PageResult<WorkOrderVO> data = fundUseOrderList.getData();

        List<FundUseWorkOrderVO> orderVOList = Lists.newArrayList();
        Set<Integer> caseIds = data.getPageList().stream()
                .map(WorkOrderVO::getCaseId)
                .collect(Collectors.toSet());
        Set<Integer> fundUseProgressIds = data.getPageList().stream()
                .map(WorkOrderVO::getFundUseProgressId)
                .collect(Collectors.toSet());
        List<Long> workOrderIdList = data.getPageList().stream()
                .map(WorkOrderVO::getWorkOrderId)
                .collect(toList());
        Map<Long, CfAmountReasonableTaskWorkOrder> reasonableTaskWorkOrderMap = cfAmountReasonableTaskWorkOrderDao.selectByWorkOrderIdList(workOrderIdList)
                .stream()
                .collect(Collectors.toMap(CfAmountReasonableTaskWorkOrder::getWorkOrderId, Function.identity(), (x, y) -> x));

        Map<Integer, AdminCrowdfundingProgress> crowdfundingProgressMap = adminCfFundUseAuditDao.selectByProgressIdList(new ArrayList<>(fundUseProgressIds))
                .stream()
                .collect(Collectors.toMap(AdminCrowdfundingProgress::getId, Function.identity(), (x, y) -> x));

        Map<Integer, CrowdfundingAuthor> crowdfundingAuthorMap = authorBiz.getByInfoIdList(new ArrayList<>(caseIds));
        Map<Integer, List<CrowdfundingReport>> reportListMap = adminCrowdfundingReportBiz.getByInfoIdsV2(new ArrayList<>(caseIds))
                .stream()
                .filter(item -> item.getDealStatus() == CaseReportDealStatus.HANDLEING.getValue()
                        || item.getDealStatus() == CaseReportDealStatus.FINISH.getValue())
                .collect(Collectors.groupingBy(CrowdfundingReport::getActivityId));

        data.getPageList().forEach(item -> {
            FundUseWorkOrderVO fundUseWorkOrderVO = new FundUseWorkOrderVO();
            BeanUtils.copyProperties(item, fundUseWorkOrderVO);

            AdminCrowdfundingProgress adminCrowdfundingProgress = crowdfundingProgressMap.get(item.getFundUseProgressId());
            CrowdfundingAuthor author = crowdfundingAuthorMap.get(item.getCaseId());
            List<CrowdfundingReport> crowdfundingReports = reportListMap.get(item.getCaseId());

            if (Objects.nonNull(adminCrowdfundingProgress)) {
                fundUseWorkOrderVO.setContent(adminCrowdfundingProgress.getContent());
                fundUseWorkOrderVO.setFundUseRejectedReason(adminCrowdfundingProgress.getFundUseRejectedReason());
                Timestamp timestamp = org.apache.commons.lang.StringUtils.isNotEmpty(adminCrowdfundingProgress.getDrawFinishTimeStr()) ? new Timestamp(DateUtil.stringToDate(adminCrowdfundingProgress.getDrawFinishTimeStr(), "yyyy-MM-dd HH:mm:ss").getTime()) : null;
                fundUseWorkOrderVO.setDrawFinishTime(timestamp);
                fundUseWorkOrderVO.setAttachmentUrls(adminCrowdfundingProgress.getImageUrls());
            }
            fundUseWorkOrderVO.setPatientName("暂无患者姓名");
            if (Objects.nonNull(author) && StringUtils.isNotEmpty(author.getName())) {
                fundUseWorkOrderVO.setPatientName(author.getName());
            } else if (fund_use_work_order_patient_name){
                com.shuidihuzhu.cf.client.response.FeignResponse<CfFirsApproveMaterial> cfFirstApproveMaterialByCaseId = cfFirstApproveFeignClient.getCfFirstApproveMaterialByCaseId(fundUseWorkOrderVO.getCaseId());
                if (Objects.nonNull(cfFirstApproveMaterialByCaseId) && cfFirstApproveMaterialByCaseId.ok() && Objects.nonNull(cfFirstApproveMaterialByCaseId.getData())) {
                    CfFirsApproveMaterial cfFirsApproveMaterial = cfFirstApproveMaterialByCaseId.getData();
                    fundUseWorkOrderVO.setPatientName(cfFirsApproveMaterial.getPatientRealName());
                }
            }
            CfAmountReasonableTaskWorkOrder cfAmountReasonableTaskWorkOrder = reasonableTaskWorkOrderMap.get(item.getWorkOrderId());
            if (Objects.nonNull(cfAmountReasonableTaskWorkOrder)) {
                fundUseWorkOrderVO.setAmountReasonableTaskId(cfAmountReasonableTaskWorkOrder.getTaskId());
                fundUseWorkOrderVO.setAmountReasonableTaskContent(cfAmountReasonableTaskWorkOrder.getContent());
                fundUseWorkOrderVO.setAmountReasonableTaskImages(cfAmountReasonableTaskWorkOrder.getImages());
                fundUseWorkOrderVO.setAmountReasonableTaskType(cfAmountReasonableTaskWorkOrder.getTaskType());
                fundUseWorkOrderVO.setAmountReasonableTaskAmountStart(cfAmountReasonableTaskWorkOrder.getAmountStart());
                fundUseWorkOrderVO.setAmountReasonableTaskAmountEnd(cfAmountReasonableTaskWorkOrder.getAmountEnd());
                fundUseWorkOrderVO.setAmountReasonableTaskPlan(cfAmountReasonableTaskWorkOrder.getTaskPlan());
                fundUseWorkOrderVO.setAmountReasonableTaskPlanWorkDataId(cfAmountReasonableTaskWorkOrder.getId());
                fundUseWorkOrderVO.setAmountReasonableAfterDays(cfAmountReasonableTaskWorkOrder.getTaskAfterDays());
            }


            fundUseWorkOrderVO.setHasReport(CollectionUtils.isNotEmpty(crowdfundingReports));
            fundUseWorkOrderVO.setMobileMask(maskUtil.buildByDecryptPhone(fundUseWorkOrderVO.getMobile()));
            fundUseWorkOrderVO.setMobile(null);

            orderVOList.add(fundUseWorkOrderVO);
        });

        promoteBillService.fillPromoteUploadInfo(orderVOList);

        // 填充非实时资金
        delayFinanceWorkOrderService.fillWorkOrderData(orderVOList);

        pageResult.setHasNext(data.isHasNext());
        pageResult.setPageList(orderVOList);
        return NewResponseUtil.makeSuccess(pageResult);
    }


    //是否需要标记
    public static boolean isNeedMarkUnCommitBill(List<CfFundUseDetailDO> fundUseDetailDOList, CfCapitalAccount cfCapitalAccount) {
        if (cfCapitalAccount == null) {
            return false;
        }
        long drawCashAmount = cfCapitalAccount.getDrawCashAmount() - cfCapitalAccount.getAllRefundAmount();
        long sum = fundUseDetailDOList.stream().mapToLong(CfFundUseDetailDO::getBillMoney).sum();
        boolean containsSelfPay = fundUseDetailDOList.stream().anyMatch(detail -> detail.getSelfPayTag() == 1);
        double compareAmount = 0;
        if (containsSelfPay) {
            compareAmount = drawCashAmount * 0.9;
        } else {
            compareAmount = drawCashAmount * 0.8;
        }
        return compareAmount > sum;
    }


    /**
     *  创建工单
     */
    public long createFundUseWorkOrder(int caseId, String fundUseProgressId, int orderType) {
        log.info("createFundUseWorkOrder caseId:{},fundUseProgressId:{},orderType:{}", caseId, fundUseProgressId, orderType);
        FundUseWorkOrder fundUseWorkOrder = new FundUseWorkOrder();
        fundUseWorkOrder.setFundUseProgressId(fundUseProgressId);
        fundUseWorkOrder.setCaseId(caseId);
        fundUseWorkOrder.setOrderType(orderType);
        Response<Long> workId = fundUseWorkOrderClient.createFunduse(fundUseWorkOrder);
        if (workId.ok()) {
            return workId.getData();
        }
        return 0;
    }


    public Response<Boolean> handleFundUseWork(FundUseHandleOrderParam handleOrderParam, int adminUserId) {
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(handleOrderParam.getCaseId());
        String comment = handleOrderParam.getOperComment();
        //备注只记录在fund_use_progress上
        handleOrderParam.setOperComment("");
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeFail("无案例数据");
        }
        int fundUseProgressId = handleOrderParam.getFundUseProgressId();
        int handleResult = handleOrderParam.getHandleResult();
        AdminCrowdfundingProgress crowdFundingProgress = adminCfFundUseAuditDao.selectByProgressId(fundUseProgressId);
        if (crowdFundingProgress == null) {
            return NewResponseUtil.makeFail("不存在资金用途材料");
        }
        //handle work
        Response<Void> response = workOrderCoreFeignClient.handle(handleOrderParam);
        boolean handleSuc = response.ok();
        if (!handleSuc) {
            log.warn("工单操作错误,response:{}", response);
            return NewResponseUtil.makeSuccess(false);
        }
        //审核通过
        if (HandleResultEnum.audit_pass.getType() == handleResult) {
            auditPass(crowdfundingInfo, crowdFundingProgress, handleOrderParam, comment);
        }
        //驳回
        if (HandleResultEnum.audit_reject.getType() == handleResult) {
            auditReject(crowdfundingInfo, handleOrderParam, comment, adminUserId);

        }
        return NewResponseUtil.makeSuccess(true);
    }


    public Response<FundUseCaseDetailVo> getCaseDetail(int caseId) {
        FundUseCaseDetailVo fundUseDetailVo = new FundUseCaseDetailVo();

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeFail("无案例数据");
        }

        fundUseDetailVo.setCaseStatus(Optional.ofNullable(crowdfundingInfo.getStatus()).map(CrowdfundingStatus::getApproveMsg).orElse(""));
        //案例结束状态
        fillCaseEndStatus(caseId, fundUseDetailVo, crowdfundingInfo);
        fundUseDetailVo.setInfoUUId(crowdfundingInfo.getInfoId());
        fundUseDetailVo.setCaseRaiseTime(crowdfundingInfo.getBeginTime());
        fundUseDetailVo.setCaseId(caseId);

        CrowdfundingOperation crowdfundingOperation = operationBiz.getByInfoId(crowdfundingInfo.getInfoId());
        if (crowdfundingOperation != null) {
            CaseReportStatusEnum reportStatusEnum = CaseReportStatusEnum.getByValue(crowdfundingOperation.getReportStatus());
            fundUseDetailVo.setReportStatus(Optional.ofNullable(reportStatusEnum).map(CaseReportStatusEnum::getWords).orElse(""));
        }


        long userId = crowdfundingInfo.getUserId();
        if (userId > 0) {
            UserInfoModel userInfo = userInfoServiceBiz.getUserInfoByUserId(userId);
            if (userInfo != null) {
                String cryptoMobile = userInfo.getCryptoMobile();
                String phoneNum = shuidiCipher.decrypt(cryptoMobile);
                fundUseDetailVo.setRaiserPhone(phoneNum);
                String applicantName = userInfo.getRealName();
                if (StringUtils.isBlank(applicantName)) {
                    CrowdfundingAuthor crowdfundingAuthor = authorBiz.get(caseId);
                    applicantName = Optional.ofNullable(crowdfundingAuthor)
                            .map(CrowdfundingAuthor::getName).orElse("");
                }
                fundUseDetailVo.setApplicantName(applicantName);
            }
        }

        List<String> labels = caseLabelBiz.selectCaseLabels(caseId).stream().map(CfOperatingProfileSettings::getContent).collect(toList());
        fundUseDetailVo.setCaseLabels(labels);


        FeignResponse<CfCapitalAccount> capitalAccountFeignResponse = accountFeignClient.capitalAccountGetByInfoUuid(crowdfundingInfo.getInfoId());
        Optional<CfCapitalAccount> capitalAccount = Optional.ofNullable(capitalAccountFeignResponse.getData());
        int drawCashNum = capitalAccount.map(CfCapitalAccount::getDrawCashNum).orElse(0);
        long drawCashAmount = capitalAccount.map(CfCapitalAccount::getDrawCashAmount).orElse(0L) - capitalAccount.map(CfCapitalAccount::getAllRefundAmount).orElse(0L);
        if (drawCashAmount < 0) {
            drawCashAmount = 0;
        }
        long unPayAmount = capitalAccount.map(CfCapitalAccount::getSurplusAmount).orElse(0L);
        //资金相关
        fundUseDetailVo.setDrawCashAmount(Double.parseDouble(MoneyUtil.buildBalance(drawCashAmount)));
        fundUseDetailVo.setDrawCashNums(drawCashNum);
        fundUseDetailVo.setHadCfAmount(Double.parseDouble(MoneyUtil.buildBalance(capitalAccount.map(CfCapitalAccount::getPayAmount).orElse(0L))));
        fundUseDetailVo.setUnPayAmount(Double.parseDouble(MoneyUtil.buildBalance(unPayAmount)));

        FeignResponse<List<CfCapitalAccountRecord>> accountRecords = accountFeignClient.getRecordListByInfoUuidAndBizType(crowdfundingInfo.getInfoId(), 4);
        Ordering<CfCapitalAccountRecord> ordering = Ordering.natural().reverse().onResultOf(CfCapitalAccountRecord::getCreateTime);
        Optional<CfCapitalAccountRecord> lastDraw = Optional.ofNullable(accountRecords.getData()).orElse(Lists.newArrayList())
                .stream().min(ordering);
        fundUseDetailVo.setLastDrawTime(lastDraw.map(CfCapitalAccountRecord::getCreateTime).orElse(null));
        fundUseDetailVo.setLastDrawAmount(Double.parseDouble(MoneyUtil.buildBalance(lastDraw.map(CfCapitalAccountRecord::getAmount).orElse(0))));

        List<CfFundUseDetailDO> fundUseDetailDOList = detailBiz.listAuditDetailByCaseId(caseId);
        long billAmount = fundUseDetailDOList.stream().mapToLong(CfFundUseDetailDO::getBillMoney).sum();
        boolean containsSelfPay = fundUseDetailDOList.stream().anyMatch(item -> item.getSelfPayTag() == 1);

        //票据相关
        fundUseDetailVo.setCommitBillAmount(Double.parseDouble(MoneyUtil.buildBalance(billAmount)));
        double unCommitBills = Double.parseDouble(MoneyUtil.buildBalance(drawCashAmount - billAmount));
        if (unCommitBills < 0) {
            unCommitBills = 0.00;
        }
        fundUseDetailVo.setUnCommitBillAmount(unCommitBills);

        boolean needMarkUnCommitBill = isNeedMarkUnCommitBill(fundUseDetailDOList, capitalAccount.get());
        fundUseDetailVo.setNeedMarkUnCommitBill(needMarkUnCommitBill);
        fundUseDetailVo.setHasSelfPay(containsSelfPay);

        return NewResponseUtil.makeSuccess(fundUseDetailVo);
    }


    private void fillCaseEndStatus(int caseId, FundUseCaseDetailVo fundUseDetailVo, CrowdfundingInfo crowdfundingInfo) {
        CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByCaseId(caseId);
        if (cfInfoExt != null) {
            int finishStatus = cfInfoExt.getFinishStatus();
            for (CfFinishStatus cfFinishStatus : CfFinishStatus.values()) {
                if (cfFinishStatus.getValue() == finishStatus) {
                    if (crowdfundingInfo.getEndTime().getTime() <= System.currentTimeMillis()
                            && finishStatus == CfFinishStatus.NOT_FINISH.getValue()) {
                        fundUseDetailVo.setCaseEndStatus(CfFinishStatus.EXPIRE.getDescription());
                    } else {
                        fundUseDetailVo.setCaseEndStatus(cfFinishStatus.getDescription());
                    }
                }
            }
        }
    }


    public Response<List<FundUseWorkOrderInfo>> listFundUseWorkOrder(int caseId, int workOrderId) {
        Response<List<WorkOrderVO>> workOrderResponse = fundUseWorkOrderClient.funduseListByCaseId(caseId, fundUseType);

        if (workOrderResponse.notOk()) {
            log.warn("调用工单系统失败,caseId:{},workOrderId:{}", caseId, workOrderId);
            return NewResponseUtil.makeFail("调用工单系统失败");
        }

        List<WorkOrderVO> workOrderVOList = workOrderResponse.getData();
        if (CollectionUtils.isEmpty(workOrderVOList)) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }

        List<FundUseWorkOrderInfo> fundUseWorkOrderInfoList = Lists.newArrayList();
        Map<Integer, List<CfFundUseDetailDO>> progressId2Details = detailBiz.listByProgressIds(workOrderVOList
                .stream()
                .map(WorkOrderVO::getFundUseProgressId)
                .distinct()
                .collect(Collectors.toList()));


        //组装工单列表信息
        for (WorkOrderVO workOrderVO : workOrderVOList) {
            FundUseWorkOrderInfo fundUseWorkOrder = createFundUseWorkOrderInfo(progressId2Details, workOrderVO);
            fundUseWorkOrderInfoList.add(fundUseWorkOrder);
        }

        List<FundUseWorkOrderInfo> sortedList = Lists.newArrayList();
        if (workOrderId > 0) {
            Optional<FundUseWorkOrderInfo> first = fundUseWorkOrderInfoList.stream().filter(item -> item.getWorkOrderId() == workOrderId).findFirst();
            if (first.isPresent()) {
                sortedList.add(first.get());
                fundUseWorkOrderInfoList = fundUseWorkOrderInfoList.stream().filter(item -> item.getWorkOrderId() != workOrderId).collect(toList());
            }
        }
        Ordering<FundUseWorkOrderInfo> ordering = Ordering.natural().reverse().onResultOf(FundUseWorkOrderInfo::getWorkOrderId);
        sortedList.addAll(fundUseWorkOrderInfoList.stream().sorted(ordering).collect(Collectors.toList()));
        return NewResponseUtil.makeSuccess(sortedList);
    }


    @NotNull
    private FundUseWorkOrderInfo createFundUseWorkOrderInfo(Map<Integer, List<CfFundUseDetailDO>> progressId2Details, WorkOrderVO workOrderVO) {
        FundUseWorkOrderInfo fundUseWorkOrder = new FundUseWorkOrderInfo();
        BeanUtils.copyProperties(workOrderVO, fundUseWorkOrder);
        Integer fundUseProgressId = workOrderVO.getFundUseProgressId();

        String comment = adminCfFundUseAuditDao.getAuditStatusRejectedReason(fundUseProgressId);

        String operator = "";
        AuthRpcResponse<AdminUserAccountModel> account = seaAccountClientV1.getValidUserAccountById((int) workOrderVO.getOperatorId());
        if (account.getResult() != null) {
            operator = account.getResult().getName();
        }

        List<FundUseWorkOrderInfo.CfFundUseDetailExtend> fundUseDetailExtends = Lists.newArrayList();

        int singleAmount = 0;
        for (CfFundUseDetailDO cfFundUseDetailDO : progressId2Details.getOrDefault(fundUseProgressId, Lists.newArrayList())) {
            singleAmount += cfFundUseDetailDO.getBillMoney();
            fundUseDetailExtends.add(FundUseWorkOrderInfo.createFundUseDetailExtend(cfFundUseDetailDO));
        }
        fundUseWorkOrder.setOperator(operator);
        fundUseWorkOrder.setComment(comment);
        fundUseWorkOrder.setFundUseDetailList(fundUseDetailExtends);
        fundUseWorkOrder.setSingleCommitBillAmount(Double.parseDouble(MoneyUtil.buildBalance(singleAmount)));
        return fundUseWorkOrder;
    }


    private void auditPass(CrowdfundingInfo crowdfundingInfo, AdminCrowdfundingProgress adminFundingProgress, FundUseHandleOrderParam handleOrderParam, String comment) {
        log.info("auditPass infoUuid:{}", crowdfundingInfo.getInfoId());

        int fundUseProgressId = handleOrderParam.getFundUseProgressId();
        //更新new_crowdfunding_fund_use_progress
        int updateResult = adminCfFundUseAuditDao.updateAuditPass(fundUseProgressId, comment);

        // 将填充后的资金进展progress 入 crowdfunding_progress表
        CrowdFundingProgress crowdFundingProgress = fillCrowdFundingProgress(adminFundingProgress, handleOrderParam);
        adminCrowdFundingProgressBiz.insertInCrowdfundingProgress(crowdFundingProgress);

        // 资金用途审核/分批票据审核 通过，发送图片掩码消息
        if (StringUtils.isNotBlank(crowdFundingProgress.getImageUrls())) {
            log.info("AiImageMaskServiceImpl sendImageMaskMqByBizId {} {}", crowdfundingInfo.getId(), ImageMaskBizEnum.CF_PROGRESS_IMAGE.getDesc());
            aiImageMaskService.sendImageMaskMqByBizId(crowdfundingInfo.getId(), ImageMaskBizEnum.CF_PROGRESS_IMAGE.getCode(), Long.valueOf(crowdFundingProgress.getId()));
        }

        // 重新审核时需要，需将已发布的动态驳回，C端显示删除
        workOrderExtFeignClient.addByNameValue(handleOrderParam.getWorkOrderId(), OrderExtName.crowdfundingProgressId.name(), String.valueOf(crowdFundingProgress.getId()));
        if (updateResult > 0 && producer != null) {
            MessageResult messageResult = producer.send(new Message(MQTopicCons.CF,
                    MQTagCons.CF_PUBLISH_PROGRESS,
                    MQTagCons.CF_PUBLISH_PROGRESS + "_" + crowdFundingProgress.getId(),
                    crowdFundingProgress));
            crowdfundingDelegate.updateTimes(crowdfundingInfo.getInfoId(), CfTaskEnum.Rule.PUBLISH_PROCESS);
            log.info("mq msg:{}", messageResult.toString());
        }
        eventCenterPublishService.sendFundAuditPass(crowdfundingInfo, crowdFundingProgress.getId());
        log.info("progressId : {} update success, insert {} success", fundUseProgressId, crowdFundingProgress);
    }

    private void auditReject(CrowdfundingInfo crowdfundingInfo, FundUseHandleOrderParam handleOrderParam, String comment, int adminUserId) {
        log.info("auditReject infoUuid:{}", crowdfundingInfo.getInfoId());

        adminCfFundUseAuditDao.updateAuditStatusRejected(handleOrderParam.getFundUseProgressId(), comment);
        // 如果是重新审核生成的工单，驳回需要将已经发布的资金用途删除掉
        Response<WorkOrderExtVO> orderExtVOResponse = workOrderExtFeignClient.getLastByName(handleOrderParam.getWorkOrderId(), OrderExtName.crowdfundingProgressId.name());
        if (Objects.nonNull(orderExtVOResponse) && orderExtVOResponse.ok() && Objects.nonNull(orderExtVOResponse.getData())) {
            WorkOrderExtVO data = orderExtVOResponse.getData();
            String value = data.getValue();
            adminCrowdFundingProgressBiz.delProgressById(Integer.parseInt(value));
        }
        //驳回时,需要发送消息
        eventCenterPublishService.sendFundAuditRejected(crowdfundingInfo, comment, handleOrderParam.getFundUseProgressId());
        commonOperationRecordClient.create()
                .buildBasicPlatform(crowdfundingInfo.getId(), adminUserId, OperationActionTypeEnum.REFUSE_FUND_FOR_USE_APPROVE)
                .save();
        log.info("auditReject : {} success", handleOrderParam.getFundUseProgressId());
    }


    /**
     * @return 填充 资金进展信息 填充后待入库
     */
    private CrowdFundingProgress fillCrowdFundingProgress(AdminCrowdfundingProgress adminFundingProgress, FundUseHandleOrderParam handleOrderParam) {
        CrowdFundingProgress crowdFundingProgress = new CrowdFundingProgress();
        crowdFundingProgress.setUserId(adminFundingProgress.getUserId());
        crowdFundingProgress.setImageUrls(adminFundingProgress.getImageUrls());
        crowdFundingProgress.setContent(adminFundingProgress.getContent());
        crowdFundingProgress.setType(CrowdFundingProgressType.MEDICAL_EXPENSES.value());
        crowdFundingProgress.setTitle(adminFundingProgress.getTitle());
        crowdFundingProgress.setActivityId(handleOrderParam.getCaseId());
        return crowdFundingProgress;
    }




}
