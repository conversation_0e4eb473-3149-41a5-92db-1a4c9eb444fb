package com.shuidihuzhu.cf.service.markfollowuptime.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.dao.markfollowuptime.CfMarkFollowConfigDao;
import com.shuidihuzhu.cf.dao.markfollowuptime.CfMarkFollowDao;
import com.shuidihuzhu.cf.dao.markfollowuptime.CfMarkFollowRecordDao;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.enhancer.exception.ServiceResponseException;
import com.shuidihuzhu.cf.enhancer.model.response.EhResponse;
import com.shuidihuzhu.cf.enhancer.model.response.EnhancerErrorCode;
import com.shuidihuzhu.cf.enhancer.mq.MQHelperService;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.enhancer.utils.EhResponseUtils;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.markfollowuptime.CfMarkFollowConfigEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.markfollowuptime.CfMarkFollowDo;
import com.shuidihuzhu.cf.model.markfollowuptime.CfMarkFollowPayload;
import com.shuidihuzhu.cf.model.markfollowuptime.CfMarkFollowRecordVO;
import com.shuidihuzhu.cf.model.markfollowuptime.CfMarkFollowVO;
import com.shuidihuzhu.cf.model.report.schedule.ReportScheduleVO;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.service.markfollowuptime.CfMarkFollowService;
import com.shuidihuzhu.cf.service.stream.StreamBizService;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderTypeFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderTypeRecord;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/8/13  3:30 下午
 */
@Service
@Slf4j
public class CfMarkFollowServiceImpl implements CfMarkFollowService {

    @Autowired
    private CfMarkFollowConfigDao cfMarkFollowConfigDao;

    @Autowired
    private CfMarkFollowDao cfMarkFollowDao;

    @Autowired
    private CfMarkFollowRecordDao cfMarkFollowRecordDao;

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    //sea后台页面
    private static final int PUSH_TYPE_PAGE = 1;
    //企业微信
    private static final int PUSH_TYPE_WX = 2;

    //标记跟进时间失效
    private static final int LOSE_EFFICACY = 1;

    //已经处理完成
    private static final int DONE = 1;

    @Autowired
    private ApplicationService applicationService;

    @Resource
    private MQHelperService mqHelperService;

    @Resource
    private StreamBizService streamService;

    @Resource
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private SeaAccountDelegate seaAccountDelegate;

    @Autowired
    private CfWorkOrderTypeFeignClient cfWorkOrderTypeFeignClient;

    @Override
    public EhResponse<Void> add(int adminUserId, int orderType, long bizId, Date targetTime) {
        EhResponse<Void> timeCheck = checkTime(targetTime);
        if (timeCheck.notOk()) {
            return EhResponseUtils.makeRelayFail(timeCheck);
        }

        if (orderType == WorkOrderType.cailiao_fuwu.getType()) {
            EhResponse<Void> checkOrder = checkOrder(bizId);
            if (checkOrder.notOk()) {
                return EhResponseUtils.makeRelayFail(checkOrder);
            }
        }

        CfMarkFollowDo cfMarkFollowDo = CfMarkFollowDo.builder()
                .bizId(bizId)
                .operatorId(adminUserId)
                .version(System.currentTimeMillis())
                .targetTime(targetTime)
                .orderType(orderType)
                .build();
        int res = cfMarkFollowDao.add(cfMarkFollowDo);

        boolean success = res > 0;
        if (success) {
            long id = cfMarkFollowDo.getId();
            sendDelayPush(id, cfMarkFollowDo.getVersion(), orderType, targetTime.getTime());
            cfMarkFollowRecordDao.add(id, adminUserId, "添加跟进时间");
        }

        return success ? EhResponseUtils.success() : EhResponseUtils.fail(AdminErrorCode.SYSTEM_ERROR);
    }

    @Override
    public EhResponse<Void> update(int adminUserId, int id, Date targetTime) {
        CfMarkFollowDo cfMarkFollowDo = cfMarkFollowDao.getById(id);
        EhResponse<Void> check = check(cfMarkFollowDo);
        if (check.notOk()) {
            return EhResponseUtils.makeRelayFail(check);
        }

        EhResponse<Void> timeCheck = checkTime(targetTime);
        if (timeCheck.notOk()) {
            return EhResponseUtils.makeRelayFail(timeCheck);
        }
        long version = System.currentTimeMillis();
        int res = cfMarkFollowDao.updateTargetTimeById(id, adminUserId, targetTime, System.currentTimeMillis());
        boolean success = res > 0;
        int orderType = Optional.ofNullable(cfMarkFollowDo).map(CfMarkFollowDo::getOrderType).orElse(0);
        if (success) {
            sendDelayPush(id, version, orderType, targetTime.getTime());
            cfMarkFollowRecordDao.add(id, adminUserId, "修改跟进时间");
        }
        return success ? EhResponseUtils.success() : EhResponseUtils.fail(AdminErrorCode.SYSTEM_ERROR);
    }

    @Override
    public EhResponse<Void> remove(int adminUserId, int id) {
        CfMarkFollowDo cfMarkFollowDo = cfMarkFollowDao.getById(id);
        EhResponse<Void> check = check(cfMarkFollowDo);
        if (check.notOk()) {
            return EhResponseUtils.makeRelayFail(check);
        }
        int res = cfMarkFollowDao.removeById(id);
        boolean success = res > 0;
        if (success) {
            cfMarkFollowRecordDao.add(id, adminUserId, "删除此次跟进");
        }
        return success ? EhResponseUtils.success() : EhResponseUtils.fail(AdminErrorCode.SYSTEM_ERROR);
    }

    @Override
    public EhResponse<ReportScheduleVO> getByCaseId(int caseId) {
        return null;
    }

    @Override
    public EhResponse<Void> done(int adminUserId, int id) {
        CfMarkFollowDo cfMarkFollowDo = cfMarkFollowDao.getById(id);
        EhResponse<Void> check = check(cfMarkFollowDo);
        if (check.notOk()) {
            return EhResponseUtils.makeRelayFail(check);
        }
        int res = cfMarkFollowDao.doneById(id);
        boolean success = res > 0;
        if (success) {
            cfMarkFollowRecordDao.add(id, adminUserId, "完成此次跟进");
        }
        return success ? EhResponseUtils.success() : EhResponseUtils.fail(AdminErrorCode.SYSTEM_ERROR);
    }

    @Override
    public int updateOperatorIdByBizId(long bizId, long adminUserId) {
        return cfMarkFollowDao.updateOperatorIdByBizId(bizId,adminUserId);
    }

    @Override
    public EhResponse<CfMarkFollowRecordVO> getListByWorkOrderId(long workOrderId) {
        CfMarkFollowDo cfMarkFollowDo = cfMarkFollowDao.getByBizId(workOrderId);
        if (Objects.isNull(cfMarkFollowDo)) {
            return EhResponseUtils.success();
        }
        long id = cfMarkFollowDo.getId();
        int operatorId = cfMarkFollowDo.getOperatorId();
        Date targetTime = cfMarkFollowDo.getTargetTime();
        String name = seaAccountDelegate.getNameByUserId(operatorId);

        CfMarkFollowRecordVO cfMarkFollowRecordVO = CfMarkFollowRecordVO.builder()
                .id(id)
                .targetTime(DateUtil.getDate2LStr(targetTime))
                .operatorId(operatorId)
                .operatorName(name).build();

        return EhResponseUtils.success(cfMarkFollowRecordVO);
    }

    @Override
    public void onDelayHandle(String json) {
        CfMarkFollowPayload payload = JSON.parseObject(json, CfMarkFollowPayload.class);
        long id = payload.getId();
        long version = payload.getVersion();
        int type = payload.getType();
        int orderType = payload.getOrderType();
        CfMarkFollowDo cfMarkFollowDo = cfMarkFollowDao.getById(id);

        log.info("onDelayHandle 发送通知 payload {}, data {}", payload, cfMarkFollowDo);
        if (Objects.isNull(cfMarkFollowDo)) {
            log.info("onDelayHandle null 已经被删除或已过期 payload {}, data {}", JSON.toJSONString(payload), JSON.toJSONString(cfMarkFollowDo));
            return;
        }

        if (cfMarkFollowDo.getVersion() != version) {
            log.info("onDelayHandle version 事件版本不一致 payload {}, data {}", JSON.toJSONString(payload), JSON.toJSONString(cfMarkFollowDo));
            return;
        }
        if (cfMarkFollowDo.getDone() == DONE) {
            log.info("onDelayHandle getDone 已标记处理 payload {}, data {}", JSON.toJSONString(payload), JSON.toJSONString(cfMarkFollowDo));
            return;
        }

        long bizId = cfMarkFollowDo.getBizId();
        String bizType = cfMarkFollowConfigDao.getBizType(orderType);
        if (StringUtils.isEmpty(bizType)) {
            return;
        }

        WorkOrderVO workOrderVO = null;
        switch (CfMarkFollowConfigEnum.getByName(bizType)) {
            case CASE_ID:
                //代码逻辑
                if (WorkOrderType.REPORT_TYPES.contains(orderType)) {
                    Response<WorkOrderVO> response = cfWorkOrderClient.getLastWorkOrderByTypes(Math.toIntExact(bizId), WorkOrderType.REPORT_TYPES);
                    if (Objects.isNull(response) || response.notOk()) {
                        throw ServiceResponseException.create("rpc fail", EnhancerErrorCode.RPC_FAIL);
                    }
                    workOrderVO = response.getData();
                }
                break;
            case WORK_ORDER_ID:
                //代码逻辑
                Response<WorkOrderVO> response = cfWorkOrderClient.getWorkOrderById(bizId);
                if (Objects.isNull(response) || response.notOk()) {
                    throw ServiceResponseException.create("rpc fail", EnhancerErrorCode.RPC_FAIL);
                }
                workOrderVO = response.getData();
                break;
            default:
                break;
        }

        if (type == PUSH_TYPE_PAGE) {
            streamService.pushWorkOrderPageShow(Optional.ofNullable(workOrderVO).map(WorkOrderVO::getOperatorId).orElse(0L));
        }
        if (type == PUSH_TYPE_WX) {
            sendWxNotice(cfMarkFollowDo, workOrderVO);
        }
    }

    @Override
    public EhResponse<CfMarkFollowVO> judgeMarkFollowTime(long workOrderId) {
        CfMarkFollowDo cfMarkFollowDo = cfMarkFollowDao.getByBizId(workOrderId);
        if (Objects.nonNull(cfMarkFollowDo)) {
            CfMarkFollowVO cfMarkFollowVO = CfMarkFollowVO.builder()
                    .targetTime(DateUtil.getDate2LStr(cfMarkFollowDo.getTargetTime()))
                    .id(cfMarkFollowDo.getId())
                    .flag(false).build();
            return EhResponseUtils.success(cfMarkFollowVO);
        }
        return EhResponseUtils.success(CfMarkFollowVO.builder().flag(true).build());
    }

    @Override
    public List<CfMarkFollowDo> getByBizIds(List<Long> bizIds) {
        if(CollectionUtils.isEmpty(bizIds)){
            return Lists.newArrayList();
        }
        return cfMarkFollowDao.getByBizIds(bizIds);
    }

    @Override
    public EhResponse<List<CfMarkFollowVO>> getListByOperatorId(int adminUserId) {
        List<CfMarkFollowDo> cfMarkFollowDos = cfMarkFollowDao.getListByOperatorId(adminUserId);
        if (CollectionUtils.isEmpty(cfMarkFollowDos)) {
            return EhResponseUtils.success(Lists.newArrayList());
        }
        cfMarkFollowDos = cfMarkFollowDos.stream().filter(v ->
                v.getTargetTime().getTime() < System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(6)).collect(Collectors.toList());
        List<CfMarkFollowVO> cfMarkFollowVOList = Lists.newArrayList();
        for (CfMarkFollowDo cfMarkFollowDo : cfMarkFollowDos) {
            long bizId = cfMarkFollowDo.getBizId();
            Response<WorkOrderVO> response = cfWorkOrderClient.getWorkOrderById(bizId);
            WorkOrderVO workOrderVO = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);
            if (Objects.isNull(workOrderVO)) {
                continue;
            }
            int caseId = workOrderVO.getCaseId();
            CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
            if(Objects.isNull(crowdfundingInfo)){
                continue;
            }

            String title = crowdfundingInfo.getTitle();
            String infoUuid = crowdfundingInfo.getInfoId();
            int handleResult = workOrderVO.getHandleResult();
            int orderType = workOrderVO.getOrderType();
            long workOrderId = workOrderVO.getWorkOrderId();
            Date targetTime = cfMarkFollowDo.getTargetTime();

            long id = cfMarkFollowDo.getId();

            CfMarkFollowVO vo = CfMarkFollowVO.builder()
                    .caseTitle(title)
                    .orderStatus(HandleResultEnum.getFromType(handleResult).getShowMsg())
                    .caseId(caseId)
                    .targetTime(DateUtil.getDate2LStr(targetTime))
                    .id(id)
                    .infoUuid(infoUuid)
                    .handleResult(handleResult)
                    .orderType(orderType)
                    .workOrderId(workOrderId)
                    .workOrderStatus(handleResult).build();
            cfMarkFollowVOList.add(vo);
        }

        List<CfMarkFollowVO> vos = cfMarkFollowVOList.stream().sorted(Comparator.comparing(CfMarkFollowVO::getTargetTime))
                .collect(Collectors.toList());
        return EhResponseUtils.success(vos);
    }

    @Override
    public EhResponse<Boolean> judge(long workOrderId, int adminUserId) {

        Response<WorkOrderVO> response = cfWorkOrderClient.getWorkOrderById(workOrderId);
        WorkOrderVO workOrderVO = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);
        if (Objects.isNull(workOrderVO)) {
            return EhResponseUtils.success(false,"工单信息查询为空");
        }
        if (workOrderVO.getOperatorId() != adminUserId) {
            return EhResponseUtils.success(false,"此工单已经被回收");
        }
        if (workOrderVO.getHandleResult() == HandleResultEnum.exception_done.getType() ||
                workOrderVO.getHandleResult() == HandleResultEnum.manual_lock.getType() ||
                workOrderVO.getHandleResult() == HandleResultEnum.handle_manual_lock.getType() ||
                workOrderVO.getHandleResult() == HandleResultEnum.done.getType()
        ) {
            return EhResponseUtils.success(false,"此工单处理完成");
        }
        return EhResponseUtils.success(true);
    }

    private void sendWxNotice(CfMarkFollowDo cfMarkFollowDo, WorkOrderVO workOrderVO) {
        int caseId = 0;
        long operatorId = 0L;
        String orderResultMsg = StringUtils.EMPTY;
        String orderTypeMsg = StringUtils.EMPTY;
        if (Objects.nonNull(workOrderVO)) {
            caseId = workOrderVO.getCaseId();
            operatorId = workOrderVO.getOperatorId();
            Response<WorkOrderTypeRecord> resp = cfWorkOrderTypeFeignClient.getByOrderTypeCode(workOrderVO.getOrderType());
            if(resp.ok() && resp.getData() != null) {
                orderTypeMsg = resp.getData().getMsg();
            }
            orderResultMsg = HandleResultEnum.getFromType(workOrderVO.getHandleResult()).getMsg();
        }
        Date targetTime = cfMarkFollowDo.getTargetTime();
        String targetTimeMsg = StringUtils.defaultString(DateUtil.getDate2LStr(targetTime));

        String temp = "【" + orderTypeMsg + "跟进提醒】\n" +
                "\n" +
                "【案例ID】：%d\n" +
                "\n" +
                "【工单类型】：%s\n" +
                "\n" +
                "【工单状态】：%s\n" +
                "\n" +
                "【约定跟进时间】：%s\n" +
                "\n" +
                "【最新处理人】%s";

        ArrayList<String> noticer = Lists.newArrayList("zhangchuanhui", "liuyunzhou", "supei", "yanglijia", "lihao");

        String name = StringUtils.EMPTY;
        if (operatorId > 0) {
            AdminUserAccountModel adminUserAccountModel = seaAccountDelegate.getAccountModel(Math.toIntExact(operatorId));
            String misByUserId = Optional.ofNullable(adminUserAccountModel).map(AdminUserAccountModel::getMis).orElse("");
            name = Optional.ofNullable(adminUserAccountModel).map(AdminUserAccountModel::getName).orElse("");
            noticer.add(misByUserId);
        }

        String content = String.format(temp, caseId, orderTypeMsg, orderResultMsg, targetTimeMsg, name);

        String[] noticerArr = noticer.toArray(String[]::new);
        String key;
        if (applicationService.isProduction()) {
            key = "d1e98cef-80a0-4cbd-8721-ee69a291db2c";
        } else {
            key = "4a16eab1-e7b2-491a-be1d-3ac4298275f9";
        }
        AlarmBotService.sentText(key, content, noticerArr, null);
    }

    private void sendDelayPush(long id, long version, int orderType, long time) {
        push(id, version, orderType, PUSH_TYPE_PAGE, time - TimeUnit.MINUTES.toMillis(5));
        push(id, version, orderType, PUSH_TYPE_WX, time - TimeUnit.MINUTES.toMillis(5));
    }

    private void push(long id, long version, int orderType, int type, long targetTime) {
        CfMarkFollowPayload p = new CfMarkFollowPayload();
        p.setId(id);
        p.setType(type);
        p.setVersion(version);
        p.setOrderType(orderType);
        mqHelperService.builder()
                .setTags(MQTagCons.MARK_FOLLOW_PUSH)
                .addKey(MQTagCons.MARK_FOLLOW_PUSH, id, System.currentTimeMillis())
                .setTargetTime(targetTime)
                .setPayload(JSON.toJSONString(p))
                .send();
    }

    private EhResponse<Void> check(CfMarkFollowDo cfMarkFollowDo) {
        if (Objects.isNull(cfMarkFollowDo)) {
            return EhResponseUtils.fail(AdminErrorCode.REPORT_SCHEDULE_TARGET_TIME_PASSED);
        }
        return EhResponseUtils.success();
    }

    private EhResponse<Void> checkTime(Date targetTime) {
        long now = System.currentTimeMillis();
        long target = targetTime.getTime();
        return target - now > TimeUnit.MINUTES.toMillis(5) ? EhResponseUtils.success() :
                EhResponseUtils.failWithMessage("请选择" + 5 + "分钟以后的时间");
    }

    private EhResponse<Void> checkOrder(long bizId) {
        Response<WorkOrderVO> response = cfWorkOrderClient.getWorkOrderById(bizId);
        WorkOrderVO workOrderVO = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);

        if (Objects.isNull(workOrderVO)) {
            return EhResponseUtils.failWithMessage("一个案例的材审主动服务库“处理中”工单同时只能标记一个下次跟进时间");
        }

        int caseId = workOrderVO.getCaseId();
        Response<List<WorkOrderVO>> listResponse = cfWorkOrderClient.listByCaseIdAndTypeAndResult(caseId, Lists.newArrayList(WorkOrderType.cailiao_fuwu.getType()), Lists.newArrayList(HandleResultEnum.doing.getType()));
        List<WorkOrderVO> workOrderVOList = Optional.ofNullable(listResponse).filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());
        if (CollectionUtils.isEmpty(workOrderVOList)) {
            return EhResponseUtils.failWithMessage("一个案例的材审主动服务库“处理中”工单同时只能标记一个下次跟进时间");
        }
        List<Long> workOrderIds = workOrderVOList.stream().map(WorkOrderVO::getWorkOrderId).collect(Collectors.toList());

        List<CfMarkFollowDo> cfMarkFollowDos = cfMarkFollowDao.getByBizIds(workOrderIds);

        if (CollectionUtils.isNotEmpty(cfMarkFollowDos)) {
            return EhResponseUtils.failWithMessage("一个案例的材审主动服务库“处理中”工单同时只能标记一个下次跟进时间");
        }
        return EhResponseUtils.success();
    }

}
