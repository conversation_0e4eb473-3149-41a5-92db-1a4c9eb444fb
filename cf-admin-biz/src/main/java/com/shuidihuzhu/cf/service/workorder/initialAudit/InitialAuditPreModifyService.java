package com.shuidihuzhu.cf.service.workorder.initialAudit;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.feign.RiverReviewFeignClient;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.client.material.feign.CfPreSubmitMaterialClient;
import com.shuidihuzhu.cf.client.material.model.CfBasicLivingGuardModel;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.client.material.model.annotation.PreModifyFieldMapping;
import com.shuidihuzhu.cf.client.material.model.preSubmit.CfMaterialPreModifyHandleVo;
import com.shuidihuzhu.cf.client.material.model.preSubmit.CfMaterialPreModifyState;
import com.shuidihuzhu.cf.client.material.model.preSubmit.CfRejectPositionFields;
import com.shuidihuzhu.cf.client.material.utils.MaterialCollectionUtils;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.BaseInfoTemplateConst;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.CrowdfundingInitialAuditInfo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.model.river.RiverRejectLocationEnum;
import com.shuidihuzhu.cf.model.river.RiverReviewDO;
import com.shuidihuzhu.cf.model.river.RiverStatusEnum;
import com.shuidihuzhu.cf.model.river.RiverUsageTypeEnum;
import com.shuidihuzhu.cf.vo.admin.initialAudit.*;
import com.shuidihuzhu.cf.vo.initialaudit.CfPropertyInsuranceVO;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class InitialAuditPreModifyService {

    @Autowired
    private CfRefuseReasonEntityBiz entityBiz;
    @Autowired
    private CfRefuseReasonItemBiz reasonItemBiz;
    @Autowired
    private ICrowdfundingOperationDelegate cfOperateDelegate;
    @Autowired
    private CfRefuseReasonCommonBiz reasonCommonBiz;
    @Autowired
    private CfRefuseReasonTagBiz reasonTagBiz;
    @Autowired
    private RiverReviewFeignClient reviewFeignClient;
    @Autowired
    private CfMaterialVerityHistoryBiz historyBiz;
    @Autowired
    private CfPreSubmitMaterialClient submitMaterialClient;
    @Autowired
    private InitialAuditPreModifyChangeService preModifyChangeService;
    @Autowired
    private AdminCfInfoExtBiz cfInfoExtBiz;
    @Autowired
    private IRiskDelegate cfDelegate;
    @Autowired
    private CfFirstApproveOperatorBiz firstApproveOperatorBiz;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private PreModifyParamConvert preModifyParamConvert;

    private static Map<InitialAuditItem.EditMaterialType, Set<String>> REJECT_POSITION_FIELD_MAP = Maps.newHashMap();
    static {

        REJECT_POSITION_FIELD_MAP.putAll(getRejectFieldMappingByType(InitialAuditCaseDetail.CaseBaseInfo.class));
        REJECT_POSITION_FIELD_MAP.putAll(getRejectFieldMappingByType(InitialAuditCaseDetail.FirstApproveCaseInfo.class));
        REJECT_POSITION_FIELD_MAP.putAll(getRejectFieldMappingByType(CfPropertyInsuranceInfoModel.class));
        REJECT_POSITION_FIELD_MAP.putAll(getRejectFieldMappingByType(CfBasicLivingGuardModel.class));
    }

    static Map<InitialAuditItem.EditMaterialType, Set<String>> getRejectFieldMappingByType(Class clazz) {
        Map<InitialAuditItem.EditMaterialType, Set<String>> positionFieldMap = Maps.newHashMap();
        if (clazz == null) {
            return positionFieldMap;
        }

        Field[] allFields = clazz.getDeclaredFields();
        for (Field field : allFields) {
            PreModifyFieldMapping fieldMapping = field.getAnnotation(PreModifyFieldMapping.class);
            if (fieldMapping == null || fieldMapping.rejectPositions() == null) {
                continue;
            }

            for (InitialAuditItem.EditMaterialType editType : fieldMapping.rejectPositions()) {
                MaterialCollectionUtils.putValueToSet(positionFieldMap, editType, fieldMapping.fieldNameForSea());
            }
        }

        return positionFieldMap;
    }

    public InitialCanPreModifyState queryCanModifyState(InitialPreModifyHandleVo handleVo) {

        List<CfRefuseReasonEntity> reasonEntities = entityBiz.selectByIds(handleVo.getRejectIds());
        Map<Integer, CrowdfundingInfoDataStatusTypeEnum> entityIdMap = getByEntity(reasonEntities);

        Map<CrowdfundingInfoDataStatusTypeEnum, Set<String>> canModifyFields = Maps.newHashMap();
        for (CfRefuseReasonEntity reasonEntity : reasonEntities) {

            CrowdfundingInfoDataStatusTypeEnum materialType = entityIdMap.get(reasonEntity.getId());
            List<Integer> itemIds = reasonCommonBiz.getIdListSplitterByComma(reasonEntity.getItemIds());
            if (materialType == null || CollectionUtils.isEmpty(itemIds)) {
                log.error("不能找到驳回理由对应的材料类型和驳回位置.entityId:{}", reasonEntity.getId());
                continue;
            }
            // 低保和脱贫户/脱贫人口是一项材料
            if (materialType == CrowdfundingInfoDataStatusTypeEnum.DI_BAO
                    || materialType == CrowdfundingInfoDataStatusTypeEnum.PIN_KUN_HU) {
                materialType = CrowdfundingInfoDataStatusTypeEnum.BASIC_LIVING_GUARD;
            }

            List<CfRefuseReasonItem> reasonItems = reasonItemBiz.selectByIds(Sets.newHashSet(itemIds));
            for (CfRefuseReasonItem reasonItem : reasonItems) {
                InitialAuditItem.EditMaterialType editType = getEditType(materialType, reasonItem.getContent());
                if (editType == null) {
                    log.error("驳回位置不能在程序中找到对应的位置.rejectContent:{}", reasonItem);
                    continue;
                }
                Set<String> fields = REJECT_POSITION_FIELD_MAP.get(editType);
                if (CollectionUtils.isEmpty(fields)) {
                    log.error("驳回位置不能找到对应的字段. editType:{}", JSON.toJSONString(editType));
                    continue;
                }
                MaterialCollectionUtils.putValuesToSet(canModifyFields, materialType, fields);
            }
        }

        // 如果是驳回工单 去掉那些已经提交的项
        InitialCanPreModifyState canPreModifyState = InitialCanPreModifyState.create()
                .withCanModifyFields(canModifyFields);
        if (handleVo.getWorkOrderType() == WorkOrderType.bohui.getType()) {
            canPreModifyState.withHasSubmitMaterials(findHasSubmitMaterials(handleVo.getCaseId()));
        }

        return canPreModifyState;
    }

    private InitialAuditItem.EditMaterialType getEditType(CrowdfundingInfoDataStatusTypeEnum materialType, String content) {
        if (materialType == CrowdfundingInfoDataStatusTypeEnum.BASIC_LIVING_GUARD) {
            RiverRejectLocationEnum locationEnum = RiverRejectLocationEnum.getByName(content);
            return locationEnum == null ? null : InitialAuditItem.EditMaterialType.valueOfCode(locationEnum.getValue());
        }

        return InitialAuditOperateService.getEditTypeByContent(content);
    }

    // 初审是否提交过材料
    private Set<CrowdfundingInfoDataStatusTypeEnum> findHasSubmitMaterials(int caseId) {
        Set<CrowdfundingInfoDataStatusTypeEnum> hasSubmitMaterials = Sets.newHashSet();
        CrowdfundingInitialAuditInfo initialAuditInfo = cfOperateDelegate.selectCrowdfundingInitialAuditInfoByCaseId(caseId);
        if (initialAuditInfo == null) {
            throw new RuntimeException("不能找到用户的初审记录、请刷新页面在试～");
        }

        if (isMaterialSubmit(initialAuditInfo.getBaseInfo())) {
            hasSubmitMaterials.add(CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT);
        }
        if (isMaterialSubmit(initialAuditInfo.getFirstApproveInfo())) {
            hasSubmitMaterials.add(CrowdfundingInfoDataStatusTypeEnum.FIRST_APPROVE_SUBMIT);
        }
        if (isMaterialSubmit(initialAuditInfo.getCreditInfo())) {
            hasSubmitMaterials.add(CrowdfundingInfoDataStatusTypeEnum.CREDIT_INFO_NEW);
        }

        if (isLivingGuardSubmit(caseId)) {
            hasSubmitMaterials.add(CrowdfundingInfoDataStatusTypeEnum.BASIC_LIVING_GUARD);
        }

        return hasSubmitMaterials;
    }

    private boolean isLivingGuardSubmit(int caseId) {

        Response<RiverReviewDO> pinKunResponse = reviewFeignClient.getByCaseIdAndUsageType(caseId, RiverUsageTypeEnum.PIN_KUN.getValue());
        Response<RiverReviewDO> diBaoResponse = reviewFeignClient.getByCaseIdAndUsageType(caseId, RiverUsageTypeEnum.DI_BAO.getValue());

        if (pinKunResponse != null && pinKunResponse.getData() != null && diBaoResponse != null && diBaoResponse.getData() != null) {

            return (isRiverMaterialSubmit(pinKunResponse.getData().getInfoStatus()) && isRiverMaterialSubmit(diBaoResponse.getData().getInfoStatus()))
                    || (isRiverMaterialSubmit(pinKunResponse.getData().getInfoStatus()) && isRiverMaterialPass(diBaoResponse.getData().getInfoStatus()))
                    || (isRiverMaterialPass(pinKunResponse.getData().getInfoStatus()) && isRiverMaterialSubmit(diBaoResponse.getData().getInfoStatus()));
        }

        return false;
    }

    // 主要的方法 判断两个对象是否一致
    public AdminErrorCode savePreModifyMaterials(InitialPreModifyHandleVo handleVo) {
        if (handleVo == null || handleVo.getUserSourceMaterials() == null || handleVo.getModifyMaterials() == null) {
            return AdminErrorCode.SYSTEM_PARAM_ERROR;
        }

        CfMaterialPreModifyHandleVo preModifyVo = preModifyParamConvert.convertFromInitialHandleSeaVo(handleVo);
        preModifyVo.setOperateTime(System.currentTimeMillis());
        preModifyVo.setOrgName(historyBiz.queryOperateDetail(Math.toIntExact(handleVo.getUserId())));
        preModifyVo.setHasModifyFields(getHasModifyFields(handleVo));
        preModifyVo.setMaterialStage(CfMaterialPreModifyState.MaterialStage.INITIAL_AUDIT);

        if (!validateInsurance(preModifyVo)) {
            return AdminErrorCode.SYSTEM_PARAM_ERROR;
        }
        if (MapUtils.isEmpty(preModifyVo.getHasModifyFields())) {
            log.info("当前并没有代用户修改初审材料.param:{}", handleVo);
            return AdminErrorCode.SUCCESS;
        }

        if (!canPreModifySubmit(handleVo.getCaseId(), handleVo.getWorkOrderType(), preModifyVo.getHasModifyFields().keySet())) {
            return AdminErrorCode.USER_HAS_SUBMIT;
        }

        // 保存
        RpcResult<String> result = submitMaterialClient.savePreModifyMaterials(preModifyVo);
        log.info("运营代修改数据保存.param:{} result:{}", preModifyVo, result);

        return AdminErrorCode.SUCCESS;
    }

    private boolean validateInsurance(CfMaterialPreModifyHandleVo preModifyVo) {
        if (preModifyVo == null || preModifyVo.getModifyMaterials() == null
                || preModifyVo.getModifyMaterials().getPropertyInsuranceParam() == null) {
            return true;
        }

        CfPropertyInsuranceVO vo =
                CfPropertyInsuranceVO.buildInsuranceModel(preModifyVo.getModifyMaterials().getPropertyInsuranceParam());

        CfErrorCode code = vo.validate();
        log.info("校验增信材料 param:{} result:{}", preModifyVo.getModifyMaterials().getPropertyInsuranceParam(),
                code);
        return code == CfErrorCode.SUCCESS;
    }

    // 驳回工单
    private boolean canPreModifySubmit(int caseId, int workOrderType, Collection<CrowdfundingInfoDataStatusTypeEnum> hasModifys) {

        if (workOrderType != WorkOrderType.bohui.getType()) {
            return true;
        }

        Set<CrowdfundingInfoDataStatusTypeEnum> hasSubmits = findHasSubmitMaterials(caseId);
        if (CollectionUtils.isEmpty(hasModifys) || CollectionUtils.isEmpty(hasSubmits)) {
            return true;
        }

        for (CrowdfundingInfoDataStatusTypeEnum modify : hasModifys) {
            if (hasSubmits.contains(modify)) {
                return false;
            }
        }
        return true;
    }

    public List<InitialPreModifyHandleHistory> selectPreModifyHistory(int caseId) {
        RpcResult<List<CfMaterialPreModifyHandleVo>>  historyResult = submitMaterialClient.selectPreModifyHistory(caseId);

        return historyResult == null || historyResult.getData() == null ? null :
                preModifyParamConvert.convertHisListFromCfHandleVos(historyResult.getData(), shuidiCipher);
    }

    private Map<CrowdfundingInfoDataStatusTypeEnum, CfRejectPositionFields> getHasModifyFields(
            InitialPreModifyHandleVo handleVo) {

        InitialPreModifyHandleVo.InitialMaterial userSourceMaterials = handleVo.getUserSourceMaterials();
        InitialPreModifyHandleVo.InitialMaterial modifyMaterials = handleVo.getModifyMaterials();
        Map<CrowdfundingInfoDataStatusTypeEnum, Set<String>> canModifyFields = handleVo.getCanModifyFields();

        Map<CrowdfundingInfoDataStatusTypeEnum, CfRejectPositionFields> hasModifyFields = Maps.newHashMap();

        // 图文
        CfRejectPositionFields titleModify = preModifyChangeService.getBaseInfoChangeFields(userSourceMaterials.getCaseBaseInfo(),
                modifyMaterials.getCaseBaseInfo(), canModifyFields.get(CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT));
        if (titleModify != null) {
            hasModifyFields.put(CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT, titleModify);
        }

        // 前置
        CfRejectPositionFields firstModify = preModifyChangeService.getModifyFields(userSourceMaterials.getFirstApproveCaseInfo(),
                    modifyMaterials.getFirstApproveCaseInfo(), canModifyFields.get(CrowdfundingInfoDataStatusTypeEnum.FIRST_APPROVE_SUBMIT));
        if (firstModify != null) {
            hasModifyFields.put(CrowdfundingInfoDataStatusTypeEnum.FIRST_APPROVE_SUBMIT, firstModify);
        }

        // 增信
        CfRejectPositionFields insuranceModify = preModifyChangeService.getInsuranceModifyFields(
                preModifyParamConvert.convertFromCreditInfo(userSourceMaterials.getCreditInfo()),
                preModifyParamConvert.convertFromCreditInfo(modifyMaterials.getCreditInfo()),
                canModifyFields.get(CrowdfundingInfoDataStatusTypeEnum.CREDIT_INFO_NEW));
        if (insuranceModify != null) {
            hasModifyFields.put(CrowdfundingInfoDataStatusTypeEnum.CREDIT_INFO_NEW, insuranceModify);
        }

        // 建档立卡
        CfRejectPositionFields livingGuardModify = preModifyChangeService.getModifyFields(userSourceMaterials.getAdditionInfo(),
                modifyMaterials.getAdditionInfo(), canModifyFields.get(CrowdfundingInfoDataStatusTypeEnum.BASIC_LIVING_GUARD));
        if (livingGuardModify != null) {
            hasModifyFields.put(CrowdfundingInfoDataStatusTypeEnum.BASIC_LIVING_GUARD, livingGuardModify);
        }

        return hasModifyFields;
    }

    private boolean isMaterialSubmit(int materialState) {
        return materialState == InitialAuditItem.MaterialStatus.SUBMIT.getCode();
    }

    private boolean isRiverMaterialSubmit(int materialState) {
        return materialState == RiverStatusEnum.SUBMITTED.getValue();
    }

    private boolean isRiverMaterialPass(int materialState) {
        return materialState == RiverStatusEnum.PASS.getValue();
    }

    // 可以提前将数据缓存～
    private Map<Integer, CrowdfundingInfoDataStatusTypeEnum> getByEntity(List<CfRefuseReasonEntity> reasonEntities) {

        Map<Integer, CrowdfundingInfoDataStatusTypeEnum> dataTypeMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(reasonEntities)) {
            return dataTypeMap;
        }

        Set<Integer> tagIds = reasonEntities.stream().map(CfRefuseReasonEntity::getTagId).collect(Collectors.toSet());
        List<CfRefuseReasonTag> reasonTags = reasonTagBiz.selectByTagIds(tagIds);

        Map<Integer, CrowdfundingInfoDataStatusTypeEnum> tagId2Maps = Maps.newHashMap();
        for (CfRefuseReasonTag reasonTag : reasonTags) {
            tagId2Maps.put(reasonTag.getId(), CrowdfundingInfoDataStatusTypeEnum.parse(reasonTag.getDataType()));
        }

        for (CfRefuseReasonEntity entity : reasonEntities) {
            CrowdfundingInfoDataStatusTypeEnum currType = tagId2Maps.get(entity.getTagId());
            if (currType == null) {
                log.error("驳回理由不能找到对应的类型.entityId:{}", JSON.toJSONString(entity.getId()));
                continue;
            }
            dataTypeMap.put(entity.getId(), currType);
        }

        return dataTypeMap;
    }

    public int selectCaseVersion(String infoUuid) {

        CfInfoExt infoExt = cfInfoExtBiz.getByInfoUuid(infoUuid);

        return infoExt == null ? -1 : infoExt.getCfVersion();
    }

    public InitialAuditCaseDetail.FirstApproveCaseInfo selectIdCardForPreModify(int caseId) {
        CfFirsApproveMaterial material = cfDelegate.getCfFirsApproveMaterialByInfoId(caseId);

        if (material == null) {
            return null;
        }

        InitialAuditCaseDetail.FirstApproveCaseInfo caseInfo = new InitialAuditCaseDetail.FirstApproveCaseInfo();

        caseInfo.setSelfRealName(material.getSelfRealName());
        caseInfo.setSelfIdCard(shuidiCipher.decrypt(material.getSelfCryptoIdcard()));
        caseInfo.setUserRelationType(material.getUserRelationType());
        caseInfo.setChild(firstApproveOperatorBiz.isChild(material) ? 1 : 0);
        caseInfo.setPatientRealName(material.getPatientRealName());
        caseInfo.setPatientIdCard(shuidiCipher.decrypt(material.getPatientCryptoIdcard()));
        caseInfo.setPatientBornCard(material.getPatientBornCard());
        caseInfo.setPatientIdType(material.getPatientIdType());
        caseInfo.setUserRelationTypeForC(material.getUserRelationTypeForC());

        return caseInfo;
    }


    public CfErrorCode verifyIdCard(InitialAuditCaseDetail.FirstApproveCaseInfo caseInfo, int userId) {

        CfErrorCode verifyIdcardResult =  cfDelegate.verifyIdcard(
                StringUtils.trimToEmpty(caseInfo.getSelfRealName()),
                StringUtils.trimToEmpty(caseInfo.getSelfIdCard()),
                StringUtils.trimToEmpty(caseInfo.getPatientRealName()),
                StringUtils.trimToEmpty(caseInfo.getPatientIdCard()),
                caseInfo.getPatientIdType(),
                BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.getByCode(caseInfo.getUserRelationTypeForC()).getRaiseRealType(),
                userId);
        log.info("身份证校验信息param:{} result:{}", caseInfo, verifyIdcardResult);
        if (verifyIdcardResult == CfErrorCode.ADD_CROWDFUNDING_VERIFY_SUCCESS_IN_WHITELIST) {
            verifyIdcardResult = CfErrorCode.SUCCESS;
        }
        return verifyIdcardResult;

    }
}
