package com.shuidihuzhu.cf.service.crowdfunding.report;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.client.apipure.model.report.CrowdfundingReportExtDo;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecordClient;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.client.feign.CfCommonFeignClient;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfHospitalAuditDao;
import com.shuidihuzhu.cf.dao.crowdfunding.MarkReportExtDAO;
import com.shuidihuzhu.cf.delegate.OrganizationDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.BooleanEnum;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.ReportSourceEnum;
import com.shuidihuzhu.cf.enums.approve.ApproveSourceTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.finance.client.feign.CfFinancePauseFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.enums.CfDrawCashPauseRecordEnum;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.admin.CfHospitalAuditInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.report.AdminCfReportRiskTagLabel;
import com.shuidihuzhu.cf.model.report.BaseCaseReportInfoVo;
import com.shuidihuzhu.cf.service.FinanceApproveService;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.admin.CfHospitalAuditService;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.crowdfunding.CfRiskService;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.client.cf.risk.client.CfRiskClient;
import com.shuidihuzhu.client.cf.risk.client.CfRiskOperateDetailClient;
import com.shuidihuzhu.client.cf.risk.model.CfRiskOperateDetail;
import com.shuidihuzhu.client.cf.risk.model.CfRiskOperateDetailParam;
import com.shuidihuzhu.client.cf.risk.model.CfRiskOperateLimitParam;
import com.shuidihuzhu.client.cf.risk.model.enums.PhoneOperateEnum;
import com.shuidihuzhu.client.cf.risk.model.enums.RiskOperateSourceEnum;
import com.shuidihuzhu.client.cf.risk.model.enums.UserOperationEnum;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfReportWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.ReportHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.CaseReport;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2019/5/16 下午2:06
 * @desc
 */
@Slf4j
@Service
public class CfReportService {

    private static final String CONTENT = "感谢各位爱心人士的关注、转发与支持，近期出现的水滴筹个人求助项目“因转发次数不够，钱无法到账”的传言属于虚假信息。患者在疾病证明等相关资料通过专业审核后，即可申请提现。是否能提现与转发次数无任何关联。建议大家在遇到类似传言时，可以通过“水滴筹”微信公众号或官方服务热线（400-686-1179）联系我们，我们将第一时间进行妥善处理。再次感谢！";
    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CfAdminOperationRecordBiz cfAdminOperationRecordBiz;
    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;
    @Autowired
    private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;
    @Autowired
    private AdminApproveService adminApproveService;
    @Autowired
    private AdminCrowdfundingOperationBiz adminCrowdfundingOperationBiz;
    @Autowired
    private AdminCfHospitalAuditBiz cfHospitalAuditBiz;
    @Autowired
    private CfHospitalAuditService cfHospitalAuditService;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;
    @Autowired(required = false)
    private Producer producer;
    @Resource
    private CfFinancePauseFeignClient cfFinancePauseFeignClient;

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Autowired
    private AdminCaseVisitConfigBiz caseVisitConfigBiz;

    @Autowired
    private CfRiskService cfRiskService;

    @Autowired
    private AdminCrowdfundingReportBiz adminCrowdfundingReportBiz;

    @Autowired
    private CfReportFollowCommentBiz cfReportFollowCommentBiz;

    @Autowired
    private AdminWorkOrderReportBiz adminWorkOrderReportBiz;

    @Autowired
    private AdminWorkOrderBiz adminWorkOrderBiz;

    @Autowired
    private ApproveRemarkOldService approveRemarkOldService;

    @Autowired
    private MarkReportExtDAO markReportExtDAO;

    @Autowired
    private CfCommonFeignClient cfCommonFeignClient;

    @Autowired
    private OrganizationDelegate organizationDelegate;

    @Autowired
    private AdminCfHospitalAuditDao adminCfHospitalAuditDao;

    @Autowired
    private CfRiskClient cfRiskClient;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private FinanceApproveService financeApproveService;

    @Autowired
    private CfReportWorkOrderClient reportWorkOrderClient;
    @Autowired
    private CfWorkOrderClient workOrderClient;
    @Autowired
    private Analytics analytics;
    @Autowired
    private CfQcWorkOrderClient cfQcWorkOrderClient;
    @Autowired
    private Environment environment;
    @Resource
    private CfRiskOperateDetailClient cfRiskOperateDetailClient;
    @Resource
    private AdminCaseVisitConfigBiz adminCaseVisitConfigBiz;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Resource
    private WonRecordClient wonRecordClient;

    @Resource
    private SeaAccountDelegate seaAccountDelegate;

    public int markReport(Integer caseId, String content, String reporterName, String reporterMobile,
                          String reportTypes, CrowdfundingInfo crowdfundingInfo, boolean realNameReport,
                          String reporterIdentity, int adminUserId, int reportChannel, String reportChannelOther,
                          String imageUrls, ReportSourceEnum reportSource,String amountRaisedIndex) {

        reporterName = StringUtils.isNotEmpty(reporterName) ? reporterName : "";
        String encryptMobile = StringUtils.isNotEmpty(reporterMobile) ? oldShuidiCipher.aesEncrypt(reporterMobile) : "";

        //初始化状态
        int handleStatus = CfReportHandleStatus.NO_HANDLE.getKey();
        int connectStatus = CfReportConnectStatus.NO_CONNECT.getKey() ;

        //查看举报用户是否命中黑名单，如果命中黑名单本次举报不暂停打款
        boolean queryPhoneValid = true;
        if (StringUtils.isNotBlank(reporterMobile)){
            queryPhoneValid = cfRiskService.queryPhoneValid(reporterMobile, PhoneOperateEnum.REPORT);
        }

        String infoUuid = crowdfundingInfo.getInfoId();
        String org = organizationDelegate.getSimpleOrganization(adminUserId);
        AdminUserAccountModel adminUserAccountModel = seaAccountClientV1.getValidUserAccountById(adminUserId).getResult();
        String userName = Objects.nonNull(adminUserAccountModel) ? adminUserAccountModel.getName() : "";
        try {
            //根据黑名单命中状态，判断是否暂停打款
            if (queryPhoneValid) {
                FeignResponse feignResponse = cfFinancePauseFeignClient.addPause(adminUserId, infoUuid,
                        crowdfundingInfo.getId(), CfDrawCashPauseRecordEnum.PauseSourceTypeEnum.REPORT.getCode(),
                        Lists.newArrayList(CfDrawCashPauseRecordEnum.PauseReasonTypeEnum.USER_REPORT.getCode()),
                        CfOperatingRecordEnum.Role.OPERATOR.getCode(), userName, "cf-admin-用户举报", false);
                log.info("markReport addPause caseId{},feignResponse:{}", crowdfundingInfo.getId(), JSON.toJSONString(feignResponse));
            }
        } catch (Exception e) {
            log.error("markReport addPause Exception", e);
        }

        CrowdfundingReport crowdfundingReport = new CrowdfundingReport();
        crowdfundingReport.setActivityId(caseId);
        crowdfundingReport.setUserId(-1);
        crowdfundingReport.setContent(content);
        crowdfundingReport.setIsNewreport(1);
        crowdfundingReport.setRealNameReport(realNameReport ? BooleanEnum.TRUE_FLAG.getValue() : BooleanEnum.FALSE_FLAG.getValue());
        crowdfundingReport.setName(reporterName);
        crowdfundingReport.setIdentity(StringUtils.isNotEmpty(reporterIdentity) ? oldShuidiCipher.aesEncrypt(reporterIdentity) : "");
        crowdfundingReport.setHandleStatus(handleStatus);
        crowdfundingReport.setConnectStatus(connectStatus);
        crowdfundingReport.setReportChannel(reportChannel);
        crowdfundingReport.setReportChannelOther(StringUtils.trimToEmpty(reportChannelOther));
        crowdfundingReport.setImageUrls(imageUrls);
        crowdfundingReport.setEncryptContact(encryptMobile);
        if (!queryPhoneValid){
            //如果命中黑名单，设置处理状态为已处理
            crowdfundingReport.setHitBlackList(true);
            crowdfundingReport.setHandleStatus(CfReportHandleStatus.HANDLED.getKey());
        }

        adminCrowdfundingReportBiz.addRealNameReport(crowdfundingReport);

        reportBigData(crowdfundingReport, crowdfundingInfo, reportTypes, reportSource);

        /**
         * 写入标记举报的人员操作扩展信息
         */
        AdminMarkReportExtDO extDO = new AdminMarkReportExtDO();
        extDO.setCaseId(caseId);
        extDO.setReportId(crowdfundingReport.getId());
        extDO.setReporterName(reporterName);
        extDO.setReporterMobile(reporterMobile);
        extDO.setMarkerUserId(adminUserId);
        extDO.setMarkerName(userName);
        extDO.setMarkerOrg(org);
        extDO.setEncryptReporterMobile("");
        if (StringUtils.isNotBlank(extDO.getReporterMobile())){
            extDO.setEncryptReporterMobile(oldShuidiCipher.aesEncrypt(extDO.getReporterMobile()));
        }


        markReportExtDAO.insert(extDO);

        /**
         * 写入举报标签
         */
//        cfCommonFeignClient.addReportLabel(crowdfundingReport.getId(), reportTypes);
        adminCrowdfundingReportBiz.addLabel(crowdfundingReport.getId(), reportTypes);

        adminApproveService.reportFromCSDChangeCaseReportStatus(crowdfundingInfo);

        try {
            crowdfundingOperationDelegate.saveCfOperatingRecord(infoUuid, adminUserId, userName, CfOperatingRecordEnum.Type.MARK_REPORT, CfOperatingRecordEnum.Role.OPERATOR);
        } catch (Exception e) {
            log.error("markReport saveCfOperatingRecord Exception", e);
        }
        cfAdminOperationRecordBiz.addOneOperationRecord(infoUuid, adminUserId, CfOperationRecordEnum.REPORT.value(), content);

        sendMsgWorkOrderReport(infoUuid,crowdfundingReport.getId(),amountRaisedIndex);

        return crowdfundingReport.getId();
    }

    // 手机号加密
    public String getEncryptReporterMobile(String mobile) {
        return StringUtils.isNotBlank(mobile) ? oldShuidiCipher.aesEncrypt(mobile) : "";
    }

    private void reportBigData(CrowdfundingReport crowdfundingReport, CrowdfundingInfo crowdfundingInfo, String reportLabel, ReportSourceEnum reportSource) {
        CaseReport caseReport = new CaseReport();
        try {
            log.info("case_report reportBigData:{}", JSON.toJSONString(caseReport));
            caseReport.setReport_id(Long.valueOf(crowdfundingReport.getId()));
            caseReport.setInfo_id((long) crowdfundingInfo.getId());
            caseReport.setCase_id(StringUtils.trimToEmpty(crowdfundingInfo.getInfoId()));
            caseReport.setReport_type(reportLabel);
            caseReport.setReport_content(crowdfundingReport.getContent());
            caseReport.setUser_tag(String.valueOf(crowdfundingReport.getUserId()));
            caseReport.setUser_tag_type(UserTagTypeEnum.userid);
            caseReport.setLaunch_user_id(crowdfundingInfo.getUserId());
            caseReport.setReport_source((long) reportSource.getCode());
            log.info("reportBigData:{}", JSON.toJSONString(caseReport));
            analytics.track(caseReport);
        } catch (Exception e) {
            log.error("reportBigData:" + JSON.toJSONString(caseReport), e);
        }
    }

    public Response hospitalAudit(String infoUuid, Integer auditStatus, String operatorContent, String reasonSupplement, int workOrderId, int workOrderType,int pageSource, Integer userId){

        log.info("CrowdfundingReportController hospitalAudit infoUuid:{}, userId:{},auditStatus:{}, operatorContent:{},pageSource:{}", infoUuid, userId, auditStatus, operatorContent,pageSource);
        if (StringUtils.isBlank(infoUuid) || userId == null || auditStatus == null ) {
            return  NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        //必须指明是不是新的举报处理详情页来的操作 2:新的举报处理详情页
        if(pageSource != CfReportSourceEnum.DEFAULT.getKey() && pageSource != CfReportSourceEnum.NEW_REPORT_PAGE.getKey()){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingOperation crowdfundingOperation = adminCrowdfundingOperationBiz.getByInfoId(infoUuid);
        if (crowdfundingOperation == null){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        AdminUserAccountModel userAccount = this.seaAccountClientV1.getValidUserAccountById(userId).getResult();
        if (userAccount == null) {
            return NewResponseUtil.makeError(AdminErrorCode.ADMIN_ACCOUNT_NOT_EXISTS);
        }
        CfHospitalAuditInfoExt cfHospitalAuditInfo = adminCfHospitalAuditDao.getByInfoUuidNoCareDelete(infoUuid);
        if (auditStatus.equals(CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode())) {
            //下发医院核实信息
            return cfHospitalAuditService.sendHospitalAudit(crowdfundingInfo, crowdfundingOperation, userId,
                    operatorContent, reasonSupplement, cfHospitalAuditInfo, HospitalAuditTypeEnum.OPERATOR, pageSource);
        } else  if (auditStatus.equals(CrowdfundingInfoStatusEnum.REJECTED.getCode())) {
            //驳回医院核实信息
            return cfHospitalAuditService.rejectdHospitalAudit(crowdfundingInfo, crowdfundingOperation, userAccount,
                    cfHospitalAuditInfo, operatorContent, workOrderId, workOrderType);
        } else if (auditStatus.equals(CrowdfundingInfoStatusEnum.PASSED.getCode())) {
            //审核通过医院核实信息
            return cfHospitalAuditService.passHospitalAudit(crowdfundingInfo, crowdfundingOperation, userAccount,
                    cfHospitalAuditInfo, operatorContent, workOrderId, workOrderType);
        }
        return NewResponseUtil.makeSuccess(null);

    }

    public Response<String> rule(CfRule cfRule){
        int caseId = Long.valueOf(cfRule.getCaseId()).intValue();
        CrowdfundingInfo crowdfundingInfo = this.crowdfundingDelegate.getFundingInfoById(caseId);
        if(crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        //TODO ###请勿改动、待删除####
        AdminCfCaseVisitConfig config = this.caseVisitConfigBiz.get(caseId);
        boolean newConfig = false;
        if(config == null) {
            config = new AdminCfCaseVisitConfig();
            //首次插入时   设置为可以转发
            config.setSharable(true);
            newConfig = true;
        }

        config.setOperatorId(1);
        config.setOperator("system");

        config.setCaseId(caseId);
        if (!config.isShowBanner()){
            config.setShowBanner(cfRule.isBanner());
        }

        Boolean isSend = false;
        // Sharable true 表示可以转发 和别的逻辑判断是相反的
        // cfRule里面的 share true表示的禁止转发   这个需要判断一下
        if (config.isSharable() && cfRule.isShare()){
            //原逻辑是可以转发 并且 规则判断是禁止转发的时候才禁止转发
            config.setSharable(false);
            cfRiskService.addOperateLimit(caseId, config.isSharable(), null, null, "","",RiskOperateSourceEnum.ABNORMAL_TRAFFIC.getCode(), null, null);
            // 禁止转发
            if (cfRule.isAlert()){
                isSend = true;
            }
        }

        // 案例未结束 并且报警 并且 案例不禁止转发 share等于true是禁止转发
        if (cfRule.isAlert() && !cfRule.isFinish() && !cfRule.isShare()){
            isSend = true;
        }

        boolean dynamicFlag = false;
        if (!config.isOfficialDynamic() && cfRule.isDynamic()){
            config.setOfficialDynamic(cfRule.isDynamic());
            dynamicFlag = true;
        }
        boolean titleFlag = false;
        if (!config.isChangeitle() && cfRule.isTitle()){
            config.setChangeitle(cfRule.isTitle());
            titleFlag = true;
        }

        try {
            if (isSend){
                sendNotice(caseId, cfRule);
            }
        }catch (Exception e){
            log.error("sendNotice ", e);
        }

        log.info("ConfigFeign config={},cfRule={}",config,cfRule);

        if(newConfig) {
            this.caseVisitConfigBiz.add(config,dynamicFlag,titleFlag);
        } else {
            this.caseVisitConfigBiz.update(config,dynamicFlag,titleFlag);
        }

        List<CfRiskOperateDetailParam> detailParams = Lists.newArrayList();

        // cfRule里面的 share true表示的禁止转发   这个需要判断一下
        CfRiskOperateDetailParam detailParam = new CfRiskOperateDetailParam();
        detailParam.setUserOperate(UserOperationEnum.SHARE.getCode());
        detailParam.setAction(!cfRule.isShare());
        detailParam.setReason("定时job设置转发权限");
        detailParams.add(detailParam);

        detailParam = new CfRiskOperateDetailParam();
        detailParam.setUserOperate(UserOperationEnum.BANNER.getCode());
        detailParam.setAction(!cfRule.isBanner());
        detailParam.setReason("定时job设置banner权限");
        detailParams.add(detailParam);

        CfRiskOperateLimitParam riskParam = new CfRiskOperateLimitParam();
        riskParam.setCaseId(caseId);
        riskParam.setOperateSource(RiskOperateSourceEnum.ABNORMAL_TRAFFIC.getCode());
        riskParam.setOperator("system");
        riskParam.setOperatorId(1);
        riskParam.setDetailParams(detailParams);

        cfRiskClient.writeRiskOperate(riskParam);

        return NewResponseUtil.makeSuccess("succ");

    }

    private void sendNotice(int caseId, CfRule cfRule) {
        if (!cfRule.isAlert()){
            return;
        }
        String wxContent = "";
        if (cfRule.isShare()){
            wxContent ="命中的风控策略名称：【异常转发监控】\n" +
                    "命中风险类型：【累计异常多次转发】\n" +
                    "命中动作类型：【禁止转发】\n" +
                    "触发时间：%s\n" +
                    "案例ID：%d\n" +
                    "案例结束状态：%s\n"+
                    "提示：请确认是否为谣言转发案例，以及是否需要解禁";
            String end = cfRule.isFinish() ? "用户主动结束" : "未结束";
            wxContent = String.format(wxContent, DateUtil.getCurrentDateStr(), caseId, end);
        }else{
            wxContent ="命中的风控策略名称：【异常转发监控】\n" +
                    "命中风险类型：【累计异常多次转发】\n" +
                    "触发时间：%s\n" +
                    "案例ID：%d\n" +
                    "案例结束状态：未结束\n"+
                    "提示：请确认是否为谣言转发案例，以及是否需要禁止转发";
            wxContent = String.format(wxContent, DateUtil.getCurrentDateStr(), caseId);
        }
        if (environment.acceptsProfiles("production")){
            AlarmBotService.sentText("e54cc7a8-82b3-40d3-995f-73be85153b3e", wxContent, null, null);
        }else {
            AlarmBotService.sentText("162ad519-f3b1-4e42-a420-ab6289baca7b", wxContent, null, null);
        }
    }


    private void sendMsgWorkOrderReport(String infoUuid, int reportId,String amountRaisedIndex) {
        log.info("CfReportService.sendMsgWorkOrderReport sendMsgWorkOrderReport infoUuid:{}", infoUuid);
        String identifier = "";
        String lockName = "sendMsgWorkOrderCfReport" + infoUuid;
        try {
            identifier = cfRedissonHandler.tryLock(lockName, 30 * 1000L);
            if (StringUtils.isBlank(identifier)) {
                log.info(" 获取案例审核工单生成锁失败，identifier:{}", identifier);
                throw new RuntimeException();
            }
            this.sendNewReportMq(infoUuid, reportId,amountRaisedIndex);
        } catch (Exception e) {
            log.error("error:", e);
        } finally {
            cfRedissonHandler.unLock(lockName, identifier);
        }
    }

    private void sendNewReportMq(String infoUuid, int reportId,String amountRaisedIndex) {
        Map<String, String> newReportMap = Maps.newHashMap();
        newReportMap.put("infoUuid", infoUuid);
        newReportMap.put("reportId", String.valueOf(reportId));
        newReportMap.put("amountRaisedIndex",amountRaisedIndex);
        producer.send(new Message<>(MQTopicCons.CF, MQTagCons.CF_WORK_ORDER_REPORT_NEW_LOGIC,
                MQTagCons.CF_WORK_ORDER_REPORT_NEW_LOGIC + "_" + infoUuid, newReportMap, DelayLevel.S5));
    }

    public Response<Void> modifyDepartmentTelNumber(String infoUuid, String newNumber, int operatorId, int workOrderId) {
        CfHospitalAuditInfo info = cfHospitalAuditBiz.getByInfoUuid(infoUuid);
        if (info == null) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        String oldNumber = info.getDepartmentTelNumber();
        info.setDepartmentTelNumber(newNumber);
        cfHospitalAuditBiz.update(info);
        CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);

        String prefix = workOrderId > 0 ? String.format("【工单ID：%d】", workOrderId) : "";
        String content = String.format("%s修改科室座机号，\"%s\"修改为\"%s\"", prefix, oldNumber, newNumber);
        approveRemarkOldService.add(fundingInfo.getId(), operatorId, content, ApproveSourceTypeEnum.HOSPITAL_AUDIT_PRIVATE);
        return NewResponseUtil.makeSuccess(null);
    }

    /**
     * 查询举报对应的新工单处理人
     * @param caseId
     * @param reportId
     * @return
     */
    public long getNewReportWorkOrderOperatorId(int caseId, int reportId) {
        final Response<WorkOrderVO> resp = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, WorkOrderType.REPORT_TYPES);
        if (NewResponseUtil.isNotOk(resp)) {
            return 0;
        }
        final WorkOrderVO lastOrder = resp.getData();
//        if (lastOrder.getHandleResult() != HandleResultEnum.doing.getType()) {
//            return 0;
//        }
        if (lastOrder == null) {
            return 0;
        }
        return lastOrder.getOperatorId();
//        Response<List<WorkOrderExt>> listResponse = cfWorkOrderClient.queryExtByCase(caseId, WorkOrderType.REPORT_TYPES,
//                OrderExtName.reportId.getName(), String.valueOf(reportId));
//        if (listResponse.ok() && CollectionUtils.isNotEmpty(listResponse.getData())) {
//            List<WorkOrderExt> workOrderExts = listResponse.getData();
//            WorkOrderExt workOrderExt = workOrderExts.get(0);
//            Response<WorkOrderVO> workOrderById = cfWorkOrderClient.getWorkOrderById(workOrderExt.getWorkOrderId());
//            if (workOrderById.ok()) {
//                WorkOrderVO workOrderVO = workOrderById.getData();
//                return workOrderVO.getOperatorId();
//            }
//        }
//        return 0;
    }

    public void updateHandleStatus(int caseId, int userId, int reportId) {
        // 修改举报条目状态,兼容旧状态
        adminCrowdfundingReportBiz.updateHandleAndConnectStatus(reportId, CfReportHandleStatus.HANDLED.getKey(),
                CfReportConnectStatus.NO_CONNECT.getKey(), userId, CaseReportDealStatus.FINISH.getValue());
        //修改案例举报状态
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        this.updateReportStatus(caseId, crowdfundingInfo.getInfoId());
        // 记录举报处理进展
        String comment = "将【举报id : " + reportId + "】的处理状态置为已处理";
        AdminUserAccountModel adminUserAccountModel = this.seaAccountClientV1.getValidUserAccountById(userId).getResult();
        cfReportFollowCommentBiz.save(userId, comment, 0, caseId, adminUserAccountModel.getName());
        // 记录操作记录
        String operation = "处理举报条目";
        financeApproveService.addApprove(crowdfundingInfo, operation, comment, userId);
    }

    /**
     * 更新案例举报状态
     *
     * @param caseId
     */
    public void updateReportStatus(int caseId, String infoUuid) {
        List<CrowdfundingReport> crowdfundingReports = adminCrowdfundingReportBiz.getListByInfoId(caseId);
        boolean anyMatch = crowdfundingReports.stream().anyMatch(crowdfundingReport -> crowdfundingReport.getHandleStatus()
                == CfReportHandleStatus.NO_HANDLE.getKey());
        if (anyMatch) {
            //举报未处理
            int changeResult = crowdfundingOperationDelegate.updateReportStatus(CaseReportStatusEnum.NO_HANDLE.getValue(), infoUuid);
            if (changeResult == 1) {
                this.crowdfundingOperationDelegate.saveCfOperatingRecord(infoUuid, 0,
                        "系统", CfOperatingRecordEnum.Type.REPORT_NO_HANDLE,
                        CfOperatingRecordEnum.Role.SYSTEM);
            }
        } else {
            //判断举报是否都已处理
            boolean allMatch = crowdfundingReports.stream().allMatch(crowdfundingReport -> crowdfundingReport.getHandleStatus()
                    == CfReportHandleStatus.HANDLED.getKey());
            if (allMatch) {
                int changeResult = crowdfundingOperationDelegate.updateReportStatus(CaseReportStatusEnum.FINISH.getValue(), infoUuid);
                if (changeResult == 1) {
                    this.crowdfundingOperationDelegate.saveCfOperatingRecord(infoUuid, 0, "系统", CfOperatingRecordEnum.Type.REPORT_HANDLE_FINISH,
                            CfOperatingRecordEnum.Role.SYSTEM);
                }
            }
        }
    }

    public Map<String, Object> getLatestHandler(int caseId) {
        //查询旧举报工单信息
        List<AdminWorkOrderReport> adminWorkOrderReports = adminWorkOrderReportBiz.findByCaseIdAndDealResult(caseId, List.of(
                AdminWorkOrderReportConst.DealResult.REPORT_FOLLOW.getCode(),
                AdminWorkOrderReportConst.DealResult.DEAL_COMPLETE.getCode(),
                AdminWorkOrderReportConst.DealResult.NO_DEAL.getCode()));
        AdminWorkOrderReport adminWorkOrderReport = adminWorkOrderReports.stream()
                .max(Comparator.comparing(AdminWorkOrderReport::getCreateTime)).orElse(null);
        //查询新举报工单信息
        WorkOrderVO newWorkOrder = this.getNewWorkOrder(caseId);
        Map<String, Object> resultMap = Maps.newHashMap();
        if (adminWorkOrderReport != null && newWorkOrder == null) {
            //取就举报工单处理人
            int workOrderId = adminWorkOrderReport.getWorkOrderId();
            AdminWorkOrder adminWorkOrder = adminWorkOrderBiz.selectById(workOrderId);
            this.setResultMap(resultMap, adminWorkOrder.getOperatorId());
        } else if (adminWorkOrderReport == null && newWorkOrder != null) {
            //取新举报工单处理人
            long operatorId = newWorkOrder.getOperatorId();
            this.setResultMap(resultMap, (int) operatorId);
        } else if (adminWorkOrderReport != null && newWorkOrder != null) {
            //对比新旧工单取最新的处理人
            Timestamp oldCreateTime = adminWorkOrderReport.getCreateTime();
            Date newCreateTime = newWorkOrder.getCreateTime();
            long operatorId = 0;
            if (oldCreateTime.getTime() > newCreateTime.getTime()) {
                int workOrderId = adminWorkOrderReport.getWorkOrderId();
                AdminWorkOrder adminWorkOrder = adminWorkOrderBiz.selectById(workOrderId);
                operatorId = adminWorkOrder.getOperatorId();
            } else {
                operatorId = newWorkOrder.getOperatorId();
            }
            this.setResultMap(resultMap, (int) operatorId);
        }
        return resultMap;
    }

    private void setResultMap(Map<String, Object> resultMap, int operatorId) {
        AuthRpcResponse<AdminUserAccountModel> userAccountById = seaAccountClientV1.getValidUserAccountById(operatorId);
        if (userAccountById.isSuccess() && Objects.nonNull(userAccountById.getResult())) {
            resultMap.put("userId", userAccountById.getResult().getId());
            resultMap.put("name", userAccountById.getResult().getName());
        }
    }

    private WorkOrderVO getNewWorkOrder(int caseId) {
        try {
            List<Integer> orderResult = List.of(HandleResultEnum.doing.getType(), HandleResultEnum.reach_agree.getType(),
                    HandleResultEnum.noneed_deal.getType(), HandleResultEnum.end_deal.getType());
            Response<List<WorkOrderVO>> response = cfWorkOrderClient.listByCaseIdAndTypeAndResult(caseId, WorkOrderType.REPORT_TYPES, orderResult);
            if (response.ok() && CollectionUtils.isNotEmpty(response.getData())) {
                List<WorkOrderVO> workOrderVOS = response.getData();
                return workOrderVOS.stream().max(Comparator.comparing(WorkOrderVO::getCreateTime)).orElse(null);
            }
        } catch (Exception e) {
            log.error("CfReportService.getNewWorkOrder error", e);
        }
        return null;
    }

    @Deprecated
    public void updateCaseReportStatus() {
        //清洗案例举报状态
        List<CrowdfundingOperation> crowdfundingOperations = adminCrowdfundingOperationBiz.findByReportStatus(
                List.of(CaseReportStatusEnum.NO_HANDLE.getValue(), CaseReportStatusEnum.HANDLEING.getValue()));
        crowdfundingOperations.forEach(crowdfundingOperation ->
                this.updateReportStatus(crowdfundingOperation.getCaseId(),crowdfundingOperation.getInfoId()));
    }


    public List<CrowdfundingReport> getReportByCaseIdAndName(int id, String name) {
        return adminCrowdfundingReportBiz.getListByInfoIdAndName(id, name);
    }

    public void autoReport(CrowdfundingInfo fundingInfo) {
        if (fundingInfo == null) {
            return;
        }
        //预标记举报的内容
        String userName = "系统：系统自动标记";
        String content = "已筹金额≥30万，需电联沟通打款方式";
        //查询举报
        List<CrowdfundingReport> crowdfundingReports = getReportByCaseIdAndName(fundingInfo.getId(),userName).stream().filter(v
                        -> content.equals(v.getContent())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(crowdfundingReports)) {
            log.warn("auto CrowdfundingReport is exist");
            return;
        }
        //超过30万由原先的自动下发医院核实变为自动举报
        int reportId = markReport(fundingInfo.getId(), content, userName, "", "9",
                fundingInfo, false, "", 102, 11,
                "系统自动标记","", ReportSourceEnum.STRATEGY,"amountRaisedIndex");
        log.info("caseId:{} auto report id:{}", fundingInfo.getId(), reportId);
    }

    public BaseCaseReportInfoVo getCaseReportInfo(int caseId) {
        BaseCaseReportInfoVo baseCaseReportInfoVo = new BaseCaseReportInfoVo();
        List<CrowdfundingReport> crowdfundingReports = adminCrowdfundingReportBiz.getListByInfoId(caseId);
        CrowdfundingReport firstReport = crowdfundingReports.stream().min(Comparator.comparing(CrowdfundingReport::getCreateTime)).orElse(null);
        CrowdfundingReport lastReport = crowdfundingReports.stream().max(Comparator.comparing(CrowdfundingReport::getCreateTime)).orElse(null);
        baseCaseReportInfoVo.setCaseId(caseId);
        baseCaseReportInfoVo.setReportNumber(crowdfundingReports.size());
        baseCaseReportInfoVo.setHandleStatus(HandleResultEnum.done.getType());
        baseCaseReportInfoVo.setFirstReportTime(firstReport == null ? null : firstReport.getCreateTime());
        baseCaseReportInfoVo.setLastReportTime(lastReport == null ? null : lastReport.getCreateTime());
        baseCaseReportInfoVo.setLastOperationTime(lastReport == null ? null : lastReport.getCreateTime());
        this.formNewOperationTime(baseCaseReportInfoVo);
        return baseCaseReportInfoVo;
    }

    /**
     * 获取最新工单操作时间
     *
     * @param baseCaseReportInfoVo
     */
    private void formNewOperationTime(BaseCaseReportInfoVo baseCaseReportInfoVo) {
        try {
            Response<WorkOrderVO> workOrderVOResponse = workOrderClient.getLastWorkOrderByTypes(
                    baseCaseReportInfoVo.getCaseId(), WorkOrderType.REPORT_TYPES);
            if (workOrderVOResponse.ok() && workOrderVOResponse.getData() != null) {
                WorkOrderVO workOrderVO = workOrderVOResponse.getData();
                baseCaseReportInfoVo.setLastOperationTime(workOrderVO.getUpdateTime());
                baseCaseReportInfoVo.setHandleStatus(workOrderVO.getHandleResult());
            }
        } catch (Exception e) {
            log.error("get new report operation time error", e);
        }
    }

    public Response<Boolean> reportWorkOrderUpgradeWindow(String reasonType, String remark, int caseId, long workOrderId, int adminUserId) {
        CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getFundingInfoById(caseId);
        String comment = reasonType + "：" + remark;
        financeApproveService.addApprove(crowdfundingInfo, "将工单升级二线", comment, adminUserId);
        final String org = seaAccountDelegate.getOrg(adminUserId);
        wonRecordClient.create()
                .buildActionId(112)
                .buildBizId(workOrderId)
                .buildCaseId(caseId)
                .buildOperatorId(adminUserId)
                .buildExtValue("workOrderId", workOrderId)
                .buildExtValue("caseId", caseId)
                .buildExtValue("remark", remark)
                .buildExtValue("reasonType", reasonType)
                .buildExtValue("org", org)
                .buildExtValue("operatorId", adminUserId)
                .save();
        return NewResponseUtil.makeSuccess(true);
    }


    public void sendCheckHandleMq(long workOrderId) {
        log.info("AdminWorkOrderReportNewLogicConsumer.sendCheckHandleMq workOrderId:{}", workOrderId);
        if (workOrderId == 0) {
            return;
        }
        String content = "该工单距创建已超过%sh，仍未进行首次触达;" + workOrderId;
        /**
         * 1.5 小时
         */
        var aHalfHourContent = String.format(content, 1.5);
        Message message_1 = Message.ofDelay(MQTopicCons.CF, com.shuidihuzhu.cf.constants.admin.MQTagCons.REPORT_WORK_ORDER_HANDLE_REMIND,
                com.shuidihuzhu.cf.constants.admin.MQTagCons.REPORT_WORK_ORDER_HANDLE_REMIND + "_" + workOrderId,
                aHalfHourContent, 90, TimeUnit.MINUTES);
        producer.send(message_1);

        /**
         * 5 小时
         */
        var fiveHourContent = String.format(content, 5);
        Message message_2 = Message.ofDelay(MQTopicCons.CF, com.shuidihuzhu.cf.constants.admin.MQTagCons.REPORT_WORK_ORDER_HANDLE_REMIND,
                com.shuidihuzhu.cf.constants.admin.MQTagCons.REPORT_WORK_ORDER_HANDLE_REMIND + "_" + workOrderId,
                fiveHourContent, 300, TimeUnit.MINUTES);
        producer.send(message_2);
    }

    public Response laterProcessing(WorkOrderVO workOrderVO, int reason, String otherReason, int adminUserId) {

        ReportHandleReasonEnum handleReasonEnum = ReportHandleReasonEnum.getByCode(reason);
        //  Add log ;
        String comment = "";
        if (handleReasonEnum != null && handleReasonEnum.getCode() == ReportHandleReasonEnum.REASON_6.getCode()) {
            comment = otherReason;
        } else if (handleReasonEnum != null) {
            comment = handleReasonEnum.getDesc();
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(workOrderVO.getCaseId());
        financeApproveService.addApprove(crowdfundingInfo, "稍后处理", comment, adminUserId);
        //  Add extension ;
        cfQcWorkOrderClient.addExtValue(workOrderVO.getWorkOrderId(), OrderExtName.workOrderHandleReason.getName(), Integer.toString(reason));
        //  Modify workOrder status ;
        ReportHandleOrderParam param = new ReportHandleOrderParam();
        param.setCaseId(workOrderVO.getCaseId());
        param.setWorkOrderId(workOrderVO.getWorkOrderId());
        param.setHandleResult(HandleResultEnum.later_doing.getType());
        param.setOperComment("举报工单稍后处理的时间");
        param.setUserId(adminUserId);
        param.setOrderType(workOrderVO.getOrderType());
        Response response = reportWorkOrderClient.handleReport(param);
        return ResponseUtil.makeSuccess(response.getCode(), response.getMsg());
    }

    public void setDoingCount(int count) {
        MessageResult result = producer.send(new Message<>(MQTopicCons.CF, CfClientMQTagCons.REPORT_SET_DOING_COUNT,
                CfClientMQTagCons.REPORT_SET_DOING_COUNT + "_" + count, count, DelayLevel.S1));
        log.info("CfReportService.setDoingCount result:{}", JSON.toJSONString(result));
    }

    public boolean autoMarkBusinessForward(int caseId, String prob) {
        List<CrowdfundingReport> crowdfundingReportList = adminCrowdfundingReportBiz.getListByInfoId(caseId);
        List<Integer> reportIdList = crowdfundingReportList.stream()
                .map(CrowdfundingReport::getId)
                .collect(Collectors.toList());
        List<CrowdfundingReportLabel> crowdfundingReportLabelList = adminCrowdfundingReportBiz.getReportLabels(reportIdList);
        List<CrowdfundingReportLabel> reportLabelList = crowdfundingReportLabelList.stream()
                .filter(f -> f.getReportLabel() == CfReportTypeEnum.commercialPromotion.getCode() || f.getReportLabel() == CfReportTypeEnum.BUSINESS_PROMOTION.getCode())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(reportLabelList)) {
            return true;
        }
        List<AdminCfReportRiskTagLabel> cfReportRiskTagLabelList = adminCrowdfundingReportBiz.getReportRiskTagLabel(caseId);
        List<AdminCfReportRiskTagLabel> tagLabelList = cfReportRiskTagLabelList.stream()
                .filter(f -> {
                    List<String> list = Splitter.on(",").splitToList(f.getRiskReportType());
                    return list.contains(String.valueOf(CfReportTypeEnum.commercialPromotion.getCode())) || list.contains(String.valueOf(CfReportTypeEnum.BUSINESS_PROMOTION.getCode()));
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(tagLabelList)) {
            return true;
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            return true;
        }
        String content = "商业推广案例-概率值" + prob;
        String userName = "系统：系统自动标记";
        String reportTypes = String.valueOf(CfReportTypeEnum.BUSINESS_PROMOTION.getCode());
        markReport(crowdfundingInfo.getId(), content, userName, "", reportTypes,
                crowdfundingInfo, false, "", 102, 11,
                "系统自动标记","", ReportSourceEnum.STRATEGY,"");
        return false;
    }

    public boolean autoMarkUserShareAndView(long userId) {
        UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(userId);
        if (Objects.isNull(userInfoModel)) {
            return true;
        }
        Response<List<CfRiskOperateDetail>> shareResponse = cfRiskOperateDetailClient.getOpeateDetail(userId, 0, UserOperationEnum.SHARE);
        boolean share = Optional.ofNullable(shareResponse)
                .map(Response::getData)
                .orElse(new ArrayList<>())
                .stream()
                .map(CfRiskOperateDetail::isResult)
                .findFirst()
                .orElse(true);
        if (share) {
            adminCaseVisitConfigBiz.judeRiskCase(0, 102, false, userId, UserOperationEnum.SHARE, RiskOperateSourceEnum.BIG_DATA, null);
        }
        Response<List<CfRiskOperateDetail>> viewResponse = cfRiskOperateDetailClient.getOpeateDetail(userId, 0, UserOperationEnum.VIEW);
        boolean view = Optional.ofNullable(viewResponse)
                .map(Response::getData)
                .orElse(new ArrayList<>())
                .stream()
                .map(CfRiskOperateDetail::isResult)
                .findFirst()
                .orElse(true);
        if (view) {
            adminCaseVisitConfigBiz.judeRiskCase(0, 102, false, userId, UserOperationEnum.VIEW, RiskOperateSourceEnum.BIG_DATA, null);
        }
        return false;
    }


}
