package com.shuidihuzhu.cf.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.call.CallTagBO;
import com.shuidihuzhu.cf.call.CallTagVO;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.delegate.crowdfunding.impl.CrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.facade.AdminApolloCofig;
import com.shuidihuzhu.cf.model.admin.workorder.CfCallOutRecord;
import com.shuidihuzhu.cf.model.admin.workorder.CfWorkOrderStatusDetail;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.workorder.juanzhan.JuanzhuanWorkOrderService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackTaskFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewCallRecordsDO;
import com.shuidihuzhu.client.cf.clewtrack.model.CfUserInvitedLaunchCaseRecordModel;
import com.shuidihuzhu.client.cf.clewtrack.model.ClewCallRecordModel;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolVolunteerFeignClient;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.workorder.CfShouciWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.config.WorkOrderHelper;
import com.shuidihuzhu.client.cf.workorder.ext.WorkOrderExtFeignClient;
import com.shuidihuzhu.client.cf.workorder.ext.WorkOrderExtVO;
import com.shuidihuzhu.client.cf.workorder.model.QueryListParam;
import com.shuidihuzhu.client.cf.workorder.model.ShouciWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OneTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.storage.VonStorageVO;
import com.shuidihuzhu.client.cf.workorder.storage.WorkOrderStorageService;
import com.shuidihuzhu.client.feign.CfClewtrackClient;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.joda.time.DateTime;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by wangsf on 18/4/9.
 */
@Slf4j
@Service
@RefreshScope
public class CfCaseWorkOrderService {

	/**
	 *
	 */
	private static final Set<String> NO_FIRST_CONTACT_CHANNEL_SET = Sets.newHashSet("线下筹款顾问","微信1V1服务");


	@Resource(name = "cfRedissonHandler")
	private RedissonHandler redissonHandler;

	@Autowired
	private CfClewtrackClient cfClewtrackClient;

	@Autowired
	private CfShouciWorkOrderClient shouciWorkOrderClient;

	@Autowired
	private CfFirstApproveOperatorBiz cfFirstApproveOperatorBiz;
	@Autowired
	private AdminApproveService adminApproveService;
	@Autowired
	private CfRefuseReasonCommonBiz reasonCommonBiz;
	@Autowired
	private ShuidiCipher shuidiCipher;
	@Autowired
	private CfWorkOrderClient workOrderClient;
	@Autowired
	private AdminCrowdfundingInfoBiz fundingInfoBiz;
	@Autowired
	private CfMaterialVerityHistoryBiz verityHistoryBiz;
	@Autowired
	private CfClewtrackFeignClient clewtrackFeignClient;
	@Autowired
	private SeaAccountClientV1 accountClientV1;
	@Autowired
	private CfClewtrackTaskFeignClient clewtrackTaskClient;
	@Resource
	private UserInfoServiceBiz userInfoServiceBiz;

	@Autowired
	private JuanzhuanWorkOrderService juanzhuanWorkOrderService;

	@Autowired
	private CfGrowthtoolVolunteerFeignClient cfGrowthtoolVolunteerFeignClient;

	@Autowired
	private AdminCfInfoExtBiz adminCfInfoExtBiz;

	@Resource
	private CrowdfundingDelegate crowdfundingDelegate;

	@Resource
	private WorkOrderStorageService workOrderStorageService;

	@Autowired
	private ClewPreproseMaterialFeignClient clewPreproseMaterialFeignClient;

	@Autowired
	private WorkOrderExtFeignClient workOrderExtFeignClient;

	public boolean addWorkOrder(int infoId) {

		String lockName = "FIRST_CONTACT_"+infoId;
		RLock lock = redissonHandler.getLock(lockName);

		if(!lock.tryLock()){
			return true;
		}
		try {

			// 如果是 线下bd发起的案例则不生成 首次审核工单 线下bd = crowdfunding_volunteer.type = 1 且在职 有效
			if (isCfCaseRaiseByOffLineBd(infoId)) {
				return false;
			}
			//如果再线索处生成了  就不再生成首次沟通
			if(checkClewtrack(infoId)){
				return false;
			}

			juanzhuanWorkOrderService.createD0(0, infoId, false);

			String shouciSwitch = AdminApolloCofig.getValueFromApollo(AdminApolloCofig.shouci_create_switch,"off");
			log.info("shouci_create_switch={}",shouciSwitch);
			if ("on".equals(shouciSwitch)){
				ShouciWorkOrder shouciWorkOrder = new ShouciWorkOrder();
				shouciWorkOrder.setCaseId(infoId);
// 暂时保留
//				shouciWorkOrderClient.createShouci(shouciWorkOrder);
			}

		}catch (Exception e){
			log.error("addWorkOrder error infoId={}",infoId,e);
		}finally {
			lock.unlock();
		}
		return true;
	}

	private boolean checkClewtrack(int caseId){

		try {
			Response<Boolean> response =  cfClewtrackClient.checkFuwuByCaseid(caseId);

			log.info("checkClewtrack caseId={}, response={}",caseId, JSON.toJSONString(response));

			if (response != null && response.getCode() == 0){

				Boolean result = response.getData();

				if (result != null){
					return result.booleanValue();
				}
			}
		}catch (Exception e){
			log.error("checkClewtrack caseId={}",caseId,e);
		}

		return false;

	}


	/**
	 *
	 * @param caseId
	 * @return
	 */
	public boolean isCfCaseRaiseByOffLineBd(int caseId) {

		Map<Integer, CfUserInvitedLaunchCaseRecordModel> caseRecordMap = adminApproveService
				.getCaseChannelRecordMap(Lists.newArrayList(caseId));

		if(MapUtils.isEmpty(caseRecordMap) || !caseRecordMap.containsKey(caseId)){
			return false;
		}

		CfUserInvitedLaunchCaseRecordModel caseRecordModel = caseRecordMap.get(caseId);
		String raiseChanelDesc = cfFirstApproveOperatorBiz.getGuideUserLaunchChannel(caseRecordModel.getServiceUserInfo(shuidiCipher));

		if(NO_FIRST_CONTACT_CHANNEL_SET.contains(raiseChanelDesc)){
			log.info("not generate first contact workorder, because channel is {}", raiseChanelDesc);
			return true;
		}

		return false;
	}


	// wiki  https://wiki.shuiditech.com/pages/viewpage.action?pageId=288851934
	public void fillTimeIfNullByHandleResult(WorkOrderListParam listParam, OneTypeEnum orderType) {
		if (!needHandleTime(listParam, orderType)) {
			return;
		}
		listParam.setStartTime(LocalDate.now().plusDays(-7L).toString()+" 00:00:00");
		listParam.setEndTime(LocalDate.now().plusDays(1L).toString()+" 00:00:00");
	}

	private boolean needHandleTime(WorkOrderListParam listParam, OneTypeEnum orderType) {
		if (listParam == null ||
				(StringUtils.isNotBlank(listParam.getStartTime()) && StringUtils.isNotBlank(listParam.getEndTime()))) {
			return false;
		}

		// caseId 点查 不加时间
		if (listParam.getCaseId() != 0) {
		    return false;
        }
		List<Integer> handleResults = reasonCommonBiz.getIdListSplitterByComma(listParam.getHandleResult());

		// 只处理 材料审核、首次沟通、初审
		if (orderType == OneTypeEnum.shouci) {
			return !handleResults.contains(HandleResultEnum.doing.getType()) ;
		}

		if (orderType == OneTypeEnum.cailiao || orderType == OneTypeEnum.chuci) {
			return !handleResults.contains(HandleResultEnum.doing.getType()) && !handleResults.contains(HandleResultEnum.later_doing.getType());
		}

		return false;
	}

	public void fillWorkOrderStartAndEndTime(QueryListParam queryListParam) {
		if (queryListParam == null) {
			return;
		}

		if(queryListParam.getOrderType() == null){
			queryListParam.setOrderType("");
		}

		// 如果输入了 工单id、 案例id、 手机号 则不设置默认的创建时间
		if (queryListParam.getWorkOrderId() != 0 || queryListParam.getCaseId() != 0
				|| StringUtils.isNotBlank(queryListParam.getMobile())) {
			return;
		}

		if (StringUtils.isBlank(queryListParam.getStartCreateTime())
				|| StringUtils.isBlank(queryListParam.getEndCreateTime())) {

			Date now = new Date();
			queryListParam.setStartCreateTime(DateUtil.getDate2LStr(DateUtils.addDays(now, -7)));
			queryListParam.setEndCreateTime(DateUtil.getDate2LStr(now));
		}
	}

	public CfWorkOrderStatusDetail selectWorkOrderById(int caseId, long workOrderId) {

		QueryListParam param = new QueryListParam();
		param.setWorkOrderId(workOrderId);

		Response<WorkOrderVO>  response = workOrderClient.getWorkOrderById(workOrderId);
		if (response == null || response.getData() == null) {
			log.warn("查询工单返回为空 workOrderId:{} response:{}", workOrderId, JSON.toJSONString(response));
			return null;
		}
		CfWorkOrderStatusDetail orderStatus = new CfWorkOrderStatusDetail();

		WorkOrderVO workResult = response.getData();
		orderStatus.setHandleResult(workResult.getHandleResult());
		orderStatus.setWorkOrderId(workOrderId);
		orderStatus.setCreateTime(workResult.getCreateTime());
		orderStatus.setAssignTime(workResult.getHandleTime());
		orderStatus.setHandleTime(workResult.getUpdateTime());
		orderStatus.setOperatorId(workResult.getOperatorId());

		CrowdfundingInfo info = fundingInfoBiz.getFundingInfoById(caseId);
		orderStatus.setCurrentStatus(info != null ? info.getStatus().value() : 0);
		orderStatus.setOrgName(verityHistoryBiz.queryOperateDetail(Math.toIntExact(workResult.getOperatorId())));

		return orderStatus;
	}
	//收款人手机号
	private static final int PAYEE_MOBILE = 0B1;
	// 发起人手机号
	private static final int RAISER_MOBILE = 0B10;
	// 线下顾问
	private static final int BD_MOBILE = 0B100;
	//紧急联系人
	private static final int EMERGENCY_MOBILE = 0B1000;
	// 待录入手机号
	private static final int INPUT_MOBILE = 0B10000;

	public CfCallOutRecord selectCallRecords(long workOrderId, int caseId, int userId) {
	    return selectCallRecordsOption(workOrderId, caseId, userId,
			    PAYEE_MOBILE + RAISER_MOBILE + BD_MOBILE + EMERGENCY_MOBILE + INPUT_MOBILE);
	}

	/**
	 * 查询质检工单对应的被质检工单的录音信息
	 * 1.查询质检工单对应的材审工单
	 * 2.查询材审工单的录音
	 * 3.填充质检工单标记过的录音标签
	 */
	public CfCallOutRecord selectQcCallRecordsAll(long workOrderId, int caseId, int userId) {
		// 1.查询质检工单对应的材审工单
		Response<VonStorageVO> sourceOrderResp = workOrderStorageService.getLastByType(workOrderId,
				WorkOrderHelper.Storage.SOURCE_ORDER_ID);
		if (sourceOrderResp.notOk()) {
			return new CfCallOutRecord();
		}
		Optional<Long> sourceOrderOptional = VonStorageVO.getByData(sourceOrderResp.getData(), Long.class);
		if (sourceOrderOptional.isEmpty()) {
			return new CfCallOutRecord();
		}
		Long sourceOrderId = sourceOrderOptional.get();
		// 2.查询材审工单的录音
		CfCallOutRecord cfCallOutRecord = selectCallRecordsOption(sourceOrderId, caseId, userId,
				PAYEE_MOBILE + RAISER_MOBILE + BD_MOBILE + EMERGENCY_MOBILE + INPUT_MOBILE);

		// 3.填充质检工单标记过的录音标签
		cfCallOutRecord = fillCallTag(workOrderId, cfCallOutRecord);

		return cfCallOutRecord;
	}

	private CfCallOutRecord selectCallRecordsOption(long workOrderId, int caseId, int userId, int flag) {

		// 允许caseId不传 根据工单id查询
		if (caseId <= 0 || userId <= 0) {
			WorkOrderVO  workOrder = queryWorkOrder(workOrderId);
			if (workOrder == null || workOrder.getHandleTime() == null) {
				return new CfCallOutRecord().withName("");
			}
			caseId = workOrder.getCaseId();
			userId = (int) workOrder.getOperatorId();
		}

		CrowdfundingInfo info = fundingInfoBiz.getFundingInfoById(caseId);
		if (info == null) {
			AuthRpcResponse<AdminUserAccountModel> model = accountClientV1.getValidUserAccountById(userId);
			String userName = model != null && model.getResult() != null ? model.getResult().getName() : "";
			return new CfCallOutRecord().withName(userName);
		}
		CfInfoExt ext = adminCfInfoExtBiz.getByCaseId(info.getId());

		HashSet<String> mobiles = Sets.newHashSet();

		if ((flag & PAYEE_MOBILE) > 0) {
			// 收款人手机号
			if (StringUtils.isNotBlank(info.getPayeeMobile())) {
				mobiles.add(info.getPayeeMobile());
			}
		}

		if ((flag & RAISER_MOBILE) > 0) {
			// 发起人手机号
			UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(info.getUserId());
			if (userInfoModel != null && StringUtils.isNotBlank(userInfoModel.getCryptoMobile()) &&
					!Objects.equals(userInfoModel.getCryptoMobile(), info.getPayeeMobile())) {
				mobiles.add(userInfoModel.getCryptoMobile());
			}
		}

		if ((flag & BD_MOBILE) > 0) {
			// 线下顾问
			String volunteerUniqueCode = ext.getVolunteerUniqueCode();
			if (StringUtils.isNotBlank(volunteerUniqueCode)) {
				CrowdfundingVolunteer volunteer = cfGrowthtoolVolunteerFeignClient.getCfVolunteerDOByUniqueCode(volunteerUniqueCode).getData();
				if (volunteer != null) {
					mobiles.add(volunteer.getMobile());
				}
			}
		}

		if ((flag & EMERGENCY_MOBILE) > 0) {
			// 紧急联系人
			CrowdfundingInfoPayee payee = crowdfundingDelegate.getCrowdfundingInfoPayeeByInfoUuid(info.getInfoId());
			if (payee != null && StringUtils.isNotEmpty(payee.getEmergencyPhone())) {
				mobiles.add(payee.getEmergencyPhone());
			}
		}


		if ((flag & INPUT_MOBILE) > 0) {
			// 代录入手机号
			Response<PreposeMaterialModel.MaterialInfoVo> response = clewPreproseMaterialFeignClient.selectMaterialByCaseIdForQC(caseId);
			if (response.getData() != null){
				mobiles.add(shuidiCipher.encrypt(response.getData().getRaiseMobile()));
			}
		}

		return selectCallRecords(userId, workOrderId, caseId, Lists.newArrayList(mobiles));
	}

	public CfCallOutRecord selectCallRecords(int userId, long workOrderId, int caseId, List<String> mobiles) {

		AuthRpcResponse<AdminUserAccountModel> model = accountClientV1.getValidUserAccountById(userId);
		String userName = model != null && model.getResult() != null ? model.getResult().getName() : "";
		String misName = model != null && model.getResult() != null ? model.getResult().getMis() : "";

//		List<String> allUuids = getAllCallUniId(workOrderId);
//		if (CollectionUtils.isEmpty(allUuids)) {
//			return new CfCallOutRecord().withName(userName);
//		}

		int totalSize = 0, validSize = 0, totalDuration = 0;
		List<CfCallOutRecord.CallOutDetail> outDetails = Lists.newArrayList();

//		Response<List<ClewCallRecordModel>> records = clewtrackFeignClient.getClewCallRecordsByUniqueIds(allUuids);
//		List<ClewCallRecordModel> recordModels = records != null && records.getData() != null ?
//				records.getData() : Lists.newArrayList();
//		for (ClewCallRecordModel recordModel : recordModels) {
//
//			if (!Objects.equals(userName, recordModel.getClientName())) {
//				log.info("当前的电话记录不是当前运营拨打的. msg:{} userId:{}", JSON.toJSONString(recordModel), userId);
//				continue;
//			}
//
//			++totalSize;
//			if (recordModel.getSipCause() == 200) {
//				++validSize;
//			}
//
//			totalDuration += recordModel.getTotalDuration();
//
//			CfCallOutRecord.CallOutDetail outDetail = new CfCallOutRecord.CallOutDetail();
//			outDetail.setMobile(shuidiCipher.decrypt(recordModel.getEncryptCustomerNumber()));
//			outDetail.setCreateTime(recordModel.getCnoStartTime());
//			outDetail.setEndTime(recordModel.getCnoEndTime());
//			outDetail.setDuration(recordModel.getTotalDuration());
//			outDetail.setVideoUrl(recordModel.getCosFile());
//			outDetail.setPhoneStatus(recordModel.getSipCause());
//			outDetails.add(outDetail);
//		}


		WorkOrderVO  workOrder = queryWorkOrder(workOrderId);
		if (workOrder == null || workOrder.getHandleTime() == null) {
			return new CfCallOutRecord().withName(userName);
		}

		long taskAssignTime = workOrder.getHandleTime().getTime();
		Date finishTime = new DateTime(workOrder.getFinishTime() == null ? workOrder.getUpdateTime() : workOrder.getFinishTime())
				.plusMinutes(10).toDate();
		List<CfClewCallRecordsDO> recordDos = selectCallRecords(misName, taskAssignTime, mobiles);
		for (CfClewCallRecordsDO recordsDO : recordDos) {

			if (Math.toIntExact(recordsDO.getCaseId()) != caseId || !isInRange(finishTime, recordsDO.getCreateTime())) {
				continue;
			}

			Integer phoneStatus = recordsDO.getPhoneStatus();
			boolean callSuccess = phoneStatus == 200;

			++totalSize;
			if (callSuccess){
				++validSize;
			}

			CfCallOutRecord.CallOutDetail outDetail = new CfCallOutRecord.CallOutDetail();
			outDetail.setCallRecordId(recordsDO.getId());
			outDetail.setMobile(shuidiCipher.decrypt(recordsDO.getEncryptCustomerNumber()));
			outDetail.setVideoUrl(recordsDO.getCosFile());
			outDetail.setPhoneStatus(recordsDO.getPhoneStatus());
			//1001:客户挂断 1000:坐席挂断
			String endReason = recordsDO.getEndReason();
			String endReasonMsg = "";
			if (StringUtils.equals(endReason, "1001")) {
				endReasonMsg = "客户挂断";
			} else if (StringUtils.equals(endReason, "1000")) {
				endReasonMsg = "坐席挂断";
			}
			outDetail.setEndReason(endReasonMsg);

			Date answerTime = recordsDO.getAnswerTime();
			long answerTimestamp = answerTime.getTime();
			long cnoEndTimestamp = recordsDO.getCnoEndTime().getTime();
			long realTotalDuration = 0;
			if (callSuccess) {
				realTotalDuration = (cnoEndTimestamp - answerTimestamp) / 1000;
			}
			outDetail.setDuration((int) realTotalDuration);
			outDetail.setEndTime(recordsDO.getCnoEndTime());
			outDetail.setCreateTime(recordsDO.getCnoStartTime());
			outDetail.setAnswerTime(recordsDO.getAnswerTime());

			totalDuration += outDetail.getDuration();

			outDetails.add(outDetail);
		}

		// 时间正序排序
		outDetails = outDetails.stream()
					.sorted(Comparator.comparing(CfCallOutRecord.CallOutDetail::getCreateTime))
					.collect(Collectors.toList());

		CfCallOutRecord outRecord = new CfCallOutRecord();
		outRecord.setTotalSize(totalSize);
		outRecord.setValidSize(validSize);
		outRecord.setTotalDuration(totalDuration);
		outRecord.setCallOutDetails(outDetails);
		outRecord.setName(userName);

		return outRecord;
	}

	private CfCallOutRecord fillCallTag(long workOrderId, CfCallOutRecord outRecord) {
		Response<VonStorageVO> orderStorageResp = workOrderStorageService.getLastByType(workOrderId,
				WorkOrderHelper.Storage.CALL_TAG_INFO);
		if (orderStorageResp.notOk()) {
			return outRecord;
		}
		VonStorageVO data = orderStorageResp.getData();
		Optional<CallTagVO> callTagOptional = VonStorageVO.getByData(data, CallTagVO.class);
		if (callTagOptional.isEmpty()) {
			return outRecord;
		}
		CallTagVO callTagVO = callTagOptional.get();
		List<CallTagBO> tags = callTagVO.getTags();
		if (CollectionUtils.isEmpty(tags)) {
			return outRecord;
		}
		Map<Integer, Integer> recordIdTagMap = tags.stream()
				.collect(Collectors.toMap(CallTagBO::getCallRecordId, CallTagBO::getCallTag));
		List<CfCallOutRecord.CallOutDetail> details = outRecord.getCallOutDetails();
		if (CollectionUtils.isEmpty(details)) {
			return outRecord;
		}
		for(CfCallOutRecord.CallOutDetail e : details) {
		    e.setCallTag(recordIdTagMap.getOrDefault(e.getCallRecordId(), 0));
		}
		return outRecord;
	}

	private boolean isInRange(Date finishTime, Date phoneTime) {

		return finishTime == null || phoneTime == null || finishTime.after(phoneTime);
	}

	private WorkOrderVO queryWorkOrder(long workOrderId) {

		Response<WorkOrderVO>  response = workOrderClient.getWorkOrderById(workOrderId);
		if (response == null || response.getData() == null) {
			log.warn("查询工单返回为空 workOrderId:{} response:{}", workOrderId, JSON.toJSONString(response));
			return null;
		}

		return response == null ? null : response.getData();
	}


	public boolean hasCallPhone(long workOrderId, int userId) {
		List<String> allCallUnids = getAllCallUniId(workOrderId);

		if (CollectionUtils.isEmpty(allCallUnids)) {
			log.info("当前工单没有拨出电话的记录。workOrderId：{} userId:{}", workOrderId, userId);
			return false;
		}

		AuthRpcResponse<AdminUserAccountModel> model = accountClientV1.getValidUserAccountById(userId);
		String userName = model != null && model.getResult() != null ? model.getResult().getName() : "";
		if (StringUtils.isBlank(userName)) {

		}

		Response<List<ClewCallRecordModel>> records = clewtrackFeignClient.getClewCallRecordsByUniqueIds(allCallUnids);
		List<ClewCallRecordModel> recordModels = records != null && records.getData() != null ?
				records.getData() : Lists.newArrayList();
		for (ClewCallRecordModel recordModel : recordModels) {
			if (recordModel.getSipCause() != 200) {
				log.info("当前的电话记录不是200. msg:{} userId:{}", JSON.toJSONString(recordModel), userId);
				continue;
			}

			if (!Objects.equals(userName, recordModel.getClientName())) {
				log.info("当前的电话记录不是当前运营拨打的. msg:{} userId:{}", JSON.toJSONString(recordModel), userId);
				continue;
			}
			log.info("能找到工单的电话记录。workOrderId:{} userId:{} callRecord:{}", workOrderId, userId,
					JSON.toJSONString(recordModel));
			return true;
		}

		return false;

	}

	private List<String> getAllCallUniId(long workOrderId) {
		Response<List<WorkOrderExt>> response = workOrderClient.queryAllWorkExtIgnoreDelete(workOrderId,
				Lists.newArrayList(OrderExtName.callUnicode.getName()));
		List<String> result = Lists.newArrayList();

		if (response == null || CollectionUtils.isEmpty(response.getData())) {
			return result;
		}

		response.getData().forEach(item->{result.add(item.getExtValue());});

		return result;
	}

	private List<CfClewCallRecordsDO> selectCallRecords(String mis, long taskAssignTime, List<String> mobiles) {

		List<CfClewCallRecordsDO> allResult = Lists.newArrayList();
		if (taskAssignTime == 0) {
			return allResult;
		}

		for (String mobile : mobiles) {
			Response<List<CfClewCallRecordsDO>> raiseRecords = clewtrackTaskClient.listCallRecordByMisAndPhone(mis,
					mobile, null, taskAssignTime);
			if (raiseRecords != null && CollectionUtils.isNotEmpty(raiseRecords.getData())) {
				allResult.addAll(raiseRecords.getData());
			}
		}

		return allResult;
	}

	public Response<Void> addCallRecordTag(long workOrderId, CallTagVO tagInfo) {
		Response<VonStorageVO> resp = workOrderStorageService.addByTypeOfJson(workOrderId, WorkOrderHelper.Storage.CALL_TAG_INFO, tagInfo);

		List<String> callTagList = tagInfo.getTags().stream()
				.map(tag -> String.valueOf(tag.getCallTag()))
				.distinct()
				.collect(Collectors.toList());

		List<WorkOrderExtVO> extVOS = callTagList.stream().map(tag -> {
			WorkOrderExtVO extVO = WorkOrderExtVO.create(OrderExtName.qcCallType, tag);
			extVO.setWorkOrderId(workOrderId);
			return extVO;
		}).collect(Collectors.toList());

		Response<Void> type = workOrderExtFeignClient.markDeleteByName(workOrderId, OrderExtName.qcCallType.getName());
		Response<Void> response = workOrderExtFeignClient.addByList(extVOS);

		if (resp.ok() && type.ok() && response.ok()){
			return NewResponseUtil.makeSuccess(null);
		}
		return NewResponseUtil.makeFail(null);
	}
}
