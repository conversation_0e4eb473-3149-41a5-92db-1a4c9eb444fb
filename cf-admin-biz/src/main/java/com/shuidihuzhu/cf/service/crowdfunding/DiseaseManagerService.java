package com.shuidihuzhu.cf.service.crowdfunding;


import com.shuidihuzhu.cf.delegate.saas.AdminOrganization;
import com.shuidihuzhu.cf.delegate.saas.OrganizationClientV1;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.model.admin.vo.CfDiseaseManagerRecordVo;
import com.shuidihuzhu.cf.model.admin.vo.CfDiseaseManagerVo;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import com.shuidihuzhu.pf.common.v2.model.pagehelper.PaginationListVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * @author: fengxuan
 * @create 2019-11-08 17:15
 **/
@Service
@Slf4j
public class DiseaseManagerService {

    @Autowired
    private CfDiseaseManagerBiz cfDiseaseManagerBiz;

    @Autowired
    private CfDiseaseManagerRecordBiz recordBiz;

    @Autowired
    SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    CfDiseaseClassifyBiz cfDiseaseClassifyBiz;

    @Resource
    private OrganizationClientV1 organizationClientV1;

    //创建
    public Response<Boolean> create(CfDiseaseManagerDO cfDiseaseManagerDO, long userId) {
        boolean addSuc = cfDiseaseManagerBiz.add(cfDiseaseManagerDO);
        long managerId = cfDiseaseManagerDO.getId();
        if (addSuc && managerId > 0) {
            CfDiseaseManagerRecordDO recordDO = buildRecord(managerId, userId, CfDiseaseManagerRecordDO.OperateTypeEnum.CREATE.getCode());
            recordBiz.add(recordDO);
        } else {
            log.warn("create disease info error cfDiseaseManagerDO:{}", cfDiseaseManagerDO);
        }
        return ResponseUtil.makeSuccess(true);
    }


    //编辑、修改
    public Response<Boolean> edit(CfDiseaseManagerDO cfDiseaseManagerDO, long userId) {
        boolean addSuc = cfDiseaseManagerBiz.edit(cfDiseaseManagerDO);
        long managerId = cfDiseaseManagerDO.getId();
        if (addSuc && managerId > 0) {
            CfDiseaseManagerRecordDO recordDO = buildRecord(managerId, userId, CfDiseaseManagerRecordDO.OperateTypeEnum.EDIT.getCode());
            recordBiz.add(recordDO);
        } else {
            log.warn("edit disease info error cfDiseaseManagerDO:{}", cfDiseaseManagerDO);
        }
        return ResponseUtil.makeSuccess(true);
    }

    //删除
    public Response<Boolean> delete(long userId, long managerId) {
        boolean delete = cfDiseaseManagerBiz.delete(managerId);
        if (delete) {
            CfDiseaseManagerRecordDO recordDO = buildRecord(managerId, userId, CfDiseaseManagerRecordDO.OperateTypeEnum.DELETE.getCode());
            recordBiz.add(recordDO);
        } else {
            log.warn("delete disease info error managerId:{}", managerId);
        }
        return ResponseUtil.makeSuccess(true);
    }


    public Response<PaginationListVO<CfDiseaseManagerVo>> list(CfDiseaseManagerDO cfDiseaseManagerDO, int current, int pageSize) {
        List<CfDiseaseManagerDO> cfDiseaseManagerDOS = cfDiseaseManagerBiz.listForAdminSearch(cfDiseaseManagerDO, current, pageSize);
        PaginationListVO<CfDiseaseManagerDO> pageResult = PaginationListVO.createWithList(cfDiseaseManagerDOS);
        List<CfDiseaseManagerVo> managerVoList = cfDiseaseManagerDOS.stream()
                .map(this::populateManagerInfo)
                .collect(Collectors.toList());
        return ResponseUtil.makeSuccess(PaginationListVO.create(managerVoList, pageResult.getPagination()));
    }



    public Response<CfDiseaseManagerVo> get(long id) {
        CfDiseaseManagerDO managerDO = cfDiseaseManagerBiz.getById(id);
        if (managerDO == null) {
            return ResponseUtil.makeSuccess(null);
        }
        return ResponseUtil.makeSuccess(populateManagerInfo(managerDO));
    }



    public Response<List<CfDiseaseManagerRecordVo>> listRecord(long managerId) {
        List<CfDiseaseManagerRecordDO> recordDOList = recordBiz.listByManagerId(managerId);
        List<CfDiseaseManagerRecordVo> recordVoList = recordDOList.stream().map(DiseaseManagerService::populateRecord).collect(Collectors.toList());
        return ResponseUtil.makeSuccess(recordVoList);
    }



    public List<CfDiseaseClassifyDO> listAllClassifyInfo() {
        //use cache or db
        return cfDiseaseClassifyBiz.listAll();
    }


    //组装额外信息
    private static CfDiseaseManagerRecordVo populateRecord(CfDiseaseManagerRecordDO recordDO) {
        CfDiseaseManagerRecordVo recordVo = new CfDiseaseManagerRecordVo();
        BeanUtils.copyProperties(recordDO, recordVo);
        CfDiseaseManagerRecordDO.OperateTypeEnum operateTypeEnum = CfDiseaseManagerRecordDO.OperateTypeEnum.findByCode(recordDO.getOperateType());
        recordVo.setOptDesc(operateTypeEnum == null ? null : operateTypeEnum.getDes());
        return recordVo;
    }


    //添加一些文字描述
    private CfDiseaseManagerVo populateManagerInfo(CfDiseaseManagerDO managerDO) {
        CfDiseaseManagerVo cfDiseaseManagerVo = new CfDiseaseManagerVo();
        BeanUtils.copyProperties(managerDO, cfDiseaseManagerVo);

        CfDiseaseManagerDO.RaiseTypeEnum raiseTypeEnum = CfDiseaseManagerDO.RaiseTypeEnum.findByCode(cfDiseaseManagerVo.getRaiseType());
        cfDiseaseManagerVo.setRaiseTypeDesc(raiseTypeEnum == null ? "" : raiseTypeEnum.getDesc());

        Map<Long, String> classifyDOMap = listAllClassifyInfo().stream()
                .collect(Collectors.toMap(CfDiseaseClassifyDO::getId, CfDiseaseClassifyDO::getClassifyDesc, (before, after) -> before));
        cfDiseaseManagerVo.setDiseaseClassifyDesc(classifyDOMap.getOrDefault(cfDiseaseManagerVo.getDiseaseClassifyId(), ""));

        CfDiseaseManagerDO.TreatmentProjectEnum treatmentEnum = CfDiseaseManagerDO.TreatmentProjectEnum.findByCode(cfDiseaseManagerVo.getTreatmentProject());
        cfDiseaseManagerVo.setTreatmentProjectDesc(treatmentEnum == null ? "" : treatmentEnum.getDesc());
        if (treatmentEnum == CfDiseaseManagerDO.TreatmentProjectEnum.custom) {
            cfDiseaseManagerVo.setTreatmentProjectDesc(managerDO.getCustomTreatment());
        }
        return cfDiseaseManagerVo;
    }


    public CfDiseaseManagerRecordDO buildRecord(long managerId, long userId, int optType) {
        String organizationName = null;
        AuthRpcResponse<AdminOrganization> userOrgInfo = organizationClientV1.getUserOrgInfo((int) userId);

        if (userOrgInfo.isSuccess()) {
            AdminOrganization organization = userOrgInfo.getResult();
            organizationName = organization.getName();
        } else {
            log.warn("获取org信息异常!userId:{}", userId);
        }
        AuthRpcResponse<AdminUserAccountModel> accountResponse = seaAccountClientV1.getValidUserAccountById((int) userId);
        String name = "";
        if (accountResponse.isSuccess()) {
            AdminUserAccountModel accountResult = accountResponse.getResult();
            name = (organizationName != null ? organizationName + "-" + accountResult.getName() : accountResult.getName());
        } else {
            log.warn("获取mis信息异常!userId:{}", userId);
        }

        CfDiseaseManagerRecordDO recordDO = new CfDiseaseManagerRecordDO();
        recordDO.setOperatorId(userId);
        recordDO.setOperatorName(name);
        recordDO.setDiseaseManagerId(managerId);
        recordDO.setOperateType(optType);
        return recordDO;
    }

}
