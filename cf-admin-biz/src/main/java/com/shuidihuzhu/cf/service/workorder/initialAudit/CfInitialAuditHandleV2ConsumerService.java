package com.shuidihuzhu.cf.service.workorder.initialAudit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.admin.util.admin.AdminCfIdCardUtil;
import com.shuidihuzhu.cf.biz.admin.ChuciAnalyticsBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.client.adminpure.enums.WorkOrderExtContentTypeEnum;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfMaterialWriteClient;
import com.shuidihuzhu.cf.client.material.feign.CfRaiseMaterialClient;
import com.shuidihuzhu.cf.client.material.feign.PreposeMaterialClient;
import com.shuidihuzhu.cf.client.material.model.CfMaterialAddOrUpdateVo;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.material.model.RaiseBasicInfoModel;
import com.shuidihuzhu.cf.client.material.model.materialField.MaterialExtKeyConst;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.crowdfunding.impl.CrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.enums.InitialAudit.CfRefuseReasonCustomEnum;
import com.shuidihuzhu.cf.enums.InitialAudit.InitialAuditNoSmartReason;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdFundingProgressType;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.CrowdfundingInitialAuditInfo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.mq.OcrMedicalCaseInfoMqBody;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClassifyFeignClientV2;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClient;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyEnum;
import com.shuidihuzhu.cf.risk.model.risk.DiseaseAmountResultRecord;
import com.shuidihuzhu.cf.risk.model.risk.diease.*;
import com.shuidihuzhu.cf.service.ai.AiImageMaskServiceImpl;
import com.shuidihuzhu.cf.service.disease.impl.DiseaseNormServiceImpl;
import com.shuidihuzhu.cf.service.tag.CfThreeBodyService;
import com.shuidihuzhu.cf.service.tag.impl.CfCaseLabelServiceImpl;
import com.shuidihuzhu.cf.service.tag.impl.CfThreeBodyServiceImpl;
import com.shuidihuzhu.cf.service.workorder.WorkOrderExtService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationItem;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationMqVO;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.baseservice.pay.enums.BankCardVerifyEnum;
import com.shuidihuzhu.client.cf.admin.enums.ThreeBodyCaseTabEnum;
import com.shuidihuzhu.client.cf.api.client.CfPublicAuditFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.ChannelRefine;
import com.shuidihuzhu.client.cf.clewtrack.param.ChannelRefineDTO;
import com.shuidihuzhu.client.cf.growthtool.client.CfChannelFeignClient;
import com.shuidihuzhu.client.cf.search.client.CfSearchClient;
import com.shuidihuzhu.client.cf.search.model.EsCaseLabelColum;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.QcWorkOrderCreateParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.constant.CfClientMQTopicCons;
import com.shuidihuzhu.client.model.CfPublicAuditAction;
import com.shuidihuzhu.client.model.PatientRaiseCheckModel;
import com.shuidihuzhu.client.model.enums.ImageMaskBizEnum;
import com.shuidihuzhu.client.model.enums.PublicAuditTypeEnum;
import com.shuidihuzhu.client.util.TreatmentParse;
import com.shuidihuzhu.common.util.BeanUtils;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

import static com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditOperateService.FIRST_APPROVE_TAG;

/**
 * @Author: wangpeng
 * @Date: 2022/6/13 14:38
 * @Description:
 */
@Slf4j
@Service
@RefreshScope
public class CfInitialAuditHandleV2ConsumerService {

    /**
     *  测试人员的id 不生成补充医疗信息工单
      */
    @Value("#{'${apollo.no.create.supply.hospital.userIds:}'.split(',')}")
    private List<Long> noCreateSupplyHospitalUserIds;
    @Value("${apollo.handle.initial.save.snapShot:false}")
    private boolean handleInitialSaveSnapShot;
    @Value("${apollo.admin.case-label-switch:true}")
    private boolean caseLabelSwitch;

    @Resource
    private Producer producer;
    @Resource
    private CfRefuseReasonEntityBiz cfRefuseReasonEntityBiz;
    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Resource
    private AdminCfInfoExtBiz adminCfInfoExtBiz;
    @Resource
    private CrowdfundingOperationDelegate crowdfundingOperationDelegate;
    @Resource
    private AdminCrowdfundingAttachmentBiz adminCrowdfundingAttachmentBiz;
    @Resource
    private ChuciAnalyticsBiz chuciAnalyticsBiz;
    @Resource
    private InitialAuditCreateOrder initialAuditCreateOrder;
    @Resource
    private InitialAuditOperateService initialAuditOperateService;
    @Resource
    private CfMaterialWriteClient cfMaterialWriteClient;
    @Resource
    private WorkOrderExtService workOrderExtService;
    @Resource
    private CfThreeBodyServiceImpl cfThreeBodyService;
    @Resource
    private IRiskDelegate riskDelegate;
    @Resource
    private AdminCrowdFundingProgressBiz adminCrowdFundingProgressBiz;
    @Resource
    private AiImageMaskServiceImpl aiImageMaskService;
    @Resource
    private SeaAccountClientV1 seaAccountClientV1;
    @Autowired
    private CfWorkOrderClient workOrderClient;
    @Resource
    private CfPublicAuditFeignClient cfPublicAuditFeignClient;
    @Resource
    private InitialAuditSearchService initialAuditSearchService;
    @Resource
    private CfRaiseMaterialClient cfRaiseMaterialClient;
    @Resource
    private DiseaseClassifyFeignClientV2 diseaseClassifyClientV2;
    @Resource
    private DiseaseClient diseaseClient;
    @Resource
    private PreposeMaterialClient preposeMaterialClient;
    @Resource
    private ShuidiCipher shuidiCipher;
    @Resource
    private CfCaseLabelServiceImpl cfCaseLabelService;
    @Resource
    private CfSearchClient cfSearchClient;

    @Value("${approve.first-approve.success.progress:医疗材料已公示}")
    private String progressContent;

    /**
     * 自定义驳回项处理逻辑
     */
    public void customRefuse(InitialAuditOperationMqVO handleCaseInfoParam) {
        Map<Integer, String> customRefuseReason = handleCaseInfoParam.getCustomRefuseReason();
        if (MapUtils.isEmpty(customRefuseReason)) {
            return;
        }
        List<CfRefuseReasonCustomEntity> entityBizCustomByCaseId = cfRefuseReasonEntityBiz.getCustomByCaseId(handleCaseInfoParam.getCaseId());
        Optional.ofNullable(entityBizCustomByCaseId)
                .orElse(Collections.emptyList())
                .forEach(f -> {
                    f.setRefuseStatus(CfRefuseReasonCustomEnum.NOT_NORMAL.getType());
                    cfRefuseReasonEntityBiz.updateStatusByCaseIdAndRefuseId(f);
                });

        for (Map.Entry<Integer, String> entry : customRefuseReason.entrySet()) {
            CfRefuseReasonCustomEntity cfRefuseReasonCustomEntity = new CfRefuseReasonCustomEntity();
            cfRefuseReasonCustomEntity.setCaseId(handleCaseInfoParam.getCaseId());
            cfRefuseReasonCustomEntity.setWorkOrderId(handleCaseInfoParam.getWorkOrderId());
            cfRefuseReasonCustomEntity.setRefuseReasonEntityId(entry.getKey());
            cfRefuseReasonCustomEntity.setRefuseCustomReason(entry.getValue());
            cfRefuseReasonCustomEntity.setRefuseStatus(CfRefuseReasonCustomEnum.NORMAL.getType());
            cfRefuseReasonEntityBiz.insertCfRefuseReasonCustomEntity(cfRefuseReasonCustomEntity);
        }
    }

    /**
     * 初审-高风险工单 审核通过/审核驳回/停止筹款 生成内审-高风险质检工单
     * @param handleCaseInfoParam
     */
    public void createHighRiskQcWorkOrder(InitialAuditOperationMqVO handleCaseInfoParam) {
        if(handleCaseInfoParam.getOrderType() != WorkOrderType.highriskshenhe.getType()) {
            return;
        }
        if (handleCaseInfoParam.getHandleType() != InitialAuditOperationItem.HandleTypeEnum.END_CASE.getCode() &&
                handleCaseInfoParam.getHandleType() != InitialAuditOperationItem.HandleTypeEnum.SUBMIT.getCode()) {
            return;
        }
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(handleCaseInfoParam.getCaseId());
        CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByInfoUuid(crowdfundingInfo.getInfoId());
        CrowdfundingStatus crowdfundingStatus = crowdfundingOperationDelegate.checkDataUpdateCaseStatus(crowdfundingInfo, cfInfoExt);

        QcWorkOrderCreateParam qcWorkOrderCreateParam = QcWorkOrderCreateParam.builder()
                .caseId(handleCaseInfoParam.getCaseId())
                .handleResult(Optional.ofNullable(crowdfundingStatus).map(CrowdfundingStatus::value).orElse(-1))
                .operatorId((long) handleCaseInfoParam.getUserId())
                .workOrderId(handleCaseInfoParam.getWorkOrderId())
                .build();

        MessageResult sendResult = producer.send(new Message<>(MQTopicCons.CF,
                CfClientMQTagCons.QC_HIGH_RISK_WORK_ORDER_CREATE,
                CfClientMQTagCons.QC_HIGH_RISK_WORK_ORDER_CREATE + "_" + System.currentTimeMillis(),
                qcWorkOrderCreateParam, DelayLevel.S1));
        log.info("生成高风险质检工单 sendResult:{}", JSON.toJSONString(sendResult));
    }

    /**
     * 审核通过，给保险供给匹配策略消息
     * @param initialAuditOperationMqVO
     */
    public void ocrMedicalCaseInfo(InitialAuditOperationMqVO initialAuditOperationMqVO) {
        if (initialAuditOperationMqVO.getCaseInitialAuditResult().getHandleResult() != HandleResultEnum.audit_pass.getType()) {
            return;
        }
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(initialAuditOperationMqVO.getCaseId());
        if (Objects.isNull(crowdfundingInfo)) {
            log.info("CfInitialAuditHandleV2ConsumerService ocrMedicalCaseInfo crowdfundingInfo is empty : {}", initialAuditOperationMqVO.getCaseId());
            return;
        }
        List<CrowdfundingAttachment> attachmentList = adminCrowdfundingAttachmentBiz.getAttachmentsByType(crowdfundingInfo.getId(), AttachmentTypeEnum.ATTACH_FIRST_APPROVE_MEDICAL);
        if (CollectionUtils.isEmpty(attachmentList)) {
            log.info("InitialAuditOperateService handleWorkOrder ocrMedicalCaseInfoMq attachmentList is empty : {}", crowdfundingInfo.getId());
            return;
        }
        OcrMedicalCaseInfoMqBody caseInfoMqBody = OcrMedicalCaseInfoMqBody.builder()
                .medicalCaseId(String.valueOf(crowdfundingInfo.getId()))
                .title(crowdfundingInfo.getTitle())
                .content(crowdfundingInfo.getContent())
                .pictureUrl(attachmentList.get(0).getUrl())
                .build();
        Message<OcrMedicalCaseInfoMqBody> message = new Message<>(OcrMedicalCaseInfoMqBody.MQ_TOPIC, OcrMedicalCaseInfoMqBody.MQ_TAG, OcrMedicalCaseInfoMqBody.MQ_TAG + "_" + crowdfundingInfo.getId(), caseInfoMqBody);
        producer.send(message);
    }

    /**
     * 是否能创建医疗审核工单和打点
     */
    public void canCreateMedicalWorkOrderAndTrack(InitialAuditOperationMqVO initialAuditOperationMqVO) {
        boolean canCreateMedicalWorkOrder = initialAuditOperationMqVO.isCanCreateMedicalWorkOrder();
        // 打点上报给数据组
        if (!canCreateMedicalWorkOrder) {
            chuciAnalyticsBiz.initialVerify(initialAuditOperationMqVO.getCaseId(), initialAuditOperationMqVO.getWorkOrderId(), initialAuditOperationMqVO.getUserId(), initialAuditOperationMqVO.getCaseInitialAuditResult().getHandleResult());
            return;
        }

        boolean contactWithWorkOrderOk = initialAuditOperationMqVO.isContactWithWorkOrderOk();
        if (!contactWithWorkOrderOk) {
            log.info("canCreateMedicalWorkOrderAndTrack contactWithWorkOrderOk is not ok {}", initialAuditOperationMqVO);
            return;
        }
        //判断是否需要生成医疗审核工单
        initialAuditCreateOrder.createYiliaoWork(initialAuditOperationMqVO.getCaseId());
    }

    /**
     * 初审通过 创建补充医院信息工单
     * @param initialAuditOperationMqVO
     */
    public void handleAfterCreateYiYuanBuChongWork(InitialAuditOperationMqVO initialAuditOperationMqVO) {
        int handleResult = initialAuditOperationMqVO.getCaseInitialAuditResult().getHandleResult();
        boolean contactWithWorkOrderOk = initialAuditOperationMqVO.isContactWithWorkOrderOk();
        if (!contactWithWorkOrderOk) {
            log.info("handleAfterCreateYiYuanBuChongWork contactWithWorkOrderOk is not ok {}", initialAuditOperationMqVO);
            return;
        }
        if (HandleResultEnum.audit_pass.getType() != handleResult && HandleResultEnum.smart_audit_pass.getType() != handleResult) {
            log.info("handleAfterCreateYiYuanBuChongWork handleResult is limit {}", initialAuditOperationMqVO);
            return;
        }
        if (CollectionUtils.isEmpty(noCreateSupplyHospitalUserIds) || noCreateSupplyHospitalUserIds.contains((long) initialAuditOperationMqVO.getUserId())) {
            log.info("当前用户在白名单中，不生成补充医院信息工单。userId:{}", initialAuditOperationMqVO.getUserId());
            return;
        }
        initialAuditCreateOrder.createYiYuanBuChongWork(initialAuditOperationMqVO.getCaseId());

    }

    /**
     * 初审通过医疗材料插入动态
     */
    public void sendProgressAfterInitialAuditPass(InitialAuditOperationMqVO handleCaseInfoParam) {

        CrowdfundingInitialAuditInfo initialAuditInfo = crowdfundingOperationDelegate.selectCrowdfundingInitialAuditInfoByCaseId(handleCaseInfoParam.getCaseId());
        log.info("sendProgressAfterInitialAuditPass {}", handleCaseInfoParam);
        if (!initialAuditOperateService.isCasePass(initialAuditInfo)) {
            log.info("sendProgressAfterInitialAuditPass case not pass {}", handleCaseInfoParam);
            return;
        }

        CfFirsApproveMaterial material = riskDelegate.getCfFirsApproveMaterialByInfoId(handleCaseInfoParam.getCaseId());
        if (Objects.isNull(material)) {
            log.error("初审处理不能找到CfFirsApproveMaterial {}", handleCaseInfoParam);
            return;
        }
        if (handleCaseInfoParam.isSpecialReport()) {
            log.info("案例走了特殊报备流程业务人员并标记，不发动态 {}", handleCaseInfoParam);
            return;
        }
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(handleCaseInfoParam.getCaseId());
        if (Objects.isNull(crowdfundingInfo)) {
            log.error("初审处理不能找到CrowdfundingInfo {}", handleCaseInfoParam);
            return;
        }

        CrowdFundingProgress progress = new CrowdFundingProgress();
        progress.setImageUrls(material.getImageUrl());
        progress.setContent(progressContent);
        progress.setUserId(crowdfundingInfo.getUserId());
        progress.setActivityId(handleCaseInfoParam.getCaseId());
        progress.setTitle("");
        progress.setType(CrowdFundingProgressType.PROGRESS.value());
        adminCrowdFundingProgressBiz.insertInCrowdfundingProgress(progress);

        // 动态图片掩码发送通知
        if (progress.getId() > 0) {
            log.info("AiImageMaskServiceImpl sendImageMaskMq {} {}", handleCaseInfoParam.getCaseId(), ImageMaskBizEnum.CF_PROGRESS_IMAGE.getDesc());
            aiImageMaskService.sendImageMaskMqByBizId(handleCaseInfoParam.getCaseId(), ImageMaskBizEnum.CF_PROGRESS_IMAGE.getCode(), Long.valueOf(progress.getId()));
        }

    }

    /**
     * 保存公示审核动作
     */
    public void savePublicAuditAction(InitialAuditOperationMqVO handleCaseInfoParam) {

        // 只保存 二次审核工单 和 高风险工单 的数据
        if (handleCaseInfoParam.getOrderType() != WorkOrderType.highriskshenhe.getType()
                && handleCaseInfoParam.getOrderType() != WorkOrderType.ai_erci.getType()
                && handleCaseInfoParam.getOrderType() != WorkOrderType.yiliaoshenhe.getType()) {
            return;
        }

        // 只保存审核通过的
        int handleResult = handleCaseInfoParam.getCaseInitialAuditResult().getHandleResult();
        if (handleResult != HandleResultEnum.audit_pass.getType()) {
            return;
        }

        long operatorId = handleCaseInfoParam.getUserId();
        long workOrderId = handleCaseInfoParam.getWorkOrderId();

        // 如果是自动审核通过，审核人员花名取文章录入审核员
        if (handleCaseInfoParam.getSystemAutoAudit() == 1) {
            Response<List<WorkOrderVO>> response = workOrderClient.listByCaseIdAndTypeAndResult(handleCaseInfoParam.getCaseId(),
                    Lists.newArrayList(WorkOrderType.ai_content.getType()), Lists.newArrayList(HandleResultEnum.done.getType()));
            List<WorkOrderVO> workOrderVOS = Optional.ofNullable(response)
                    .filter(Response::ok)
                    .map(Response::getData)
                    .orElse(null);
            if (CollectionUtils.isEmpty(workOrderVOS)) {
                log.info("savePublicAuditAction workOrderVOS is null {}", handleCaseInfoParam.getWorkOrderId());
                return;
            }

            workOrderVOS = workOrderVOS.stream()
                    .sorted(Comparator.comparing(WorkOrderVO::getFinishTime).reversed()).collect(Collectors.toList());
            WorkOrderVO workOrderVO = workOrderVOS.get(0);
            operatorId = workOrderVO.getOperatorId();
            workOrderId = workOrderVO.getWorkOrderId();
        }

        // 取sea后台人员信息
        AuthRpcResponse<AdminUserAccountModel> response = seaAccountClientV1.getValidUserAccountById((int) operatorId);
        AdminUserAccountModel adminUserAccountModel = Optional.ofNullable(response)
                .filter(AuthRpcResponse::isSuccess)
                .map(AuthRpcResponse::getResult)
                .orElse(null);
        if (Objects.isNull(adminUserAccountModel)) {
            log.info("savePublicAuditAction adminUserAccountModel is null {} {} {}", handleCaseInfoParam.getCaseId(), handleCaseInfoParam.getWorkOrderId(), handleCaseInfoParam.getUserId());
            return;
        }

        CfPublicAuditAction cfPublicAuditAction = CfPublicAuditAction.builder()
                .caseId(handleCaseInfoParam.getCaseId())
                .operatorId(operatorId)
                .workOrderId(workOrderId)
                .operatorName(adminUserAccountModel.getName())
                .auditType(PublicAuditTypeEnum.RAISE_AUDIT.getCode())
                .build();
        log.info("savePublicAuditAction cfPublicAuditAction is {}", JSONObject.toJSONString(cfPublicAuditAction));

        // 公示审核信息入库
        cfPublicAuditFeignClient.savePublicAuditInfo(cfPublicAuditAction);

    }

    public void saveInitialAuditDisease(InitialAuditOperationMqVO handleCaseInfoParam) {

        if (Objects.isNull(handleCaseInfoParam)) {
            return;
        }
        List<CfMaterialAddOrUpdateVo> builds = Lists.newArrayList();

        // 初审疾病
        final List<String> diseaseNameList = getRaiseDiseaseNames(handleCaseInfoParam.getCaseId());
        if (CollectionUtils.isNotEmpty(diseaseNameList)) {
            // 初审疾病判断归一后可发疾病
            Map<String, String> raiseDiseaseMap = getDiseaseMap(diseaseNameList, handleCaseInfoParam.getCaseId());
            String raiseDiseaseNames = JSONObject.toJSONString(raiseDiseaseMap);
            CfMaterialAddOrUpdateVo raiseDiseases = CfMaterialAddOrUpdateVo
                    .builder()
                    .caseId(handleCaseInfoParam.getCaseId())
                    .materialName(MaterialExtKeyConst.RAISE_DISEASE_NAME_TAG)
                    .materialValue(raiseDiseaseNames)
                    .materialLabel("")
                    .materialExt("")
                    .build();
            if (!raiseDiseaseMap.isEmpty()) {
                builds.add(raiseDiseases);
            }
        }

        // 查待录入信息
        PreposeMaterialModel.MaterialInfoVo materialInfoVo = getMaterialInfoVo(handleCaseInfoParam.getCaseId());
        if (Objects.nonNull(materialInfoVo)) {
            // 解析待录入治疗方案
            Map<String, List<String>> treatmentInfoMap = TreatmentParse.parseTreatmentMap(materialInfoVo.getTreatmentInfo());
            String treatmentInfo = JSONObject.toJSONString(treatmentInfoMap);
            CfMaterialAddOrUpdateVo treatment = CfMaterialAddOrUpdateVo
                    .builder()
                    .caseId(handleCaseInfoParam.getCaseId())
                    .materialName(MaterialExtKeyConst.TREATMENT_INFO)
                    .materialValue(treatmentInfo)
                    .materialLabel("")
                    .materialExt("")
                    .build();
            if (!treatmentInfoMap.isEmpty()) {
                builds.add(treatment);
            }
        }

        // 保存材料
        RpcResult<String> stringRpcResult = cfMaterialWriteClient.addOrUpdateByFields(handleCaseInfoParam.getCaseId(), builds);
        log.info("saveInitialAuditDisease addOrUpdateByFields param:{} result:{}", builds, JSON.toJSONString(stringRpcResult));

    }

    public void patientMultiPlatformControl(InitialAuditOperationMqVO handleCaseInfoParam) {

        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(handleCaseInfoParam.getCaseId());

        // 只检测初审工单
        if (handleCaseInfoParam.getOrderType() != WorkOrderType.highriskshenhe.getType()
                && handleCaseInfoParam.getOrderType() != WorkOrderType.ai_erci.getType()
                && handleCaseInfoParam.getOrderType() != WorkOrderType.yiliaoshenhe.getType()) {
            log.info("patientMultiPlatformControl order type not ok {}", crowdfundingInfo.getId());
            return;
        }

        // 只检测审核通过的
        int handleResult = handleCaseInfoParam.getCaseInitialAuditResult().getHandleResult();
        if (handleResult != HandleResultEnum.audit_pass.getType()) {
            log.info("patientMultiPlatformControl handleResult not ok {}", crowdfundingInfo.getId());
            return;
        }

        // 取发起时录入的信息
        RpcResult<RaiseBasicInfoModel> modelResult = cfRaiseMaterialClient.selectRaiseBasicInfo(handleCaseInfoParam.getCaseId());
        RaiseBasicInfoModel raiseBasicInfoModel = Optional.ofNullable(modelResult)
                .filter(RpcResult::isSuccess)
                .map(RpcResult::getData)
                .orElse(null);
        if (Objects.isNull(raiseBasicInfoModel)) {
            log.info("patientMultiPlatformControl result not ok {}", crowdfundingInfo.getId());
            return;
        }
        CfFirsApproveMaterial material = riskDelegate.getCfFirsApproveMaterialByInfoId(crowdfundingInfo.getId());
        if (Objects.isNull(material)) {
            log.info("patientMultiPlatformControl is null {}", crowdfundingInfo.getId());
            return ;
        }

        String idCard = StringUtils.isBlank(material.getPatientCryptoIdcard()) ?
                material.getPatientCryptoIdcard() :
                shuidiCipher.decrypt(material.getPatientCryptoIdcard());

        // 取生日
        String birth = "";
        if (raiseBasicInfoModel.getRaisePatientIdType() == 1) {
            birth = AdminCfIdCardUtil.parsePatientBirth(shuidiCipher.decrypt(raiseBasicInfoModel.getRaisePatientIdCard()));
        }
        if (StringUtils.isBlank(birth)) {
            birth = AdminCfIdCardUtil.parsePatientBirth(idCard);
        }

        // 疾病
        List<String> diseaseNameList = List.of(Objects.requireNonNull(StringUtils.split(raiseBasicInfoModel.getDiseaseName(), ",，")));
        if (CollectionUtils.isEmpty(diseaseNameList)) {
            log.info("patientMultiPlatformControl diseaseNameList not ok {}", crowdfundingInfo.getId());
            return;
        }

        // 获取疾病范围
        Response<List<DiseaseAmountResultRecord>> response = diseaseClient.getAmountResultRecordByCaseId(crowdfundingInfo.getId());
        List<DiseaseAmountResultRecord> resultRecords = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Lists.newArrayList())
                .stream()
                .filter(record -> record.getWorkOrderId() == handleCaseInfoParam.getWorkOrderId())
                .filter(record -> record.getVersion() == 2)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(resultRecords)) {
            log.info("patientMultiPlatformControl resultRecords not ok {}", crowdfundingInfo.getId());
            return;
        }

        DiseaseAmountResultRecord record = resultRecords.get(resultRecords.size() - 1);

        PatientRaiseCheckModel patientRaiseCheckModel = PatientRaiseCheckModel.builder()
                .caseId(crowdfundingInfo.getId())
                .patientName(material.getPatientRealName())
                .birth(birth)
                .adviseMaxAmount(record.getAdviseMaxAmount())
                .amount(crowdfundingInfo.getAmount())
                .targetAmount(crowdfundingInfo.getTargetAmount())
                .createTime(crowdfundingInfo.getCreateTime())
                .diseaseName(diseaseNameList)
                .build();
        log.info("患者多平台发起管控 patientRaiseCheckModel {}", JSONObject.toJSONString(patientRaiseCheckModel));

        MessageResult result = producer.send(new Message<>(CfClientMQTopicCons.CF, CfClientMQTagCons.CF_MORE_PLATFORM_CHECK, CfClientMQTagCons.CF_MORE_PLATFORM_CHECK + handleCaseInfoParam.getWorkOrderId(), patientRaiseCheckModel));
        log.info("患者多平台发起管控 messageResult {}", result);

    }

    private Map<String, String> getDiseaseMap(List<String> diseaseNameList, Integer caseId) {

        // 疾病归一
        Response<List<DiseaseClassifyVOV2>> response = diseaseClassifyClientV2.diseaseNorm(diseaseNameList);
        List<DiseaseClassifyVOV2> diseaseClassifyVOV2List = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
        if (CollectionUtils.isEmpty(diseaseClassifyVOV2List)) {
            return Maps.newHashMap();
        }

        List<String> diseaseNormNames = diseaseClassifyVOV2List.stream()
                .map(DiseaseClassifyVOV2::getNorm)
                .filter(CollectionUtils::isNotEmpty)
                .map(norm -> norm.get(0))
                .collect(Collectors.toList());
        // 初审归一疾病打标
        if (CollectionUtils.isNotEmpty(diseaseNormNames)) {
            cfSearchClient.updateCfCase(Map.of(
                    EsCaseLabelColum.ID, caseId,
                    EsCaseLabelColum.FIRST_DISEASE_NORM_NAME, diseaseNormNames
            ));
        }

        Response<List<RiskDiseaseDataVO>> result = diseaseClient.getRiskDiseaseDataByClassNameList(diseaseNormNames);
        List<RiskDiseaseDataVO> riskDiseaseDataVOList = Optional.ofNullable(result)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
        if (CollectionUtils.isEmpty(riskDiseaseDataVOList)) {
            return Maps.newHashMap();
        }

        List<String> raiseDiseases = riskDiseaseDataVOList.stream()
                .filter(f -> f.getRaiseType() == 1 || f.getRaiseType() == 3)
                .map(RiskDiseaseDataVO::getDiseaseClassName)
                .collect(Collectors.toList());

        diseaseClassifyVOV2List = diseaseClassifyVOV2List.stream()
                // 过滤非空归一后疾病
                .filter(diseaseClassifyVOV2 -> CollectionUtils.isNotEmpty(diseaseClassifyVOV2.getNorm()))
                // 过滤可发疾病
                .filter(diseaseClassifyVOV2 -> raiseDiseases.contains(diseaseClassifyVOV2.getNorm().get(0)))
                .collect(Collectors.toList());

        if (diseaseClassifyVOV2List.size() > 3) {
            diseaseClassifyVOV2List = diseaseClassifyVOV2List.subList(0, 3);
        }

        // 拼装
        return diseaseClassifyVOV2List.stream()
                .collect(Collectors.toMap(diseaseClassifyVOV2 -> diseaseClassifyVOV2.getDisease().trim(),
                        diseaseClassifyVOV2 -> diseaseClassifyVOV2.getNorm().get(0).trim()));
    }

    private PreposeMaterialModel.MaterialInfoVo getMaterialInfoVo(Integer caseId) {
        CfFirsApproveMaterial material = riskDelegate.getCfFirsApproveMaterialByInfoId(caseId);
        if (Objects.isNull(material)) {
            log.info("saveInitialAuditDisease material is null {}", caseId);
            return null;
        }

        if (StringUtils.isBlank(material.getPatientCryptoIdcard()) && StringUtils.isBlank(material.getPatientBornCard())) {
            log.info("saveInitialAuditDisease idCard is null {}", caseId);
            return null;
        }

        String idCard = shuidiCipher.decrypt(material.getPatientCryptoIdcard());
        idCard = StringUtils.isNotEmpty(idCard) ? idCard : material.getPatientBornCard();
        if (StringUtils.isEmpty(idCard) || StringUtils.isEmpty(material.getPatientRealName())) {
            log.info("saveInitialAuditDisease info is null {}", caseId);
            return null;
        }

        RpcResult<PreposeMaterialModel.MaterialInfoVo> result = preposeMaterialClient.selectLatelyByIdCard(material.getPatientRealName(),
                idCard, BankCardVerifyEnum.UserIdentityType.IDENTITY.getCode());
        return Optional.ofNullable(result)
                .filter(RpcResult::isSuccess)
                .map(RpcResult::getData)
                .orElse(null);
    }

    private List<String> getRaiseDiseaseNames(Integer caseId) {
        RpcResult<RaiseBasicInfoModel> modelResult = cfRaiseMaterialClient.selectRaiseBasicInfo(caseId);
        RaiseBasicInfoModel raiseBasicInfoModel = Optional.ofNullable(modelResult)
                .filter(RpcResult::isSuccess)
                .map(RpcResult::getData)
                .orElse(null);
        if (Objects.isNull(raiseBasicInfoModel)) {
            log.info("saveInitialAuditDisease raiseBasicInfoModel is null {}", caseId);
            return Lists.newArrayList();
        }
        String diseaseNames = raiseBasicInfoModel.getDiseaseName();
        if (StringUtils.isEmpty(diseaseNames)) {
            log.info("saveInitialAuditDisease diseaseName is null {}", caseId);
            return Lists.newArrayList();
        }
        return List.of(Objects.requireNonNull(StringUtils.split(diseaseNames, ",，")));
    }

    public void saveInitialAuditSnapshot(InitialAuditOperationMqVO initialAuditOperationMqVO) {
        if (initialAuditOperationMqVO.getHandleType() == InitialAuditOperationItem.HandleTypeEnum.RETURN_VISIT.getCode()
                || initialAuditOperationMqVO.getHandleType() == InitialAuditOperationItem.HandleTypeEnum.DELAY_HANDLE.getCode()) {
            return;
        }
        InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam = new InitialAuditOperationItem.HandleCaseInfoParam();
        BeanUtils.copyProperties(initialAuditOperationMqVO, handleCaseInfoParam);
        InitialAuditCaseDetail initialAuditCaseDetail = initialAuditOperateService.prepareCaseDetailSnapshot(handleCaseInfoParam);
        if (Objects.isNull(initialAuditCaseDetail)) {
            log.info("CfInitialAuditHandleV2ConsumerService saveInitialAuditSnapshot initialAuditCaseDetail is null {}", handleCaseInfoParam);
            return;
        }
        // 前置信息通过，并且标记了特殊报备，并且之前没存过，将其存起来
        if (handleCaseInfoParam.getPassIds().contains(FIRST_APPROVE_TAG) && handleCaseInfoParam.isSpecialReport() && Objects.isNull(initialAuditCaseDetail.getFirstApproveCaseInfo().getSpecialReport())) {
            initialAuditCaseDetail.getFirstApproveCaseInfo().setSpecialReport(handleCaseInfoParam.isSpecialReport());
            CfMaterialAddOrUpdateVo build = CfMaterialAddOrUpdateVo
                    .builder()
                    .caseId(initialAuditOperationMqVO.getCaseId())
                    .materialName(MaterialExtKeyConst.first_approve_special_report)
                    .materialValue(String.valueOf(handleCaseInfoParam.isSpecialReport()))
                    .materialLabel("")
                    .materialExt("")
                    .build();
            RpcResult<String> stringRpcResult = cfMaterialWriteClient.addOrUpdateByFields(initialAuditOperationMqVO.getCaseId(), Collections.singletonList(build));
            log.info("CfInitialAuditHandleV2ConsumerService saveInitialAuditSnapshot addOrUpdateFirstApprove stringRpcResult : {}", stringRpcResult);
        }

        // 保存案例 三体案例 打标情况
        if (initialAuditOperationMqVO.getCaseInitialAuditResult().getHandleResult() == HandleResultEnum.audit_pass.getType()) {
            if (caseLabelSwitch) {
                cfCaseLabelService.bindingCaseLabel(initialAuditOperationMqVO.getCaseId());
            } else {
                cfThreeBodyService.saveThreeBodyCaseTab(initialAuditOperationMqVO.getCaseId());
            }
        }

        int caseId = handleCaseInfoParam.getCaseId();
        long workOrderId = handleCaseInfoParam.getWorkOrderId();
        workOrderExtService.save(caseId, workOrderId, WorkOrderExtContentTypeEnum.INITIAL_AUDIT_CASE_BASE_INFO,
                initialAuditCaseDetail.getCaseBaseInfo());
        workOrderExtService.save(caseId, workOrderId, WorkOrderExtContentTypeEnum.INITIAL_AUDIT_FIRST_APPROVE,
                initialAuditCaseDetail.getFirstApproveCaseInfo());
        workOrderExtService.save(caseId, workOrderId, WorkOrderExtContentTypeEnum.INITIAL_AUDIT_CREDIT_INFO,
                initialAuditCaseDetail.getCreditInfo());

        log.info("初审保存快照成功 saveInitialAuditSnapshot. initialAuditCaseDetail:{}, workOrderId:{}", initialAuditCaseDetail, workOrderId);
    }

    public void saveInitialAuditSnapshotV2(long workOrderId, int caseId) {
        InitialAuditCaseDetail caseDetail = initialAuditSearchService.queryCaseDetail(workOrderId, caseId);
        workOrderExtService.save(caseId, workOrderId, WorkOrderExtContentTypeEnum.INITIAL_AUDIT_CASE_BASE_INFO, caseDetail.getCaseBaseInfo());
        workOrderExtService.save(caseId, workOrderId, WorkOrderExtContentTypeEnum.INITIAL_AUDIT_FIRST_APPROVE, caseDetail.getFirstApproveCaseInfo());
        workOrderExtService.save(caseId, workOrderId, WorkOrderExtContentTypeEnum.INITIAL_AUDIT_CREDIT_INFO, caseDetail.getCreditInfo());
    }

}
