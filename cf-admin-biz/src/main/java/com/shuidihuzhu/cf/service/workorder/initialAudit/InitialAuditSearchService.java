package com.shuidihuzhu.cf.service.workorder.initialAudit;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.admin.constant.AdminWonActionConst;
import com.shuidihuzhu.cf.admin.river.impl.RiverDiBaoFacadeImpl;
import com.shuidihuzhu.cf.admin.river.impl.RiverPinKunFacadeImpl;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.biz.admin.common.MaskCodeOperationRecordBiz;
import com.shuidihuzhu.cf.biz.admin.impl.AdminCfInitialAuditCheckInfoRefService;
import com.shuidihuzhu.cf.biz.admin.impl.AdminCfInitialAuditCheckInfoService;
import com.shuidihuzhu.cf.biz.aiphoto.ImageWatermarkService;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAttachmentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAuthorBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfFirstApproveOperatorBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfMaterialVerityHistoryBiz;
import com.shuidihuzhu.cf.biz.mask.AdminViewMaskBiz;
import com.shuidihuzhu.cf.client.adminpure.enums.WorkOrderExtContentTypeEnum;
import com.shuidihuzhu.cf.client.base.enums.BaseErrorCodeEnum;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.feign.AuthorFeignClient;
import com.shuidihuzhu.cf.client.feign.CaseInfoApproveStageFeignClient;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.client.material.feign.CfMaterialReadClient;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.material.model.authenticity.AuthenticityIndicator;
import com.shuidihuzhu.cf.client.material.model.materialField.MaterialExtKeyConst;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecord;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecordClient;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCaseDetailsMsgDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CfAiMaterialsDao;
import com.shuidihuzhu.cf.delegate.PreposeMaterialDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.saas.AdminOrganization;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.OrganizationClientV1;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.domain.caseinfo.CaseInfoApproveStageDO;
import com.shuidihuzhu.cf.domain.cf.AdminCfInitialAuditCheckInfoDO;
import com.shuidihuzhu.cf.domain.cf.AdminCfInitialAuditCheckInfoRefDO;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.BackgroundLogEnum;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.MaskCodeOperationType;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveIdcardVerifyStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.enums.maskcode.MaskCodePageEnum;
import com.shuidihuzhu.cf.finance.client.feign.CfServiceChargeConfigFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.vo.AdminServiceChargeVo;
import com.shuidihuzhu.cf.model.admin.AdminMaskParam;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.model.admin.UserCommentVO;
import com.shuidihuzhu.cf.model.clew.CfUserInvitedLaunchCaseRecordVO;
import com.shuidihuzhu.cf.model.common.MaskCodeOperationRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CfMaterialVerityHistory;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterials;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterialsResult;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterialsVo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.CrowdfundingInitialAuditInfo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClient;
import com.shuidihuzhu.cf.risk.model.risk.DiseaseAmountResultRecord;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeConst;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeResult;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.approve.OCRApproveService;
import com.shuidihuzhu.cf.service.risk.highrisk.HighRiskService;
import com.shuidihuzhu.cf.service.workorder.WorkOrderExtService;
import com.shuidihuzhu.cf.store.model.AnalysisUrl;
import com.shuidihuzhu.cf.vo.admin.initialAudit.AdminCfInitialAuditCheckInfoVO;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationItem;
import com.shuidihuzhu.cf.vo.approve.CreditInfoVO;
import com.shuidihuzhu.cf.vo.approve.InitialAuditAdditionInfoVO;
import com.shuidihuzhu.cf.vo.approve.RiverDetailVO;
import com.shuidihuzhu.cf.vo.approve.RiverHandleParamVO;
import com.shuidihuzhu.cf.vo.approve.RiverHandleVO;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.admin.model.AdminCaseDetailsMsg;
import com.shuidihuzhu.client.cf.clewtrack.model.ChannelRefine;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolFeginClient;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CfCaseSpecialPrePoseDetail;
import com.shuidihuzhu.client.cf.risk.client.UserTagClient;
import com.shuidihuzhu.client.cf.risk.model.result.UserTagHistory;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.SecondWorkOrderTypeSourceEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class InitialAuditSearchService {

    @Resource
    private ICrowdfundingDelegate crowdfundingInfoBiz;
    @Autowired
    private IRiskDelegate firstApproveBiz;
    @Autowired
    private CfFirstApproveOperatorBiz firstApproveOperatorBiz;
    @Autowired
    private WorkOrderExtService workOrderSnapshotService;
    @Autowired
    private UserCommentBiz commentBiz;
    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;
    @Autowired
    private OrganizationClientV1 orgClientV1;
    @Autowired
    private InitialAuditRejectSettingsService rejectSettingsService;
    @Autowired
    private AdminCfInfoExtBiz cfInfoExtBiz;
    @Autowired
    private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired
    private AdminApproveService adminApproveService;
    @Autowired
    private MaskCodeOperationRecordBiz maskCodeOperationRecordBiz;

    @Autowired
    private CfMaterialReadClient cfMaterialReadClient;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private CaseInfoApproveStageFeignClient caseInfoApproveStageFeignClient;

    @Autowired
    private OCRApproveService ocrApproveService;

    @Autowired
    private DiseaseClient diseaseClient;

    @Autowired
    private ImageWatermarkService watermarkService;
    private final static Set<Integer> CAN_VERIFY_IDCARD_STATUS = Sets.newHashSet(

            FirstApproveIdcardVerifyStatusEnum.SYSTEM_ERROR.getCode(),
            FirstApproveIdcardVerifyStatusEnum.NOT_MATCH.getCode(),
            FirstApproveIdcardVerifyStatusEnum.NO_EXISTS.getCode()
    );
    @Resource
    private AdminCfInitialAuditCheckInfoService checkInfoService;
    @Resource
    private AdminCfInitialAuditCheckInfoRefService checkInfoRefService;

    @Resource
    private UserTagClient userTagClient;

    @Autowired
    private CfMaterialVerityHistoryBiz cfMaterialVerityHistoryBiz;

    @Autowired
    private PreposeMaterialDelegate preposeMaterialDelegate;

    @Autowired
    private RiverPinKunFacadeImpl riverPinKunFacade;

    @Autowired
    private RiverDiBaoFacadeImpl riverDiBaoFacade;

    @Autowired
    private InitialAuditBrainService auditBrainService;

    @Autowired
    private ClewPreproseMaterialFeignClient clewPreproseMaterialFeignClient;

    @Autowired
    private CfAiMaterialsDao cfAiMaterialsDao;

    @Autowired
    private CfWorkOrderClient workOrderClient;

    @Autowired
    private AdminCaseDetailsMsgDao adminCaseDetailsMsgDao;

    @Autowired
    private HighRiskService highRiskService;

    @Autowired
    private WonRecordClient wonRecordClient;

    @Autowired
    private AdminCrowdfundingAuthorBiz crowdfundingAuthorBiz;

    @Autowired
    private AuthorFeignClient authorFeignClient;

    @Resource
    private CfServiceChargeConfigFeignClient cfServiceChargeConfigFeignClient;

    @Resource
    private AdminCrowdfundingAttachmentBiz adminCrowdfundingAttachmentBiz;

    @Value("${apollo.admin.high-risk.v2.enable:true}")
    private boolean highRiskV2Enable;

    @Autowired
    private MaskUtil maskUtil;

    @Resource
    private AdminViewMaskBiz adminViewMaskBiz;

    @Resource
    private CfGrowthtoolFeginClient cfGrowthtoolFeginClient;

    /**
     * 查询案例收取服务费情况
     */
    public Response<Integer> queryCaseServiceCharge(int caseId) {

        // 获取案例服务费配置
        FeignResponse<AdminServiceChargeVo> response = cfServiceChargeConfigFeignClient.getByCaseId(caseId);
        AdminServiceChargeVo adminServiceChargeVo = (Objects.nonNull(response.getData()) && response.ok())
                ? response.getData() : null;
        if (Objects.isNull(adminServiceChargeVo)) {
            return NewResponseUtil.makeFail("获取不到该案例服务费配置");
        }

        log.info("InitialAuditSearchService queryCaseServiceCharge caseId:{} serverCharge:{}",
                caseId, adminServiceChargeVo.getServiceCharge());

        // adminServiceChargeVo.getServiceCharge() 的值对应的枚举类 CapitalServiceEnum
        return NewResponseUtil.makeSuccess(adminServiceChargeVo.getServiceCharge());
    }

    // 查询图文 和 前置审核的数据
    public InitialAuditCaseDetail queryCaseDetail(long workOrderId, int caseId) {

        InitialAuditCaseDetail caseDetail = new InitialAuditCaseDetail();
        CrowdfundingInfo cf = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(cf)) {
            return caseDetail;
        }

        InitialAuditCaseDetail.CaseBaseInfo baseInfoSnapshot = getBaseInfoSnapshot(workOrderId);
        InitialAuditCaseDetail.FirstApproveCaseInfo firstApproveSnapshot = getFirstApproveCaseInfoSnapshot(workOrderId);
        InitialAuditCaseDetail.CreditInfo creditInfo = getCreditInfoSnapshot(workOrderId);

        // 设置公约2.0 低保 贫困数据或快照数据
        RiverDetailVO<InitialAuditAdditionInfoVO> pinkunDetail = riverPinKunFacade.getDetail(caseId, workOrderId);
        RiverDetailVO<InitialAuditAdditionInfoVO> diBaoDetail = riverDiBaoFacade.getDetail(caseId, workOrderId);
        if (Objects.nonNull(diBaoDetail.getInfo())) {
            caseDetail.setAdditionInfo(diBaoDetail.getInfo().getDiBaoAndPinKunInfo());
            caseDetail.setDiBaoHandleParam(diBaoDetail.getHandleParam());
        }

        if (Objects.nonNull(pinkunDetail.getInfo())) {
            caseDetail.setPinKunHandleParam(pinkunDetail.getHandleParam());
        }

        if (baseInfoSnapshot == null) {
            baseInfoSnapshot = getBaseInfo(cf);
        }
        caseDetail.setCaseBaseInfo(baseInfoSnapshot);
        if (firstApproveSnapshot != null && checkCreateTime(firstApproveSnapshot)) {
            maskFirstApproveCaseInfo(cf, firstApproveSnapshot);
        }
        caseDetail.setFirstApproveCaseInfo(firstApproveSnapshot != null ? firstApproveSnapshot :
                getFirstApproveCaseInfo(cf));

        caseDetail.setTagInfo(acquireTagInfo(baseInfoSnapshot.getChannel()));


        // 兼容下以前的老数据
        fillBaseInfoExtField(cf, caseDetail.getCaseBaseInfo());

        // 前置报备信息
        getLastPrepost(caseId, caseDetail);

        // 增信信息
        caseDetail.setCreditInfo(Optional.ofNullable(creditInfo).orElseGet(() -> getInitialCreditInfo(caseId)));

        List<AdminCfInitialAuditCheckInfoVO> voList = getCheckInfo(caseId);
        caseDetail.setCheckInfo(voList);

        UserTagHistory userTagHistory = userTagClient.getOperatorValid(caseId);
        if (userTagHistory != null && CollectionUtils.isNotEmpty(userTagHistory.getUnitList())) {
            caseDetail.setUserTagHistoryUnits(userTagHistory.getUnitList());
        }
        ocrApproveService.fillResult(caseDetail.getFirstApproveCaseInfo(), caseId, workOrderId);
        auditBrainService.judgeNameConsistency(workOrderId, caseId, caseDetail);
        //写入是否待录入
        Response<List<CfCaseSpecialPrePoseDetail>> relationResponse = clewPreproseMaterialFeignClient.getSpecialPrePoseDetail(Lists.newArrayList(caseId));
        if (relationResponse.ok()) {
            caseDetail.setMaterialTypeInStatus(CollectionUtils.isNotEmpty(relationResponse
                    .getData().stream().filter(v -> v.getPreposeId() != 0).collect(Collectors.toList())));
            List<CfCaseSpecialPrePoseDetail> l = relationResponse.getData();
            if (CollectionUtils.isNotEmpty(l)) {
                caseDetail.setPrePostMobile(l.stream().filter(r -> StringUtils.isNotEmpty(r.getMobile())).map(CfCaseSpecialPrePoseDetail::getMobile).findAny().orElse(""));
            }
        }

        // 注入高风险信息
        injectHighRiskInfo(workOrderId, caseDetail);


        WorkOrderVO workOrderVO = getWorkOrderMsg(workOrderId);
        if (Objects.nonNull(workOrderVO)) {
            caseDetail.setOrderResult(workOrderVO.getHandleResult());
        }
        if (StringUtils.isNotBlank(caseDetail.getPrePostMobile())) {
            caseDetail.setPrePostMobileMask(maskUtil.buildByDecryptPhone(caseDetail.getPrePostMobile()));
            caseDetail.setPrePostMobile(null);
        }

        // 设置评估疾病花费枚举Code
        caseDetail.setEvaluatingTheCostOfIllnessCode(getEvaluatingTheCostOfIllnessCode(workOrderId));

        return caseDetail;
    }

    /**
     * 根据工单id去工单的扩展属性中查询评估疾病花费枚举Code
     */
    public List<Integer> getEvaluatingTheCostOfIllnessCode(long workOrderId) {
        Response<List<WorkOrderExt>> evaluatingTheCostOfIllness = workOrderClient.queryAllWorkExtIgnoreDelete(workOrderId, Collections.singletonList(OrderExtName.evaluatingTheCostOfIllness.getName()));
        // 默认为空List
        List<Integer> evaluatingTheCostOfIllnessCode = new ArrayList<>();
        if (evaluatingTheCostOfIllness != null && !CollectionUtils.isEmpty(evaluatingTheCostOfIllness.getData())) {
            // 如果有名字重复的，就保留最后一个值
            for (WorkOrderExt orderExt : evaluatingTheCostOfIllness.getData()) {
                // 如果额外字段的值不为空
                if (StringUtils.isNotBlank(orderExt.getExtValue())) {
                    String extValue = orderExt.getExtValue();
                    if ("".equals(extValue)) {
                        continue;
                    }
                    evaluatingTheCostOfIllnessCode = Arrays.stream(extValue.split(",")).map(Integer::parseInt).collect(Collectors.toList());
                }
            }
        }
        return evaluatingTheCostOfIllnessCode;
    }

    /**
     * 2024-02-27 00:00:00之前的工单，敏感信息查实时数据，不查快照。
     */
    private Boolean checkCreateTime(InitialAuditCaseDetail.FirstApproveCaseInfo firstApproveSnapshot) {
        Date createTime = firstApproveSnapshot.getCreateTime();
        if (Objects.isNull(createTime)) {
            return true;
        }

        // 创建一个SimpleDateFormat对象，用于解析和格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        try {
            // 设置比较的日期为2024-02-27 00:00:00
            Date comparisonDate = sdf.parse("2024-02-27 00:00:00");

            // 使用before方法判断dateToCheck是否在comparisonDate之前
            return createTime.before(comparisonDate);
        } catch (Exception e) {
            log.error("checkCreateTime error ", e);
        }

        return true;
    }

    private void maskFirstApproveCaseInfo(CrowdfundingInfo cf, InitialAuditCaseDetail.FirstApproveCaseInfo firstApproveCaseInfo) {
        if (firstApproveCaseInfo == null || cf == null) {
            return;
        }

        AdminMaskParam raiseIdCardParam = new AdminMaskParam();
        raiseIdCardParam.setCaseId(cf.getId());
        raiseIdCardParam.setInfoId(cf.getInfoId());

        String raiseIdCard = adminViewMaskBiz.viewRaiseMaskIdCard(raiseIdCardParam);
        firstApproveCaseInfo.setSelfIdCardMask(maskUtil.buildByDecryptStrAndType(raiseIdCard, DesensitizeEnum.IDCARD));
        firstApproveCaseInfo.setSelfIdCard(null);

        String patientIdCard = adminViewMaskBiz.viewPatientMaskIdCardV2(raiseIdCardParam);
        firstApproveCaseInfo.setPatientIdCardMask(maskUtil.buildByDecryptStrAndType(patientIdCard, DesensitizeEnum.IDCARD));
        if (Objects.nonNull(firstApproveCaseInfo.getPatientIdCardMask()) && StringUtils.isBlank(firstApproveCaseInfo.getPatientIdCardMask().getMaskNumber())) {
            firstApproveCaseInfo.getPatientIdCardMask().setMaskNumber(patientIdCard);
            firstApproveCaseInfo.setPatientBornCard(patientIdCard);
        }
        firstApproveCaseInfo.setPatientIdCard(null);
        // 身份证兼容
        if (firstApproveCaseInfo.getPatientBornCardMask() != null) {
            String encryptBornCard = firstApproveCaseInfo.getPatientBornCardMask().getEncryptNumber();
            if (StringUtils.isNotBlank(encryptBornCard)) {
                String bornCard = shuidiCipher.decrypt(encryptBornCard);
                firstApproveCaseInfo.setPatientBornCard(bornCard);
                firstApproveCaseInfo.getPatientBornCardMask().setMaskNumber(bornCard);
                return;
            }
        }
        firstApproveCaseInfo.setPatientBornCardMask(maskUtil.buildByDecryptStrAndType(firstApproveCaseInfo.getPatientBornCard(), DesensitizeEnum.IDCARD));
        if (firstApproveCaseInfo.getPatientBornCardMask() != null) {
            firstApproveCaseInfo.getPatientBornCardMask().setMaskNumber(firstApproveCaseInfo.getPatientBornCard());
        }
        firstApproveCaseInfo.setPatientBornCard(firstApproveCaseInfo.getPatientBornCard());
    }

    /**
     * 分别查询用户初审内容和报备内容的风险。结果做合并去重展示
     * 注入高风险信息
     *
     * @param workOrderId
     * @param caseDetail
     */
    private void injectHighRiskInfo(long workOrderId, InitialAuditCaseDetail caseDetail) {
        if (caseDetail == null) {
            return;
        }
        Response<WorkOrderVO> orderResp = workOrderClient.getWorkOrderById(workOrderId);
        if (orderResp == null || orderResp.notOk()) {
            return;
        }
        WorkOrderVO order = orderResp.getData();
        if (order == null) {
            return;
        }
        int orderType = order.getOrderType();
        // 仅高风险工单查询风险信息
        AuthenticityIndicator authenticityIndicator = Optional.ofNullable(caseDetail.getCreditInfo())
                .map(InitialAuditCaseDetail.CreditInfo::getInfo)
                .map(CreditInfoVO::getAuthenticityIndicator)
                .orElse(null);
        if (orderType != WorkOrderType.highriskshenhe.getType() && Objects.isNull(authenticityIndicator)) {
            return;
        }
        // 如果没有高风险新版标识则走老数据
        OperationResult<WonRecord> highRiskFlagRecordResp = wonRecordClient.getLastByBizId(caseDetail.getCaseBaseInfo().getCaseId(),
                AdminWonActionConst.HIGH_RISK_V2_FLAG);
        if (highRiskFlagRecordResp.isSuccess() && highRiskFlagRecordResp.getData() == null) {
            return;
        }
//        if (!highRiskV2Enable) {
//            return;
//        }
        CreditInfoVO creditInfoVO = Optional.of(caseDetail)
                .map(InitialAuditCaseDetail::getCreditInfo)
                .map(InitialAuditCaseDetail.CreditInfo::getInfo)
                .orElse(null);
        CreditInfoVO prepose = Optional.of(caseDetail)
                .map(InitialAuditCaseDetail::getPrepost)
                .orElse(null);

        List<String> riskTips = Lists.newArrayList();
        // 获取走策略后的案例最大花费
        int maxAmount = 0;
        Response<List<DiseaseAmountResultRecord>> amountResultRecordByCaseId = diseaseClient.getAmountResultRecordByCaseId(caseDetail.getCaseBaseInfo().getCaseId());
        List<DiseaseAmountResultRecord> diseaseAmountResultRecords = Optional.ofNullable(amountResultRecordByCaseId)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(diseaseAmountResultRecords)) {
            // 取diseaseAmountResultRecords最新一条，version=2的数据
            DiseaseAmountResultRecord latestRecord = diseaseAmountResultRecords.stream()
                    .filter(record -> record.getVersion() == 2)
                    .max(Comparator.comparing(DiseaseAmountResultRecord::getCreateTime))
                    .orElse(null);
            if (latestRecord != null) {
                maxAmount = latestRecord.getAdviseMaxAmount();
                log.info("获取走策略后的案例最大花费 maxAmount:{}", maxAmount);
            }
        }
        if (creditInfoVO != null) {
            creditInfoVO.setCaseId(caseDetail.getCaseBaseInfo().getCaseId());
            creditInfoVO.setMaxTreatmentCost(maxAmount);
            HighRiskJudgeResult result = highRiskService.judgeRisk(HighRiskJudgeConst.Source.CREDIT, workOrderId, creditInfoVO);
            creditInfoVO.setRiskLabels(result.getRiskLabels());
            creditInfoVO.setRisk(result.isHighRisk() ? 1 : 0);
            riskTips.addAll(CollectionUtils.isNotEmpty(result.getRiskTips()) ? result.getRiskTips() : Collections.emptyList());
        }
        if (prepose != null) {
            prepose.setCaseId(caseDetail.getCaseBaseInfo().getCaseId());
            prepose.setMaxTreatmentCost(maxAmount);
            HighRiskJudgeResult preposeResult = highRiskService.judgeRisk(HighRiskJudgeConst.Source.PREPOSE, workOrderId, prepose);
            prepose.setRiskLabels(preposeResult.getRiskLabels());
            prepose.setRisk(preposeResult.isHighRisk() ? 1 : 0);
            riskTips.addAll(CollectionUtils.isNotEmpty(preposeResult.getRiskTips()) ? preposeResult.getRiskTips() : Collections.emptyList());
        }
        // 提示要合并去重
        riskTips = riskTips.stream().distinct().collect(Collectors.toList());

        caseDetail.setRiskTips(mutexPrompt(riskTips));
        caseDetail.setUseHighRiskV2(true);
    }

    private List<String> mutexPrompt(List<String> riskTips) {
        if (CollectionUtils.isEmpty(riskTips) || CollectionUtils.size(riskTips) == 1) {
            return riskTips;
        }
        riskTips.remove("家庭经济情况初步系统预判较合理");
        return riskTips;
    }

    @NotNull
    public List<AdminCfInitialAuditCheckInfoVO> getCheckInfo(int caseId) {
        List<AdminCfInitialAuditCheckInfoVO> voList = Lists.newArrayList();
        List<AdminCfInitialAuditCheckInfoDO> checkInfoDOS = checkInfoService.listByCaseIdOrderByCheckType(caseId);
        if (CollectionUtils.isNotEmpty(checkInfoDOS)) {
            checkInfoDOS.forEach(infoDO -> voList.add(new AdminCfInitialAuditCheckInfoVO(infoDO)));
        }
        return voList;
    }

    public RiverHandleVO getCreditHandleInfoVO(int caseId) {
        RiverHandleVO v = new RiverHandleVO();
        CreditInfoVO creditInfoVO = getCreditInfoVO(caseId);

        if (creditInfoVO == null) {
            return null;
        }
        v.setSubmitTime(creditInfoVO.getLastUpdateTime());

        CrowdfundingInitialAuditInfo crowdfundingInitialAuditInfo = crowdfundingOperationDelegate.selectCrowdfundingInitialAuditInfoByCaseId(caseId);
        if (crowdfundingInitialAuditInfo == null) {
            return null;
        }

        int status = crowdfundingInitialAuditInfo.getCreditInfo();
        v.setStatus(status);

        CfMaterialVerityHistory.CfMaterialVerityHistoryVo lastDetail = cfMaterialVerityHistoryBiz.getLastDetail(caseId, InitialAuditItem.CREDIT_INFO, CfMaterialVerityHistory.REJECT_TYPE);
        v.setRejectReason(lastDetail.getRejectReason());

        // 驳回次数
        int rejectCount = cfMaterialVerityHistoryBiz.getCount(caseId, InitialAuditItem.CREDIT_INFO, CfMaterialVerityHistory.REJECT_TYPE);
        v.setRejectCount(rejectCount);

        v.setOperatorDetail(lastDetail.getOperatorDetail());

        // 增信是否通过
        boolean passed = status == InitialAuditItem.MaterialStatus.PASS.getCode();
        Date operationTime;
        if (passed) {
            operationTime = crowdfundingInitialAuditInfo.getUpdateTime();
        } else {
            operationTime = DateUtil.getDateFromLongString(lastDetail.getOperatorTime());
        }
        v.setOperationTime(operationTime);
        return v;
    }

    public CreditInfoVO getLastPrepose(int caseId) {
        PreposeMaterialModel.MaterialInfoVo i = preposeMaterialDelegate.queryByCaseId(caseId);
        if (i == null) {
            return null;
        }
        return CreditInfoVO.build(i);
    }


    public void getLastPrepost(int caseId, InitialAuditCaseDetail caseDetail) {
        Response<PreposeMaterialModel.MaterialInfoVo> materialInfoVoResponse = clewPreproseMaterialFeignClient.selectMaterialByCaseIdForQC(caseId);
        if (Objects.isNull(materialInfoVoResponse) || Objects.isNull(materialInfoVoResponse.getData())) {
            return;
        }
        PreposeMaterialModel.MaterialInfoVo data = materialInfoVoResponse.getData();
        InitialAuditCaseDetail.CaseBaseInfo caseBaseInfo = caseDetail.getCaseBaseInfo();

        Map<Integer, PreposeMaterialModel.BigAmountReasonEnum> bigAmountReasonMap = PreposeMaterialModel.bigAmountReasonMap;

        caseBaseInfo.setPrivacyImage(data.getBigAmountPic());
        if (Objects.nonNull(data.getBigAmountReason())) {
            PreposeMaterialModel.BigAmountReasonEnum bigAmountReasonEnum = bigAmountReasonMap.get(data.getBigAmountReason());
            caseBaseInfo.setTargetAmountBiggerReason(Objects.nonNull(bigAmountReasonEnum) && bigAmountReasonEnum != PreposeMaterialModel.BigAmountReasonEnum.reason_7 ? bigAmountReasonEnum.getDesc() : data.getBigAmountOtherReason());
        }

        caseDetail.setCaseBaseInfo(caseBaseInfo);

        // 设置前置报备信息
        caseDetail.setPrepost(CreditInfoVO.build(data));
        caseDetail.setPrePatientIdCard(data.getPatientIdCardType() == PreposeMaterialModel.PatientIdCardTypeEnum.IDENTITY_CARD.getCode() ? data.getPatientIdCard() : "");
        caseDetail.setPreSelfIdCard(shuidiCipher.decrypt(data.getSelfCryptoIdcard()));

    }

    /**
     * 获取初审详情的增信信息 若非初审审核的增信 不展示增信模块
     *
     * @param caseId
     * @return
     */
    public InitialAuditCaseDetail.CreditInfo getInitialCreditInfo(int caseId) {
        CrowdfundingInitialAuditInfo info = crowdfundingOperationDelegate.selectCrowdfundingInitialAuditInfoByCaseId(caseId);
        if (info == null || info.getCreditInfo() == InitialAuditItem.MaterialStatus.DEFAULT.getCode()) {
            return null;
        }
        return getCreditInfo(caseId);
    }

    private InitialAuditCaseDetail.CreditInfo getCreditInfo(int caseId) {
        InitialAuditCaseDetail.CreditInfo v = new InitialAuditCaseDetail.CreditInfo();
        CreditInfoVO creditInfoVO = getCreditInfoVO(caseId);
        v.setInfo(creditInfoVO);
        return v;
    }

    public CreditInfoVO getCreditInfoVO(int caseId) {
        RpcResult<CfPropertyInsuranceInfoModel> resp = cfMaterialReadClient.selectCfPropertyInsuranceInfo(caseId);
        //只有接口返回空  或者 fallback的时候  才需要刷新
        if (resp == null || resp.getCode() == BaseErrorCodeEnum.FALL_BACK.getCode()) {
            throw new RuntimeException("增信信息请求失败,请刷新后重试");
        }
        return Optional.ofNullable(resp)
                .filter(RpcResult::isSuccess)
                .map(RpcResult::getData)
                .map(CreditInfoVO::build)
                .orElse(null);
    }

    public AdminCfInitialAuditCheckInfoVO updateCheckInfo(int caseId, int checkType,
                                                          String checkName) {
        // 验真
        int checkResult = this.checkInfo(checkName) ? 1 : 0;
        // 保存验真结果
        AdminCfInitialAuditCheckInfoDO checkInfoDO = checkInfoService.getByCaseIdAndCheckType(caseId, checkType);
        if (checkInfoDO != null) {
            checkInfoService.update(checkInfoDO.getId(), checkName, checkResult);
        } else {
            checkInfoService.add(caseId, checkType, checkName, checkResult);
        }
        return new AdminCfInitialAuditCheckInfoVO(checkType, checkName, checkResult);
    }

    public boolean checkInfo(String checkName) {
        AdminCfInitialAuditCheckInfoRefDO refDO = checkInfoRefService.getByHospitalName(checkName);
        return refDO != null;
    }

    public void addOperationRecord(int caseId, long workOrderId, String operateDesc) {
        UserComment comment = new UserComment();
        comment.setOperatorId(ContextUtil.getAdminUserId());
        comment.setCaseId(caseId);
        comment.setCommentSource(UserCommentSourceEnum.INITIAL_AUDIT.getCode());
        comment.setCommentType(UserCommentSourceEnum.CommentType.CHECK_INFO.getCode());
        comment.setOperateMode(UserCommentSourceEnum.CommentType.CHECK_INFO.getDesc());
        comment.setWorkOrderId(workOrderId);
        comment.setComment("");
        comment.setOperateDesc(operateDesc);
        commentBiz.insert(comment);
    }

    public InitialAuditCaseDetail.CaseBaseInfo getBaseInfo(CrowdfundingInfo cf) {


        Response<CaseInfoApproveStageDO> stageInfoResp = caseInfoApproveStageFeignClient.getStageInfo(cf.getId());
        CaseInfoApproveStageDO stageInfo = stageInfoResp.getData();

        InitialAuditCaseDetail.CaseBaseInfo caseBaseInfo = new InitialAuditCaseDetail.CaseBaseInfo();
        caseBaseInfo.setTargetAmount(cf.getTargetAmount());

        List<CrowdfundingAttachment> attachmentList = adminCrowdfundingAttachmentBiz.queryAttachment(cf.getId());

        if (stageInfoResp.ok()) {
            if (stageInfo != null) {
                // 有暂存图文
                caseBaseInfo.setTitle(stageInfo.getTitle());
                caseBaseInfo.setContent(stageInfo.getContent());
                List<CrowdfundingAttachmentVo> attachments = Arrays.stream(StringUtils.split(stageInfo.getImages(), ","))
                        .map(v -> {
                            AnalysisUrl analysisUrl = AnalysisUrl.parse(v);
                            Integer id = attachmentList.stream()
                                    .filter(f -> f.getUrl().contains(analysisUrl.getPath()))
                                    .map(CrowdfundingAttachment::getId)
                                    .findFirst()
                                    .orElse(0);
                            CrowdfundingAttachmentVo o = new CrowdfundingAttachmentVo();
                            o.setType(AttachmentTypeEnum.ATTACH_CF.value());
                            o.setUrl(v);
                            o.setId(id);
                            return o;
                        }).collect(Collectors.toList());
                caseBaseInfo.setAttachments(attachments);
            } else {
                List<CrowdfundingAttachmentVo> attachmentVos = crowdfundingInfoBiz.getAttachmentsByType(cf.getId(),
                        AttachmentTypeEnum.ATTACH_CF);
                caseBaseInfo.setTitle(cf.getTitle());
                caseBaseInfo.setContent(cf.getContent());
                caseBaseInfo.setAttachments(CollectionUtils.isNotEmpty(attachmentVos) ? attachmentVos : Lists.newArrayList());
            }
        }

        fillBaseInfoExtField(cf, caseBaseInfo);
        watermarkService.fillCaseBaseWaterMark(cf.getId(), caseBaseInfo);


        return caseBaseInfo;
    }

    private void fillBaseInfoExtField(CrowdfundingInfo cf, InitialAuditCaseDetail.CaseBaseInfo caseBaseInfo) {
        // 新增展示字段
        caseBaseInfo.setCaseId(cf.getId());
        caseBaseInfo.setInfoUUid(cf.getInfoId());

        UserInfoModel userInfoModel = getUserInfoByUserId(cf.getUserId());
        if (userInfoModel != null && StringUtils.isNotBlank(userInfoModel.getCryptoMobile())) {
            caseBaseInfo.setMobile(shuidiCipher.decrypt(userInfoModel.getCryptoMobile()));
        } else {
            log.warn("不能找到案例的发起的手机号 caseId：{}, userId:{}", cf.getId(), cf.getUserId());
        }

        // 渠道
        Optional<CfUserInvitedLaunchCaseRecordVO> optional = adminApproveService.getCaseChannelRecordVO(cf.getId());
        if (!optional.isEmpty()) {
            caseBaseInfo.setRecordVO(optional.get());
            caseBaseInfo.setChannel(optional.get().getServiceUserInfo(shuidiCipher));
        }
        caseBaseInfo.setMobileMask(maskUtil.buildByDecryptPhone(caseBaseInfo.getMobile()));
        caseBaseInfo.setMobile(null);
    }

    private InitialAuditCaseDetail.CaseBaseInfo getBaseInfoSnapshot(long workOrderId) {
        return workOrderSnapshotService.getByClazz(workOrderId,
                WorkOrderExtContentTypeEnum.INITIAL_AUDIT_CASE_BASE_INFO, InitialAuditCaseDetail.CaseBaseInfo.class);
    }

    public InitialAuditCaseDetail.FirstApproveCaseInfo getFirstApproveCaseInfo(CrowdfundingInfo cf) {
        InitialAuditCaseDetail.FirstApproveCaseInfo caseInfo = new InitialAuditCaseDetail.FirstApproveCaseInfo();
        CfFirsApproveMaterial material = firstApproveBiz.getCfFirsApproveMaterialByInfoId(cf.getId());

        if (material == null) {
            log.error("初次审核不能找到前置信息:{}", cf.getId());
            return caseInfo;
        }
        caseInfo.setSelfRealName(material.getSelfRealName());
        caseInfo.setSelfIdCardMask(maskUtil.buildByEncryptStrAndType(material.getSelfCryptoIdcard(), DesensitizeEnum.IDCARD));
        caseInfo.setSelfIdCard(null);
        caseInfo.setUserRelationType(material.getUserRelationType());
        caseInfo.setChild(firstApproveOperatorBiz.isChild(material) ? 1 : 0);
        caseInfo.setPatientRealName(material.getPatientRealName());

        caseInfo.setPatientIdCardMask(maskUtil.buildByEncryptStrAndType(material.getPatientCryptoIdcard(), DesensitizeEnum.IDCARD));
        if (caseInfo.getPatientIdCardMask() != null) {
            if (StringUtils.isBlank(caseInfo.getPatientIdCardMask().getMaskNumber())) {
                caseInfo.getPatientIdCardMask().setMaskNumber(shuidiCipher.decrypt(material.getPatientCryptoIdcard()));
                caseInfo.setPatientBornCard(shuidiCipher.decrypt(material.getPatientCryptoIdcard()));
            }
        }
        caseInfo.setPatientIdCard(null);
        caseInfo.setTargetAmount(cf.getTargetAmount());
        caseInfo.setImageUrl(material.getImageUrl());

        caseInfo.setPoverty(material.getPoverty());
        caseInfo.setPovertyImageUrl(material.getPovertyImageUrl());
        caseInfo.setPatientBornCardMask(maskUtil.buildByDecryptStrAndType(material.getPatientBornCard(), DesensitizeEnum.IDCARD));
        if (caseInfo.getPatientBornCardMask() != null) {
            caseInfo.getPatientBornCardMask().setMaskNumber(material.getPatientBornCard());
        }
        caseInfo.setPatientBornCard(material.getPatientBornCard());
        caseInfo.setPatientIdType(material.getPatientIdType());
        caseInfo.setUserRelationTypeForC(material.getUserRelationTypeForC());
        caseInfo.setTargetAmountDesc(material.getTargetAmountDesc());

        caseInfo.setFirstApproveIdcardVerifyStatus(material.getStatus());
        caseInfo.setImageUrlType(material.getImageUrlType());

        //补充信息 是否展示标签 0：展示 1：false
        AdminCaseDetailsMsg detailsMsg = adminCaseDetailsMsgDao.getByCaseId(cf.getId());
        caseInfo.setShowTag(detailsMsg == null ? 0 : detailsMsg.getShowTag());

        caseInfo.setSpecialPrePoseDetail(preposeMaterialDelegate.getSpecialPrePoseDetailByCaseId(cf.getId()));

        RpcResult<Map<String, List<String>>> mapRpcResult = cfMaterialReadClient.selectValueByFields(cf.getId(), Collections.singletonList(MaterialExtKeyConst.first_approve_special_report));
        if (Objects.nonNull(mapRpcResult) && mapRpcResult.isSuccess() && MapUtils.isNotEmpty(mapRpcResult.getData())) {
            Boolean aBoolean = mapRpcResult.getData()
                    .get(MaterialExtKeyConst.first_approve_special_report)
                    .stream()
                    .map(Boolean::valueOf)
                    .findFirst()
                    .orElse(null);
            caseInfo.setSpecialReport(aBoolean);
        }

        watermarkService.fillFirstMedicalWaterMark(cf.getId(), caseInfo);
        return caseInfo;
    }

    private InitialAuditCaseDetail.FirstApproveCaseInfo getFirstApproveCaseInfoSnapshot(long workOrderId) {

        com.shuidihuzhu.cf.domain.cf.WorkOrderExt ext = workOrderSnapshotService.getWorkOrderExt(workOrderId, WorkOrderExtContentTypeEnum.INITIAL_AUDIT_FIRST_APPROVE);
        if (ext == null) {
            return null;
        }
        String content = ext.getContent();
        InitialAuditCaseDetail.FirstApproveCaseInfo firstApproveCaseInfo = JSON.parseObject(content, InitialAuditCaseDetail.FirstApproveCaseInfo.class);
        firstApproveCaseInfo.setCreateTime(ext.getCreateTime());
        return firstApproveCaseInfo;
    }

    private InitialAuditCaseDetail.CreditInfo getCreditInfoSnapshot(long workOrderId) {
        return workOrderSnapshotService.getByClazz(workOrderId,
                WorkOrderExtContentTypeEnum.INITIAL_AUDIT_CREDIT_INFO, InitialAuditCaseDetail.CreditInfo.class);
    }

    // 查询图文和前置驳回项
    public InitialAuditOperationItem.RejectOptionSet queryRejectList(int caseId, long workOrderId) {
        InitialAuditOperationItem.RejectOptionSet rejectSet = new InitialAuditOperationItem.RejectOptionSet();

        rejectSet.setBaseInfoRejectOption(rejectSettingsService.queryDataTypeList(InitialAuditOperateService.BASE_INFO_TAG));
        rejectSet.setCreditInfoRejectOption(rejectSettingsService.queryDataTypeList(InitialAuditOperateService.CREDIT_TAG));
        rejectSet.setPinKunRejectOption(rejectSettingsService.queryDataTypeList(InitialAuditOperateService.PIN_KUN_TAG));
        rejectSet.setDiBaoRejectOption(rejectSettingsService.queryDataTypeList(InitialAuditOperateService.DI_BAO_TAG));

        List<CfRefuseReasonTag> firstApproveTags = rejectSettingsService.queryDataTypeList(InitialAuditOperateService.FIRST_APPROVE_TAG);
        Iterator<CfRefuseReasonTag> reasonTagIterator = firstApproveTags.iterator();
        while (reasonTagIterator.hasNext()) {
            CfRefuseReasonTag curr = reasonTagIterator.next();
            if ("停止筹款".equals(curr.getDescribe())) {
                rejectSet.setEndCaseRejectOption(Lists.newArrayList(curr));
                reasonTagIterator.remove();
                break;
            }
        }
        rejectSet.setFirstApproveRejectOption(firstApproveTags);
        fillPassAndRejectIds(rejectSet, caseId, workOrderId);

        return rejectSet;
    }

    // 填充案例通过和驳回的情况
    private void fillPassAndRejectIds(InitialAuditOperationItem.RejectOptionSet rejectSet, int caseId, long workOrderId) {

        rejectSet.setPassIds(Lists.newArrayList());
        rejectSet.setRejectIds(Lists.newArrayList());

        boolean findInRealTime = true;
        if (workOrderId != 0) {
            // 取快照
            InitialAuditCaseDetail.CaseBaseInfo baseInfoSnapshot = getBaseInfoSnapshot(workOrderId);
            InitialAuditCaseDetail.FirstApproveCaseInfo firstApproveSnapshot = getFirstApproveCaseInfoSnapshot(workOrderId);
            InitialAuditCaseDetail.CreditInfo creditInfoSnapshot = getCreditInfoSnapshot(workOrderId);

            if (baseInfoSnapshot != null) {
                if (baseInfoSnapshot.getPass() == 1) {
                    rejectSet.getPassIds().add(InitialAuditOperateService.BASE_INFO_TAG);
                }
                if (CollectionUtils.isNotEmpty(baseInfoSnapshot.getRejectIds())) {
                    rejectSet.getRejectIds().addAll(baseInfoSnapshot.getRejectIds());
                }
            }

            if (firstApproveSnapshot != null) {
                if (firstApproveSnapshot.getPass() == 1) {
                    rejectSet.getPassIds().add(InitialAuditOperateService.FIRST_APPROVE_TAG);
                }

                if (CollectionUtils.isNotEmpty(firstApproveSnapshot.getRejectIds())) {
                    rejectSet.getRejectIds().addAll(firstApproveSnapshot.getRejectIds());
                }
            }

            if (creditInfoSnapshot != null) {
                if (creditInfoSnapshot.getPass() == 1) {
                    rejectSet.getPassIds().add(InitialAuditOperateService.CREDIT_TAG);
                }

                if (CollectionUtils.isNotEmpty(creditInfoSnapshot.getRejectIds())) {
                    rejectSet.getRejectIds().addAll(creditInfoSnapshot.getRejectIds());
                }
            }

            if (baseInfoSnapshot != null && firstApproveSnapshot != null) {
                findInRealTime = false;
            }
        }

        if (findInRealTime) {
            log.info("查询实时的状态：caseId:{}, workorderId:{}", caseId, workOrderId);
            fillPassAndRejectIdsByRealTime(rejectSet, caseId);
        }

        // 设置公约2.0 低保 贫困数据或快照数据
        RiverDetailVO<InitialAuditAdditionInfoVO> pinkunDetail = riverPinKunFacade.getDetail(caseId, workOrderId);
        RiverDetailVO<InitialAuditAdditionInfoVO> diBaoDetail = riverDiBaoFacade.getDetail(caseId, workOrderId);
        if (pinkunDetail.getInfo() != null && diBaoDetail.getInfo() != null) {
            RiverHandleParamVO pinkunDetailHandleParam = pinkunDetail.getHandleParam();
            RiverHandleParamVO diBaoDetailHandleParam = diBaoDetail.getHandleParam();
            if (pinkunDetailHandleParam != null) {
                CollectionUtils.addAll(rejectSet.getRejectIds(), pinkunDetail.getHandleParam().getRejectIds());
                if (pinkunDetailHandleParam.getHandleType() == RiverHandleParamVO.HandleType.PASS) {
                    CollectionUtils.addIgnoreNull(rejectSet.getPassIds(), CrowdfundingInfoDataStatusTypeEnum.PIN_KUN_HU.getCode());
                }
            } else {
                if (pinkunDetail.isPassed()) {
                    CollectionUtils.addIgnoreNull(rejectSet.getPassIds(), CrowdfundingInfoDataStatusTypeEnum.PIN_KUN_HU.getCode());
                }
                CollectionUtils.addAll(rejectSet.getRejectIds(), pinkunDetail.getRejectIds());
            }
            if (diBaoDetailHandleParam != null) {
                CollectionUtils.addAll(rejectSet.getRejectIds(), diBaoDetail.getHandleParam().getRejectIds());
                if (diBaoDetailHandleParam.getHandleType() == RiverHandleParamVO.HandleType.PASS) {
                    CollectionUtils.addIgnoreNull(rejectSet.getPassIds(), CrowdfundingInfoDataStatusTypeEnum.DI_BAO.getCode());
                }
            } else {
                if (diBaoDetail.isPassed()) {
                    CollectionUtils.addIgnoreNull(rejectSet.getPassIds(), CrowdfundingInfoDataStatusTypeEnum.DI_BAO.getCode());
                }
                CollectionUtils.addAll(rejectSet.getRejectIds(), diBaoDetail.getRejectIds());
            }
        }
    }

    private void fillPassAndRejectIdsByRealTime(InitialAuditOperationItem.RejectOptionSet rejectSet, int caseId) {

        rejectSet.setPassIds(Lists.newArrayList());
        rejectSet.setRejectIds(Lists.newArrayList());

        CrowdfundingInitialAuditInfo auditInfo = crowdfundingOperationDelegate.selectCrowdfundingInitialAuditInfoByCaseId(caseId);
        if (auditInfo != null) {
            if (auditInfo.getBaseInfo() == InitialAuditItem.MaterialStatus.PASS.getCode()) {
                rejectSet.getPassIds().add(InitialAuditOperateService.BASE_INFO_TAG);
            }

            if (auditInfo.getFirstApproveInfo() == InitialAuditItem.MaterialStatus.PASS.getCode()) {
                rejectSet.getPassIds().add(InitialAuditOperateService.FIRST_APPROVE_TAG);
            }

            if (auditInfo.getCreditInfo() == InitialAuditItem.MaterialStatus.PASS.getCode()) {
                rejectSet.getPassIds().add(InitialAuditOperateService.CREDIT_TAG);
            }

            InitialAuditItem.RejectReasonSet reasonSet = crowdfundingOperationDelegate.parseRejectDetail(auditInfo.getRejectDetail(), caseId);
            List<Integer> rejectIds = Lists.newArrayList();
            for (List<Integer> ids : reasonSet.getRejectIds().values()) {
                rejectIds.addAll(ids);
            }
            rejectSet.setRejectIds(rejectIds);
        }
    }


    // 查询图文和前置的操作记录
    public Map<String, Object> queryInitialAuditOperationHistory(int caseId, int current, int pageSize) {
        List<UserCommentVO> commentVoList = Lists.newArrayList();

        Map<String, Object> result = Maps.newHashMap();

        result.put("total", commentBiz.countByCommentSoure(caseId, UserCommentSourceEnum.INITIAL_AUDIT.getCode()));
        List<UserComment> commentList = commentBiz.getUserCommentDescByCommentSource(caseId, UserCommentSourceEnum.INITIAL_AUDIT.getCode(),
                (current - 1) * pageSize, pageSize);

        for (UserComment comment : commentList) {
            commentVoList.add(generateCommentVo(comment));
        }
        result.put("list", commentVoList);
        return result;
    }

    private UserCommentVO generateCommentVo(UserComment comment) {
        UserCommentVO commentVO = new UserCommentVO(comment);

        commentVO.setCommentTypeStr(UserCommentSourceEnum.CommentType.getCommetTypefromCode(commentVO.getCommentType()).getDesc());
        commentVO.setCreateTime(DateFormatUtils.format(comment.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
        commentVO.setOperatorName(comment.getOperatorId() != 0 ? queryOperatorName((int) comment.getOperatorId()) : "系统操作");
        return commentVO;
    }

    public String queryOperatorName(int operatorId) {
        AuthRpcResponse<Map<Integer, List<AdminOrganization>>> userOrgs = orgClientV1
                .getUserOrgs(Lists.newArrayList(operatorId));

        List<String> orgLevelName = Lists.newArrayList();
        if (userOrgs != null && userOrgs.getResult() != null
                && userOrgs.getResult().get(operatorId) != null) {

            List<AdminOrganization> adminOrgs = userOrgs.getResult().get(operatorId);
            for (AdminOrganization org : adminOrgs) {
                orgLevelName.add(org.getName());
            }
        } else {
            log.warn("当前操作人不能找到组织. userId:{}, result:{}", operatorId, userOrgs);
        }

        AuthRpcResponse<String> misName = seaAccountClientV1.getMisByUserId(operatorId);
        if (misName != null && StringUtils.isNotBlank(misName.getResult())) {
            orgLevelName.add(misName.getResult());
        } else {
            log.warn("当前操作人不能找到姓名. userId:{}, result:{}", operatorId, misName);
        }

        return Joiner.on("-").join(orgLevelName);
    }

    public boolean initialPass(String infoUuid) {
        CfInfoExt cfInfoExt = cfInfoExtBiz.getByInfoUuid(infoUuid);

        return cfInfoExt == null || cfInfoExt.getFirstApproveStatus() == FirstApproveStatusEnum.DEFAULT.getCode()
                || cfInfoExt.getFirstApproveStatus() == FirstApproveStatusEnum.APPLY_SUCCESS.getCode();
    }

    public Response<Integer> verifyIdCard(int caseId, int userId, long workOrderId) {

        CfFirsApproveMaterial firsApproveMaterial = firstApproveBiz.getCfFirsApproveMaterialByInfoId(caseId);
        if (firsApproveMaterial == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NO_PRE_APPROVE_MATERIAL);
        }

        if (!CAN_VERIFY_IDCARD_STATUS.contains(firsApproveMaterial.getStatus())) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NO_NEED_VERIFY);
        }

        FirstApproveIdcardVerifyStatusEnum resultEnum = getInitialVerifyResult(firsApproveMaterial);

        // 添加ugc的评论
        UserComment comment = new UserComment();
        comment.setOperatorId(userId);
        comment.setCaseId(caseId);
        comment.setCommentSource(UserCommentSourceEnum.INITIAL_AUDIT.getCode());
        comment.setCommentType(UserCommentSourceEnum.CommentType.INITIAL_SENCOND_VERIFY_IDCARD.getCode());
        comment.setOperateMode(UserCommentSourceEnum.CommentType.INITIAL_SENCOND_VERIFY_IDCARD.getDesc());
        comment.setWorkOrderId(workOrderId);
        comment.setComment("");
        comment.setOperateDesc(resultEnum.getDesc());
        commentBiz.insert(comment);

        // 详情页的备注
        adminApproveService.addComment(firsApproveMaterial.getInfoUuid(), "二次身份校验: " + comment.getOperateDesc(),
                userId, "初次审核【工单id:" + workOrderId + "】", BackgroundLogEnum.INITIAL_AUDIT_HANDLE);

        return NewResponseUtil.makeSuccess(resultEnum.getCode());
    }

    private FirstApproveIdcardVerifyStatusEnum getInitialVerifyResult(CfFirsApproveMaterial firsApproveMaterial) {
        String patientIdCard = "";
        if (StringUtils.isNotEmpty(firsApproveMaterial.getPatientCryptoIdcard())) {
            patientIdCard = shuidiCipher.decrypt(firsApproveMaterial.getPatientCryptoIdcard());
        }

        // 初审 这里不校验发起人的身份证 https://wiki.shuiditech.com/pages/viewpage.action?pageId=251005100
        CfErrorCode verifyIdcardResult = firstApproveBiz.verifyIdcard("", "",
                firsApproveMaterial.getPatientRealName(), patientIdCard, firsApproveMaterial.getPatientIdType(),
                UserRelTypeEnum.SELF, firsApproveMaterial.getUserId());
        log.info("初审校验结果 caseId:{} PatientRealName:{} patientIdCard:{} result:{}",
                firsApproveMaterial.getInfoId(), firsApproveMaterial.getPatientRealName(), patientIdCard, verifyIdcardResult);

        FirstApproveIdcardVerifyStatusEnum resultEnum = FirstApproveIdcardVerifyStatusEnum.NOT_MATCH;
        if (verifyIdcardResult == CfErrorCode.ADD_CROWDFUNDING_VERIFY_SUCCESS_IN_WHITELIST) {
            verifyIdcardResult = CfErrorCode.SUCCESS;
        }
        if (verifyIdcardResult == CfErrorCode.SUCCESS ||
                verifyIdcardResult == CfErrorCode.ADD_CROWDFUNDING_NEED_CHANGE_ID_CARD) {
            log.info("更新案例的前置身份证信息.caseId:{}, status:{}", firsApproveMaterial.getInfoId(), verifyIdcardResult);
            resultEnum = verifyIdcardResult == CfErrorCode.SUCCESS ? FirstApproveIdcardVerifyStatusEnum.MATCH :
                    FirstApproveIdcardVerifyStatusEnum.NO_EXISTS;
            firstApproveBiz.updateStatusByCaseId(firsApproveMaterial.getInfoId(), resultEnum);
        }

        return resultEnum;
    }


    public InitialAuditCaseDetail queryInitialAuditInfoRealTime(int caseId) {

        InitialAuditCaseDetail caseDetail = new InitialAuditCaseDetail();
        CrowdfundingInfo cf = crowdfundingInfoBiz.getFundingInfoById(caseId);

        caseDetail.setCaseBaseInfo(getBaseInfo(cf));
        caseDetail.setFirstApproveCaseInfo(getFirstApproveCaseInfo(cf));
        caseDetail.setCreditInfo(getCreditInfo(caseId));

        return caseDetail;
    }

    private UserInfoModel getUserInfoByUserId(long userId) {
        UserInfoModel userInfoModel = null;
        try {
            userInfoModel = userInfoServiceBiz.getUserInfoByUserId(userId);
        } catch (Throwable e) {
            log.warn("查询案例发起人手机号的grpc异常。 caseId:{}", userId, e);
        }
        return userInfoModel;
    }

    public Response<String> queryPatientIdCard(int userId, int caseId, long workOrderId,
                                               MaskCodePageEnum maskCodePageEnum) {
        CfFirsApproveMaterial material = firstApproveBiz.getCfFirsApproveMaterialByInfoId(caseId);

        if (material == null) {
            log.error("初次审核不能找到前置信息 caseId:{}", caseId);
            return ResponseUtil.makeSuccess("");
        }
        String idCard = material.getPatientBornCard();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(material.getPatientCryptoIdcard())) {
            idCard = shuidiCipher.decrypt(material.getPatientCryptoIdcard());
        }

        // 记录操作日志
        MaskCodeOperationRecord maskCodeOperationRecord = new MaskCodeOperationRecord();
        maskCodeOperationRecord.setCaseId(caseId);
        maskCodeOperationRecord.setOperationPage(maskCodePageEnum.getPageName());
        maskCodeOperationRecord.setOperationType(MaskCodeOperationType.INITIAL_VIEW_PATIENT_IDCARD.getOperationTypeDesc());
        maskCodeOperationRecord.setQueryContent(idCard + "-" + workOrderId);
        maskCodeOperationRecord.setOperationTime(DateUtil.dateToString(new Date(), "yyyy-MM-dd"));
        maskCodeOperationRecord.setOperatorId(userId);
        maskCodeOperationRecord.setClewId(0);
        maskCodeOperationRecord.setWechatId("");
        AuthRpcResponse<String> mis = seaAccountClientV1.getMisByUserId(userId);
        if (null != mis && mis.isSuccess()) {
            maskCodeOperationRecord.setOperatorMis(mis.getResult());
        }
        maskCodeOperationRecordBiz.addMaskOperationRecord(maskCodeOperationRecord);


        return ResponseUtil.makeSuccess(idCard);
    }


    private InitialAuditCaseDetail.TagInfo acquireTagInfo(String channel) {
        InitialAuditCaseDetail.TagInfo tagInfo = new InitialAuditCaseDetail.TagInfo();
        List<String> noShowCallableList = Lists.newArrayList(ChannelRefine.ChannelRefineResuleEnum.WEIXIN_1V1.getChannelDesc(),
                ChannelRefine.ChannelRefineResuleEnum.XIANXIA_BD.getChannelDesc());
        for (String noShowCallable : noShowCallableList) {
            if (channel != null && channel.contains(noShowCallable)) {
                tagInfo.setCallable(1);
                break;
            }
        }
        return tagInfo;
    }

    /**
     * 创建工单时预加载ocr信息
     *
     * @param workOrderId
     * @param caseId
     */
    public void loadOcrInfo(long workOrderId, int caseId) {
        CrowdfundingInfo cf = crowdfundingInfoBiz.getFundingInfoById(caseId);
        InitialAuditCaseDetail.FirstApproveCaseInfo firstApproveCaseInfo = getFirstApproveCaseInfo(cf);
        ocrApproveService.fillResult(firstApproveCaseInfo, caseId, workOrderId);
    }

    public CfAiMaterialsVo queryAiData(int caseId, long workOrderId) {
        CfAiMaterialsVo cfAiMaterialsVo = new CfAiMaterialsVo();
        List<CfAiMaterials> cfAiMaterialsList = Lists.newArrayList();

        if (workOrderId > 0) {
            //查询小于工单创建时间最新的结果
            Response<WorkOrderVO> workOrderVOResponse = workOrderClient.getWorkOrderById(workOrderId);
            WorkOrderVO workOrderVO = Optional.of(workOrderVOResponse).filter(Response::ok).map(Response::getData).orElse(null);
            if (workOrderVO != null && workOrderVO.getOrderType() == WorkOrderType.ai_erci.getType()) {

                cfAiMaterialsVo.setErOrderFromAiAudit(isErOrderAiSource(workOrderId));

                String date = workOrderVO.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().toString();
                cfAiMaterialsList.add(cfAiMaterialsDao.getByCaseIdAndDate(caseId, CfAiMaterials.tType, date));
                cfAiMaterialsList.add(cfAiMaterialsDao.getByCaseIdAndDate(caseId, CfAiMaterials.jType, date));
                cfAiMaterialsList.add(cfAiMaterialsDao.getByCaseIdAndDate(caseId, CfAiMaterials.zType, date));
                cfAiMaterialsList.add(cfAiMaterialsDao.getByCaseIdAndDate(caseId, CfAiMaterials.qType, date));
            }
        }

        if (CollectionUtils.isEmpty(cfAiMaterialsList)) {
            cfAiMaterialsList.add(cfAiMaterialsDao.getByCaseId(caseId, CfAiMaterials.tType));
            cfAiMaterialsList.add(cfAiMaterialsDao.getByCaseId(caseId, CfAiMaterials.jType));
            cfAiMaterialsList.add(cfAiMaterialsDao.getByCaseId(caseId, CfAiMaterials.zType));
            cfAiMaterialsList.add(cfAiMaterialsDao.getByCaseId(caseId, CfAiMaterials.qType));
        }

        cfAiMaterialsVo.setCfAiMaterials(cfAiMaterialsList);
        //获取该案例是否生成过 图片录入工单
        Response<WorkOrderVO> aiPhotoWorkOrder = workOrderClient.getLastWorkOrder(caseId, WorkOrderType.ai_photo.getType());
        WorkOrderVO aiPhotoData = Objects.isNull(aiPhotoWorkOrder) ? null : aiPhotoWorkOrder.notOk() ? null : aiPhotoWorkOrder.getData();
        //获取该案例是否生成过 求助说明录入工单
        Response<WorkOrderVO> aiContentWorkOrder = workOrderClient.getLastWorkOrder(caseId, WorkOrderType.ai_content.getType());
        WorkOrderVO aiContentData = Objects.isNull(aiContentWorkOrder) ? null : aiContentWorkOrder.notOk() ? null : aiContentWorkOrder.getData();
        //
        cfAiMaterialsVo.setAiPhotoDataFlag(Objects.nonNull(aiPhotoData) && aiPhotoData.getHandleResult() == HandleResultEnum.done.getType());
        cfAiMaterialsVo.setAiContentDataFlag(Objects.nonNull(aiContentData) && aiContentData.getHandleResult() == HandleResultEnum.done.getType());

        CfAiMaterialsResult cfAiMaterialsResult = cfAiMaterialsDao.getResultByCaseId(caseId);
        if (Objects.nonNull(cfAiMaterialsResult)) {
            if (!"-1".equals(cfAiMaterialsResult.getLabel())) {
                cfAiMaterialsVo.setLabel(cfAiMaterialsResult.getLabel());
            }
            if (!"-1".equals(cfAiMaterialsResult.getRejectField())) {
                cfAiMaterialsVo.setRejectFields(Lists.newArrayList(Splitter.on(",").split(cfAiMaterialsResult.getRejectField())));
            }
        }

        return cfAiMaterialsVo;
    }


    public boolean isErOrderAiSource(long workOrderId) {
        Response<List<WorkOrderExt>> result = workOrderClient.queryAllWorkExtIgnoreDelete(workOrderId,
                Lists.newArrayList(OrderExtName.secondWorkOrderSource.getName()));

        if (result == null || CollectionUtils.isEmpty(result.getData())) {
            return false;
        }

        for (WorkOrderExt orderExt : result.getData()) {
            if (StringUtils.isNotBlank(orderExt.getExtValue())
                    && SecondWorkOrderTypeSourceEnum.smart_second_work_order.name().equals(orderExt.getExtValue())) {
                return true;
            }
        }

        return false;
    }

    private WorkOrderVO getWorkOrderMsg(long workOrderId) {
        Response<WorkOrderVO> response = workOrderClient.getWorkOrderById(workOrderId);
        return Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);
    }
}
