package com.shuidihuzhu.cf.service.approve.impl;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfRepeatInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.dao.approve.CfMultipleCaseRiskDao;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.dto.CfMultipleCaseRiskDto;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceDrawCashFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.finance.drawcash.CfDrawCashRecordV2;
import com.shuidihuzhu.cf.model.SimilarCaseRecord;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCfRepeatInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.approve.CfMultipleCaseRiskService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.search.client.CfSearchClient;
import com.shuidihuzhu.client.cf.search.model.CfCaseIndexSearchParam;
import com.shuidihuzhu.client.cf.search.model.CfCaseIndexSearchResult;
import com.shuidihuzhu.client.cf.search.model.CfCaseModel;
import com.shuidihuzhu.client.cf.search.model.SearchRpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CfMultipleCaseRiskServiceImpl implements CfMultipleCaseRiskService {
    @Autowired
    private AdminCfRepeatInfoBiz adminCfRepeatInfoBiz;
    @Autowired
    private CfMultipleCaseRiskDao cfMultipleCaseRiskDao;
    @Autowired
    private CfFinanceDrawCashFeignClient cfFinanceDrawCashFeignClient;
    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Override
    public void judgeAmountRisk(Long workOrderId, int caseId) {
        if (workOrderId == null || workOrderId <= 0 || caseId <= 0) {
            log.error("生成材审工单时，判断是否有案例多发的金额风险失败, caseId: {}", caseId);
            return;
        }
        if (cfMultipleCaseRiskDao.selectByWorkOrderId(workOrderId) != null) {
            return;
        }
        
        List<CrowdfundingInfo> crowdfundingInfoList = new ArrayList<>();
        crowdfundingInfoList = getAllRepeatCaseIds(caseId); 
        CfMultipleCaseRiskDto multipleCaseRiskDto = new CfMultipleCaseRiskDto();
        CrowdfundingInfo firstCaseHasDonation = null;
        crowdfundingInfoList = crowdfundingInfoList.stream().sorted(Comparator.comparing(CrowdfundingInfo::getId)).collect(Collectors.toList());
        for (CrowdfundingInfo crowdfundingInfo : crowdfundingInfoList) {
            if (crowdfundingInfo.getDonationCount() > 0) {
                firstCaseHasDonation = crowdfundingInfo;
                break;
            }
        }

        // 提现记录
        List<CfDrawCashRecordV2> drawCashRecordList  = new ArrayList<>();
        List<Integer> caseIdList = crowdfundingInfoList.stream().map(CrowdfundingInfo::getId).collect(Collectors.toList());
        FeignResponse<Map<Integer, List<CfDrawCashRecordV2>>> feignResponse = cfFinanceDrawCashFeignClient.getSuccessDrawCashRecordV2ListBatch(caseIdList);
        if (feignResponse == null || feignResponse.notOk()) {
            log.error("获取案例累计打款金额之和失败，response: {}, caseIdList: {}", feignResponse, caseIdList);
        } else {
            feignResponse.getData().values().forEach(drawCashRecordList::addAll);
        }
        long amount = getAllDonateAmountInFen(drawCashRecordList);
        Map<Integer, Long> oldCaseAllDonate = getOldCaseAllDonate(caseIdList);
        amount += getOldCaseAllDonateAmountInFen(oldCaseAllDonate);
        if (firstCaseHasDonation != null) {
            if (amount * 2 > firstCaseHasDonation.getTargetAmount()) {
                multipleCaseRiskDto.setHaveAmountRisk(1);
            }
        }

        multipleCaseRiskDto.setAmount(amount);
        multipleCaseRiskDto.setFirstApproveTime(getEarliestCreateTime(crowdfundingInfoList));
        multipleCaseRiskDto.setWorkOrderId(workOrderId);
        multipleCaseRiskDto.setCaseId(caseId);
        // 记录每个案例提现的钱
        List<SimilarCaseRecord> similarCaseRecords = buildEachCaseDrawCashRecord(crowdfundingInfoList, drawCashRecordList, oldCaseAllDonate);
        multipleCaseRiskDto.setSimilarCase(JSON.toJSONString(similarCaseRecords));
        cfMultipleCaseRiskDao.insertOne(multipleCaseRiskDto);
    }

    @Override
    public CfMultipleCaseRiskDto getRecordByWorkOrderId(long workOrderId) {
        if (workOrderId <= 0) {
            return null;
        }
        return cfMultipleCaseRiskDao.selectByWorkOrderId(workOrderId);
    }
    // 根据患者身份证获取重复的案例 id
    private List<CrowdfundingInfo> getAllRepeatCaseIds(int currentCaseId) {
        AdminCfRepeatInfo adminCfRepeatInfo = adminCfRepeatInfoBiz.buildRepeatInfo(currentCaseId);
        Map<Integer, Set<Integer>> repeatBaseInfoMap = adminCfRepeatInfo.getRepeatBaseInfoMap();
        List<AdminCfRepeatInfo.RepeatReason> repeatReasons = Arrays.asList(AdminCfRepeatInfo.RepeatReason.values());
        repeatReasons = filterRepeatReasons(repeatReasons);
        Set<Integer> repeatStatus = repeatReasons.stream().map(AdminCfRepeatInfo.RepeatReason::getCode).collect(Collectors.toSet());
        List<Integer> caseIds = new ArrayList<>();
        for (Map.Entry<Integer, Set<Integer>> entry : repeatBaseInfoMap.entrySet()) {
            int status = entry.getKey();

            if (!repeatStatus.contains(status)) {
                continue;
            }
            caseIds.addAll(entry.getValue());
        }
        if (!caseIds.contains(currentCaseId)) {
            caseIds.add(currentCaseId);
        }
        return crowdfundingInfoBiz.getListByIds(caseIds);
    }
    private List<AdminCfRepeatInfo.RepeatReason> filterRepeatReasons(List<AdminCfRepeatInfo.RepeatReason> repeatReasons) {
        Set<Integer> needRepeatStatus = Sets.newHashSet(
                AdminCfRepeatInfo.RepeatReason.R5.getCode(),
                AdminCfRepeatInfo.RepeatReason.R6.getCode(),
                AdminCfRepeatInfo.RepeatReason.R13.getCode(),
                AdminCfRepeatInfo.RepeatReason.R14.getCode(),
                AdminCfRepeatInfo.RepeatReason.R15.getCode(),
                AdminCfRepeatInfo.RepeatReason.R16.getCode(),
                AdminCfRepeatInfo.RepeatReason.R19.getCode(),
                AdminCfRepeatInfo.RepeatReason.R20.getCode(),
                AdminCfRepeatInfo.RepeatReason.R21.getCode(),
                AdminCfRepeatInfo.RepeatReason.R22.getCode(),
                AdminCfRepeatInfo.RepeatReason.R25.getCode(),
                AdminCfRepeatInfo.RepeatReason.R26.getCode(),
                AdminCfRepeatInfo.RepeatReason.R27.getCode(),
                AdminCfRepeatInfo.RepeatReason.R28.getCode(),
                AdminCfRepeatInfo.RepeatReason.R29.getCode(),
                AdminCfRepeatInfo.RepeatReason.R30.getCode(),
                AdminCfRepeatInfo.RepeatReason.R31.getCode(),
                AdminCfRepeatInfo.RepeatReason.R32.getCode(),
                AdminCfRepeatInfo.RepeatReason.R33.getCode(),
                AdminCfRepeatInfo.RepeatReason.R34.getCode(),
                AdminCfRepeatInfo.RepeatReason.R37.getCode(),
                AdminCfRepeatInfo.RepeatReason.R38.getCode(),
                AdminCfRepeatInfo.RepeatReason.R39.getCode(),
                AdminCfRepeatInfo.RepeatReason.R40.getCode(),
                AdminCfRepeatInfo.RepeatReason.R41.getCode(),
                AdminCfRepeatInfo.RepeatReason.R42.getCode(),
                AdminCfRepeatInfo.RepeatReason.R43.getCode(),
                AdminCfRepeatInfo.RepeatReason.R44.getCode(),
                AdminCfRepeatInfo.RepeatReason.R45.getCode(),
                AdminCfRepeatInfo.RepeatReason.R46.getCode(),
                AdminCfRepeatInfo.RepeatReason.R47.getCode(),
                AdminCfRepeatInfo.RepeatReason.R48.getCode(),
                AdminCfRepeatInfo.RepeatReason.R49.getCode(),
                AdminCfRepeatInfo.RepeatReason.R50.getCode(),
                AdminCfRepeatInfo.RepeatReason.R51.getCode(),
                AdminCfRepeatInfo.RepeatReason.R52.getCode(),
                AdminCfRepeatInfo.RepeatReason.R53.getCode(),
                AdminCfRepeatInfo.RepeatReason.R54.getCode(),
                AdminCfRepeatInfo.RepeatReason.R55.getCode(),
                AdminCfRepeatInfo.RepeatReason.R56.getCode(),
                AdminCfRepeatInfo.RepeatReason.R57.getCode(),
                AdminCfRepeatInfo.RepeatReason.R58.getCode(),
                AdminCfRepeatInfo.RepeatReason.R59.getCode(),
                AdminCfRepeatInfo.RepeatReason.R60.getCode(),
                AdminCfRepeatInfo.RepeatReason.R61.getCode(),
                AdminCfRepeatInfo.RepeatReason.R62.getCode()
        );
        return repeatReasons.stream().filter(repeatReason -> needRepeatStatus.contains(repeatReason.getCode())).collect(Collectors.toList());
    }
    private long getAllDonateAmountInFen(List<CfDrawCashRecordV2> drawCashRecord) {
        long sum = 0L;
        if (CollectionUtils.isEmpty(drawCashRecord)) {
            return sum;
        }
        for (CfDrawCashRecordV2 record : drawCashRecord) {
            sum += Math.max(record.getSuccessDrawAmount() - record.getBackAmount(), 0L);
        }
        return sum;
    }

    private Map<Integer, Long> getOldCaseAllDonate(List<Integer> caseIdList) {
        FeignResponse<Map<Integer, Long>> response = cfFinanceDrawCashFeignClient.totalDrawCashSuccessAmountWithoutFeeAndRefundBatch(caseIdList);
        if (response == null || response.notOk() || MapUtils.isEmpty(response.getData())) {
            return new HashMap<>();
        }
        return response.getData();
    }
    private long getOldCaseAllDonateAmountInFen(Map<Integer, Long> map) {
        long sum = 0L;
        if (MapUtils.isEmpty(map)) {
            return sum;
        }
        sum = map.values().stream().mapToLong(Long::longValue).sum();
        return sum;
    }
    @Override
    public Date getEarliestCreateTime(List<CrowdfundingInfo> sameIdCardCaseList) {
        if (CollectionUtils.isEmpty(sameIdCardCaseList)) {
            return null;
        }
        sameIdCardCaseList.sort(Comparator.comparing(CrowdfundingInfo::getCreateTime));
        for (CrowdfundingInfo cfInfo : sameIdCardCaseList) {
            if (cfInfo.getDonationCount() > 0) {
                return cfInfo.getCreateTime();
            }
        }
        return null;
    }

    @Override
    public CfMultipleCaseRiskDto getLatestRecordByCaseId(int caseId) {
        return cfMultipleCaseRiskDao.selectLatestByCaseId(caseId);
    }

    private List<SimilarCaseRecord> buildEachCaseDrawCashRecord(List<CrowdfundingInfo> crowdfundingInfoList, List<CfDrawCashRecordV2> drawCashRecordList, Map<Integer, Long> oldCaseAllDonate) {
        List<SimilarCaseRecord> cashRecords = new ArrayList<>();
        if (CollectionUtils.isEmpty(crowdfundingInfoList)) {
            return cashRecords;
        }
        Map<Integer, List<CfDrawCashRecordV2>> map = drawCashRecordList.stream().collect(Collectors.groupingBy(CfDrawCashRecordV2::getCaseId));
        crowdfundingInfoList.stream().forEach(crowdfundingInfo -> {
            SimilarCaseRecord cashRecord = new SimilarCaseRecord();
            cashRecord.setCaseId(crowdfundingInfo.getId());
            long amount = getAllDonateAmountInFen(map.get(crowdfundingInfo.getId())) + oldCaseAllDonate.getOrDefault(crowdfundingInfo.getId(), 0L);
            cashRecord.setAmount(amount);
            cashRecords.add(cashRecord);
        });
        return cashRecords;
    }
}
