package com.shuidihuzhu.cf.service.admin.dream;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.admin.AdminCfCreditSupplementBiz;
import com.shuidihuzhu.cf.biz.admin.AdminCfOperatingRecordBiz;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingUserDelegate;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.model.admin.AdminCreditSupplement;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.vo.admin.AdminCfCreditSupplementVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class CfCreditSupplementService {
    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Autowired
    private AdminCfCreditSupplementBiz adminCfCreditSupplementBiz;
    @Autowired
    private ICrowdfundingUserDelegate crowdfundingUserDelegate;
    @Autowired
    private AdminCrowdfundingInfoStatusBiz adminCrowdfundingInfoStatusBiz;
    @Autowired
    private AdminApproveService adminApproveService;
    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;
    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Autowired
    private AdminCfOperatingRecordBiz cfOperatingRecordBiz;
    @Autowired
    private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;
    @Resource
    private AdminCfInfoExtBiz adminCfInfoExtBiz;
    @Autowired
    private UserCommentBiz userCommentBiz;

    @Value("${credit.search.all-status:3}")
    private int ALL_STATUS;

    /**
     * 对增信驳回或者通过
     *
     * @param infoUuidList
     * @param status
     * @param userId
     * @return
     */
    public Response updateCreditSupplementInfo(List<String> infoUuidList, int status, int userId) {

        if (CollectionUtils.isEmpty(infoUuidList) || status < 0 || status > 4 || userId < 0) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        AdminUserAccountModel userAccount = seaAccountClientV1.getValidUserAccountById(userId).getResult();
        if (null == userAccount) {
            return NewResponseUtil.makeError(CfErrorCode.USER_ACCOUNT_NO_LOGIN);
        }
        Set<String> infoUuidSet = Sets.newHashSet();
        infoUuidSet.addAll(infoUuidList);
//        crowdfunding_info_status 记录案例各审核材料状态的表
        //对Type为5且status为4的进行更新
        if (status == CrowdfundingInfoStatusEnum.PASSED.getCode()) {
            adminCrowdfundingInfoStatusBiz.updateByInfoUuidsAndType(
                    CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT.getCode(), infoUuidSet,
                    CrowdfundingInfoStatusEnum.SUBMITTED.getCode(), status);
        } else {
            adminCrowdfundingInfoStatusBiz.updateByInfoIdSet(infoUuidSet,
                    CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT,
                    CrowdfundingInfoStatusEnum.getByCode(status));
        }
        CfOperatingRecordEnum.Type type;
        String comment;
        if (status == CrowdfundingInfoStatusEnum.PASSED.getCode()) {
            type = CfOperatingRecordEnum.Type.PASS_CREDIT_SUPPLEMENT;
            comment = "案例增信审核通过";
        } else {
            //驳回
            type = CfOperatingRecordEnum.Type.REFUSE_CREDIT_SUPPLEMENT;
            comment = "案例增信审核驳回";
        }
        List<CrowdfundingInfo> crowdfundingInfoList = adminCrowdfundingInfoBiz.getListByInfoUuIds(infoUuidList);
        //通过状态机检验，是否要将案例置为通过
        for (CrowdfundingInfo crowdfundingInfo : crowdfundingInfoList) {
            if (null == crowdfundingInfo) {
                continue;
            }
            CfOperatingRecord cfOperatingRecord = crowdfundingDelegate.beforeCrowdfundingInfoComment(crowdfundingInfo, userId, userAccount.getName(), type,
                    CfOperatingRecordEnum.Role.OPERATOR, comment);
            //删除回绝信息
            adminApproveService.deleteRefuseByInfoUuidAndTypes(crowdfundingInfo.getInfoId(),
                    Sets.newHashSet(CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT.getCode()));

            //检查案例状态，同时更新案例状态
            CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByInfoUuid(crowdfundingInfo.getInfoId());

            CrowdfundingStatus crowdfundingStatus = crowdfundingOperationDelegate.checkDataUpdateCaseStatus(crowdfundingInfo, cfInfoExt);
            log.info("anlishenhe,status:{}", crowdfundingStatus);
            if (crowdfundingStatus.equals(CrowdfundingStatus.CROWDFUNDING_STATED)) {
                //整个案例审核通过
                adminApproveService.casePassHandle(crowdfundingInfo);
                this.crowdfundingDelegate.updateTimes(crowdfundingInfo.getInfoId(), CfTaskEnum.Rule.REPLENISH_MATERIAL);
            }

            if (crowdfundingStatus.equals(CrowdfundingStatus.APPROVE_DENIED)) {
                adminApproveService.caseRefuseHandle(crowdfundingInfo, "增信材料补充");
//                adminApproveService.refuseReasonHandle(Lists.newArrayList(238), comment, crowdfundingInfo);
                //不需要加入备注信息系
            }
            crowdfundingDelegate.afterCfOperatingRecord(cfOperatingRecord);

            //增加UGC图文审核记录 增加房产评论
            UserComment userComment = new UserComment(crowdfundingInfo.getId(),UserCommentSourceEnum.UGC,UserCommentSourceEnum.CommentType.UGC_2,userId,
                    status==CrowdfundingInfoStatusEnum.PASSED.getCode()?"审核通过并公示":"审核不通过","审核", "客服操作");
            userCommentBiz.add(userComment);

        }
        return Response.OK;
    }

    /**
     * 多条查询
     *
     * @param pageSize
     * @param current
     * @param infoId
     * @param houseSupplement
     * @param carSupplement
     * @param startDate
     * @param endDate
     * @param infoStatus
     * @return
     */
    public JSONObject SelectByAttributes(int pageSize, int current, int infoId, AdminCreditSupplement houseSupplement,
                                         AdminCreditSupplement carSupplement, String startDate, String endDate, int infoStatus) {
        if (pageSize < 0 || pageSize > 50 || current < 0 || infoStatus < 0 || infoStatus > 4) {
            return new JSONObject();
        }
        //有案例id
        if (infoId > 0) {
            JSONObject resultJson = new JSONObject();

            resultJson.put("creditSupplement", this.getCreditSupplementVoByInfoIdExcludeConvention(infoId));

            JSONObject pagination = new JSONObject();
            pagination.put("pageSize", pageSize);
            pagination.put("current", 1);
            pagination.put("total", 1);
            resultJson.put("pagination", pagination);
            return resultJson;
        }

        //没有案例id

        return this.SelectByAttributesNoInfoId(pageSize, current, houseSupplement, carSupplement, startDate, endDate,
                infoStatus);
    }

    /**
     * 检查是否是多条件查询
     *
     * @param pageSize
     * @param current
     * @param houseSupplement
     * @param carSupplement
     * @param startDate
     * @param endDate
     * @param infoStatus
     * @return
     */
    private JSONObject SelectByAttributesNoInfoId(int pageSize, int current, AdminCreditSupplement houseSupplement,
                                                  AdminCreditSupplement carSupplement, String startDate, String endDate,
                                                  int infoStatus) {
        //房
        houseSupplement = this.initCredit(houseSupplement, 1);
        //车
        carSupplement = this.initCredit(carSupplement, 2);

        Date startTime;
        Date endTime;

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("YYYY-MM-DD HH:mm:ss");
        try {
            if (StringUtils.isNotEmpty(startDate)) {
                startTime = simpleDateFormat.parse(startDate);
            } else {
                startTime = new Date(1000L);
            }
        } catch (Exception e) {
            log.error(" startDate:{} date exception", startDate, e);
            startTime = new Date(1000L);
        }
        try {
            if (StringUtils.isNotEmpty(endDate)) {
                endTime = simpleDateFormat.parse(endDate);
            } else {
                endTime = new Date();
            }
        } catch (Exception e) {
            log.error("endDate:{} date exception", endDate, e);
            endTime = new Date();
        }
        if (StringUtils.isEmpty(startDate) && StringUtils.isEmpty(endDate)) {
            startTime = endTime = null;
        }
        int offset = (current - 1) * pageSize;


        List<String> infoUuidList = adminCrowdfundingInfoStatusBiz.selectByAttributes(houseSupplement, carSupplement,
                CrowdfundingInfoStatusEnum.getByCode(infoStatus), startTime, endTime, offset, pageSize);

        //查询案例增信材料的状态
        List<CrowdfundingInfoStatus> crowdfundingInfoStatusList = adminCrowdfundingInfoStatusBiz.getByInfoUuidsAndType(
                infoUuidList, CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT);
        List<AdminCfCreditSupplementVo> adminCfCreditSupplementVoList = this.getCreditSupplementInfo(crowdfundingInfoStatusList, carSupplement);

        int totalCount = adminCrowdfundingInfoStatusBiz.getCountByAttributes(houseSupplement, carSupplement,
                CrowdfundingInfoStatusEnum.getByCode(infoStatus), startTime, endTime);
        JSONObject resultJson = new JSONObject();
        resultJson.put("creditSupplement", adminCfCreditSupplementVoList);
        JSONObject pagination = new JSONObject();
        pagination.put("pageSize", pageSize);
        pagination.put("current", current);
        pagination.put("total", totalCount);
        resultJson.put("pagination", pagination);
        return resultJson;
    }

    /**
     * 指明案例id查询
     *
     * @param infoId
     * @return
     * @deprecated @see {@link com.shuidihuzhu.cf.service.admin.dream.CfCreditSupplementService#getCreditSupplementVoByInfoIdExcludeConvention(int)}
     */
    @Deprecated
    private List<AdminCfCreditSupplementVo> getCreditSupplementVoByInfoId(int infoId) {
        //案例
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(infoId);
        if (null == crowdfundingInfo) {
            return Lists.newArrayList();
        }

        return getAdminCfCreditSupplementVos(crowdfundingInfo);
    }

    private List<AdminCfCreditSupplementVo> getAdminCfCreditSupplementVos(CrowdfundingInfo crowdfundingInfo) {
        //cf_credit_supplement 患者基本信息增信补充部分
        CrowdfundingInfoStatus crowdfundingInfoStatus = adminCrowdfundingInfoStatusBiz.getByInfoUuidAndType(
                crowdfundingInfo.getInfoId(), CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT.getCode());
        return this.getCreditSupplementInfo(Lists.newArrayList(crowdfundingInfoStatus), null);
    }

    /**
     * 指明案例id查询，如果是公约版则不返回
     *
     * @param infoId
     * @return
     */
    private List<AdminCfCreditSupplementVo> getCreditSupplementVoByInfoIdExcludeConvention(int infoId) {
        //案例
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(infoId);
        if (null == crowdfundingInfo) {
            return Lists.newArrayList();
        }

        CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByInfoUuid(crowdfundingInfo.getInfoId());
        if(cfInfoExt.getCfVersion() == CfVersion.definition_20181131.getCode()){
            return Lists.newArrayList();
        }

        //cf_credit_supplement 患者基本信息增信补充部分
        return getAdminCfCreditSupplementVos(crowdfundingInfo);
    }



    /**
     * 根据案例状态查询全部增信信息
     *
     * @param crowdfundingInfoStatusList
     * @param creditSupplement
     * @return
     */
    private List<AdminCfCreditSupplementVo> getCreditSupplementInfo(List<CrowdfundingInfoStatus> crowdfundingInfoStatusList,
                                                                    AdminCreditSupplement creditSupplement) {

        if (CollectionUtils.isEmpty(crowdfundingInfoStatusList)) {
            return Lists.newArrayList();
        }

        Set<String> infoUuidSet = crowdfundingInfoStatusList.stream()
                .filter(item -> null != item)
                .map(item -> item.getInfoUuid())
                .collect(Collectors.toSet());
        //对处于不在处理的案例不在展示,在查询的时候已经过滤了

        //cf_credit_supplement 患者基本信息增信补充部分
        List<CfCreditSupplement> creditSupplementList = this.getCfCreditSupplement(infoUuidSet, creditSupplement);

        if (CollectionUtils.isEmpty(creditSupplementList)) {
            return Lists.newArrayList();
        }

        infoUuidSet = creditSupplementList.stream()
                .filter(item -> null != item)
                .map(item -> item.getInfoUuid())
                .collect(Collectors.toSet());

        List<CrowdfundingInfo> crowdFundingInfoList = adminCrowdfundingInfoBiz.getListByInfoUuIds(Lists.newArrayList(infoUuidSet));

        if (CollectionUtils.isEmpty(crowdFundingInfoList)) {
            return Lists.newArrayList();
        }

//        //得到发起人的手机号
//        List<UserInfoModel> userInfoModelList = accountService.getUserInfoByUserIds(Lists.newArrayList(infoUserIdSet));
//        Map<Integer, UserInfoModel> userInfoModelMap = Maps.newHashMap();
//        if (CollectionUtils.isNotEmpty(userInfoModelList)) {
//            for (UserInfoModel userInfoModel : userInfoModelList) {
//                if (null == userInfoModel) {
//                    continue;
//                }
//                userInfoModelMap.put(userInfoModel.getUserId(), userInfoModel);
//            }
//        }
        //crowdfunding_author 筹款患者基本信息表
        //案例id
        Set<Integer> infoIdSet = crowdFundingInfoList.stream()
                .filter(item -> null != item)
                .map(CrowdfundingInfo::getId)
                .collect(Collectors.toSet());
        Map<Integer, CrowdfundingAuthor> crowdfundingAuthorMap = crowdfundingUserDelegate.getByInfoIdList(
                Lists.newArrayList(infoIdSet));

        Map<String, CrowdfundingInfo> infoUuidCfInfoMap = crowdFundingInfoList.stream()
                .filter(item -> null != item && !StringUtils.isEmpty(item.getInfoId()))
                .collect(Collectors.toMap(CrowdfundingInfo::getInfoId, item -> item, (a, b) -> b));

        Map<String, AdminCfCreditSupplementVo> cfCreditSupplementVoMap = Maps.newHashMap();

        for (CfCreditSupplement itemCreditSupplement : creditSupplementList) {
            if (null == itemCreditSupplement || StringUtils.isEmpty(itemCreditSupplement.getInfoUuid())) {
                continue;
            }
            String infoUuid = itemCreditSupplement.getInfoUuid();
            CrowdfundingInfo itemInfo = infoUuidCfInfoMap.get(infoUuid);
            //保存于案例相关的信息
            if (null == itemInfo) {
                log.warn("infoUuid:{},有增信数据案例怎么会是空号呢,将增信不再展示", infoUuid);
                continue;
            }

            AdminCfCreditSupplementVo adminCfCreditSupplementVo = cfCreditSupplementVoMap.get(infoUuid);
            if (null == adminCfCreditSupplementVo) {
                adminCfCreditSupplementVo = new AdminCfCreditSupplementVo();
                cfCreditSupplementVoMap.put(infoUuid, adminCfCreditSupplementVo);
            }

            List<CfCreditSupplement> itemList = adminCfCreditSupplementVo.getCfCreditSupplementList();
            if (CollectionUtils.isEmpty(itemList)) {
                //第一次找到这个案例时
                itemList = Lists.newArrayList();
                adminCfCreditSupplementVo.setCfCreditSupplementList(itemList);

                adminCfCreditSupplementVo.setInfoUuid(infoUuid);
                adminCfCreditSupplementVo.setInfoId(itemInfo.getId());
                adminCfCreditSupplementVo.setTitle(itemInfo.getTitle());
                adminCfCreditSupplementVo.setTargetAmountYuan(MoneyUtil.buildBalance(itemInfo.getTargetAmount()));
                adminCfCreditSupplementVo.setInfoStatus(itemInfo.getStatus().value());
                adminCfCreditSupplementVo.setCreateTime(itemInfo.getCreateTime());
                adminCfCreditSupplementVo.setApplicantUserId(itemInfo.getUserId());
                adminCfCreditSupplementVo.setSubmitTime(itemCreditSupplement.getLastModified());

                //患者姓名
                CrowdfundingAuthor crowdfundingAuthor = crowdfundingAuthorMap.get(itemInfo.getId());
                if (null != crowdfundingAuthor) {
                    adminCfCreditSupplementVo.setFundingNickname(crowdfundingAuthor.getName());
                }
//                //发起人手机号
//                UserInfoModel userInfoModel = userInfoModelMap.get(itemInfo.getUserId());
//                if (null != userInfoModel) {
//                    adminCfCreditSupplementVo.setApplicantMobile(shuidiCipher.decrypt(userInfoModel.getCryptoMobile()));
//                }
                adminCfCreditSupplementVo.setApplicantMobile("***");
                CrowdfundingInfoStatus crowdfundingInfoStatus = adminCrowdfundingInfoStatusBiz.getByInfoUuidAndType(infoUuid,
                        CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT.getCode());
                adminCfCreditSupplementVo.setCreditSupplementStatus(crowdfundingInfoStatus.getStatus());

                //案例的操作审核人员姓名
                String operatorName = "患者提交,暂未操作";
                CfOperatingRecordEnum.Type type = null;
                if (crowdfundingInfoStatus.getStatus() == CrowdfundingInfoStatusEnum.PASSED.getCode()) {
                    type = CfOperatingRecordEnum.Type.PASS_CREDIT_SUPPLEMENT;
                } else if (crowdfundingInfoStatus.getStatus() == CrowdfundingInfoStatusEnum.REJECTED.getCode()) {
                    //驳回
                    type = CfOperatingRecordEnum.Type.REFUSE_CREDIT_SUPPLEMENT;
                }
                if (null != type) {
                    CfOperatingRecord cfOperatingRecord = cfOperatingRecordBiz.getByInfoUuidAndType(infoUuid, type);
                    if (null != cfOperatingRecord) {
                        operatorName = cfOperatingRecord.getUserName();
                    }
                }
                adminCfCreditSupplementVo.setOperatorName(operatorName);
            }
            itemList.add(itemCreditSupplement);
        }

        List<AdminCfCreditSupplementVo> adminCfCreditSupplementVoList = Lists.newArrayList(cfCreditSupplementVoMap.values());
        if (CollectionUtils.isEmpty(adminCfCreditSupplementVoList)) {
            return Lists.newArrayList();
        }
        adminCfCreditSupplementVoList.sort(
                Comparator.comparing(AdminCfCreditSupplementVo::getInfoId).reversed()
        );
        return adminCfCreditSupplementVoList;
    }

    /**
     * 查询，筛选增信数据
     *
     * @param infoUuidSet
     * @param creditSupplement
     * @return
     */
    private List<CfCreditSupplement> getCfCreditSupplement(Set<String> infoUuidSet, AdminCreditSupplement creditSupplement) {
        //cf_credit_supplement 患者基本信息增信补充部分
        List<CfCreditSupplement> creditSupplementList = adminCfCreditSupplementBiz.selectByInfoUuidList(
                Lists.newArrayList(infoUuidSet));
        if (CollectionUtils.isEmpty(creditSupplementList)) {
            return Lists.newArrayList();
        }
        //查找符合的infoUuid
        List<String> resultInfoUuidList = creditSupplementList.stream()
                .filter(item -> null != item && this.checkoutCreditSupplement(creditSupplement, item))
                .map(CfCreditSupplement::getInfoUuid)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(resultInfoUuidList)) {
            return Lists.newArrayList();
        }
        return creditSupplementList.stream()
                .filter(item -> null != item && resultInfoUuidList.contains(item.getInfoUuid()))
                .collect(Collectors.toList());
    }


    /**
     * init查询对象
     *
     * @param creditSupplement
     * @param propertyType
     * @return
     */
    private AdminCreditSupplement initCredit(AdminCreditSupplement creditSupplement, int propertyType) {
        if (null == creditSupplement) {
            creditSupplement = new AdminCreditSupplement();
            creditSupplement.setStatus(ALL_STATUS);
        }
        if (0 == creditSupplement.getEndCount() && 0 == creditSupplement.getEndTotalValue()
                && ALL_STATUS == creditSupplement.getStatus()
                && 0 == creditSupplement.getStartCount() && 0 == creditSupplement.getStartTotalValue()) {
            return null;
        }

        creditSupplement.setPropertyType(propertyType);
        if (0 == creditSupplement.getEndTotalValue()) {
            creditSupplement.setEndTotalValue(Integer.MAX_VALUE);
        }
        if (0 == creditSupplement.getEndCount()) {
            creditSupplement.setEndCount(Integer.MAX_VALUE);
        }
        return creditSupplement;
    }

    /**
     * 符合查询规则的返回true
     *
     * @param creditSupplement
     * @param item
     * @return
     */
    private boolean checkoutCreditSupplement(AdminCreditSupplement creditSupplement, CfCreditSupplement item) {
        if (creditSupplement == null || null == item) {
            return true;
        }
        //不是一个类型
        if (item.getPropertyType() != creditSupplement.getPropertyType()) {
            return false;
        }
        //数量
        if (item.getCount() < creditSupplement.getStartCount() || item.getCount() > creditSupplement.getEndCount()) {
            return false;
        }
        //价值
        if (item.getTotalValue() < creditSupplement.getStartTotalValue() || item.getTotalValue() > creditSupplement.getEndTotalValue()) {
            return false;
        }
        return true;
    }
}
