package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Ordering;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.delegate.saas.AdminOrganization;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.OrganizationClientV1;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdFundingProgressBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAuthorBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminPushDynamicMsgService;
import com.shuidihuzhu.cf.biz.crowdfunding.supply.CfSupplyActionBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.supply.CfSupplyProgressBiz;
import com.shuidihuzhu.cf.client.feign.CfCommonFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecordDTO;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.delegate.OrganizationDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.admin.DynamicMsgEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdFundingProgressType;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.model.admin.common.SupplyProgressReasonItem;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyAction.ActionType;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyAction.SupplyHandleStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyProgress.SupplyProgressStatus;
import com.shuidihuzhu.cf.model.param.SupplyActionSearchParam;
import com.shuidihuzhu.cf.model.param.SupplyOrgEnum;
import com.shuidihuzhu.cf.model.param.SupplyProgressWorkHandleParam;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.rejectmanager.impl.SupplyProgressReasonService;
import com.shuidihuzhu.cf.vo.crowdfunding.CfSupplyProgressDetailListVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CfSupplyProgressVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CfSupplyProgressVo.SupplyButtonMsgEnum;
import com.shuidihuzhu.cf.vo.crowdfunding.CfSupplyProgressVo.SupplyProgressButtonInfo;
import com.shuidihuzhu.cf.vo.crowdfunding.CfSupplyProgressWorkDetailVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CfSupplyProgressWorkDetailVo.SupplyProgressRecord;
import com.shuidihuzhu.cf.vo.crowdfunding.CfSupplyProgressWorkDetailVo.SupplyProgressStatusInfo;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderRecordClient;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.model.vo.WorkOrderRecordVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.pf.common.v2.model.pagehelper.PaginationListVO;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-01-10 12:13
 **/
@Service
@Slf4j
@Deprecated
public class CfSupplyProgressService {

    @Autowired
    private CfSupplyActionBiz cfSupplyActionBiz;

    @Autowired
    private CfSupplyProgressBiz cfSupplyProgressBiz;

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;

    @Resource
    private OrganizationClientV1 organizationClientV1;

    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private CfWorkOrderRecordClient cfWorkOrderRecordClient;

    @Autowired
    private AdminPushDynamicMsgService dynamicMsgService;

    @Autowired
    AdminCrowdFundingProgressBiz adminCrowdFundingProgressBiz;

    @Autowired
    private SupplyProgressReasonService supplyProgressRejectService;

    @Autowired
    private CfCommonFeignClient cfCommonFeignClient;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Autowired
    private AdminCrowdfundingAuthorBiz authorBiz;

    @Resource
    private IRiskDelegate riskDelegate;

    @Resource
    private ApproveRemarkOldService remarkOldService;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private OrganizationDelegate orgService;

    //备注中的额外字段信息
    private static final String OPERATION_RECORD_EXT_COMMENT = "rejectComment";

    private static final String OPERATION_RECORD_EXT_PROGRESS_ID = "supplyProgressId";

    @Autowired
    private MaskUtil maskUtil;


    //案例详情页下发按钮信息
    public CfSupplyProgressVo.SupplyProgressButtonInfo getSupplyButtonInfo(int caseId) {
//        boolean canShowOrOperate = canShowOrOperate(caseId);
//        if (!canShowOrOperate) {
//            return null;
//        }
        //检查案例下的下发
        List<CfInfoSupplyAction> supplyActions = cfSupplyActionBiz.listByCaseIdAndType(caseId, ActionType.progress.getCode());
        //当存在下发'已下发','待审核','审核驳回' 展示 “相关动态”
        Optional<CfInfoSupplyAction> relationSupplyAction = supplyActions.stream().filter(item -> {
                    SupplyHandleStatus status = CfInfoSupplyAction.findHandleStatusByCode(item.getHandleStatus());
                    return CfInfoSupplyAction.can_not_create_new_supply_action.contains(status);
                }
        ).findFirst();
        SupplyProgressButtonInfo buttonInfo = new SupplyProgressButtonInfo();
        SupplyButtonMsgEnum buttonMsgEnum = null;
        if (relationSupplyAction.isPresent()) {
            buttonMsgEnum = SupplyButtonMsgEnum.button_msg_2;
            buttonInfo.setSupplyId(relationSupplyAction.get().getId());
        } else {
            buttonMsgEnum = SupplyButtonMsgEnum.button_msg_1;
        }
        buttonInfo.setId(buttonMsgEnum.getCode());
        buttonInfo.setButtonMsg(buttonMsgEnum.getButtonMsg());
        return buttonInfo;
    }



    public boolean checkBeforeSupply(int caseId, List<SupplyHandleStatus> handleStatusList) {
        //检查当前是否仍有未处理完成的下发,当前所有下发是否都处于“审核通过”或者“已撤销”
        List<CfInfoSupplyAction> supplyActions = cfSupplyActionBiz.listByCaseIdAndType(caseId, ActionType.progress.getCode());
        return checkBeforeSupply(handleStatusList, supplyActions);
    }

    private boolean checkBeforeSupply(List<SupplyHandleStatus> handleStatusList, List<CfInfoSupplyAction> supplyActions) {
        List<Long> unFinishIds = supplyActions.stream()
                .filter(item -> {
                    SupplyHandleStatus status = CfInfoSupplyAction.findHandleStatusByCode(item.getHandleStatus());
                    return handleStatusList.contains(status);
                })
                .map(CfInfoSupplyAction::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(unFinishIds)) {
            log.info("案例存在其他仍在处理的下发,下发id为:{}", JSON.toJSON(unFinishIds));
            return false;
        }
        return true;
    }


    public Map<Integer, Boolean> batchObtainReprocessStatus(List<Integer> caseIds) {
        Map<Integer, Boolean> batchStatus = Maps.newHashMap();
        Map<Integer, List<CfInfoSupplyAction>> caseIdTSupplyActions = cfSupplyActionBiz.listByCaseIds(caseIds, ActionType.progress.getCode())
                .stream().collect(Collectors.groupingBy(CfInfoSupplyAction::getCaseId));
        for (Integer caseId : caseIdTSupplyActions.keySet()) {
            batchStatus.put(caseId, checkBeforeSupply(CfInfoSupplyAction.not_allow_reprocess_status, caseIdTSupplyActions.get(caseId)));
        }
        return batchStatus;
    }


    //撤销
    public Response<Boolean> doCancel(int caseId, long supplyId, int adminUserId) {
        //校验是否可以撤销
        CfInfoSupplyAction cfInfoSupplyAction = cfSupplyActionBiz.getById(supplyId);
        if (cfInfoSupplyAction == null) {
            return NewResponseUtil.makeFail("下发不存在!");
        }
        if (!CfInfoSupplyAction.canSubmit(cfInfoSupplyAction.getHandleStatus())) {
            log.warn("无法撤销该下发,caseId:{}, cfInfoSupplyAction:{}", caseId, JSON.toJSONString(cfInfoSupplyAction));
            SupplyHandleStatus handleStatus = CfInfoSupplyAction.findHandleStatusByCode(cfInfoSupplyAction.getHandleStatus());
            if (handleStatus == SupplyHandleStatus.wait_audit) {
                //查询对应的工单
                Response<WorkOrderVO> workOrderVOResponse = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, Lists.newArrayList(WorkOrderType.xiafaprogress.getType()));
                String msg = String.format("该下发已生成编号为%d的下发动态审核工单", Optional.ofNullable(workOrderVOResponse.getData()).map(WorkOrderVO::getWorkOrderId).orElse(0L));
                return NewResponseUtil.makeFail(msg);
            }
            return NewResponseUtil.makeFail(String.format("当前下发为%s无法撤销该下发", handleStatus.getDesc()));
        }
        cfSupplyActionBiz.updateHandleStatus(supplyId, SupplyHandleStatus.cancel.getCode());
        commonOperationRecordClient
                .create()
                .buildBasicPlatform(supplyId, adminUserId, OperationActionTypeEnum.CANCEL_SUPPLY_PROGRESS)
                .save();
        //案例备注
        remarkOldService.add(caseId, adminUserId, String.format("下发动态[下发id:%d],下发已撤销", supplyId));
        //调用取消暂停打款接口
        Response recoverPauseResponse = dynamicMsgService.recoverPauseDrawCashByDynamic(caseId);
        if (recoverPauseResponse.notOk()) {
            log.warn("取消暂停打款失败,caseId:{},response:{}", caseId, recoverPauseResponse);
        }
        return NewResponseUtil.makeSuccess(true);
    }


    //查看相关动态
    public Response<CfSupplyProgressVo> showRelationProgress(int caseId, long supplyId) {
//        boolean canShowOrOperate = canShowOrOperate(caseId);
//        if (!canShowOrOperate) {
//            return NewResponseUtil.makeFail("案例审核通过无法查看");
//        }
        //检查案例下的下发
        List<CfInfoSupplyAction> supplyActions = cfSupplyActionBiz.listByCaseIdAndType(caseId, ActionType.progress.getCode());
        Optional<CfInfoSupplyAction> supplyInfo = supplyActions.stream().filter(item -> item.getId() == supplyId).findFirst();
        boolean presentSupplyId = supplyInfo.isPresent();
        if (!presentSupplyId) {
            return NewResponseUtil.makeFail("不存在该下发记录");
        }
        CfInfoSupplyAction supplyAction = supplyInfo.get();
        SupplyHandleStatus handleStatus = CfInfoSupplyAction.findHandleStatusByCode(supplyAction.getHandleStatus());
        //当前下发是否能满足展示相关动态 只有传入下发状态为‘已下发’，'待审核','审核驳回' 其他的下发非次状态才展示查看相关状态
        boolean canShowInfo = CfInfoSupplyAction.can_not_create_new_supply_action.contains(handleStatus);
        //校验
        canShowInfo = canShowInfo && supplyActions.stream()
                .filter(item -> item.getId() != supplyId)
                .allMatch(item -> {
                    SupplyHandleStatus status = CfInfoSupplyAction.findHandleStatusByCode(item.getHandleStatus());
                    return !CfInfoSupplyAction.can_not_create_new_supply_action.contains(status);
                });
        if (!canShowInfo) {
            log.info(String.format("下发状态不符,下发id:%s,下发状态:%s", supplyId, handleStatus.getDesc()));
            return NewResponseUtil.makeError(AdminErrorCode.SUPPLY_STATUS_NOT_MATCH);
        }

        //展示
        CfSupplyProgressVo cfSupplyProgressVo = new CfSupplyProgressVo();

        cfSupplyProgressVo.setCfInfoSupplyAction(supplyAction);

        cfSupplyProgressVo.setSupplyReasons(convertSupplyReasons(supplyAction));

        cfSupplyProgressVo.setSupplyOperatorName(getAdminUserName(supplyAction.getSupplyUserId()));

        //查询下发时间点之后的动态工单
        Response<List<WorkOrderVO>> ugcProgressWorkResponse = cfWorkOrderClient.listByCaseIdAndTypeAndResult(caseId, Lists.newArrayList(WorkOrderType.ugcprogress.getType()), Lists.newArrayList());
        if (ugcProgressWorkResponse.ok()) {
            List<CfSupplyProgressVo.ProgressWorkOrderInfo> workOrderInfoList = getProgressWorkOrderInfos(supplyAction, ugcProgressWorkResponse.getData());

            cfSupplyProgressVo.setRelationWorkList(workOrderInfoList);

            cfSupplyProgressVo.setRelationWorkNum(workOrderInfoList.size());
        }

        //判断撤销按钮展示,只有 ‘已下发’，'待审核','审核驳回' 才能走到这一步
        cfSupplyProgressVo.setShowCancelAction(handleStatus != SupplyHandleStatus.cancel);

        return NewResponseUtil.makeSuccess(cfSupplyProgressVo);
    }


    @NotNull
    private List<CfSupplyProgressVo.ProgressWorkOrderInfo> getProgressWorkOrderInfos(CfInfoSupplyAction supplyAction, List<WorkOrderVO> ugcProgressWorkList) {
        Ordering<WorkOrderVO> sortWorkOrder = Ordering.natural().onResultOf(WorkOrderVO::getCreateTime);
        List<WorkOrderVO> sortedWorkOrderLists = ugcProgressWorkList.stream()
                .filter(item -> item.getCreateTime().after(supplyAction.getCreateTime()))
                .sorted(sortWorkOrder)
                .collect(Collectors.toList());
        //查询对应的下发id
        Map<Long, Integer> workId2WordId = Maps.newHashMap();
        Response<List<WorkOrderExt>> listResponse = cfWorkOrderClient.listExtInfos(sortedWorkOrderLists.stream().map(WorkOrderVO::getWorkOrderId).collect(Collectors.toList()), OrderExtName.wordId.getName());
        if (listResponse.ok()) {
            workId2WordId = listResponse.getData().stream().collect(Collectors.toMap(WorkOrderExt::getWorkOrderId, item -> Integer.valueOf(item.getExtValue()), (before, after) -> before));
        }
        //查找动态信息
        Map<Integer, CrowdFundingProgress> map = adminCrowdFundingProgressBiz.getMapByIds(Lists.newArrayList(workId2WordId.values()));

        List<CfSupplyProgressVo.ProgressWorkOrderInfo> workOrderInfoList = Lists.newArrayList();
        final Map<Long, Integer> finalWorkId2WordId = workId2WordId;
        sortedWorkOrderLists.forEach(item -> {
            CfSupplyProgressVo.ProgressWorkOrderInfo workInfo = new CfSupplyProgressVo.ProgressWorkOrderInfo();
            BeanUtils.copyProperties(item, workInfo);

            Integer wordId = finalWorkId2WordId.get(item.getWorkOrderId());
            CrowdFundingProgress record = map.get(wordId);

            if (record != null) {
                workInfo.setContent(record.getContent());
                workInfo.setImgUrls(record.getImageUrls());
                //敏感词
                Set<String> sensitiveWords = riskDelegate.getHitWords(record.getContent());
                workInfo.setSensitiveWord(Joiner.on(",").join(sensitiveWords));
                workOrderInfoList.add(workInfo);
            }
        });
        return workOrderInfoList;
    }


    @NotNull
    private List<String> convertSupplyReasons(CfInfoSupplyAction supplyAction) {
        List<Integer> supplyReasonIds = Splitter.on(',')
                .splitToList(supplyAction.getSupplyReason())
                .stream().map(Integer::valueOf)
                .collect(Collectors.toList());
        return supplyProgressRejectService.listRejectByIds(supplyReasonIds)
                .stream()
                .map(SupplyProgressReasonItem::getDescribe)
                .collect(Collectors.toList());
    }


    //检查是否能进行操作
    public boolean checkCanHandle(long supplyProgressId) {
        List<CfInfoSupplyProgress> supplyProgresses = cfSupplyProgressBiz.listByIds(Lists.newArrayList(supplyProgressId));
        if (CollectionUtils.isEmpty(supplyProgresses)) {
            return false;
        }
        CfInfoSupplyAction supplyAction = cfSupplyActionBiz.getById(supplyProgresses.get(0).getProgressActionId());
        return supplyAction != null && supplyAction.getHandleStatus() == SupplyHandleStatus.wait_audit.getCode();
    }

    //审核通过
    @Deprecated
    public Response<Boolean> pass(SupplyProgressWorkHandleParam handleParam, int adminUserId) {
        //检查是否能操作当前下发
        Response<CfInfoSupplyProgress> checkCanHandleProgress = checkCanHandleProgress(handleParam);
        if (checkCanHandleProgress.notOk()) {
            log.warn("pass异常,handleParam:{}", JSON.toJSONString(handleParam));
            return NewResponseUtil.makeFail(checkCanHandleProgress.getMsg());
        }
        CfInfoSupplyProgress supplyProgress = checkCanHandleProgress.getData();
        //修改下发状态为审核通过
        long supplyActionId = handleParam.getSupplyActionId();
        long supplyProgressId = handleParam.getSupplyProgressId();
        //修改下发动态状态
        cfSupplyActionBiz.updateHandleStatus(supplyActionId, SupplyHandleStatus.pass.getCode());

        // 将填充后的资金进展progress 入 crowdfunding_progress表
        CrowdFundingProgress crowdFundingProgress = new CrowdFundingProgress();
        crowdFundingProgress.setUserId(supplyProgress.getUserId());
        crowdFundingProgress.setImageUrls(handleParam.getImgUrls());
        crowdFundingProgress.setContent(supplyProgress.getContent());
        crowdFundingProgress.setType(CrowdFundingProgressType.PROGRESS_NEEDS_AUDIT.value());
        crowdFundingProgress.setTitle("");
        crowdFundingProgress.setActivityId(handleParam.getCaseId());
        adminCrowdFundingProgressBiz.insertInCrowdfundingProgress(crowdFundingProgress);

        //插入动态信息
        cfSupplyProgressBiz.pass(supplyProgressId, handleParam.getImgUrls(), Optional.ofNullable(crowdFundingProgress.getId()).orElse(0));

        //调用取消暂停打款接口
        Response recoverPauseResponse = dynamicMsgService.recoverPauseDrawCashByDynamic(handleParam.getCaseId());
        if (recoverPauseResponse.notOk()) {
            log.warn("取消暂停打款失败,caseId:{},response:{}", handleParam.getCaseId(), recoverPauseResponse);
        }

        //添加日志
        commonOperationRecordClient
                .create()
                .buildBasicPlatform(supplyActionId, adminUserId, OperationActionTypeEnum.SUPPLY_PROGRESS_PASS)
                .buildExt(OPERATION_RECORD_EXT_PROGRESS_ID, String.valueOf(supplyProgressId))
                .save();
        //案例备注
        remarkOldService.add(handleParam.getCaseId(), adminUserId, String.format("下发动态审核[工单id:%d],下发审核通过", handleParam.getWorkOrderId()));
        return NewResponseUtil.makeSuccess(true);
    }


    //审核驳回
    @Deprecated
    public Response<Boolean> reject(SupplyProgressWorkHandleParam handleParam, int adminUserId) {
        //检查是否能操作当前下发
        Response<CfInfoSupplyProgress> checkCanHandleProgress = checkCanHandleProgress(handleParam);
        if (checkCanHandleProgress.notOk()) {
            log.warn("reject异常,handleParam:{}", JSON.toJSONString(handleParam));
            return NewResponseUtil.makeFail(checkCanHandleProgress.getMsg());
        }
        long supplyActionId = handleParam.getSupplyActionId();
        long supplyProgressId = handleParam.getSupplyProgressId();
        //修改下发状态 为 审核驳回
        cfSupplyActionBiz.updateHandleStatus(supplyActionId, SupplyHandleStatus.reject.getCode());
        //修改下发动态状态
        CfInfoSupplyProgress rejectSupplyProgress = new CfInfoSupplyProgress();
        rejectSupplyProgress.setId(supplyProgressId);
        rejectSupplyProgress.setImgUrls(handleParam.getImgUrls());
        rejectSupplyProgress.setContentStatus(handleParam.getContentHandleStatus());
        rejectSupplyProgress.setImgStatus(handleParam.getImgUrlsHandleStatus());
        cfSupplyProgressBiz.reject(rejectSupplyProgress);
        //发送信息
        dynamicMsgService.pushDynamicMsg(handleParam.getCaseId(), DynamicMsgEnum.PushMsgType.REJECT_PUSH, (int) (handleParam.getSupplyProgressId()), (int) supplyActionId,handleParam.getWorkOrderId());
        //添加日志
        commonOperationRecordClient
                .create()
                .buildBasicPlatform(supplyActionId, adminUserId, OperationActionTypeEnum.SUPPLY_PROGRESS_REJECT)
                .buildExt(OPERATION_RECORD_EXT_PROGRESS_ID, String.valueOf(supplyProgressId))
                .buildExt(OPERATION_RECORD_EXT_COMMENT, handleParam.getComment())
                .save();
        //案例备注
        remarkOldService.add(handleParam.getCaseId(), adminUserId, String.format("下发动态审核[工单id:%d],下发驳回,驳回原因:%s", handleParam.getWorkOrderId(), handleParam.getComment()));
        return NewResponseUtil.makeSuccess(true);
    }


    //重新审核前判断以及准备
    public boolean doBeforeReprocess(WorkOrderVO workOrderVO, long operatorId) {
        //判断该案例是否有真在审核的下发
        boolean canReprocess = checkBeforeSupply(workOrderVO.getCaseId(), CfInfoSupplyAction.not_allow_reprocess_status);
        if (!canReprocess) {
            return false;
        }
        long supplyProgressId = workOrderVO.getSupplyProgressId();
        if (supplyProgressId == 0) {
            log.warn("重审时工单返回下发动态id为0,workOrderVO:{}", workOrderVO);
            return false;
        }

        List<CfInfoSupplyProgress> supplyProgresses = cfSupplyProgressBiz.listByIds(Lists.newArrayList(supplyProgressId));
        if (CollectionUtils.isEmpty(supplyProgresses)) {
            return false;
        }
        CfInfoSupplyProgress supplyProgress = supplyProgresses.get(0);
        //如果工单当前状态为审核通过,需要删除下动态记录
        FeignResponse<Boolean> deleteProgressResult = cfCommonFeignClient.deleteProgress(workOrderVO.getCaseId(), (int) supplyProgress.getProgressId());
        if (deleteProgressResult.notOk()) {
            log.warn("调用api删除动态接口失败");
            return false;
        }
        //修改当前下发的状态为 “待审核”
        cfSupplyActionBiz.updateHandleStatus(supplyProgress.getProgressActionId(), SupplyHandleStatus.wait_audit.getCode());
        //修改当前动态为初审化状态
        cfSupplyProgressBiz.reprocess(supplyProgress.getId());
        //调用暂停打款接口
        Response pauseResponse = dynamicMsgService.pauseDrawCashByDynamic(workOrderVO.getCaseId(), (int) operatorId);
        if (pauseResponse.notOk()) {
            log.warn("暂停打款失败,caseId:{},response:{}", workOrderVO.getCaseId(), pauseResponse);
        }
        return true;
    }


    @NotNull
    private Response<CfInfoSupplyProgress> checkCanHandleProgress(SupplyProgressWorkHandleParam handleParam) {
        List<CfInfoSupplyProgress> supplyProgresses = cfSupplyProgressBiz.listByIds(Lists.newArrayList(handleParam.getSupplyProgressId()));
        Optional<CfInfoSupplyProgress> supplyProgressOptional = supplyProgresses.stream()
                .filter(item -> item.getProgressActionId() == handleParam.getSupplyActionId())
                .filter(item -> {
                            List<String> originalImgs = Splitter.on(',').splitToList(item.getImgUrls());
                            List<String> auditedImgs = Lists.newArrayList();
                            if (StringUtils.isNotBlank(handleParam.getImgUrls())) {
                                auditedImgs = Splitter.on(',').splitToList(handleParam.getImgUrls());
                            }
                            return originalImgs.containsAll(auditedImgs);
                        }
                )
                .findFirst();
        if (!supplyProgressOptional.isPresent()) {
            return NewResponseUtil.makeFail("找不到对应的下发动态信息");
        }
        long supplyActionId = handleParam.getSupplyActionId();
        CfInfoSupplyAction cfInfoSupplyAction = cfSupplyActionBiz.getById(supplyActionId);
        if (cfInfoSupplyAction == null || cfInfoSupplyAction.getHandleStatus() != SupplyHandleStatus.wait_audit.getCode()) {
            return NewResponseUtil.makeFail("找不到对应的下发信息");
        }
        return NewResponseUtil.makeSuccess(supplyProgressOptional.get());
    }


    //查看下发工单详情页
    public Response<CfSupplyProgressWorkDetailVo> viewWorkDetail(int caseId, int workOrderId, long supplyProgressId, ViewPageSource pageSource) {
        CfSupplyProgressWorkDetailVo workDetailVo = new CfSupplyProgressWorkDetailVo();
        CrowdfundingInfo fundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        if (fundingInfo == null) {
            return NewResponseUtil.makeFail("无该案例");
        }
        workDetailVo.setCaseId(caseId);
        workDetailVo.setWorkOrderId(workOrderId);
        workDetailVo.setCaseTitle(fundingInfo.getTitle());

        long userId = fundingInfo.getUserId();
        if (userId > 0) {
            UserInfoModel userInfo = userInfoServiceBiz.getUserInfoByUserId(userId);
            if (userInfo != null) {
                String cryptoMobile = userInfo.getCryptoMobile();
                String phoneNum = shuidiCipher.decrypt(cryptoMobile);
                workDetailVo.setRaiserMobile(phoneNum);
                workDetailVo.setRaiserMobileMask(maskUtil.buildByDecryptPhone(phoneNum));
                String applicantName = userInfo.getRealName();
                if (StringUtils.isBlank(applicantName)) {
                    CrowdfundingAuthor crowdfundingAuthor = authorBiz.get(caseId);
                    applicantName = Optional.ofNullable(crowdfundingAuthor)
                            .map(CrowdfundingAuthor::getName).orElse("");
                }
                workDetailVo.setRaiserName(applicantName);
                workDetailVo.setRaiserMobile(null);
            }
        }

        workDetailVo.setPayeeName(fundingInfo.getPayeeName());
        workDetailVo.setPayeeMobile(null);
        workDetailVo.setPayeeMobileMask(maskUtil.buildByEncryptPhone(fundingInfo.getPayeeMobile()));

        Response<WorkOrderVO> workOrderVOResponse = cfWorkOrderClient.getWorkOrderById(workOrderId);
        if (workOrderVOResponse.notOk()) {
            return NewResponseUtil.makeFail("无该工单信息");
        }
        WorkOrderVO data = workOrderVOResponse.getData();
        if (data.getOrderType() != WorkOrderType.xiafaprogress.getType()) {
            return NewResponseUtil.makeFail("非下发动态工单");
        }
        List<CfInfoSupplyProgress> supplyProgresses = cfSupplyProgressBiz.listByIds(Lists.newArrayList(supplyProgressId));
        if (CollectionUtils.isEmpty(supplyProgresses)) {
            return NewResponseUtil.makeFail("无动态信息");
        }
        CfInfoSupplyProgress supplyProgress = supplyProgresses.get(0);
        CfInfoSupplyAction cfInfoSupplyAction = cfSupplyActionBiz.getById(supplyProgress.getProgressActionId());

        workDetailVo.setCfInfoSupplyAction(cfInfoSupplyAction);

        List<String> supplyReasons = convertSupplyReasons(cfInfoSupplyAction);

        workDetailVo.setSupplyReasons(supplyReasons);
        workDetailVo.setSupplyerName(getAdminUserName(cfInfoSupplyAction.getSupplyUserId()));

        //当前查询的下发信息+操作记录
        SupplyProgressRecord currentSupplyProgress = new SupplyProgressRecord();
        currentSupplyProgress.setCfInfoSupplyProgress(supplyProgress);
        //敏感词
        Set<String> sensitiveWords = riskDelegate.getHitWords(supplyProgress.getContent());
        currentSupplyProgress.setSensitiveWord(Joiner.on(",").join(sensitiveWords));

        if (pageSource == ViewPageSource.work_order_list) {
            //展示工单的处理状态
            SupplyProgressStatus contentStatus = CfInfoSupplyProgress.findByCode(supplyProgress.getContentStatus());
            SupplyProgressStatus imgStatus = CfInfoSupplyProgress.findByCode(supplyProgress.getImgStatus());
            if (contentStatus != SupplyProgressStatus.init || imgStatus != SupplyProgressStatus.init) {
                Response<List<WorkOrderRecordVO>> listResponse = cfWorkOrderRecordClient.listByWorkOrderId(workOrderId);
                //参考work-order中的OperateMode
                WorkOrderRecordVO workOrderRecordVO = listResponse.getData().stream().filter(item -> item.getOperateMode() == 4).findFirst().orElse(null);
                if (workOrderRecordVO == null) {
                    return NewResponseUtil.makeFail("工单记录信息缺失");
                }
                boolean rejectStatus = contentStatus == SupplyProgressStatus.reject || imgStatus == SupplyProgressStatus.reject;
                SupplyProgressStatusInfo progressStatusInfo = new SupplyProgressStatusInfo();
                progressStatusInfo.setAuditor(workOrderRecordVO.getOperatorName());
                progressStatusInfo.setRejectComment(workOrderRecordVO.getComment());
                progressStatusInfo.setAuditStatus(rejectStatus ? SupplyProgressStatus.reject.getCode() : SupplyProgressStatus.pass.getCode());
                progressStatusInfo.setOperateTime(workOrderRecordVO.getCreateTime());

                currentSupplyProgress.setSupplyProgressStatusInfo(progressStatusInfo);
            } else {
                log.info("工单列表页查看正在处理的工单,supplyProgressId:{}", supplyProgressId);
                return NewResponseUtil.makeFail("工单列表页不可查看正在处理的工单");
            }
        }

        List<SupplyProgressRecord> rejectRecords = Lists.newArrayList();

        //驳回信息查看
        //先查找到这个下发对应的所有被驳回的下发动态
        Map<Long, CfInfoSupplyProgress> progressId2InfoMap = cfSupplyProgressBiz.listBySupplyActionId(supplyProgress.getProgressActionId())
                .stream()
                .collect(Collectors.toMap(CfInfoSupplyProgress::getId, Function.identity()));

        List<OperationRecordDTO> operationRecordDTOS = commonOperationRecordClient.listByBizIdAndActionTypeEnums(supplyProgress.getProgressActionId(), Lists.newArrayList(OperationActionTypeEnum.SUPPLY_PROGRESS_REJECT));
        for (OperationRecordDTO operationRecordDTO : operationRecordDTOS) {
            SupplyProgressRecord rejectRecord = new SupplyProgressRecord();
            Map<String, String> extMap = operationRecordDTO.getExtMap();
            Long progressId = Long.valueOf(extMap.get(OPERATION_RECORD_EXT_PROGRESS_ID));
            String comment = extMap.get(OPERATION_RECORD_EXT_COMMENT);
            if (progressId == 0) {
                log.warn("驳回时记录未关联下发动态,supplyProgressId未设置值");
            }
            CfInfoSupplyProgress rejectProgress = progressId2InfoMap.get(progressId);
            rejectRecord.setCfInfoSupplyProgress(rejectProgress);
            Set<String> rejectProgressSensitiveWords = riskDelegate.getHitWords(supplyProgress.getContent());
            rejectRecord.setSensitiveWord(Joiner.on(",").join(rejectProgressSensitiveWords));

            SupplyProgressStatusInfo progressStatusInfo = new SupplyProgressStatusInfo();
            String adminUserName = getAdminUserName(operationRecordDTO.getOperatorId());
            progressStatusInfo.setAuditor(adminUserName);
            progressStatusInfo.setOperateTime(operationRecordDTO.getActionTime());
            progressStatusInfo.setRejectComment(comment);
            progressStatusInfo.setAuditStatus(SupplyProgressStatus.reject.getCode());
            rejectRecord.setSupplyProgressStatusInfo(progressStatusInfo);
            rejectRecords.add(rejectRecord);
        }

        Ordering<SupplyProgressRecord> orderByTimeDesc = Ordering.natural().reverse()
                .nullsFirst().onResultOf(item -> {
                    if (item != null) {
                        return item.getCfInfoSupplyProgress().getCreateTime();
                    }
                    return null;
                });
        List<SupplyProgressRecord> sortedRejectRecords =
                rejectRecords.stream().sorted(orderByTimeDesc).collect(Collectors.toList());
        workDetailVo.setRejectRecords(sortedRejectRecords);

        workDetailVo.setCurrentSupplyProgress(currentSupplyProgress);
        return NewResponseUtil.makeSuccess(workDetailVo);
    }


    //提供给工单列表页中使用
    public Map<Long, CfInfoSupplyProgress> getSupplyProgressList(List<QueryListResult> list, QueryListParam queryListParam) {
        if (!queryListParam.getOrderType().contains(String.valueOf(WorkOrderType.xiafaprogress.getType())) ||
                org.apache.commons.collections4.CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }
        return cfSupplyProgressBiz.listByIds(list.stream().filter(item -> item.getSupplyProgressId() > 0).map(QueryListResult::getSupplyProgressId).collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(CfInfoSupplyProgress::getId, Function.identity()));
    }


    //提供给工单中心使用
    public Map<Long, CfInfoSupplyProgress> getSupplyProgressList(List<WorkOrderVO> list, WorkOrderListParam param) {
        if (CollectionUtils.isEmpty(list) || param.getOrderType() != WorkOrderType.xiafaprogress.getType()) {
            return Maps.newHashMap();
        }
        return cfSupplyProgressBiz.listByIds(list.stream().filter(item -> item.getSupplyProgressId() > 0).map(WorkOrderVO::getSupplyProgressId).collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(CfInfoSupplyProgress::getId, Function.identity()));
    }


    //列表分页查询
    public Response<CfSupplyProgressDetailListVo> listSupplyActionSearchParam(SupplyActionSearchParam searchParam) {
        CfSupplyProgressDetailListVo detailListVo = new CfSupplyProgressDetailListVo();
        List<CfInfoSupplyAction> supplyActions = cfSupplyActionBiz.listBySearchParam(searchParam);
        PaginationListVO<CfInfoSupplyAction> pageResult = PaginationListVO.createWithList(supplyActions);
        Map<Integer, CrowdfundingInfo> cfId2CfInfo = adminCrowdfundingInfoBiz.getMapByIds(supplyActions.stream().map(CfInfoSupplyAction::getCaseId).collect(Collectors.toList()));
        List<CfSupplyProgressDetailListVo.CfSupplyProgressDetail> details = supplyActions.stream().map(
                item -> getCfSupplyProgressDetail(cfId2CfInfo, item)
        ).collect(Collectors.toList());
        detailListVo.setDetails(details);
        detailListVo.setPaginationVO(pageResult.getPagination());

        Response<Integer> allCount = cfWorkOrderClient.getAllCount(WorkOrderType.xiafaprogress.getType(), HandleResultEnum.undoing.getType());
        detailListVo.setUnHandleWorkNum(Optional.ofNullable(allCount.getData()).orElse(0));
        return NewResponseUtil.makeSuccess(detailListVo);
    }

    public List<CfInfoSupplyAction> getByWorkOrder(List<WorkOrderVO> workOrderVOS){
        if (CollectionUtils.isEmpty(workOrderVOS)){
            return Lists.newArrayList();
        }
        //老接口不返回新数据
        workOrderVOS = workOrderVOS.stream().filter(r->r.getSupplyProgressId()>0).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(workOrderVOS)){
            return Lists.newArrayList();
        }

        List<Long> ids = workOrderVOS.stream().map(WorkOrderVO::getSupplyProgressId).distinct().collect(Collectors.toList());

        List<CfInfoSupplyProgress> progresses = cfSupplyProgressBiz.listByIds(ids);

        Map<Long,CfInfoSupplyProgress> proMap = progresses.stream().collect(Collectors.toMap(CfInfoSupplyProgress::getId,Function.identity(),(o1,o2)->o2));

        List<Integer> userIds = workOrderVOS.stream().map(WorkOrderVO::getOperatorId).map(Long::intValue).distinct().collect(Collectors.toList());
        AuthRpcResponse<List<AdminUserAccountModel>> authRpcResponse = seaAccountClientV1.getUserAccountsByIds(userIds);

        List<AdminUserAccountModel> list = Optional.ofNullable(authRpcResponse)
                .filter(Objects::nonNull)
                .map(AuthRpcResponse::getResult)
                .orElse(Lists.newArrayList());

        Map<Integer,String> userMap = list.stream().collect(Collectors.toMap(AdminUserAccountModel::getId,AdminUserAccountModel::getName));

        Map<Integer, String> orgMap = orgService.getSimpleOrganizations(userIds);

        return workOrderVOS.stream().map(r->{

            CfInfoSupplyAction action = new CfInfoSupplyAction();
            int userId = Long.valueOf(r.getOperatorId()).intValue();
            action.setWorkOrderId(r.getWorkOrderId());
            action.setWorkOrderStatus(r.getHandleResult());
            CfInfoSupplyProgress p = proMap.get(r.getSupplyProgressId());
            if (p != null){
                action.setSupplyProgressId(p.getId());
                action.setInfoUUId(p.getInfoUUId());
                action.setCaseId(p.getCaseId());
            }
            action.setUpdateTime(r.getUpdateTime());
            action.setSupplyOrgName(orgMap.get(userId));
            action.setOperator(userMap.get(userId));

            return action;

        }).collect(Collectors.toList());

    }


    @NotNull
    private CfSupplyProgressDetailListVo.CfSupplyProgressDetail getCfSupplyProgressDetail(Map<Integer, CrowdfundingInfo> cfId2CfInfo, CfInfoSupplyAction item) {
        CfSupplyProgressDetailListVo.CfSupplyProgressDetail detail = new CfSupplyProgressDetailListVo.CfSupplyProgressDetail();
        detail.setCfInfoSupplyAction(item);
        //下发原因
        detail.setSupplyReasons(convertSupplyReasons(item));
        CrowdfundingInfo crowdfundingInfo = cfId2CfInfo.get(item.getCaseId());
        if (crowdfundingInfo != null) {
            detail.setInfoUUId(crowdfundingInfo.getInfoId());
            detail.setCaseTitle(crowdfundingInfo.getTitle());
            detail.setCaseStatus(crowdfundingInfo.getStatus().value());
        }
        detail.setSupplyOperatorName(getAdminUserName(item.getSupplyUserId()));

        List<CfInfoSupplyProgress> progressList = cfSupplyProgressBiz.listBySupplyActionId(item.getId());
        List<Long> progressIds = progressList.stream().map(CfInfoSupplyProgress::getId).collect(Collectors.toList());

        Response<List<WorkOrderVO>> listResponse = cfWorkOrderClient.listByCaseIdAndTypeAndResult(item.getCaseId(), Lists.newArrayList(WorkOrderType.xiafaprogress.getType()), Lists.newArrayList());
        if (listResponse.ok()) {
            Ordering<WorkOrderVO> sortByCreateTime = Ordering.natural().onResultOf(WorkOrderVO::getCreateTime);
            Optional<WorkOrderVO> firstWorkOrder = listResponse.getData().stream().filter(wv -> progressIds.contains(wv.getSupplyProgressId())).min(sortByCreateTime);
            firstWorkOrder.ifPresent(workOrderVO -> detail.setWorkCreateTime(workOrderVO.getCreateTime()));
        }

        Ordering<OperationRecordDTO> sortByCreateTime = Ordering.natural().reverse().onResultOf(OperationRecordDTO::getActionTime);
        Optional<OperationRecordDTO> lastOperatorInfo = commonOperationRecordClient.listByBizIdAndActionTypeEnums(item.getId(),
                Lists.newArrayList(OperationActionTypeEnum.SUPPLY_PROGRESS,
                        OperationActionTypeEnum.CANCEL_SUPPLY_PROGRESS,
                        OperationActionTypeEnum.SUPPLY_PROGRESS_REJECT,
                        OperationActionTypeEnum.SUPPLY_PROGRESS_PASS
                )).stream().min(sortByCreateTime);
        if (lastOperatorInfo.isPresent()) {
            OperationRecordDTO operationRecord = lastOperatorInfo.get();
            detail.setOperatorAndOrgName(operationRecord.getNameWithOrg());
            detail.setOptTime(operationRecord.getActionTime());
        }

        return detail;
    }

    //详情页中下发查看和操作能使用的前提
    private boolean canShowOrOperate(int caseId) {
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        if (crowdfundingInfo == null) {
            return false;
        }
        return crowdfundingInfo.getStatus() != CrowdfundingStatus.CROWDFUNDING_STATED;
    }


    private CfInfoSupplyAction buildActionInfo(int caseId, int adminUserId, AdminUserOrgInfo adminUserOrgInfo, String reasonIds, String comment) {
        CfInfoSupplyAction action = new CfInfoSupplyAction();
        action.setCaseId(caseId);
        action.setActionType(ActionType.progress.getCode());
        action.setHandleStatus(SupplyHandleStatus.init.getCode());
        action.setSupplyUserId(adminUserId);
        action.setSupplyOrgId(adminUserOrgInfo.getOrgId());
        Integer orgForSearch = orgTSearchOrgId.getOrDefault(adminUserOrgInfo.getOrgId(), SupplyOrgEnum.other.getCode());
        action.setOrgForSearch(orgForSearch);
        action.setSupplyOrgName(adminUserOrgInfo.getOrgName());
        action.setSupplyReason(reasonIds);
        action.setComment(comment);
        return action;
    }


    @Getter
    public enum ViewPageSource {
        my_work_order("我的工单"),
        work_order_list("查询工单"),
        ;
        String desc;

        ViewPageSource(String desc) {
            this.desc = desc;
        }
    }


    @Data
    public static class AdminUserOrgInfo {
        //组织id
        private int orgId;
        //组织名称
        private String orgName;
    }

    private AdminUserOrgInfo obtainOrgInfo(int adminUserId) {
        AdminUserOrgInfo adminUserOrgInfo = new AdminUserOrgInfo();
        AuthRpcResponse<AdminOrganization> userOrgInfo = organizationClientV1.getUserOrgInfo(adminUserId);

        if (userOrgInfo.isSuccess()) {
            adminUserOrgInfo.setOrgName(Optional.ofNullable(userOrgInfo.getResult()).map(AdminOrganization::getName).orElse(""));
            adminUserOrgInfo.setOrgId(Optional.ofNullable(userOrgInfo.getResult()).map(AdminOrganization::getId).orElse(0));
        } else {
            log.warn("获取org信息异常!userId:{}", adminUserId);
        }
        return adminUserOrgInfo;
    }

    private String getAdminUserName(int adminUserId) {
        String name = "";
        AuthRpcResponse<AdminUserAccountModel> accountResponse = seaAccountClientV1.getValidUserAccountById(adminUserId);
        if (accountResponse.isSuccess()) {
            AdminUserAccountModel accountResult = accountResponse.getResult();
            name = Optional.ofNullable(accountResult).map(AdminUserAccountModel::getName).orElse("");
        } else {
            log.warn("获取mis信息异常!userId:{}", adminUserId);
        }
        return name;
    }


    @Getter
    public static enum SpecialOrgEnum {
        //线上
        cailiaoshenhe_1(84, SupplyOrgEnum.cailiaoshenhe.getCode()),
        //线下
        cailiaoshenhe_2(87, SupplyOrgEnum.cailiaoshenhe.getCode()),
        //线上
        erxian_1(331, SupplyOrgEnum.erxian.getCode()),
        //线下
        erxian_2(143, SupplyOrgEnum.erxian.getCode()),
        ;


        private int orgId;
        private int orgIdForSearch;

        SpecialOrgEnum(int orgId, int orgIdForSearch) {
            this.orgId = orgId;
            this.orgIdForSearch = orgIdForSearch;
        }

    }

    private static Map<Integer, Integer> orgTSearchOrgId = Arrays.stream(SpecialOrgEnum.values()).collect(Collectors.toMap(SpecialOrgEnum::getOrgId, SpecialOrgEnum::getOrgIdForSearch));
}
