package com.shuidihuzhu.cf.service.workorder.read.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.biz.crowdfunding.report.AdminCfInfoLostContactService;
import com.shuidihuzhu.cf.biz.crowdfunding.report.CfReportOfficialLetterBiz;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingReportDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CfLabelRiskDAO;
import com.shuidihuzhu.cf.dao.crowdfunding.NewAdminCfFundUseAuditDao;
import com.shuidihuzhu.cf.delegate.finance.IFinanceDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.enhancer.model.response.EhResponse;
import com.shuidihuzhu.cf.enhancer.utils.EhResponseUtils;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.enums.report.ReportPayMethodEnum;
import com.shuidihuzhu.cf.finance.model.vo.CfDrawCashApplyVo;
import com.shuidihuzhu.cf.model.admin.ReportWorkOrderVO;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.report.CfReportOfficialLetter;
import com.shuidihuzhu.cf.model.report.schedule.ReportScheduleVO;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.report.ReportScheduleService;
import com.shuidihuzhu.cf.service.workorder.read.AdminJingxiSpotCheckHandler;
import com.shuidihuzhu.cf.service.workorder.read.AdminJingxiSpotCheckService;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminCrowdfundingProgress;
import com.shuidihuzhu.client.cf.workorder.*;
import com.shuidihuzhu.client.cf.workorder.helper.Von;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class AdminJingxiSpotCheckFirstReportHandler extends AdminJingxiSpotCheckHandler {

    @Autowired
    AdminJingxiSpotCheckService adminJingxiSpotCheckService;

    @Autowired
    private CfReportWorkOrderClient reportWorkOrderClient;

    @Autowired
    private AdminCrowdfundingReportBiz crowdfundingReportBiz;
    @Autowired
    private CrowdfundingFeignClient client;

    @Autowired
    private AdminCfRepeatInfoBiz repeatInfoBiz;

    @Autowired
    private AdminCrowdfundingReportDao adminCrowdfundingReportDao;

    @Resource
    private SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    private AdminCfInfoLostContactService lostContactService;

    @Autowired
    private IFinanceDelegate financeDelegate;

    @Autowired
    private AdminCrowdfundingReportBiz adminCrowdfundingReportBiz;

    @Autowired
    private CfLabelRiskDAO cfLabelRiskDAO;
    @Autowired
    private ApproveRemarkOldService approveRemarkOldService;

    @Autowired
    private AdminCfReportAddTrustBiz adminCfReportAddTrustBiz;

    @Autowired
    private CfReportOfficialLetterBiz cfReportOfficialLetterBiz;
    @Autowired
    private IAdminCredibleInfoService iAdminCredibleInfoService;

    @Autowired
    private ReportScheduleService reportScheduleService;

    @Resource
    private IRiskDelegate iRiskDelegate;
    @Autowired
    private NewAdminCfFundUseAuditDao adminCfFundUseAuditDao;


    @Override
    public Map<String, Object> getExtInfo(long workOrderId, int orderType) {
        final Response<BasicWorkOrder> orderResp = Von.read().getOrderBasicInfoById(workOrderId);
        final BasicWorkOrder basicWorkOrder = orderResp.getData();
        final List<WorkOrderExt> bizExtList = basicWorkOrder.getBizExtList();
        Map<String, String> extMap = adminJingxiSpotCheckService.getJingxiExtMap(bizExtList);
        if (extMap == null) {
            return Maps.newHashMap();
        }
        Response<List<WorkOrderVO>> workOrderVoRes = reportWorkOrderClient.queryReportByIds(Lists.newArrayList(workOrderId));
        if (ErrorCode.SUCCESS.getCode() != workOrderVoRes.getCode() || CollectionUtils.isEmpty(workOrderVoRes.getData())){
            return Maps.newHashMap();
        }
        List<ReportWorkOrderVO> reportWorkOrderList = build(workOrderVoRes.getData());
        ReportWorkOrderVO vo = reportWorkOrderList.get(0);
        Map<String, Object> extInfo = buildExtInfo(vo);
        return extInfo;
    }

    @Override
    public WorkOrderType getWorkOrderType() {
        return WorkOrderType.casefirstreport;
    }

    private List<ReportWorkOrderVO> build(List<WorkOrderVO> workOrderVOS){
        if(CollectionUtils.isEmpty(workOrderVOS)){
            return Lists.newArrayList();
        }

        List<Integer> caseIds = workOrderVOS.stream().map(WorkOrderVO::getCaseId).collect(Collectors.toList());
        FeignResponse<List<CrowdfundingInfo>> feignResponse = client.getCrowdfundingListById(caseIds);
        if (feignResponse.notOk() || CollectionUtils.isEmpty(feignResponse.getData())){
            return Lists.newArrayList();
        }
        Map<Integer, CfFirsApproveMaterial> firsApproveMaterialMap = iRiskDelegate.getMapByInfoIds(caseIds);
        Set<Integer> fundUseProgressIds = workOrderVOS.stream()
                .map(WorkOrderVO::getFundUseProgressId)
                .filter(integer -> Objects.requireNonNullElse(integer, 0) > 0)
                .collect(Collectors.toSet());

        Map<Integer, AdminCrowdfundingProgress> crowdfundingProgressMap = CollectionUtils.isNotEmpty(fundUseProgressIds) ?
                adminCfFundUseAuditDao.selectByProgressIdList(new ArrayList<>(fundUseProgressIds))
                        .stream()
                        .collect(Collectors.toMap(AdminCrowdfundingProgress::getId, Function.identity(), (x, y) -> x))
                : new HashMap<>();

        List<CrowdfundingInfo> caseList = feignResponse.getData();
        Map<Integer,CrowdfundingInfo> caseMap = caseList.stream().collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity()));

        List<ReportWorkOrderVO> reportWorkOrderVOS = workOrderVOS.stream().map(l -> {

            CrowdfundingInfo info = caseMap.get(l.getCaseId());
            List<CrowdfundingReport> reportList = crowdfundingReportBiz.getListByInfoId(l.getCaseId());
            List<Integer> reportIds = Lists.newArrayList();
            List<CrowdfundingReport> reports = Lists.newArrayList();
            List<CrowdfundingReportLabel> labelsList = Lists.newArrayList();
            if(StringUtils.isNotEmpty(l.getReportIds())){
                reportIds = Arrays.stream(l.getReportIds().split(",")).map(Integer::valueOf).collect(Collectors.toList());
                reports = adminCrowdfundingReportDao.getListByReportIds(reportIds);
                labelsList = adminCrowdfundingReportBiz.getReportLabels(reportIds);

            }
            Optional<CrowdfundingReport> r = reports.stream().filter(report -> 1 == report.getRealNameReport()).findFirst();
            boolean realNameReport = r.isPresent();


            String labels = labelsList.stream().map(CrowdfundingReportLabel::getReportLabel).map(CfReportTypeEnum::getDescFromCode).collect(Collectors.joining(","));

            AdminUserAccountModel userAccount = seaAccountClientV1.getValidUserAccountById(Integer.valueOf(String.valueOf(l.getOperatorId()))).getResult();

            int caseId = info.getId();
            CfDrawCashApplyVo drawCashApplyVo = financeDelegate.getApplyInfo(caseId).getData();

            int newReport = Objects.nonNull(l.getHandleTime()) && l.getHandleTime().before(new Date(new DateTime().dayOfMonth().roundFloorCopy().getMillis())) ? 0 : 1 ;

            CfLabelRiskDO labelRiskDO = cfLabelRiskDAO.query(l.getCaseId());
            boolean highRisk = Objects.nonNull(labelRiskDO) && labelRiskDO.getRiskType() == AdminWorkOrderReportConst.CaseRisk.HIGH_RISK.getCode();

            List<CrowdfundingApprove> approves = approveRemarkOldService.listByCaseId(l.getCaseId());
            CrowdfundingApprove lastApprove = CollectionUtils.isNotEmpty(approves) ? approves.get(approves.size() - 1) : null;

            CfReportAddTrust reportAddTrust = adminCfReportAddTrustBiz.getByInfoUuid(info.getInfoId());

            List<CfReportOfficialLetter> cfReportOfficialLetter = cfReportOfficialLetterBiz.getLastByCaseId(l.getCaseId());
            List<Integer> cfLetterStatus = null;
            if (CollectionUtils.isNotEmpty(cfReportOfficialLetter)) {
                cfLetterStatus = cfReportOfficialLetter.stream()
                        .map(CfReportOfficialLetter::getLetterStatus)
                        .distinct()
                        .collect(Collectors.toList());
            }

            CfCredibleInfoDO cfCredibleInfoDO = iAdminCredibleInfoService.getLastOneByCaseId(l.getCaseId(), CredibleTypeEnum.HELP_PROVE.getKey());

            ReportWorkOrderVO reportWorkOrderVO = new ReportWorkOrderVO();
            BeanUtils.copyProperties(l,reportWorkOrderVO);

            reportWorkOrderVO.setTitle(info.getTitle());
            reportWorkOrderVO.setAuditStatus(info.getStatus().ordinal());
            reportWorkOrderVO.setApplyStatus(Objects.nonNull(drawCashApplyVo) ? drawCashApplyVo.getApplyStatus() : CfDrawCashConstant.ApplyStatus.EMPTY_VALUE.getCode());
            reportWorkOrderVO.setCashStatus(Objects.nonNull(drawCashApplyVo) ? drawCashApplyVo.getDrawStatus() : CfDrawCashConstant.DrawStatus.EMPTY_VALUE.getCode());
            reportWorkOrderVO.setRepeatStatusList(repeatInfoBiz.selectRepeatStatusByCaseId(l.getCaseId()));
            reportWorkOrderVO.setIsNewReport(newReport);
            reportWorkOrderVO.setRealNameReport(realNameReport);
            reportWorkOrderVO.setReportNumber(CollectionUtils.isNotEmpty(reportList) ? reportList.size() : 0);
            reportWorkOrderVO.setLostStatus(lostContactService.hasLost(info.getInfoId()));
            reportWorkOrderVO.setOperatorId(l.getOperatorId());
            reportWorkOrderVO.setOperator(Objects.nonNull(userAccount) ? userAccount.getName() : "");
            if (CollectionUtils.isNotEmpty(reportList)){
                reportWorkOrderVO.setLastReportTime(reportList.get(reportList.size()-1).getCreateTime());
                reportWorkOrderVO.setFirstReportTime(reportList.get(0).getCreateTime());
            }
            reportWorkOrderVO.setCaseUuid(info.getInfoId());
            reportWorkOrderVO.setReportLabels(labels);
            reportWorkOrderVO.setHighRisk(highRisk);
            reportWorkOrderVO.setLastApproveComment(Objects.nonNull(lastApprove) ? lastApprove.getComment() : "");
            reportWorkOrderVO.setLastApproveTime(Objects.nonNull(lastApprove) ? lastApprove.getOprtime() : null);
            reportWorkOrderVO.setSupplyAuditStatus(Objects.nonNull(reportAddTrust) ? reportAddTrust.getAuditStatus() : CrowdfundingInfoStatusEnum.UN_SAVE.getCode());
            if (CollectionUtils.isNotEmpty(cfLetterStatus)){
                reportWorkOrderVO.setLetterStatus(cfLetterStatus);
            }
            if (Objects.nonNull(cfCredibleInfoDO)) {
                reportWorkOrderVO.setProveStatus(cfCredibleInfoDO.getAuditStatus());
            }

            EhResponse<ReportScheduleVO> scheduleResp = reportScheduleService.getByCaseId(caseId);
            if (EhResponseUtils.isOk(scheduleResp)) {
                ReportScheduleVO scheduleVO = scheduleResp.getData();
                if (scheduleVO != null) {
                    reportWorkOrderVO.setScheduleTime(scheduleVO.getTargetTime());
                }
            }
            ReportPayMethodEnum payMethodEnum = reportScheduleService.getPayMethodByCaseId(caseId);
            reportWorkOrderVO.setPayMethod(Optional.ofNullable(payMethodEnum).map(ReportPayMethodEnum::getCode).orElse(0));

            return reportWorkOrderVO;
        }).collect(Collectors.toList());

        return reportWorkOrderVOS;

    }

    private Map<String, Object> buildExtInfo(ReportWorkOrderVO vo) {
        Map<String, Object> extInfo = Maps.newHashMap();
        // 下次跟进时间
        extInfo.put("nextFollowTime", vo.getScheduleTime());
        // 打款方式
        extInfo.put("payMethod", Optional.ofNullable(ReportPayMethodEnum.parse(vo.getPayMethod()))
                .map(ReportPayMethodEnum::getMsg).orElse(StringUtils.EMPTY));
        // 举报组最近一次操作备注
        extInfo.put("lastReportRemark", vo.getLastReportRemark());
        // 重复二次发起
        extInfo.put("repeatStatusList", Optional.ofNullable(vo.getRepeatStatusList()).orElse(new HashSet<>()).stream()
                .map(AdminCfRepeatView.RepeatReasonView::getReasonByCode)
                .map(AdminCfRepeatView.RepeatReasonView::getDesc)
                .collect(Collectors.toList()));
        // 举报类型
        extInfo.put("reportLabels", vo.getReportLabels());
        // 提现/打款状态
        extInfo.put("cashStatus", Optional.ofNullable(CfDrawCashConstant.DrawStatus.getByCode(vo.getCashStatus()))
                .map(CfDrawCashConstant.DrawStatus::getMsg).orElse(StringUtils.EMPTY));
        // 代录入状态
        extInfo.put("proveStatus", Optional.ofNullable(AddTrustAuditStatusEnum.getByCode(vo.getProveStatus()))
                .map(AddTrustAuditStatusEnum::getMsg).orElse(StringUtils.EMPTY));
        // 补充证明状态、材料审核状态
        extInfo.put("supplyAuditStatus", Optional.ofNullable(CrowdfundingInfoStatusEnum.getByCode(vo.getSupplyAuditStatus()))
                .map(CrowdfundingInfoStatusEnum::getMsg).orElse(StringUtils.EMPTY));
        // 公函状态
        extInfo.put("letterStatus", Optional.ofNullable(vo.getLetterStatus()).orElse(new ArrayList<>()).stream()
                .map(CfReportOfficialLetterStatusEnum::getByCode)
                .map(CfReportOfficialLetterStatusEnum::getDesc)
                .collect(Collectors.toList()));
        // 是否有新举报
        extInfo.put("isNewReport", vo.getIsNewReport());
        // 是否有实名举报
        extInfo.put("realNameReport", vo.isRealNameReport());
        // 案例被举报次数
        extInfo.put("reportNumber", vo.getReportNumber());
        // 风险等级
        extInfo.put("highRisk", vo.isHighRisk());
        return extInfo;
    }

}
