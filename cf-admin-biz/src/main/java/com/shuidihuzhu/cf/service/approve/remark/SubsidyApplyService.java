package com.shuidihuzhu.cf.service.approve.remark;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.dao.approve.CfServiceSubsidyRecordDao;
import com.shuidihuzhu.cf.delegate.saas.*;
import com.shuidihuzhu.cf.domain.approve.SubsidyApplyRecordDO;
import com.shuidihuzhu.cf.vo.crowdfunding.SubsidyApplyRecordVO;
import com.shuidihuzhu.common.web.util.ContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description: 服务费申请记录
 * @Author: panghairui
 * @Date: 2023/11/3 3:29 PM
 */
@Slf4j
@Service
public class SubsidyApplyService {

    @Resource
    private CfServiceSubsidyRecordDao cfServiceSubsidyRecordDao;
    @Resource
    private SeaAccountClientV1 seaAccountClientV1;

    public List<SubsidyApplyRecordVO> listSubsidyApplyRecords(Integer caseId) {
        List<SubsidyApplyRecordDO> subsidyApplyRecordDOS = cfServiceSubsidyRecordDao.selectRecordByCaseId(caseId);
        if (CollectionUtils.isEmpty(subsidyApplyRecordDOS)) {
            return Lists.newArrayList();
        }

        return buildVO(subsidyApplyRecordDOS);
    }

    private List<SubsidyApplyRecordVO> buildVO(List<SubsidyApplyRecordDO> subsidyApplyRecordDOS) {

        List<Integer> operatorIdList = subsidyApplyRecordDOS.stream()
                .map(SubsidyApplyRecordDO::getOperatorId)
                .map(f -> Integer.parseInt(String.valueOf(f)))
                .collect(Collectors.toList());
        List<AdminUserAccountModel> adminUserAccountModels = Optional.ofNullable(seaAccountClientV1.getUserAccountsByIds(operatorIdList))
                .filter(AuthRpcResponse::isSuccess)
                .map(AuthRpcResponse::getResult)
                .orElse(Lists.newArrayList());
        if (CollectionUtils.isEmpty(adminUserAccountModels)) {
            return Lists.newArrayList();
        }

        Map<Integer, String> adminUserAccountModelMap = adminUserAccountModels.stream().collect(Collectors.toMap(AdminUserAccountModel::getId, AdminUserAccountModel::getName));

        return subsidyApplyRecordDOS.stream().map(subsidyDO -> {
            return SubsidyApplyRecordVO.builder()
                    .auditResult(subsidyDO.getAuditResult())
                    .workOrderId(subsidyDO.getWorkOrderId())
                    .handleRemark(subsidyDO.getHandleRemark())
                    .operatorId(subsidyDO.getOperatorId())
                    .createTime(subsidyDO.getCreateTime())
                    .organization(subsidyDO.getOrganization())
                    .operatorName(Optional.ofNullable(adminUserAccountModelMap.get((int) subsidyDO.getOperatorId())).orElse(""))
                    .caseId(subsidyDO.getCaseId())
                    .build();
        }).collect(Collectors.toList());
    }

}
