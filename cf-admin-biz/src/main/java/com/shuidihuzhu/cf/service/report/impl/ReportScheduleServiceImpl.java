package com.shuidihuzhu.cf.service.report.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonContent;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecord;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecordClient;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.constants.admin.QcConst;
import com.shuidihuzhu.cf.dao.report.ReportScheduleDAO;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.enhancer.enums.EnumUtils;
import com.shuidihuzhu.cf.enhancer.exception.ServiceResponseException;
import com.shuidihuzhu.cf.enhancer.model.response.EhResponse;
import com.shuidihuzhu.cf.enhancer.model.response.EnhancerErrorCode;
import com.shuidihuzhu.cf.enhancer.mq.MQHelperService;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.enhancer.utils.EhResponseUtils;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportOfficialLetterStatusEnum;
import com.shuidihuzhu.cf.enums.report.ReportPayMethodEnum;
import com.shuidihuzhu.cf.model.admin.WashPaymentMethod;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.report.CfReportOfficialLetter;
import com.shuidihuzhu.cf.model.report.schedule.ReportScheduleDO;
import com.shuidihuzhu.cf.model.report.schedule.ReportSchedulePayload;
import com.shuidihuzhu.cf.model.report.schedule.ReportScheduleVO;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.report.CfReportOfficialLetterService;
import com.shuidihuzhu.cf.service.report.ReportScheduleService;
import com.shuidihuzhu.cf.service.stream.StreamBizService;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderTypeFeignClient;
import com.shuidihuzhu.client.cf.workorder.ext.WorkOrderExtVO;
import com.shuidihuzhu.client.cf.workorder.helper.Von;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OneTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.read.WorkOrderReadFeignClient;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RefreshScope
public class ReportScheduleServiceImpl implements ReportScheduleService {

    @Resource
    private ReportScheduleDAO reportScheduleDAO;

    @Resource
    private CfWorkOrderClient cfWorkOrderClient;

    @Resource
    private WorkOrderReadFeignClient workOrderReadFeignClient;

    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Resource
    private SeaAccountDelegate seaAccountDelegate;

    @Resource
    private MQHelperService mqHelperService;

    @Resource
    private StreamBizService streamService;

    @Autowired
    private WonRecordClient wonRecordClient;

    @Autowired
    private ApproveRemarkOldService approveRemarkOldService;

    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    private CfReportOfficialLetterService cfReportOfficialLetterService;

    @Autowired
    private CfWorkOrderTypeFeignClient cfWorkOrderTypeFeignClient;

    @Value("${apollo.report-schedule.min-delay-minutes:15}")
    private int minDelayMinutes;

    @Value("${apollo.report-schedule.min-delay-minutes:15}")
    private int pageShowMinutes;

    @Value("${apollo.report-schedule.min-delay-minutes:10}")
    private int wxPushMinutes;

    @Value("${apollo.report-schedule.group-leader:}")
    private String groupLeader;

    private static final SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private static final long ACTION_PAY_METHOD = 105;

    private static final int PUSH_TYPE_PAGE = 1;
    private static final int PUSH_TYPE_WX = 2;
    private static final int NEED_SOLVE = 1;
    private int actionId;

    @Autowired
    private ApplicationService applicationService;

    @Override
    public EhResponse<Void> add(int adminUserId, int caseId, Date targetTime) {
        EhResponse<Void> timeCheck = checkTime(targetTime);
        if (timeCheck.notOk()) {
            return EhResponseUtils.makeRelayFail(timeCheck);
        }
        ReportScheduleDO d = new ReportScheduleDO();
        d.setOperatorId(adminUserId);
        d.setTargetTime(targetTime);
        d.setVersion(System.currentTimeMillis());
        d.setCaseId(caseId);
        reportScheduleDAO.insert(d);
        long id = d.getId();
        sendDelayPush(id, d.getVersion(), targetTime.getTime());
        return EhResponseUtils.success();
    }

    @Override
    public EhResponse<Void> update(int adminUserId, int id, Date targetTime) {
        EhResponse<Void> checkRes = check(id, adminUserId);
        if (checkRes.notOk()) {
            return EhResponseUtils.makeRelayFail(checkRes);
        }
        EhResponse<Void> timeCheck = checkTime(targetTime);
        if (timeCheck.notOk()) {
            return EhResponseUtils.makeRelayFail(timeCheck);
        }
        long version = System.currentTimeMillis();
        int res = reportScheduleDAO.updateTargetTimeById(id, targetTime, version);
        boolean success = res > 0;
        if (success) {
            sendDelayPush(id, version, targetTime.getTime());
        }
        return success ? EhResponseUtils.success() : EhResponseUtils.fail(AdminErrorCode.SYSTEM_ERROR);
    }

    @Override
    public EhResponse<Void> remove(int adminUserId, int id) {
        EhResponse<Void> checkRes = check(id, adminUserId);
        if (checkRes.notOk()) {
            return EhResponseUtils.makeRelayFail(checkRes);
        }
        int res = reportScheduleDAO.removeById(id);
        return res > 0 ? EhResponseUtils.success() : EhResponseUtils.fail(AdminErrorCode.SYSTEM_ERROR);
    }

    @Override
    public EhResponse<ReportScheduleVO> getByCaseId(int caseId) {
        ReportScheduleDO d = reportScheduleDAO.getLastByCaseId(caseId);
        return EhResponseUtils.success(do2vo(d));
    }

    @Override
    public EhResponse<Void> done(int adminUserId, int id) {
        EhResponse<Void> checkRes = check(id, adminUserId);
        if (checkRes.notOk()) {
            return EhResponseUtils.makeRelayFail(checkRes);
        }
        int res = reportScheduleDAO.doneById(id);
        return res > 0 ? EhResponseUtils.success() : EhResponseUtils.fail(AdminErrorCode.SYSTEM_ERROR);
    }

    /**
     * 根据人 查处理中所有工单 拿到案例。（这个过程需要限制查询数量。）
     * 再根据案例查所有待处理的跟进。
     * 展示跟进列表。
     * @param adminUserId
     * @return
     */
    @Override
    public EhResponse<List<ReportScheduleVO>> getListByOperatorId(int adminUserId) {
        QueryListParam p = new QueryListParam();
        p.setOperId(adminUserId);
        String types = WorkOrderType.REPORT_TYPES.stream().map(String::valueOf).collect(Collectors.joining(","));
        p.setOrderType(types);
        p.setHandleResult(HandleResultEnum.doing.getType());
        p.setPageSize(100);
        Response<PageResult<QueryListResult>> workOrderListResp = cfWorkOrderClient.getWorkOrderList(p);
        if (NewResponseUtil.isNotOk(workOrderListResp)) {
            return EhResponseUtils.fail(AdminErrorCode.SYSTEM_ERROR);
        }
        Optional<List<QueryListResult>> listOptional = Optional.ofNullable(workOrderListResp.getData()).map(PageResult::getPageList);
        if (listOptional.isEmpty()) {
            return EhResponseUtils.success(Lists.newArrayList());
        }
        List<QueryListResult> resultList = listOptional.get();
        if (CollectionUtils.isEmpty(resultList)) {
            return EhResponseUtils.success(Lists.newArrayList());
        }
        List<Integer> caseIdList = resultList.stream().map(WorkOrderVO::getCaseId).collect(Collectors.toList());

//        long targetTimeStart = System.currentTimeMillis() - (pageShowMinutes * 60 * 1000L);
        long targetTimeEnd = System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(16);
        List<ReportScheduleDO> list = reportScheduleDAO.getListByCaseIdList(caseIdList, new Date(targetTimeEnd));
        if (CollectionUtils.isEmpty(list)) {
            return EhResponseUtils.success(Lists.newArrayList());
        }
        List<ReportScheduleVO> vos = list.stream().map(this::do2vo)
                .sorted(Comparator.comparing(ReportScheduleVO::getTargetTime))
                .collect(Collectors.toList());
        return EhResponseUtils.success(vos);
    }

    @Override
    public void onDelayHandle(String json) {
        ReportSchedulePayload payload = JSON.parseObject(json, ReportSchedulePayload.class);
        long id = payload.getId();
        long version = payload.getVersion();
        int type = payload.getType();
        ReportScheduleDO d = reportScheduleDAO.getById(id);

        log.info("onDelayHandle 发送通知 payload {}, data {}", payload, d);
        if (d == null) {
            log.info("onDelayHandle null 已经被删除或已过期 payload {}, data {}", payload, d);
            return;
        }
        int caseId = d.getCaseId();
        if (d.getVersion() != version){
            log.info("onDelayHandle version 事件版本不一致 payload {}, data {}", payload, d);
            return;
        }
        if (d.isDone()){
            log.info("onDelayHandle isDone 已标记处理 payload {}, data {}", payload, d);
            return;
        }

        Response<WorkOrderVO> workOrderRes = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, WorkOrderType.REPORT_TYPES);
        if (workOrderRes == null || workOrderRes.notOk()) {
            throw ServiceResponseException.create("rpc fail", EnhancerErrorCode.RPC_FAIL);
        }
        long operatorId = Optional.ofNullable(workOrderRes.getData()).map(WorkOrderVO::getOperatorId).orElse(0L);

        if (type == PUSH_TYPE_PAGE) {
            streamService.pushReportSchedulePageShow(operatorId);
        }
        if (type == PUSH_TYPE_WX) {
            sendWxNotice(d, operatorId);
        }
    }

    @Override
    public Response<Void> markPayMethod(int caseId, int payMethod, long operatorId, int legalLetter) {

        if (legalLetter == NEED_SOLVE) {

            if(operatorId <= 0L){
                return EhResponseUtils.fail(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }

            AdminUserAccountModel userModel = seaAccountClientV1.getValidUserAccountByLongId(operatorId).getResult();
            String userName = userModel.getName();

            CfReportOfficialLetter cfReportOfficialLetter = new CfReportOfficialLetter();
            cfReportOfficialLetter.setCaseId(caseId);
            cfReportOfficialLetter.setName(userName);
            cfReportOfficialLetter.setLetterStatus(CfReportOfficialLetterStatusEnum.WAIT_LETTER.getCode());
            cfReportOfficialLetter.setLetterType("");
            cfReportOfficialLetter.setComment("");
            cfReportOfficialLetter.setImages("");
            cfReportOfficialLetter.setNum("");

            cfReportOfficialLetterService.addOfficialLetter(cfReportOfficialLetter, (int) operatorId);
        }

        boolean valid = EnumUtils.isValidEnum(ReportPayMethodEnum.class, payMethod);
        if (!valid) {
            return EhResponseUtils.fail(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        ReportPayMethodEnum methodEnum = EnumUtils.getEnumByCode(ReportPayMethodEnum.class, payMethod);
        if (ReportPayMethodEnum.shouldAddRemark(methodEnum)){
            approveRemarkOldService.add(caseId, (int) operatorId, "举报组标记案例打款方式：", methodEnum.getMsg());
        }
        wonRecordClient.create()
                .buildBasic(caseId, ACTION_PAY_METHOD)
                .buildData(payMethod)
                .save();

        saveWorkExt(caseId, payMethod);

        return EhResponseUtils.success();
    }

    @Override
    public ReportPayMethodEnum getPayMethodByCaseId(int caseId) {
        OperationResult<WonRecord> resp = wonRecordClient.getLastByBizId(caseId, ACTION_PAY_METHOD);
        if (resp == null || resp.isFail()) {
            throw ServiceResponseException.create("rpc fail", ErrorCode.SYSTEM_ERROR);
        }
        WonRecord data = resp.getData();
        if (data == null) {
            return null;
        }
        Integer payMethod = data.getData(Integer.class);
        return ReportPayMethodEnum.parse(payMethod);
    }

    @Override
    public Response<Integer> washTheData(List<WashPaymentMethod> dataList) {
        for (WashPaymentMethod washPaymentMethod : dataList) {
            if (Objects.isNull(washPaymentMethod) ||
                    Objects.isNull(washPaymentMethod.getCaseId()) ||
                    washPaymentMethod.getCaseId() == 0 ||
                    StringUtils.isEmpty(washPaymentMethod.getPaymentMethodJson())) {
                continue;
            }
            WonContent content = JSON.parseObject(washPaymentMethod.getPaymentMethodJson(), WonContent.class);
            Map<String, Object> extMap = content.getExtMap();
            Object o = extMap.get("d");
            int x = JSON.parseObject(JSON.toJSONString(o), Integer.class);
            saveWorkExt(washPaymentMethod.getCaseId(), x);
        }
        return NewResponseUtil.makeSuccess(0);
    }

    private void sendWxNotice(ReportScheduleDO d, long operatorId) {
        ReportScheduleVO vo = do2vo(d);
        String temp = "【举报案例跟进提醒】\n" +
                "\n" +
                "【案例ID】：%d\n" +
                "\n" +
                "【举报工单类型】：%s\n" +
                "\n" +
                "【案例工单状态】：%s\n" +
                "\n" +
                "【约定跟进时间】%s";
        String orderTypeMsg = "";
        Response<WorkOrderTypeRecord> resp = cfWorkOrderTypeFeignClient.getByOrderTypeCode(vo.getOrderType());
        if(resp.ok() && resp.getData() != null) {
            orderTypeMsg = resp.getData().getMsg();
        }
        String orderResultMsg = Optional.ofNullable(HandleResultEnum.getFromType(vo.getOrderResult())).map(HandleResultEnum::getShowMsg).orElse("");
        String targetTimeMsg = dateTimeFormat.format(d.getTargetTime());
        String content = String.format(temp, d.getCaseId(), orderTypeMsg, orderResultMsg, targetTimeMsg);
        String[] leaderList = StringUtils.split(groupLeader, ",");

        ArrayList<String> noticer = Lists.newArrayList(leaderList);
        if(operatorId > 0) {
            String misByUserId = seaAccountDelegate.getMisByUserId(Math.toIntExact(operatorId));
            noticer.add(misByUserId);
        }
        String[] noticerArr = noticer.toArray(String[]::new);
        String key ;
        if (applicationService.isProduction()){
            key = "a39f392d-0c7c-4239-a8ab-5c161d7b817e";
        } else {
            key = "f8b23bfb-e6b4-423f-96a5-31f3ec4a425b";
        }
        AlarmBotService.sentText(key, content, noticerArr, null);
    }

    private void sendDelayPush(long id, long version, long time){
        push(id, version, PUSH_TYPE_PAGE, time - (pageShowMinutes * 60 * 1000L));
        push(id, version, PUSH_TYPE_WX, time - (wxPushMinutes * 60 * 1000L));
    }

    private void push(long id, long version, int type, long targetTime) {
        ReportSchedulePayload p = new ReportSchedulePayload();
        p.setId(id);
        p.setType(type);
        p.setVersion(version);
        mqHelperService.builder()
                .setTags(MQTagCons.REPORT_SCHEDULE_PUSH)
                .addKey(MQTagCons.REPORT_SCHEDULE_PUSH, id, System.currentTimeMillis())
                .setTargetTime(targetTime)
                .setPayload(JSON.toJSONString(p))
                .send();
    }

    private ReportScheduleVO do2vo(ReportScheduleDO d) {
        if (d == null) {
            return null;
        }
        ReportScheduleVO v = new ReportScheduleVO();
        BeanUtils.copyProperties(d, v);
        int caseId = d.getCaseId();
        Response<WorkOrderVO> workOrderRes = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, WorkOrderType.REPORT_TYPES);
        if (workOrderRes == null || workOrderRes.notOk()) {
            throw ServiceResponseException.create("rpc fail", EnhancerErrorCode.RPC_FAIL);
        }
        WorkOrderVO order = workOrderRes.getData();
        if (order != null) {
            v.setOrderResult(order.getHandleResult());
            v.setOrderType(order.getOrderType());
        }

        CrowdfundingInfo fundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        v.setCaseTitle(fundingInfo.getTitle());
        int operatorId = d.getOperatorId();
        String name = seaAccountDelegate.getNameByUserId(operatorId);
        v.setOperatorName(name);

        return v;
    }

    private EhResponse<Void> check(int id, int adminUserId) {
        ReportScheduleDO d = reportScheduleDAO.getById(id);
        if (d == null) {
            return EhResponseUtils.fail(AdminErrorCode.REPORT_SCHEDULE_TARGET_TIME_PASSED);
        }
/*
        if (d.getOperatorId() != adminUserId) {
            return EhResponseUtils.fail(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
*/
        return EhResponseUtils.success();
    }

    private EhResponse<Void> checkTime(Date targetTime) {
        long now = System.currentTimeMillis();
        long target = targetTime.getTime();
        return target - now > minDelayMinutes * 60 * 1000L ? EhResponseUtils.success() :
                EhResponseUtils.failWithMessage("请选择" + minDelayMinutes + "分钟以后的时间");
    }

    private void saveWorkExt(int caseId, int payMethod) {
        Response<List<Integer>> listResponse = cfWorkOrderTypeFeignClient.getByOneLevel(OneTypeEnum.cailiao.getType());
        List<Integer> types = Optional.ofNullable(listResponse).filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());
        if(CollectionUtils.isEmpty(types)) {
            return;
        }
        Response<List<WorkOrderVO>> response = cfWorkOrderClient.listByCaseIdAndTypeAndResult(caseId, types, Lists.newArrayList());
        List<WorkOrderVO> workOrderVOList = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());
        if (CollectionUtils.isEmpty(workOrderVOList)) {
            return;
        }
        List<Long> workOrderIds = workOrderVOList.stream().map(WorkOrderVO::getWorkOrderId).collect(Collectors.toList());

        for (Long workOrderId : workOrderIds) {
            Response<WorkOrderExtVO> voResponse = Von.extUpdate().getLastByName(workOrderId, QcConst.OrderExt.payMethod);
            WorkOrderExtVO workOrderExtVO = Optional.ofNullable(voResponse).filter(Response::ok).map(Response::getData).orElse(null);
            if (Objects.isNull(workOrderExtVO)) {
                Von.extUpdate().saveByList(workOrderId, Lists.newArrayList(WorkOrderExt.create(workOrderId, QcConst.OrderExt.payMethod, payMethod)));
            } else {
                Von.extUpdate().updateByNameValue(workOrderId, QcConst.OrderExt.payMethod, String.valueOf(payMethod));
            }
        }

    }
}
