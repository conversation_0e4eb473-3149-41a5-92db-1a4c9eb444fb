package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Stopwatch;
import com.google.common.collect.*;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.admin.AdminOrganizationBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.workflow.StaffStatusBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.workflow.WorkFlowAutoAssignRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.workflow.WorkFlowFreeRecordBiz;
import com.shuidihuzhu.cf.dao.admin.AdminWorkOrderFlowDao;
import com.shuidihuzhu.cf.model.admin.AdminOrganization;
import com.shuidihuzhu.cf.model.admin.AdminOrganizationUserMap;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowStatistics;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView;
import com.shuidihuzhu.cf.model.crowdfunding.vo.workflow.WorkFlowOrgStaffVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.workflow.WorkFlowStaffVo;
import com.shuidihuzhu.cf.model.crowdfunding.workflow.StaffStatusAndNum;
import com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowFreeRecord;
import com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowStaffStatus;
import com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowStaffStatusRecord;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.pf.common.v2.model.pagehelper.PaginationListVO;
import com.shuidihuzhu.pf.common.v2.model.pagehelper.PaginationVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst.Status.CREATED;
import static com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst.Status.HANDLING;

/**
 * @author: fengxuan
 * @create 2020-02-14 11:47
 **/
@Slf4j
@Service
public class WorkFlowStaffService {

    @Autowired
    private StaffStatusBiz staffStatusBiz;

    @Autowired
    private AdminOrganizationBiz organizationBiz;

    @Autowired
    private AdminWorkOrderFlowBiz orderFlowBiz;

    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private AdminWorkOrderFlowDao adminWorkOrderFlowDao;

    @Autowired
    private WorkFlowFreeRecordBiz freeRecordBiz;
    @Autowired
    private WorkFlowAutoAssignRecordBiz assignRecordBiz;

    public static BiMap<Integer, Integer> orgIdByOrgType = HashBiMap.create();


    @PostConstruct
    public void init() {
        //设置组织映射
        if (applicationService.isDevelopment()) {
            orgIdByOrgType.put(WorkFlowStaffStatus.OrgTypeEnum.er_xian.getCode(), 152);
            orgIdByOrgType.put(WorkFlowStaffStatus.OrgTypeEnum.report.getCode(), 90);
            orgIdByOrgType.put(WorkFlowStaffStatus.OrgTypeEnum.zi_jin.getCode(), 93);

        } else {
            orgIdByOrgType.put(WorkFlowStaffStatus.OrgTypeEnum.er_xian.getCode(), 26);
            orgIdByOrgType.put(WorkFlowStaffStatus.OrgTypeEnum.report.getCode(), 4);
            orgIdByOrgType.put(WorkFlowStaffStatus.OrgTypeEnum.zi_jin.getCode(), 11);
        }
    }


    public Response<Void> changeStatus(long adminUserId, long operatedId, int status) {
        List<AdminOrganizationUserMap> userMapList = organizationBiz.getLowestOrgByUserIds(Lists.newArrayList((int) adminUserId, (int) operatedId));
        /**
         * 1、操作人和登录人一致
         * 2、要变更的操作为上线
         */
        if (operatedId == adminUserId && status == WorkFlowStaffStatus.StaffStatusEnum.offline.getCode()) {
            //组长数据
            Optional<AdminOrganizationUserMap> managerOpn = userMapList.stream()
                    .filter(item -> item.getUserId() == adminUserId && item.getIsManager() == 1)
                    .findFirst();
            //非组长----每日离线次数为两次
            if (!managerOpn.isPresent()) {
                Map<Long, List<WorkFlowStaffStatusRecord>> todayRecords = staffStatusBiz.listTodayRecord(Lists.newArrayList(operatedId));
                long todayOfflineCount = todayRecords.getOrDefault(operatedId, Lists.newArrayList())
                        .stream()
                        .filter(item -> item.getStaffStatus() == WorkFlowStaffStatus.StaffStatusEnum.offline.getCode())
                        .count();
                if (todayOfflineCount >= 2) {
                    return NewResponseUtil.makeFail("今日离线次数超过2次");
                }
            }
        }

        WorkFlowStaffStatus staffStatus = staffStatusBiz.findByUserId(operatedId);
        WorkFlowStaffStatus flowStaffStatus = new WorkFlowStaffStatus();
        flowStaffStatus.setOperatorId(adminUserId);
        int optType = operatedId == adminUserId ? WorkFlowStaffStatus.OptTypeEnum.self.getCode()
                : WorkFlowStaffStatus.OptTypeEnum.other.getCode();
        //查询被操作人的组织
        Optional<Integer> orgIdOpt = userMapList.stream()
                .filter(item -> item.getUserId() == operatedId && orgIdByOrgType.containsValue(item.getOrgId()))
                .map(AdminOrganizationUserMap::getOrgId)
                .findFirst();
        //查询对应的组织
        if (!orgIdOpt.isPresent()) {
            log.warn("找不到对应的组织关系");
        }
        Integer orgId = orgIdOpt.orElse(0);
        flowStaffStatus.setOrgType(orgIdByOrgType.inverse().getOrDefault(orgId, WorkFlowStaffStatus.OrgTypeEnum.other.getCode()));
        flowStaffStatus.setOptType(optType);
        flowStaffStatus.setUserId(operatedId);
        flowStaffStatus.setStaffStatus(status);

        if (staffStatus == null) {
            staffStatusBiz.add(flowStaffStatus);
        } else {
            if (staffStatus.getStaffStatus() != status) {
                //查询对应的组织
                staffStatusBiz.changeStatus(flowStaffStatus);
            } else {
                log.info("getStaffStatus 与 status:{} 一致",status);
            }
        }

        // 上线的时候触发 工单的自动分配
        if (status == WorkFlowStaffStatus.StaffStatusEnum.online.getCode()) {
            assignRecordBiz.assignWorkTUser(Long.valueOf(operatedId).intValue());
        }
        return NewResponseUtil.makeSuccess(null);
    }

    public List<WorkFlowOrgStaffVo> listOrgStaffStatus() {

        List<StaffStatusAndNum> staffStatusAndNums = staffStatusBiz.groupByStatusAndOrgType(DateTime.now().withTimeAtStartOfDay().toDate());
        //每组对应的人数
        List<AdminOrganizationUserMap> orgUserByOrgIds = organizationBiz.getOrgUserByOrgIds(Lists.newArrayList(orgIdByOrgType.values()));
        List<AdminWorkOrderFlowBiz.OrgUnHandleOrder> orgUnHandleOrders = orderFlowBiz.countUnHandleByOrgId(Lists.newArrayList(orgIdByOrgType.values()));

        int allOnline = 0;
        int allOffline = 0;
        int allPauseLine = 0;
        int allUnAllot = 0;
        //获取每个特殊组织对应的人数
        List<WorkFlowOrgStaffVo> workFlowOrgStaffVoList = Lists.newArrayList();
        for (Integer orgType : orgIdByOrgType.keySet()) {
            WorkFlowOrgStaffVo flowOrgStaffVo = new WorkFlowOrgStaffVo();
            // 组织内的总人数
            long orgTypeMembers = orgUserByOrgIds.stream().filter(userOrg -> userOrg.getOrgId() == orgIdByOrgType.get(orgType)).count();

            Integer unHandlerCount = orgUnHandleOrders.stream()
                    .filter(unHandleOrder -> unHandleOrder.getOrgId() == orgIdByOrgType.get(orgType))
                    .map(AdminWorkOrderFlowBiz.OrgUnHandleOrder::getTotal)
                    .findFirst()
                    .orElse(0);
            // 计算 在线  离线  暂停的具体数量
            Function<WorkFlowStaffStatus.StaffStatusEnum, Integer> function = item -> staffStatusAndNums.stream()
                    .filter(sn -> sn.getOrgType() == orgType && sn.getStaffStatus() == item.getCode())
                    .findFirst()
                    .map(StaffStatusAndNum::getNum)
                    .orElse(0);

            int onlineCount = function.apply(WorkFlowStaffStatus.StaffStatusEnum.online);
            int offlineCount = function.apply(WorkFlowStaffStatus.StaffStatusEnum.offline);
            int pauseLineCount = function.apply(WorkFlowStaffStatus.StaffStatusEnum.PAUSE);

            int offlineOrgCount = Long.valueOf(orgTypeMembers).intValue() - onlineCount - pauseLineCount;
            log.debug("offlineCount:{} offlineOrgCount:{} onlineCount:{} pauseLineCount:{} orgTypeMembers:{}",
                    offlineCount, offlineOrgCount, onlineCount, pauseLineCount, orgTypeMembers);
            flowOrgStaffVo.setOrgId(orgIdByOrgType.get(orgType));
            flowOrgStaffVo.setOrgType(orgType);
            flowOrgStaffVo.setOnlineCount(onlineCount);
            flowOrgStaffVo.setOfflineCount(offlineOrgCount);
            flowOrgStaffVo.setPauseLineCount(pauseLineCount);
            flowOrgStaffVo.setUnAllotCount(unHandlerCount);
            log.debug("flowOrgStaffVo:{}", JSON.toJSONString(flowOrgStaffVo));

            // 累计
            allOnline += onlineCount;
            allOffline += offlineOrgCount;
            allPauseLine += pauseLineCount;
            allUnAllot += unHandlerCount;
            workFlowOrgStaffVoList.add(flowOrgStaffVo);
        }
        //设置全部
        WorkFlowOrgStaffVo allOrgStaffVo = new WorkFlowOrgStaffVo();
        allOrgStaffVo.setOrgId(0);
        allOrgStaffVo.setOrgType(WorkFlowStaffStatus.OrgTypeEnum.all.getCode());
        allOrgStaffVo.setOnlineCount(allOnline);
        allOrgStaffVo.setOfflineCount(allOffline);
        allOrgStaffVo.setUnAllotCount(allUnAllot);
        allOrgStaffVo.setPauseLineCount(allPauseLine);

        workFlowOrgStaffVoList.add(0, allOrgStaffVo);
        return workFlowOrgStaffVoList;
    }


    public WorkFlowStaffVo searchStaffStatus(List<Integer> lastOrgIds, Long flowUserId, int current, int pageSize, int staffStatus) {
        Set<Long> searchUserIds = Sets.newHashSet();
        PaginationVO pageResult = null;
        Map<Long, AdminOrganizationUserMap> userIdTOrg = Maps.newHashMap();
        if (flowUserId != null) {
            searchUserIds.add(flowUserId);
            List<AdminOrganizationUserMap> lowestOrgByUserIds = organizationBiz.getLowestOrgByUserIds(Lists.newArrayList(flowUserId.intValue()));
            userIdTOrg.put(flowUserId, lowestOrgByUserIds.stream().findFirst().orElse(null));
//            pageResult = new PaginationVO(1, 20, 1);
        } else if (CollectionUtils.isNotEmpty(lastOrgIds)) {
            List<AdminOrganizationUserMap> orgUserByOrgIds = organizationBiz.getOrgUserByOrgIds(lastOrgIds);
//            pageResult = PaginationListVO.createWithList(orgUserByOrgIds).getPagination();
            searchUserIds = orgUserByOrgIds.stream().map(item -> (long) item.getUserId()).collect(Collectors.toSet());
            userIdTOrg = orgUserByOrgIds.stream().collect(Collectors.toMap(item -> (long) item.getUserId(), Function.identity(), (before, after) -> before));
        }

        PageHelper.startPage(current, pageSize);
        List<WorkFlowStaffStatus> workFlowStaffStatuses = staffStatusBiz.listByUserIds(Lists.newArrayList(searchUserIds), staffStatus);
        pageResult = PaginationListVO.createWithList(workFlowStaffStatuses).getPagination();
        List<Long> userIds = workFlowStaffStatuses.stream().map(WorkFlowStaffStatus::getUserId).collect(Collectors.toList());


//        Sets.SetView<Long> notOptUserIds = Sets.difference(searchUserIds, Sets.newHashSet(userIds));
//
//        for (Long notOptUserId : notOptUserIds) {
//            WorkFlowStaffStatus status = new WorkFlowStaffStatus();
//            status.setUserId(notOptUserId);
//            workFlowStaffStatuses.add(status);
//        }

        Map<Long, List<WorkFlowStaffStatusRecord>> userIdTRecords = staffStatusBiz.listTodayRecord(userIds);

        WorkFlowStaffVo workFlowStaffVo = new WorkFlowStaffVo();
        workFlowStaffVo.setPaginationVO(pageResult);
        List<WorkFlowStaffVo.StaffSearchResult> staffSearchList = Lists.newArrayList();
        Map<Integer, Integer> orgIdDelayCounterMappings = assignRecordBiz.getNoHandleOrgMapping();
        for (WorkFlowStaffStatus item : workFlowStaffStatuses) {
            WorkFlowStaffVo.StaffSearchResult staffSearchResult = new WorkFlowStaffVo.StaffSearchResult();
            BeanUtils.copyProperties(item, staffSearchResult);
            List<WorkFlowStaffStatusRecord> statusRecords = userIdTRecords.get(item.getUserId());
            if (CollectionUtils.isNotEmpty(statusRecords)) {
                Duration duration = Duration.ZERO;
                List<Duration> durationList = IntStream.range(0, statusRecords.size())
                        .filter(i -> statusRecords.get(i).getStaffStatus() == WorkFlowStaffStatus.StaffStatusEnum.online.getCode())
                        .mapToObj(i -> {
                            if (i == statusRecords.size() - 1) {
                                return Duration.between(statusRecords.get(i).getCreateTime().toInstant(),
                                        new Date().toInstant());
                            } else {
                                return Duration.between(statusRecords.get(i).getCreateTime().toInstant(),
                                        statusRecords.get(i + 1).getCreateTime().toInstant());
                            }
                        }).collect(Collectors.toList());
                for (Duration d : durationList) {
                    duration = duration.plus(d);
                }
                staffSearchResult.setHours(duration.toHours());
                duration = duration.minusHours(duration.toHours());
                staffSearchResult.setMinutes(duration.toMinutes());
                duration = duration.minusMinutes(duration.toMinutes());
                staffSearchResult.setSeconds(duration.toMillis() / 1000);
            }
            staffSearchResult.setName(getAdminUserName((int) item.getUserId()));
            AdminOrganizationUserMap adminOrganizationUserMap = userIdTOrg.get(item.getUserId());
            if (adminOrganizationUserMap != null) {
                AdminOrganization organization = organizationBiz.getAdminOrganizationById(adminOrganizationUserMap.getOrgId());
                String orgName = Optional.ofNullable(organization).map(AdminOrganization::getName).orElse("");
                staffSearchResult.setStaffOrgName(orgName);
                staffSearchResult.setNoHandleLimit(orgIdDelayCounterMappings.get(adminOrganizationUserMap.getOrgId()));
            }

            staffSearchList.add(staffSearchResult);
        }
        workFlowStaffVo.setSearchResultList(staffSearchList);
        return workFlowStaffVo;
    }


    public int showStatus(long adminUserId) {
        WorkFlowStaffStatus workFlowStaffStatus = staffStatusBiz.findByUserId(adminUserId);
        return Optional.ofNullable(workFlowStaffStatus)
                .map(WorkFlowStaffStatus::getStaffStatus)
                .orElse(WorkFlowStaffStatus.StaffStatusEnum.offline.getCode());
    }

    public List<WorkFlowStaffStatusRecord> listStatusChangeRecords(long adminUserId) {
        List<WorkFlowStaffStatusRecord> recordList = staffStatusBiz.listTodayRecord(Lists.newArrayList(adminUserId))
                .getOrDefault(adminUserId, Lists.newArrayList());
        recordList.forEach(item -> item.setOperatorName(getAdminUserName((int) (item.getOperatorId()))));
        return recordList;
    }

    //每日凌晨自动离线
    public boolean autoOffline() {
        log.info("系统自动离线");
        return staffStatusBiz.autoOffline();
    }


    public void recordFreeTime() {
        //计算当前时间员工是否空闲：在线、组织内存在未分配的工单、员工无再处理的工单
        Integer erxianOrg = orgIdByOrgType.get(WorkFlowStaffStatus.OrgTypeEnum.er_xian.getCode());
        AdminWorkOrderFlowStatistics.searchParam param = new AdminWorkOrderFlowStatistics.searchParam();
        param.setOrderStatusSet(Arrays.asList(CREATED.getCode(), HANDLING.getCode()));
        param.setProblemTypeCodes(Lists.newArrayList(erxianOrg));
        param.setCreateTime(getDate());
        Stopwatch stopwatch = Stopwatch.createStarted();
        List<AdminWorkOrderFlowView> flowViews = adminWorkOrderFlowDao.selectByOrderStatusAndOperators(param);
        stopwatch.stop();
        log.info("recordFreeTime data:{}", JSON.toJSONString(flowViews));
        log.info("recordFreeTime 耗时:{}", stopwatch);
        List<AdminWorkOrderFlowView> newCreateOrder = flowViews.stream().filter(item -> item.getWorkOrderStatus() == CREATED.getCode()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(newCreateOrder)) {
            log.info("二线无新工单");
        }
        //二线下所有的员工
        List<Long> userIds = organizationBiz.getOrgUserByOrgIds(Lists.newArrayList(erxianOrg))
                .stream()
                .map(item -> (long) item.getUserId())
                .collect(Collectors.toList());
        //查看员工在线情况
        Set<Long> onlineUserIds = staffStatusBiz.listByUserIds(userIds)
                .stream().filter(item -> item.getStaffStatus() == WorkFlowStaffStatus.StaffStatusEnum.online.getCode())
                .map(WorkFlowStaffStatus::getUserId)
                .collect(Collectors.toSet());
        //查看这些人是否有处理中的工单
        Set<Integer> operators = flowViews.stream()
                .map(AdminWorkOrderFlowView::getOperatorId)
                .collect(Collectors.toSet());
        List<WorkFlowFreeRecord> flowFreeRecords = Lists.newArrayList();
        DateTime now = DateTime.now();
        Sets.difference(onlineUserIds, operators).forEach(item -> {
                    WorkFlowFreeRecord freeRecord = new WorkFlowFreeRecord();
                    freeRecord.setUserId(item);
                    freeRecord.setOrgId(erxianOrg);
                    freeRecord.setFreeStartTime(now.minusMinutes(3).toDate());
                    freeRecord.setFreeEndTime(now.toDate());
                    flowFreeRecords.add(freeRecord);
                }
        );
        freeRecordBiz.batchAdd(flowFreeRecords);
    }


    private String getAdminUserName(int adminUserId) {
        String name = "";
        AuthRpcResponse<List<AdminUserAccountModel>> accountResponse = seaAccountClientV1.getUserAccountsByIds(Lists.newArrayList(adminUserId));
        if (accountResponse.isSuccess() && CollectionUtils.isNotEmpty(accountResponse.getResult())) {
            AdminUserAccountModel accountResult = accountResponse.getResult().stream().findFirst().orElse(null);
            name = Optional.ofNullable(accountResult).map(AdminUserAccountModel::getName).orElse("");
        } else {
            log.warn("获取mis信息异常!userId:{}", adminUserId);
        }
        return name;
    }

    private Date getDate() {
        LocalDate localDate = LocalDate.now().minusDays(7);

        ZoneId zoneId = ZoneId.systemDefault();

        ZonedDateTime zdt = localDate.atStartOfDay(zoneId);

        return Date.from(zdt.toInstant());
    }

}
