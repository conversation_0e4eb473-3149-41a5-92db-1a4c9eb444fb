package com.shuidihuzhu.cf.service.ai;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.admin.constant.AsyncPoolConstants;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.dao.ai.AiAdPromptConfigDao;
import com.shuidihuzhu.cf.model.ai.AiAdGenerateClassify;
import com.shuidihuzhu.cf.model.ai.AiAdPromptConfig;
import com.shuidihuzhu.cf.model.ai.ComplianceCheckResult;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.api.client.CfAiAdGenerateFeignClient;
import com.shuidihuzhu.client.cf.api.model.AdGenerationResult;
import com.shuidihuzhu.client.cf.api.model.enums.AgeProfileEnum;
import com.shuidihuzhu.client.cf.api.model.enums.CityProfileEnum;
import com.shuidihuzhu.client.cf.api.model.enums.GenderProfileEnum;
import com.shuidihuzhu.client.cf.api.model.enums.UserProfile;
import com.shuidihuzhu.common.web.util.IdGenUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * @Description: 广告语生成服务
 * @Author: panghairui
 * @Date: 2025/6/19 14:14
 */
@Slf4j
@Service
@RefreshScope
public class AdGenerationService {

    @Resource(name = AsyncPoolConstants.AI_GENERATE_AD_ASYNC_POOL)
    private Executor adGenerationExecutor;

    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Resource
    private TongYiAiModel tongYiAiModel;

    @Resource
    private CfAiAdGenerateFeignClient cfAiAdGenerateFeignClient;

    @Resource
    private AiAdPromptConfigDao aiAdPromptConfigDao;

    @Value("${apollo.admin.ai.scenario-ad-prompt:}")
    private String scenarioAdPrompt;
    @Value("${apollo.admin.ai.check-ad-prompt:}")
    private String checkAdPrompt;

    public void generateAllAdsForCase(String infoId) {

        // 获取筹款案例信息
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoId);
        if (Objects.isNull(crowdfundingInfo)) {
            log.info("AdGenerationService crowdfundingInfo is null {}", infoId);
            return;
        }

        // 获取所有用户画像枚举
        List<UserProfile> allProfiles = generateAllProfileCombinations();
        log.info("开始为案例 {} 生成 {} 个画像组合的广告语", crowdfundingInfo.getId(), allProfiles.size());

        // 生成广告语
        allProfiles.forEach(profile -> {
            CompletableFuture.supplyAsync(() ->
                            generateSingleAd(crowdfundingInfo.getId(),
                                    crowdfundingInfo.getContent(), profile), adGenerationExecutor)
                    .thenAccept(result -> {
                        // 立即处理单个结果
                        handleGenerationResults(crowdfundingInfo.getId(), result);
                    })
                    .exceptionally(throwable -> {
                        log.error("生成广告语失败, caseId: {}, profile: {}",
                                crowdfundingInfo.getId(), profile, throwable);
                        return null;
                    });
        });

    }

    private void handleGenerationResults(Integer caseId, AdGenerationResult adGenerationResult) {

        if (Objects.isNull(adGenerationResult)) {
            log.info("handleGenerationResults adGenerationResult is null");
            return;
        }

        cfAiAdGenerateFeignClient.insertAiAd(adGenerationResult);
    }

    private AdGenerationResult generateSingleAd(Integer caseId, String caseContent, UserProfile profile) {
        try {
            log.debug("正在为画像 {} 生成广告语", profile.getProfileKey());

            // 获取用户画像对应提示词
            AiAdPromptConfig aiAdPromptConfig = aiAdPromptConfigDao.selectConfigByUserProfile(profile.getProfileKey());
            if (Objects.isNull(aiAdPromptConfig)) {
                return null;
            }

            // 通过两套提示词生成多个广告语
            AiAdGenerateClassify aiAdGenerateClassify = generateMultipleAds(caseContent, profile, aiAdPromptConfig);
            if (CollectionUtils.isEmpty(aiAdGenerateClassify.getScenarioList())) {
                log.info("未能生成任何广告语, caseId: {}, profile: {}", caseId, profile.getProfileKey());
                return null;
            }

            String ad = aiAdGenerateClassify.getScenarioList().get(0);

            // 合规检查
            ComplianceCheckResult complianceResult = checkCompliance(ad, caseContent, profile);
            if (!complianceResult.isCompliant()) {
                log.info("广告语合规检查未通过, caseId: {}, profile: {}, reason: {}",
                        caseId, profile.getProfileKey(), complianceResult.getReason());
            }

            log.info("成功生成合规广告语, caseId: {}, profile: {}", caseId, profile.getProfileKey());
            return AdGenerationResult.builder()
                    .profileKey(profile.getProfileKey())
                    .adContent(ad)
                    .caseId(caseId)
                    .adId(IdGenUtil.tradeNo())
                    .promptType("场景代入")
                    .isCompliant(complianceResult.isCompliant())
                    .noCompliantReason(complianceResult.getReason())
                    .build();
        } catch (Exception e) {
            log.error("生成广告语失败, caseId: {}, profile: {}", caseId, profile.getProfileKey(), e);
            return null;
        }
    }

    /**
     * 合规检查
     */
    private ComplianceCheckResult checkCompliance(String adContent, String caseContent, UserProfile profile) {
        try {
            String compliancePrompt = buildCompliancePrompt(checkAdPrompt,
                    adContent, caseContent, profile);
            String response = tongYiAiModel.callModelApi(null, compliancePrompt);

            // 解析合规检查结果
            return parseComplianceResult(response);

        } catch (Exception e) {
            log.error("合规检查失败, adContent: {}", adContent, e);
            return ComplianceCheckResult.builder()
                    .isCompliant(false)
                    .reason("合规检查异常")
                    .build();
        }
    }

    private String getPromptType(AiAdGenerateClassify aiAdGenerateClassify, String bestAd) {

        if (CollectionUtils.isNotEmpty(aiAdGenerateClassify.getScenarioList())
                && aiAdGenerateClassify.getScenarioList().contains(bestAd)) {
            return "场景代入";
        }

        if (CollectionUtils.isNotEmpty(aiAdGenerateClassify.getEmpathyList())
                && aiAdGenerateClassify.getEmpathyList().contains(bestAd)) {
            return "共情关怀";
        }

        return "";

    }

    /**
     * 拆分广告文本
     * @param adText 广告文本，格式如："共情关怀1：风险可能随时来临，请您快速完善保障。"
     * @return 包含两个元素的字符串数组，[0]为提示词类型，[1]为广告内容
     */
    private String[] splitAdText(String adText) {
        if (StringUtils.isEmpty(adText)) {
            return new String[]{"", ""};
        }

        adText = adText.trim();
        int colonIndex = adText.indexOf("：");

        if (colonIndex != -1) {
            String titlePart = adText.substring(0, colonIndex);
            String contentPart = adText.substring(colonIndex + 1);

            // 去除标题中的数字，只保留提示词类型
            String promptType = titlePart.replaceAll("\\d+", "");

            return new String[]{promptType, contentPart};
        } else {
            // 如果没有找到冒号，将整个文本作为内容，提示词类型为空
            return new String[]{"", adText};
        }
    }


    /**
     * 解析合规检查结果
     */
    private ComplianceCheckResult parseComplianceResult(String response) {
        try {
            // 解析JSON响应
            ObjectMapper mapper = new ObjectMapper();
            JsonNode jsonNode = mapper.readTree(response);

            return ComplianceCheckResult.builder()
                    .isCompliant(jsonNode.get("isCompliant").asBoolean())
                    .reason(jsonNode.get("reason").asText())
                    .build();

        } catch (Exception e) {
            log.info("解析合规检查结果失败: {}", response, e);
            return ComplianceCheckResult.builder()
                    .isCompliant(false)
                    .reason("解析合规检查结果失败")
                    .build();
        }
    }

    /**
     * 生成广告语
     */
    private AiAdGenerateClassify generateMultipleAds(String caseContent, UserProfile profile, AiAdPromptConfig aiAdPromptConfig) {
        AiAdGenerateClassify aiAdGenerateClassify = new AiAdGenerateClassify();

        try {
            // 场景代入提示词生成n个广告语
            String scenarioPrompt = buildAdGeneratePrompt(aiAdPromptConfig.getAdPrompt(), caseContent, profile);
            String scenarioResponse = tongYiAiModel.callModelApiByModelName(null, scenarioPrompt, "qwen-max");
            aiAdGenerateClassify.setScenarioList(Lists.newArrayList(scenarioResponse));
            log.debug("场景代入广告语生成完成，明细: {}", scenarioResponse);

        } catch (Exception e) {
            log.error("生成广告语失败, profile: {}", profile.getProfileKey(), e);
        }

        return aiAdGenerateClassify;
    }

    private String buildCompliancePrompt(String basePrompt, String bestAd, String caseContent, UserProfile profile) {
        return basePrompt
                .replace("caseContent", caseContent)
                .replace("profile", profile.toString())
                .replace("bestAd", bestAd);
    }

    private String buildAdGeneratePrompt(String basePrompt, String caseContent, UserProfile profile) {
        return basePrompt
                .replace("caseContent", caseContent)
                .replace("profile", profile.toString());
    }

    private String buildSelectionPrompt(String basePrompt, List<String> ads, String caseContent, UserProfile profile) {

        StringBuilder adStr = new StringBuilder();
       for (String ad : ads) {
           adStr.append(ad).append("\n");
       }

        return basePrompt
                .replace("caseContent", caseContent)
                .replace("profile", profile.toString())
                .replace("adStr", adStr);
    }

    /**
     * 构建空结果
     */
    private AdGenerationResult buildEmptyResult(Integer caseId, UserProfile profile) {
        return AdGenerationResult.builder()
                .caseId(caseId)
                .profileKey(profile.getProfileKey())
                .adContent("")
                .build();
    }

    /**
     * 解析多个广告语
     */
    private List<String> parseMultipleAds(String response, int expectedCount) {
        List<String> ads = Arrays.stream(response.split("\n"))
                .map(String::trim)
                .filter(line -> !line.isEmpty())
                .collect(Collectors.toList());

        log.debug("解析出{}个广告语，期望{}个", ads.size(), expectedCount);
        return ads;
    }



    /**
     * 生成所有画像组合
     */
    private List<UserProfile> generateAllProfileCombinations() {
        List<UserProfile> profiles = Arrays.stream(AgeProfileEnum.values())
                .flatMap(age -> Arrays.stream(GenderProfileEnum.values())
                        .map(gender -> UserProfile.builder()
                                .ageProfile(age)
                                .genderProfile(gender)
                                .build()))
                .collect(Collectors.toList());

        log.info("生成了 {} 个画像组合", profiles.size());
        return profiles;
    }



}
