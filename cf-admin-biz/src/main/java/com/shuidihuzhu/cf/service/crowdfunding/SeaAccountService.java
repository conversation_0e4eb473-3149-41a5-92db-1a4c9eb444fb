package com.shuidihuzhu.cf.service.crowdfunding;

import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.OrganizationClientV1;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SeaAccountService {

    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;
    @Autowired
    private OrganizationClientV1 organizationClientV1;


    public String getName(int userId) {
        if (userId <= 0) {
            return "";
        }
        AuthRpcResponse<AdminUserAccountModel> accountResponse = seaAccountClientV1.getValidUserAccountById(userId);
        String name = "";
        if (accountResponse != null && accountResponse.isSuccess() && accountResponse.getResult() != null) {
            name = accountResponse.getResult().getName();
        }
        return name;
    }

    public String getOrganization(int userId) {
        AuthRpcResponse<String> rpcResponse = organizationClientV1.getUserRelationOrgName(userId);
        String organization = "";
        if (rpcResponse != null && rpcResponse.isSuccess()) {
            organization = rpcResponse.getResult();
        }
        return StringUtils.isBlank(organization) ? organization : organization + "-";
    }

    public String getOrganizationName(int userId){
        AuthRpcResponse<String> rpcResponse = organizationClientV1.getUserRelationOrgName(userId);
        String organization = "";
        if (rpcResponse != null && rpcResponse.isSuccess()) {
            organization = rpcResponse.getResult();
        }
        return StringUtils.trimToEmpty(organization);
    }

    public String getMis(int userId) {
        if (userId <= 0) {
            return "";
        }
        AuthRpcResponse<AdminUserAccountModel> accountResponse = seaAccountClientV1.getValidUserAccountById(userId);
        String name = "";
        if (accountResponse != null && accountResponse.isSuccess() && accountResponse.getResult() != null) {
            name = accountResponse.getResult().getMis();
        }
        return name;
    }
}
