package com.shuidihuzhu.cf.service.tag.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.admin.util.admin.AdminCfIdCardUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfRepeatInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfMaterialReadClient;
import com.shuidihuzhu.cf.client.material.feign.CfMaterialWriteClient;
import com.shuidihuzhu.cf.client.material.model.CfMaterialAddOrUpdateVo;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.material.model.materialField.MaterialExtKeyConst;
import com.shuidihuzhu.cf.dao.admin.CaseLabelDao;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.BaseInfoTemplateConst;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceCapitalAccountFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCfRepeatView;
import com.shuidihuzhu.cf.model.crowdfunding.CfCapitalAccount;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.datautilapi.DataAddressByIdCard;
import com.shuidihuzhu.cf.model.label.CfCaseLabelCondition;
import com.shuidihuzhu.cf.model.label.CfCaseLabelInfo;
import com.shuidihuzhu.cf.model.label.CfCaseLabelRule;
import com.shuidihuzhu.cf.model.label.CfCaseMetricsInfo;
import com.shuidihuzhu.cf.service.ai.CfAddressDataQueryService;
import com.shuidihuzhu.cf.service.disease.impl.DiseaseNormServiceImpl;
import com.shuidihuzhu.cf.service.tag.CfCaseLabelService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.admin.enums.ThreeBodyCaseTabEnum;
import com.shuidihuzhu.client.cf.admin.model.ThreeBodyTag;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackJudgeConformThreeBodyClient;
import com.shuidihuzhu.client.cf.clewtrack.model.ChannelRefine;
import com.shuidihuzhu.client.cf.clewtrack.param.ChannelRefineDTO;
import com.shuidihuzhu.client.cf.growthtool.client.CfChannelFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CaseLabelParam;
import com.shuidihuzhu.client.cf.growthtool.model.CfThreeBodyInfo;
import com.shuidihuzhu.client.cf.growthtool.model.RuleJudge;
import com.shuidihuzhu.client.cf.search.client.CfSearchClient;
import com.shuidihuzhu.client.cf.search.model.EsCaseLabelColum;
import com.shuidihuzhu.client.cf.search.model.enums.CaseTypeEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/8/9 10:41 AM
 */
@Slf4j
@Service
public class CfCaseLabelServiceImpl implements CfCaseLabelService {

    @Autowired
    private IRiskDelegate riskDelegate;
    @Resource
    private ShuidiCipher shuidiCipher;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Resource
    private CfAddressDataQueryService cfAddressDataQueryService;
    @Resource
    private DiseaseNormServiceImpl diseaseNormService;
    @Resource
    private CfChannelFeignClient cfChannelFeignClient;
    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Resource
    private CfClewtrackJudgeConformThreeBodyClient cfClewtrackJudgeConformThreeBodyClient;
    @Resource
    private CaseLabelDao caseLabelDao;
    @Resource
    private SeaAccountClientV1 seaAccountClientV1;
    @Resource
    private LabelRuleProcessService labelRuleProcessService;
    @Resource
    private CfMaterialWriteClient cfMaterialWriteClient;
    @Resource
    private CfMaterialReadClient cfMaterialReadClient;
    @Resource
    private CfThreeBodyServiceImpl cfThreeBodyService;
    @Resource
    private AdminCfRepeatInfoBiz cfRepeatInfoBiz;
    @Resource
    private CfFinanceCapitalAccountFeignClient cfFinanceCapitalAccountFeignClient;
    @Resource
    private CfSearchClient cfSearchClient;

    private final static List<String> CASE_LABEL_LIST = Lists.newArrayList(
            "三体案例",
            "高质量案例"
    );

    @Override
    public void bindingCaseLabel(Integer caseId) {

        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            log.info("getCfCaseMetricsInfo 无案例信息 {}", caseId);
            return ;
        }

        // 确认渠道是否符合线上渠道
        if (!judgeChannel(crowdfundingInfo)) {
            return ;
        }

        // 获取案例指标信息
        CfCaseMetricsInfo cfCaseMetricsInfo = getCfCaseMetricsInfo(crowdfundingInfo);

        // 匹配案例标签
        String label = matchCaseLabel(cfCaseMetricsInfo);
        if (StringUtils.isBlank(label)) {
            return ;
        }

        // 绑定案例标签
        bindLabel(caseId, label);

    }

    @Override
    public Boolean remindCaseLabel(RuleJudge ruleJudge) {

        if (Objects.isNull(ruleJudge)
                || Objects.isNull(ruleJudge.getInputContent())) {
            return false;
        }
        Map<String, String> inputContent = ruleJudge.getInputContent();
        if (Objects.isNull(inputContent)) {
            return false;
        }

        CfCaseMetricsInfo cfCaseMetricsInfo = getCfCaseMetricsInfoByMap(inputContent);
        // 获取三体案例标签
        CfCaseLabelInfo cfCaseLabelInfo = caseLabelDao.getCaseLabelByName("三体案例");
        if (Objects.isNull(cfCaseLabelInfo)) {
            return false;
        }
        cfCaseLabelInfo.setRules(JSONObject.parseArray(cfCaseLabelInfo.getRuleJson(), CfCaseLabelRule.class));

        if (judgeCaseLabel(cfCaseMetricsInfo, cfCaseLabelInfo)) {
            return true;
        }

        return false;
    }

    @Override
    public String judgeCaseLabel(RuleJudge ruleJudge) {
        if (Objects.isNull(ruleJudge)
                || Objects.isNull(ruleJudge.getInputContent())) {
            return "";
        }
        Map<String, String> inputContent = ruleJudge.getInputContent();
        if (Objects.isNull(inputContent)) {
            return "";
        }

        CfCaseMetricsInfo cfCaseMetricsInfo = getCfCaseMetricsInfoByMap(inputContent);
        for (String caseLabel : CASE_LABEL_LIST) {
            CfCaseLabelInfo cfCaseLabelInfo = caseLabelDao.getCaseLabelByName(caseLabel);
            if (Objects.isNull(cfCaseLabelInfo)) {
                continue;
            }
            cfCaseLabelInfo.setRules(JSONObject.parseArray(cfCaseLabelInfo.getRuleJson(), CfCaseLabelRule.class));
            if (judgeCaseLabel(cfCaseMetricsInfo, cfCaseLabelInfo)) {
                return caseLabel;
            }
        }

        return "";
    }

    /**
     * 根据ai预测案例捐单量获取ai案例标签,线上增长捐转任务侧使用
     * @param predictDonateCount
     * @return
     */
    private CfCaseLabelInfo getAiCaseLabel(Integer predictDonateCount) {
        if (Objects.isNull(predictDonateCount)) {
            return null;
        }
        List<CfCaseLabelInfo> cfCaseLabelInfos = caseLabelDao.queryEnableCaseLabels();
        Map<String, String> inputContent = Maps.newHashMap();
        inputContent.put("predictDonateCount", predictDonateCount.toString());
        CfCaseMetricsInfo cfCaseMetricsInfo = getCfCaseMetricsInfoByMap(inputContent);
        for (CfCaseLabelInfo cfCaseLabelInfo : cfCaseLabelInfos) {
            cfCaseLabelInfo.setRules(JSONObject.parseArray(cfCaseLabelInfo.getRuleJson(), CfCaseLabelRule.class));
            if (judgeCaseLabel(cfCaseMetricsInfo, cfCaseLabelInfo)) {
                return cfCaseLabelInfo;
            }
        }
        return null;
    }

    @Override
    public ThreeBodyTag getCaseLabel(Integer caseId, Integer predictDonateCount) {
        String caseTag = "";
        RpcResult<Map<String, List<String>>> mapRpcResult = cfMaterialReadClient.selectValueByFields(caseId, List.of(MaterialExtKeyConst.CASE_THREE_BODY_TAG));
        CfCaseLabelInfo aiCaseLabel = getAiCaseLabel(predictDonateCount);
        if (Objects.nonNull(mapRpcResult) && mapRpcResult.isSuccess() && MapUtils.isNotEmpty(mapRpcResult.getData())) {

            List<String> hitString = Optional.ofNullable(mapRpcResult.getData().get(MaterialExtKeyConst.CASE_THREE_BODY_TAG)).orElse(new ArrayList<>());
            if (org.apache.commons.collections.CollectionUtils.isEmpty(hitString)) {
                return noTagBody(caseId, aiCaseLabel);
            }

            String hit = hitString.get(0);
            if (StringUtils.isBlank(hit)) {
                return noTagBody(caseId, aiCaseLabel);
            }

            Boolean isThreeBody = ThreeBodyCaseTabEnum.judgeThreeBodyCase(hit);
            if (isThreeBody) {
                caseTag = "三体案例";
            } else {
                caseTag = hit;
            }

            CfCaseLabelInfo cfCaseLabelInfo = caseLabelDao.getCaseLabelByName(caseTag);
            if (Objects.isNull(cfCaseLabelInfo)) {
                return noTagBody(caseId, aiCaseLabel);
            }
            return ThreeBodyTag.builder()
                    .caseLabel(caseTag)
                    .caseId(caseId)
                    .aiPredictDonationCaseLabel(Optional.ofNullable(aiCaseLabel).map(CfCaseLabelInfo::getName).orElse(""))
                    .aiPredictDonationCaseLabelId(Optional.ofNullable(aiCaseLabel).map(CfCaseLabelInfo::getId).orElse(0L))
                    .caseLabelId(cfCaseLabelInfo.getId())
                    .threeBodyTag("三体案例".equals(caseTag))
                    .priority(cfCaseLabelInfo.getPriority())
                    .build();

        }
        return noTagBody(caseId, aiCaseLabel);
    }

    @Override
    public Boolean judgeMajorCase(CaseLabelParam caseLabelParam) {

        if (Objects.isNull(caseLabelParam)
                || Objects.isNull(caseLabelParam.getCaseId())) {
            return false;
        }

        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseLabelParam.getCaseId());
        if (Objects.isNull(crowdfundingInfo)) {
            log.info("judgeMajorCase 无案例信息 {}", caseLabelParam.getCaseId());
            return false;
        }

        // 获取案例指标信息
        CfCaseMetricsInfo cfCaseMetricsInfo = getCfCaseMetricsInfo(crowdfundingInfo);
        cfCaseMetricsInfo.setMajorCaseTag(Lists.newArrayList("是"));
        cfCaseMetricsInfo.setPatientProfession(Lists.newArrayList(caseLabelParam.getPatientProfession()));

        // 匹配规则
        Map<String, Object> caseTagParam = Map.of(EsCaseLabelColum.ID, crowdfundingInfo.getId());
        List<CfCaseLabelInfo> cfCaseLabelInfos = caseLabelDao.queryAllCaseLabels();
        for (CfCaseLabelInfo cfCaseLabelInfo : cfCaseLabelInfos) {
            cfCaseLabelInfo.setRules(JSONObject.parseArray(cfCaseLabelInfo.getRuleJson(), CfCaseLabelRule.class));
            if (judgeCaseLabel(cfCaseMetricsInfo, cfCaseLabelInfo)) {
                return true;
            }
        }

        return false;
    }

    @Override
    public List<ThreeBodyTag> getAllLabelInfo() {
        List<CfCaseLabelInfo> cfCaseLabelInfos = caseLabelDao.queryAllCaseLabels();
        if (CollectionUtils.isEmpty(cfCaseLabelInfos)) {
            return Lists.newArrayList();
        }

        List<ThreeBodyTag> threeBodyTags = cfCaseLabelInfos.stream().map(f -> {
            return ThreeBodyTag.builder()
                    .caseLabel(f.getName())
                    .caseLabelId(f.getId())
                    .priority(f.getPriority())
                    .build();
        }).collect(Collectors.toList());
        threeBodyTags.add(noTagBody(null, null));
        return threeBodyTags;
    }

    @Override
    public List<ThreeBodyTag> getCaseLabelByCaseIds(List<Integer> caseIds) {
        if (CollectionUtils.isEmpty(caseIds)) {
            return Lists.newArrayList();
        }

        List<ThreeBodyTag> threeBodyTags = Lists.newArrayList();
        caseIds.forEach(caseId -> {
            threeBodyTags.add(getCaseLabel(caseId, null));
        });

        return threeBodyTags;
    }

    @Override
    public Response<Long> addOrUpdateCaseLabel(CfCaseLabelInfo cfCaseLabelInfo) {

        if (Objects.isNull(cfCaseLabelInfo)) {
            return NewResponseUtil.makeFail("参数错误");
        }

        CfCaseLabelInfo repeatInfo = caseLabelDao.getRepeatCaseLabelByName(cfCaseLabelInfo.getId(), cfCaseLabelInfo.getName());
        if (Objects.nonNull(repeatInfo)) {
            return NewResponseUtil.makeFail("案例标签已存在，请更换！");
        }

        cfCaseLabelInfo.setRuleJson(JSONObject.toJSONString(cfCaseLabelInfo.getRules()));
        log.info("addOrUpdateCaseLabel cfCaseLabelInfo {}", JSONObject.toJSONString(cfCaseLabelInfo));

        if (cfCaseLabelInfo.getId() != null) {
            int flag = caseLabelDao.updateCaseLabel(cfCaseLabelInfo);
            log.info("addOrUpdateCaseLabel updateCaseLabel flag {}", flag);
        } else {
            int flag = caseLabelDao.addCaseLabel(cfCaseLabelInfo);
            log.info("addOrUpdateCaseLabel addCaseLabel flag {}", flag);
        }

        return NewResponseUtil.makeSuccess(cfCaseLabelInfo.getId());
    }

    @Override
    public Map<String, Object> queryCaseLabel(String labelName, Integer status, Integer pageNum, Integer pageSize) {

        if (pageSize > 50) {
            pageSize = 50;
        }

        int offset = (pageNum - 1) * pageSize;

        List<CfCaseLabelInfo> cfCaseLabelInfos = caseLabelDao.queryCaseLabels(labelName, status, offset, pageSize);
        if (CollectionUtils.isEmpty(cfCaseLabelInfos)) {
            return Maps.newHashMap();
        }

        List<Integer> operatorIds = cfCaseLabelInfos.stream().map(CfCaseLabelInfo::getOperatorId).map(Long::intValue).collect(Collectors.toList());
        Map<Integer, String> operatorNameMap = getOperatorNameMap(operatorIds);

        for (CfCaseLabelInfo cfCaseLabelInfo : cfCaseLabelInfos) {
            cfCaseLabelInfo.setRules(JSONObject.parseArray(cfCaseLabelInfo.getRuleJson(), CfCaseLabelRule.class));
            cfCaseLabelInfo.setOperatorName(operatorNameMap.get(cfCaseLabelInfo.getOperatorId().intValue()));
        }

        Map<String, Object> result = Maps.newHashMap();
        result.put("detail", cfCaseLabelInfos);
        result.put("total", caseLabelDao.queryCaseLabelsCount(labelName, status));
        return result;
    }

    @Override
    public CfCaseLabelInfo queryCaseLabelById(Long id) {
        CfCaseLabelInfo cfCaseLabelInfo = caseLabelDao.queryCaseLabelById(id);
        if (Objects.isNull(cfCaseLabelInfo)) {
            return null;
        }
        cfCaseLabelInfo.setRules(JSONObject.parseArray(cfCaseLabelInfo.getRuleJson(), CfCaseLabelRule.class));

        Map<Integer, String> operatorNameMap = getOperatorNameMap(Lists.newArrayList(cfCaseLabelInfo.getOperatorId().intValue()));
        if (MapUtils.isNotEmpty(operatorNameMap)) {
            cfCaseLabelInfo.setOperatorName(operatorNameMap.get(cfCaseLabelInfo.getOperatorId().intValue()));
        }

        return cfCaseLabelInfo;
    }

    @Override
    public Boolean enableCaseLabel(Long id, Integer status, Long userId) {

        // 查出当前标签
        CfCaseLabelInfo cfCaseLabelInfo = caseLabelDao.queryCaseLabelById(id);
        if (Objects.isNull(cfCaseLabelInfo)) {
            return false;
        }

        // 存在重复优先级，拦住
        int count = caseLabelDao.queryCountByPriority(id, cfCaseLabelInfo.getPriority(), 0);
        if (count > 0 && status == 0) {
            return false;
        }

        int flag = caseLabelDao.updateStatusById(id, status, userId);
        log.info("enableCaseLabel updateStatusById flag {}", flag);
        return true;

    }

    private void bindLabel(Integer caseId, String label) {
        CfMaterialAddOrUpdateVo build = CfMaterialAddOrUpdateVo
                .builder()
                .caseId(caseId)
                .materialName(MaterialExtKeyConst.CASE_THREE_BODY_TAG)
                .materialValue(label)
                .materialLabel("")
                .materialExt("")
                .build();
        RpcResult<String> stringRpcResult = cfMaterialWriteClient.addOrUpdateByFields(caseId, Collections.singletonList(build));
        log.info("CfInitialAuditHandleV2ConsumerService saveInitialAuditSnapshot addOrUpdateFirstApprove stringRpcResult : {}", stringRpcResult);
    }

    private ThreeBodyTag noTagBody(Integer caseId, CfCaseLabelInfo aiCaseLabel) {
        String aiCaseLabelName = Optional.ofNullable(aiCaseLabel).map(CfCaseLabelInfo::getName).orElse("");
        Long aiCaseLabelId = Optional.ofNullable(aiCaseLabel).map(CfCaseLabelInfo::getId).orElse(0L);
        return ThreeBodyTag.builder()
                .caseLabel("无标签")
                .caseId(caseId)
                .aiPredictDonationCaseLabelId(aiCaseLabelId)
                .aiPredictDonationCaseLabel(aiCaseLabelName)
                .caseLabelId(0L)
                .priority(Integer.MAX_VALUE)
                .build();
    }

    private String matchCaseLabel(CfCaseMetricsInfo cfCaseMetricsInfo) {

        // 获取启用的所有标签
        List<CfCaseLabelInfo> caseLabelInfos = caseLabelDao.queryEnableCaseLabels();
        if (Objects.isNull(caseLabelInfos)) {
            return "";
        }

        for (CfCaseLabelInfo cfCaseLabelInfo : caseLabelInfos) {
            cfCaseLabelInfo.setRules(JSONObject.parseArray(cfCaseLabelInfo.getRuleJson(), CfCaseLabelRule.class));
            if (judgeCaseLabel(cfCaseMetricsInfo, cfCaseLabelInfo)) {
                return cfCaseLabelInfo.getName();
            }
        }

        return "";
    }

    private boolean judgeCaseLabel(CfCaseMetricsInfo cfCaseMetricsInfo, CfCaseLabelInfo cfCaseLabelInfo) {

        log.info("judgeCaseLabel caseMetricsInfo {}", JSONObject.toJSONString(cfCaseMetricsInfo));
        List<CfCaseLabelRule> rules = cfCaseLabelInfo.getRules();
        if (CollectionUtils.isEmpty(rules)) {
            return false;
        }

        // 只要有一个规则命中，就算命中标签
        for (CfCaseLabelRule rule : rules) {
            if (labelRuleProcessService.processRule(rule, cfCaseMetricsInfo)) {
                log.info("judgeCaseLabel caseLabelName {} hitRule {}", cfCaseLabelInfo.getName(), rule);
                return true;
            }
        }

        return false;
    }

    private Map<Integer, String> getOperatorNameMap(List<Integer> operatorIds) {
        Map<Integer, String> operatorNameMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(operatorIds)) {
            return operatorNameMap;
        }

        AuthRpcResponse<List<AdminUserAccountModel>> authRpcResponse = seaAccountClientV1.getUserAccountsByIds(operatorIds);
        List<AdminUserAccountModel> adminUserAccountModels = Optional.ofNullable(authRpcResponse)
                .filter(AuthRpcResponse::isSuccess)
                .map(AuthRpcResponse::getResult)
                .orElse(Collections.emptyList());
        if (CollectionUtils.isEmpty(adminUserAccountModels)) {
            return operatorNameMap;
        }

        for (AdminUserAccountModel adminUserAccountModel : adminUserAccountModels) {
            operatorNameMap.put(adminUserAccountModel.getId(), adminUserAccountModel.getName());
        }
        return operatorNameMap;
    }

    public static void main(String[] args) {
        CfCaseLabelInfo cfCaseLabelInfo = new CfCaseLabelInfo();
        cfCaseLabelInfo.setName("三体案例");
        cfCaseLabelInfo.setPriority(1);
        cfCaseLabelInfo.setRemarks("原三体案例");
        cfCaseLabelInfo.setStatus(0);
        cfCaseLabelInfo.setOperatorId(92220L);

        List<CfCaseLabelRule> rules = Lists.newArrayList();
        List<CfCaseLabelCondition> conditions = Lists.newArrayList();

        CfCaseLabelCondition cfCaseLabelCondition = new CfCaseLabelCondition();
        cfCaseLabelCondition.setConditionName("目标金额");

        Map<String, Object> conditionDetail = Maps.newHashMap();
        conditionDetail.put("minNum", 100);
        conditionDetail.put("minOperate", ">");
        conditionDetail.put("maxNum", 10000);
        conditionDetail.put("maxOperate", "<=");
        cfCaseLabelCondition.setConditionDetail(conditionDetail);
        conditions.add(cfCaseLabelCondition);

        CfCaseLabelCondition cfCaseLabelCondition2 = new CfCaseLabelCondition();
        cfCaseLabelCondition2.setConditionName("归一后疾病");
        Map<String, Object> conditionDetail2 = Maps.newHashMap();
        conditionDetail2.put("content", "白血病,艾滋病,狂犬病");
        cfCaseLabelCondition2.setConditionDetail(conditionDetail2);
        conditions.add(cfCaseLabelCondition2);
        CfCaseLabelRule cfCaseLabelRule = new CfCaseLabelRule();
        cfCaseLabelRule.setConditionList(conditions);
        rules.add(cfCaseLabelRule);
        cfCaseLabelInfo.setRules(rules);

        String s = JSONObject.toJSONString(cfCaseLabelInfo);
        System.out.println(1);

    }

    private CfCaseMetricsInfo getCfCaseMetricsInfoByMap(Map<String, String> inputContent) {

        CfCaseMetricsInfo cfCaseMetricsInfo = new CfCaseMetricsInfo();

        // 目标金额
        String targetAmount = Optional.ofNullable(inputContent.get("targetAmount")).orElse("");
        cfCaseMetricsInfo.setTargetAmount(StringUtils.isBlank(targetAmount) ? null : Integer.parseInt(targetAmount) * 100);

        // 年龄
        String patientIdCard = Optional.ofNullable(inputContent.get("patientIdCard")).orElse("");
        if (StringUtils.isNotBlank(patientIdCard)) {
            cfCaseMetricsInfo.setAge(AdminCfIdCardUtil.getIdCardAge(patientIdCard));
        }

        // 归一后疾病
        String classifyDiseases = Optional.ofNullable(inputContent.get("classifyDiseases")).orElse("");
        cfCaseMetricsInfo.setDiseaseNormNames(StringUtils.isBlank(classifyDiseases) ? null : Lists.newArrayList(classifyDiseases));

        // 案例类型
        String accidentType = Optional.ofNullable(inputContent.get("accidentType")).orElse("");
        if (StringUtils.isBlank(accidentType)) {
            cfCaseMetricsInfo.setCaseTypes(null);
        } else {
            String accidentTypeDesc = PreposeMaterialModel.AccidentType.valueOfCode(Integer.parseInt(accidentType)).getDesc();
            cfCaseMetricsInfo.setCaseTypes(Lists.newArrayList(accidentTypeDesc));
        }

        // 患者身份
        String patientIdentity = Optional.ofNullable(inputContent.get("patientIdentity")).orElse("");
        if (StringUtils.isBlank(patientIdentity)) {
            cfCaseMetricsInfo.setPatientIdentity(null);
        } else {
            String patientIdentityDesc = PreposeMaterialModel.PatientIdentity.valueOfCode(Integer.parseInt(patientIdentity)).getDesc();
            cfCaseMetricsInfo.setPatientIdentity(Lists.newArrayList(patientIdentityDesc));
        }

        // 案例所属省份
        String raiseMobile = Optional.ofNullable(inputContent.get("raiseMobile")).orElse("");
        String selfCryptoIdCard = Optional.ofNullable(inputContent.get("selfCryptoIdcard")).orElse("");
        String patientCryptoIdCard = Optional.ofNullable(inputContent.get("patientIdCard")).orElse("");
        cfCaseMetricsInfo.setProvince(getProvinceByNumber(raiseMobile, selfCryptoIdCard, patientCryptoIdCard));

        // 发起人关系
        String userRelationType = Optional.ofNullable(inputContent.get("raisePatientRelation")).orElse("");
        if (StringUtils.isBlank(userRelationType)) {
            cfCaseMetricsInfo.setUserRelationType(null);
        } else {
            cfCaseMetricsInfo.setUserRelationType(Lists.newArrayList(userRelationType));
        }

        // 案例预测捐单量
        String predictDonateCount = Optional.ofNullable(inputContent.get("predictDonateCount")).orElse("-1");
        cfCaseMetricsInfo.setPredictDonateCount(Integer.parseInt(predictDonateCount));

        return cfCaseMetricsInfo;
    }

    private List<String> getProvinceByNumber(String raiseMobile, String selfIdCard, String patientIdCard) {
        List<String> result = Lists.newArrayList();
        // 发起人手机号是否符合三体规则
        if (StringUtils.isNotBlank(raiseMobile)) {
            DataAddressByIdCard dataAddressByIdCard = cfAddressDataQueryService.queryAddressByMobile(raiseMobile);
            String province = Optional.ofNullable(dataAddressByIdCard).map(DataAddressByIdCard::getProvince).orElse("");
            if (StringUtils.isNotBlank(province)) {
                result.add(province);
            }
        }

        // 发起人身份证号是否符合三体规则
        if (StringUtils.isNotBlank(selfIdCard)) {
            DataAddressByIdCard dataAddressByIdCard = cfAddressDataQueryService.queryAddressByIdCard(selfIdCard);
            String province = Optional.ofNullable(dataAddressByIdCard).map(DataAddressByIdCard::getProvince).orElse("");
            if (StringUtils.isNotBlank(province)) {
                result.add(province);
            }
        }

        // 患者身份证号是否符合三体规则
        if (StringUtils.isNotBlank(patientIdCard)) {
            DataAddressByIdCard dataAddressByIdCard = cfAddressDataQueryService.queryAddressByIdCard(patientIdCard);
            String province = Optional.ofNullable(dataAddressByIdCard).map(DataAddressByIdCard::getProvince).orElse("");
            if (StringUtils.isNotBlank(province)) {
                result.add(province);
            }
        }

        return result;
    }

    private CfCaseMetricsInfo getCfCaseMetricsInfo(CrowdfundingInfo crowdfundingInfo) {

        CfCaseMetricsInfo cfCaseMetricsInfo = new CfCaseMetricsInfo();

        // 目标金额
        cfCaseMetricsInfo.setTargetAmount(crowdfundingInfo.getTargetAmount());
        // 归一后疾病
        List<String> normResult = diseaseNormService.getDiseaseNormByCaseId(crowdfundingInfo.getId());
        cfCaseMetricsInfo.setDiseaseNormNames(normResult);
        // 待录入 -> 患者身份 案例类型 案例所属省份
        Response<CfThreeBodyInfo> response = cfClewtrackJudgeConformThreeBodyClient.getThreeBodyInfo(crowdfundingInfo.getId());
        CfThreeBodyInfo cfThreeBodyInfo = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
        if (Objects.nonNull(cfThreeBodyInfo)) {
            cfCaseMetricsInfo.setCaseTypes(Optional.ofNullable(cfThreeBodyInfo.getCaseType()).map(Lists::newArrayList).orElse(Lists.newArrayList()));
            cfCaseMetricsInfo.setPatientIdentity(Optional.ofNullable(cfThreeBodyInfo.getIdentityType()).map(Lists::newArrayList).orElse(Lists.newArrayList()));
            cfCaseMetricsInfo.setProvince(Optional.ofNullable(cfThreeBodyInfo.getProvince()).map(Lists::newArrayList).orElse(Lists.newArrayList()));
            cfCaseMetricsInfo.setMedicalCity(Optional.ofNullable(cfThreeBodyInfo.getMedicalCity()).map(Lists::newArrayList).orElse(Lists.newArrayList()));
        }
        // 材料 -> 案例所属省份
        CfFirsApproveMaterial material = riskDelegate.getCfFirsApproveMaterialByInfoId(crowdfundingInfo.getId());
        if (Objects.nonNull(material)) {
            List<String> province = Optional.ofNullable(cfCaseMetricsInfo.getProvince()).map(Lists::newArrayList).orElse(Lists.newArrayList());
            cfCaseMetricsInfo.setProvince(getCfCaseMetricsProvince(province, material));
        }
        // 患者年龄
        if (Objects.nonNull(material)) {
            String idCard = StringUtils.isBlank(material.getPatientCryptoIdcard()) ?
                    material.getPatientCryptoIdcard() :
                    shuidiCipher.decrypt(material.getPatientCryptoIdcard());
            if (UserIdentityType.birth.getCode() == material.getPatientIdType()) {
                cfCaseMetricsInfo.setAge(0);
            }
            else if (StringUtils.isNotBlank(idCard)) {
                cfCaseMetricsInfo.setAge(AdminCfIdCardUtil.getIdCardAge(idCard));
            }
        }
        // 发起人与患者关系
        if (Objects.nonNull(material)) {
            BaseInfoTemplateConst.CfBaseInfoRelationshipEnum relation = BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.getByCode(material.getUserRelationTypeForC());
            cfCaseMetricsInfo.setUserRelationType(Lists.newArrayList(Optional.ofNullable(relation).map(BaseInfoTemplateConst.CfBaseInfoRelationshipEnum::getWord).orElse("")));
        }
        // 二发标签
        FeignResponse<CfCapitalAccount> capitalResponse = cfFinanceCapitalAccountFeignClient.capitalAccountGetByInfoUuid(crowdfundingInfo.getInfoId());
        CfCapitalAccount cfCapitalAccount = Optional.ofNullable(capitalResponse)
                .filter(FeignResponse::ok)
                .map(FeignResponse::getData)
                .orElse(null);
        Set<String> labelList = cfRepeatInfoBiz.selectRepeatDescByCaseId(crowdfundingInfo.getId(), cfCapitalAccount);
        Set<String> labels = Sets.newHashSet();
        for (String label : labelList) {
            if (AdminCfRepeatView.RepeatReasonView.MAYBE_TWICE.getDesc().equals(label)
                    || AdminCfRepeatView.RepeatReasonView.MAYBE_REPEAT.getDesc().equals(label)) {
                labels.add(AdminCfRepeatView.RepeatReasonView.DEFALT.getDesc());
            } else {
                labels.add(label);
            }
        }

        if (CollectionUtils.isEmpty(labels)) {
            labels.add(AdminCfRepeatView.RepeatReasonView.DEFALT.getDesc());
        }
        cfCaseMetricsInfo.setRepeatLabel(Lists.newArrayList(labels));

        return cfCaseMetricsInfo;
    }

    private List<String> getCfCaseMetricsProvince(List<String> province, CfFirsApproveMaterial material) {
        Set<String> materialProvince = Sets.newHashSet(province);

        // 根据患者身份证查归属地
        String patientIdCard = material.getPatientCryptoIdcard();
        if (StringUtils.isNotBlank(patientIdCard)) {
            patientIdCard = shuidiCipher.decrypt(patientIdCard);
            DataAddressByIdCard dataAddressByIdCard = cfAddressDataQueryService.queryAddressByIdCard(patientIdCard);
            if (Objects.nonNull(dataAddressByIdCard)) {
                materialProvince.add(dataAddressByIdCard.getProvince());
            }
        }

        // 根据发起人身份证查归属地
        String cryptoIdCard = material.getUserRelationTypeForC() == UserRelTypeEnum.SELF.getValue()
                ? material.getPatientCryptoIdcard()
                : material.getSelfCryptoIdcard();
        if (StringUtils.isNotBlank(cryptoIdCard)) {
            cryptoIdCard = shuidiCipher.decrypt(cryptoIdCard);
            DataAddressByIdCard dataAddressByIdCard = cfAddressDataQueryService.queryAddressByIdCard(cryptoIdCard);
            if (Objects.nonNull(dataAddressByIdCard)) {
                materialProvince.add(dataAddressByIdCard.getProvince());
            }
        }

//        UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(material.getUserId());
//        String mobile = Objects.nonNull(userInfoModel) ? userInfoModel.getMobile() : "";
//        if (StringUtils.isNotBlank(mobile)) {
//            DataAddressByIdCard dataAddressByIdCard = cfAddressDataQueryService.queryAddressByMobile(mobile);
//            if (Objects.nonNull(dataAddressByIdCard)) {
//                materialProvince.add(dataAddressByIdCard.getProvince());
//            }
//        }

        return Lists.newArrayList(materialProvince);
    }

    /**
     * 判断是否是线上渠道
     */
    private Boolean judgeChannel(CrowdfundingInfo crowdfundingInfo) {
        ChannelRefineDTO refineDTO = buildChannelDTO(crowdfundingInfo);
        Response<String> response = cfChannelFeignClient.getChannelByInfoIdWithUserIdAndOldChannel(refineDTO);
        log.info("saveThreeBodyCaseTab judgeChannel refineDTO={} response={}", refineDTO, response);

        String channel = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse("");
        if (StringUtils.isBlank(channel)) {
            log.info("saveThreeBodyCaseTab 无渠道信息 {}", crowdfundingInfo.getId());
            return false;
        }

        ChannelRefine.ChannelRefineResuleEnum channelRefineResuleEnum = ChannelRefine.ChannelRefineResuleEnum.parse(channel);
        if (Objects.isNull(channelRefineResuleEnum)) {
            log.info("saveThreeBodyCaseTab 无渠道信息 {}", crowdfundingInfo.getId());
            return false;
        }

        return StringUtils.equalsAny(channelRefineResuleEnum.getChannelDesc(),
                ChannelRefine.ChannelRefineResuleEnum.WAIHU_YINDAO.getChannelDesc(),
                ChannelRefine.ChannelRefineResuleEnum.YONGHU_ZIZHU.getChannelDesc(),
                ChannelRefine.ChannelRefineResuleEnum.WEIXIN_1V1.getChannelDesc());
    }

    private ChannelRefineDTO buildChannelDTO(CrowdfundingInfo crowdfundingInfo) {
        ChannelRefineDTO refineDTO = new ChannelRefineDTO();
        refineDTO.setInfoId((long) crowdfundingInfo.getId());
        refineDTO.setChannel(crowdfundingInfo.getChannel());
        refineDTO.setUserId(crowdfundingInfo.getUserId());
        return refineDTO;
    }

}
