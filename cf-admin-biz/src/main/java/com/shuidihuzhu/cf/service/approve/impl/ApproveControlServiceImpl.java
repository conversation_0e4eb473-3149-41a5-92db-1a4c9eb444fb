package com.shuidihuzhu.cf.service.approve.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.admin.delegate.SeaUserAuthDelegate;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.constants.AdminUserIDConstants;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.dao.approve.ApproveControlRecordDAO;
import com.shuidihuzhu.cf.delegate.OrganizationDelegate;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.domain.approve.ApproveControlRecordDO;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.approve.ApproveControlFlowTypeEnum;
import com.shuidihuzhu.cf.enums.approve.ApproveControlHandleStatusEnum;
import com.shuidihuzhu.cf.enums.approve.ApproveControlSourceTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.enums.sona.RoleEnum;
import com.shuidihuzhu.cf.event.ApproveControlReleaseEvent;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.mq.impl.AdminCommonMessageHelperService;
import com.shuidihuzhu.cf.mq.producer.MessageBuilder;
import com.shuidihuzhu.cf.service.RedisLockService;
import com.shuidihuzhu.cf.service.approve.ApproveControlService;
import com.shuidihuzhu.cf.vo.approve.ApproveControlRecordVO;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderTypeFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OneTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@RefreshScope
@Service
@Slf4j
public class ApproveControlServiceImpl implements ApproveControlService {

    @Autowired
    private ApproveControlRecordDAO approveControlRecordDAO;

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Resource
    private SeaUserAuthDelegate seaUserAuthDelegate;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private AdminCommonMessageHelperService adminCommonMessageHelperService;

    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    private OrganizationDelegate organizationDelegate;

    @Autowired
    private SeaAccountDelegate seaAccountDelegate;

    @Autowired
    private CfWorkOrderTypeFeignClient cfWorkOrderTypeFeignClient;

    /**
     * default 30分钟失效
     */
    @Value("${apollo.approve-control.invalid-delay-time:1800000}")
    private long invalidDelayTime;

    @Value("${apollo.approve-control-enable: false}")
    private boolean approveControlEnable;

    @Autowired
    private RedisLockService redisLockService;

    private static final long LEASE_TIME_MILLS = 10 * 1000L;

    private static final String APPROVE_LOCK_KEY = "approve-control-lock";

    /**
     * <a href="https://wiki.shuiditech.com/pages/viewpage.action?pageId=*********&focusedCommentId=*********&#comment-*********">流程图</a>
     * @return
     */
    @Override
    public Response<ApproveControlRecordDO> touch2GetControl(int caseId, int operatorId, ApproveControlSourceTypeEnum sourceType, long workOrderId) {

        log.info("touch2GetControl caseId:{}", caseId);
        CrowdfundingInfo fundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        // 审核状态为通过 并且没有强制驳回权限 提示错误
        if (fundingInfo.getStatus() == CrowdfundingStatus.CROWDFUNDING_STATED &&
                !hasForceApprovePermission(operatorId)) {
            log.info("touch2GetControl has passed");
            return NewResponseUtil.makeError(AdminErrorCode.APPROVE_CONTROL_CASE_HAS_PASSED);
        }
        Response<ApproveControlRecordDO> checkLockResp = checkLock(caseId, operatorId);
        if (checkLockResp.ok()) {
            return checkLockResp;
        }
        Response<List<Integer>> resp = cfWorkOrderTypeFeignClient.getByOneLevel(OneTypeEnum.cailiao.getType());
        if (resp.notOk()) {
            log.info("touch2GetControl get order fail");
            return NewResponseUtil.makeResponse(resp.getCode(), resp.getMsg(), null);
        }
        List<Integer> types = resp.getData();
        // 排除主动服务工单
        types.remove(Integer.valueOf(WorkOrderType.cailiao_zhu_dong_fu_wu.getType()));
        types.remove(Integer.valueOf(WorkOrderType.cailiao_fuwu.getType()));
        types.remove(Integer.valueOf(WorkOrderType.picture_publicity_review.getType()));
        Response<List<WorkOrderVO>> listResponse = cfWorkOrderClient.listByCaseIdAndTypeAndResult(caseId, types, Lists.newArrayList(HandleResultEnum.unDoResult()));
        if (listResponse.notOk()) {
            log.info("touch2GetControl get order fail");
            return NewResponseUtil.makeResponse(listResponse.getCode(), listResponse.getMsg(), null);
        }
        List<WorkOrderVO> workOrders = listResponse.getData();
        WorkOrderVO workOrder = null;
        if (CollectionUtils.isNotEmpty(workOrders)) {
            workOrder = workOrders.get(0);
        }

        // 没有工单 直接取锁
        if (workOrder == null) {
            return touch2GetLock(caseId, operatorId, sourceType, workOrderId, ApproveControlFlowTypeEnum.DEFAULT);
        }

        // 有工单 判断工单状态
        workOrderId = workOrder.getWorkOrderId();

        log.info("touch2GetControl workOrderId:{}", workOrderId);
        // 没人领取
        if (workOrder.getHandleResult() == HandleResultEnum.undoing.getType()) {
            Response<ApproveControlRecordDO> controlResponse = touch2GetLock(caseId, operatorId, sourceType, workOrderId, ApproveControlFlowTypeEnum.AUTO_WORK_ORDER_FLOW);
            if (controlResponse.notOk()) {
                log.info("touch2GetControl get lock fail undoing");
                return controlResponse;
            }
            ApproveControlRecordDO controlDO = controlResponse.getData();
            // 领取工单
            Response<Long> longResponse = cfWorkOrderClient.assignWorkOrderSystem(workOrderId, operatorId);
            if (longResponse.notOk()) {
                releaseControl(controlDO);
                log.info("touch2GetControl 领取工单失败");
                return NewResponseUtil.makeResponse(longResponse.getCode(), longResponse.getMsg(), null);
            }
            log.info("touch2GetControl 领取工单成功");
            return NewResponseUtil.makeSuccess(null);
        }

        // 非本人工单未处理完成 提示错误
        long doingOperator = workOrder.getOperatorId();
        if (doingOperator != operatorId) {
            log.info("touch2GetControl 有其他人处理");
            return hasOtherDoingResponse(doingOperator);
        }
        log.info("touch2GetControl 普通获取锁");
        return touch2GetLock(caseId, operatorId, sourceType, workOrderId, ApproveControlFlowTypeEnum.NORMAL_WORK_ORDER_FLOW);
    }

    @Override
    public Response<ApproveControlRecordDO> checkLock(int caseId, int operatorId) {
        ApproveControlRecordDO record = approveControlRecordDAO.getNewestRecordByCaseIdAndHandleStatus(caseId,
                ApproveControlHandleStatusEnum.DOING.getValue());
        if (!approveControlEnable) {
            return NewResponseUtil.makeSuccess(record);
        }
        if (record == null) {
            return NewResponseUtil.makeError(AdminErrorCode.APPROVE_CONTROL_SUBMIT_LOCK_HAS_LOST);
        }
        if (record.getOperatorId() != operatorId) {
            return hasOtherDoingResponse(record.getOperatorId());
        }
        return NewResponseUtil.makeSuccess(record);
    }

    @Override
    public ApproveControlRecordDO getControlInfo(int caseId) {
        return approveControlRecordDAO.getNewestRecordByCaseIdAndHandleStatus(caseId,
                ApproveControlHandleStatusEnum.DOING.getValue());
    }

    @Override
    public ApproveControlRecordVO trans2VO(ApproveControlRecordDO record) {
        if (record == null) {
            return null;
        }
        ApproveControlRecordVO v = ApproveControlRecordVO.create(record);

        int operatorId = record.getOperatorId();
        AuthRpcResponse<AdminUserAccountModel> accountResp = seaAccountClientV1.getValidUserAccountById(operatorId);
        if (accountResp != null && accountResp.getCode() == 0 && accountResp.getResult() != null) {
            AdminUserAccountModel account = accountResp.getResult();
            v.setOperatorName(account.getName());
        }

        CrowdfundingInfo fundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(record.getCaseId());

        v.setInfoUuid(fundingInfo.getInfoId());
        v.setCaseTitle(fundingInfo.getTitle());

        String simpleOrganization = organizationDelegate.getSimpleOrganization(operatorId);
        v.setOperatorOrg(simpleOrganization);
        return v;
    }

    @Override
    public Response<Void> giveUpOperation(int controlRecordId, int operatorId) {
        ApproveControlRecordDO recordDO = approveControlRecordDAO.getById(controlRecordId);
        if (recordDO.getOperatorId() != operatorId) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        releaseControl(recordDO);
        log.info("giveUpOperation controlRecordId:{}, operatorId:{}", controlRecordId, operatorId);
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<Void> forceReleaseControl(int controlRecordId, int operatorId) {
        releaseControl(controlRecordId);
        log.info("forceReleaseControl controlRecordId:{}, operatorId:{}", controlRecordId, operatorId);
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<Void> onUserSubmit(int caseId) {
        ApproveControlRecordDO record = approveControlRecordDAO.getNewestRecordByCaseIdAndHandleStatus(caseId,
                ApproveControlHandleStatusEnum.DOING.getValue());
        if (record == null) {
            return NewResponseUtil.makeSuccess(null);
        }
        log.info("onUserSubmit caseId:{}", caseId);
        releaseControl(record);
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public void releaseControlByWorkOrder(WorkOrderVO workOrderVO) {
        long workOrderId = workOrderVO.getWorkOrderId();
        log.info("releaseControlByWorkOrder workOrderId:{}", workOrderId);
        int caseId = workOrderVO.getCaseId();
        ApproveControlRecordDO record = approveControlRecordDAO.getByCaseIdAndWorkOrderId(caseId, workOrderId);
        releaseControl(record);
    }

    @Override
    public void onWorkOrderAssign(WorkOrderVO workOrderVO) {
        log.info("onWorkOrderAssign");
        int orderType = workOrderVO.getOrderType();
        if (!WorkOrderType.CAI_LIAO_LIST.contains(orderType)) {
            log.info("不是材料工单");
            return;
        }
        Response<ApproveControlRecordDO> r = touch2GetLock(workOrderVO.getCaseId(), Math.toIntExact(workOrderVO.getOperatorId()),
                ApproveControlSourceTypeEnum.WORK_ORDER_HANDLE, workOrderVO.getWorkOrderId(), ApproveControlFlowTypeEnum.NORMAL_WORK_ORDER_FLOW);
        if (r.notOk()) {
            log.error("工单领取时不应该获取不到锁 onWorkOrderAssign workOrderId:{}, response:{}",
                    workOrderVO.getWorkOrderId(), JSON.toJSONString(r));
        }
    }

    @Override
    public void releaseControl(long controlRecordId) {
        log.info("releaseControl controlId:{}", controlRecordId);
        ApproveControlRecordDO record = approveControlRecordDAO.getById(controlRecordId);
        releaseControl(record);
    }

    private void releaseControl(ApproveControlRecordDO recordDO) {
        if (recordDO == null) {
            log.info("不存在记录");
            return;
        }
        log.info("releaseControl id:{}, caseId:{}", recordDO.getId(), recordDO.getCaseId());
        if (recordDO.getHandleStatus() != ApproveControlHandleStatusEnum.DOING.getValue()){
            log.info("不是处理中 无需释放");
            return;
        }
        updateHandleStatusDoing2Invalid(recordDO.getId());
        if (recordDO.getFlowType() == ApproveControlFlowTypeEnum.AUTO_WORK_ORDER_FLOW.getValue()) {
            long workOrderId = recordDO.getWorkOrderId();
            // 自动领取的工单在释放锁的时候要同时释放工单
            cfWorkOrderClient.releaseWorkOrderSystem(workOrderId, AdminUserIDConstants.SYSTEM);
            log.info("自动释放工单 workOrderId:{}", workOrderId);
        }
    }

    private Response<ApproveControlRecordDO> touch2GetLock(int caseId, int operatorId, ApproveControlSourceTypeEnum sourceType,
                                         long workOrderId, ApproveControlFlowTypeEnum flowTypeEnum) {
        log.info("touch2GetLock caseId:{}, operatorId:{}, sourceType:{}, workOrderId:{}",
                caseId, operatorId, sourceType, workOrderId);
        Response<ApproveControlRecordDO> result = redisLockService.callWithLock(() -> {
            ApproveControlRecordDO record = approveControlRecordDAO.getNewestRecordByCaseIdAndHandleStatus(caseId,
                    ApproveControlHandleStatusEnum.DOING.getValue());
            if (record != null) {
                if (record.getOperatorId() != operatorId) {
                    log.info("有其他人持有锁");
                    return hasOtherDoingResponse(record.getOperatorId());
                }
                // 若本人已有锁 获取失败
                log.info("本人已持有锁");
                return NewResponseUtil.makeSuccess(record);
//                updateHandleStatusDoing2Invalid(record.getId());
            }
            record = new ApproveControlRecordDO();
            record.setCaseId(caseId);
            record.setHandleStatus(ApproveControlHandleStatusEnum.DOING.getValue());
            record.setOperatorId(operatorId);
            record.setSourceType(sourceType.getValue());
            record.setWorkOrderId(workOrderId);
            record.setFlowType(flowTypeEnum.getValue());
            approveControlRecordDAO.insert(record);

            if (flowTypeEnum.isCanAutoReleaseLock()){
                // 发送30m mq回收锁
                Message<ApproveControlReleaseEvent> message = MessageBuilder
                        .createWithPayload(new ApproveControlReleaseEvent(record.getId()))
                        .setDelayTime(invalidDelayTime)
                        .setTags(MQTagCons.CF_APPROVE_CONTROL_RELEASE)
                        .addKey(MQTagCons.CF_APPROVE_CONTROL_RELEASE, caseId, workOrderId)
                        .build();
                adminCommonMessageHelperService.send(message);
            }

            log.info("create lock");
            return NewResponseUtil.makeSuccess(record);
        }, getLockKey(caseId), LEASE_TIME_MILLS);
        return result == null ? NewResponseUtil.makeError(AdminErrorCode.SYSTEM_OPERATION_FAILED) : result;
    }

    private String getLockKey(int caseId) {
        return APPROVE_LOCK_KEY + caseId;
    }

    private boolean hasForceApprovePermission(int operatorId) {
        return seaUserAuthDelegate.hasPermissionSimple(operatorId, RoleEnum.REFUSE_FORCE.getPermission());
    }

    private int updateHandleStatusDoing2Invalid(long controlRecordId){
       return updateHandleStatus(controlRecordId, ApproveControlHandleStatusEnum.DOING, ApproveControlHandleStatusEnum.INVALID);
    }

    private int updateHandleStatus(long controlRecordId,
                                   ApproveControlHandleStatusEnum oldStatus,
                                   ApproveControlHandleStatusEnum newStatus) {
        return approveControlRecordDAO
                .updateHandleStatusById(controlRecordId, oldStatus.getValue(), newStatus.getValue(), new Date());
    }

    @NotNull
    private <T> Response<T> hasOtherDoingResponse(long operatorId) {
        log.info("hasOtherDoingResponse operatorId:{}", operatorId);
        String name = seaAccountDelegate.getNameWithOrgByUserId((int) operatorId);
        return NewResponseUtil.makeResponse(AdminErrorCode.APPROVE_CONTROL_HAS_SOME_ONE_DOING.getCode(),
                name + "正在处理中，稍后重试", null);
    }

}
