package com.shuidihuzhu.cf.service.workorder.initialAudit;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.client.material.model.annotation.PreModifyFieldMapping;
import com.shuidihuzhu.cf.client.material.model.materialField.CfMaterialPreModifyField;
import com.shuidihuzhu.cf.client.material.model.preSubmit.CfRejectPositionFields;
import com.shuidihuzhu.cf.client.material.utils.MaterialCollectionUtils;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.cf.vo.initialaudit.CfPropertyInsuranceVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class InitialAuditPreModifyChangeService {

    private static Set<String> NOT_EQUAL = Sets.newHashSet(
            CfMaterialPreModifyField.carProperty,
            CfMaterialPreModifyField.houseProperty,
            CfMaterialPreModifyField.otherPlatform,
            CfMaterialPreModifyField.baseInfoAttachments
    );

    private static Set<String> DEFAULT_STRING_VALUE_FIELD_NAMES = Sets.newHashSet(
            CfMaterialPreModifyField.firstSelfRealName,
            CfMaterialPreModifyField.firstSelfIdCard,
            CfMaterialPreModifyField.patientRealName,
            CfMaterialPreModifyField.patientIdCard,
            CfMaterialPreModifyField.patientBornCard
    );

    public static CfRejectPositionFields getInsuranceModifyFields(
            CfPropertyInsuranceInfoModel source,
            CfPropertyInsuranceInfoModel preModify,
            Set<String> canModifyFields) {
        if (source == null || preModify == null || CollectionUtils.isEmpty(canModifyFields)) {
            return null;
        }

        CfRejectPositionFields modifyFields = getModifyFields(source, preModify, canModifyFields);
        Map<Integer, Set<String>> rejectPositionForSea = Maps.newHashMap();
        Map<Integer, Set<String>> rejectPositionForC = Maps.newHashMap();

        Set<String> hasModifyFields = getCarChangeFields(source.getCarProperty(), preModify.getCarProperty());
        if (CollectionUtils.isNotEmpty(hasModifyFields)) {
            rejectPositionForC.put(InitialAuditItem.EditMaterialType.CAR.getCode(), hasModifyFields);
            rejectPositionForSea.put(InitialAuditItem.EditMaterialType.CAR.getCode(), hasModifyFields);
        }

        hasModifyFields = getHouseChangeFields(source.getHouseProperty(), preModify.getHouseProperty(), false);
        if (CollectionUtils.isNotEmpty(hasModifyFields)) {
            int rejectCode = source.getSelfBuiltHouse() != null ?
                    InitialAuditItem.EditMaterialType.OTHER_HOUSE.getCode() : InitialAuditItem.EditMaterialType.HOUSE.getCode();
            rejectPositionForC.put(rejectCode, hasModifyFields);
            rejectPositionForSea.put(rejectCode, hasModifyFields);
        }

        hasModifyFields = getHouseChangeFields(source.getSelfBuiltHouse(), preModify.getSelfBuiltHouse(), true);
        if (CollectionUtils.isNotEmpty(hasModifyFields)) {
            rejectPositionForC.put(InitialAuditItem.EditMaterialType.SELF_BUILT_HOUSE.getCode(), hasModifyFields);
            rejectPositionForSea.put(InitialAuditItem.EditMaterialType.SELF_BUILT_HOUSE.getCode(), hasModifyFields);
        }

        hasModifyFields = getRaiseOnOtherChangeFields(source.getOtherPlatform(), preModify.getOtherPlatform());
        if (CollectionUtils.isNotEmpty(hasModifyFields)) {
            rejectPositionForC.put(InitialAuditItem.EditMaterialType.HAS_OTHER_PLATFORM.getCode(), hasModifyFields);
            rejectPositionForSea.put(InitialAuditItem.EditMaterialType.HAS_OTHER_PLATFORM.getCode(), hasModifyFields);
        }
        if (MapUtils.isNotEmpty(rejectPositionForSea)) {
            if (modifyFields == null) {
                modifyFields = new CfRejectPositionFields();
            }
            modifyFields.setRejectPositionForC(MaterialCollectionUtils.getMergeMap(rejectPositionForC, modifyFields.getRejectPositionForC()));
            modifyFields.setRejectPositionForSea(MaterialCollectionUtils.getMergeMap(rejectPositionForSea, modifyFields.getRejectPositionForSea()));
        }

        return modifyFields;
    }

    public static Set<String> getCarChangeFields(CfPropertyInsuranceInfoModel.CarPropertyInfo source,
                                           CfPropertyInsuranceInfoModel.CarPropertyInfo preModify) {
        if (source == null || preModify == null) {
            return Sets.newHashSet();
        }

        Set<String> fieldNames = Sets.newHashSet();
        if (!Objects.equals(source.getTotalValueRangeType(), preModify.getTotalValueRangeType())) {
            fieldNames.add(CfMaterialPreModifyField.carTotalValueRangeType);
        }

        if (!Objects.equals(source.getSaleValueRangeType(), preModify.getSaleValueRangeType())) {
            fieldNames.add(CfMaterialPreModifyField.carSaleValueRangeType);
        }

        if (!Objects.equals(source.getTotalCount(), preModify.getTotalCount())) {
            fieldNames.add(CfMaterialPreModifyField.carTotalCount);
        }

        if (!Objects.equals(source.getSaleCount(), preModify.getSaleCount())) {
            fieldNames.add(CfMaterialPreModifyField.carSaleCount);
        }

        if (!Objects.equals(source.getSaleStatus(), preModify.getSaleStatus())) {
            fieldNames.add(CfMaterialPreModifyField.carSaleStatus);
        }

        if (!Objects.equals(source.getTotalValueUserDefined(), preModify.getTotalValueUserDefined())) {
            fieldNames.add(CfMaterialPreModifyField.carTotalValueUserDefined);
        }

        if (!Objects.equals(source.getSaleValueUserDefined(), preModify.getSaleValueUserDefined())) {
            fieldNames.add(CfMaterialPreModifyField.carSaleValueUserDefined);
        }

        return fieldNames;
    }

    public static Set<String> getHouseChangeFields(CfPropertyInsuranceInfoModel.HousePropertyInfo source,
                                                   CfPropertyInsuranceInfoModel.HousePropertyInfo preModify,
                                                   boolean selfBuiltHouse) {

        if (source == null || preModify == null) {
            return Sets.newHashSet();
        }

        Set<String> fieldNames = Sets.newHashSet();

        if (!Objects.equals(source.getTotalValueRangeType(), preModify.getTotalValueRangeType())) {
            fieldNames.add(selfBuiltHouse ? CfMaterialPreModifyField.selfBuiltHouseTotalValueRangeType
                    : CfMaterialPreModifyField.houseTotalValueRangeType);
        }

        if (!Objects.equals(source.getPureValueRangeType(), preModify.getPureValueRangeType())) {
            fieldNames.add(CfMaterialPreModifyField.housePureValueRangeType);
        }


        if (!Objects.equals(source.getSaleValueRangeType(), preModify.getSaleValueRangeType())) {
            fieldNames.add(selfBuiltHouse ? CfMaterialPreModifyField.selfBuiltHouseSaleValueRangeType
                    : CfMaterialPreModifyField.houseSaleValueRangeType);
        }

        if (!Objects.equals(source.getTotalCount(), preModify.getTotalCount())) {
            fieldNames.add(selfBuiltHouse ? CfMaterialPreModifyField.selfBuiltHouseTotalCount
                    : CfMaterialPreModifyField.houseTotalCount);
        }

        if (!Objects.equals(source.getSaleCount(), preModify.getSaleCount())) {
            fieldNames.add(selfBuiltHouse ? CfMaterialPreModifyField.selfBuiltHouseSaleCount
                    : CfMaterialPreModifyField.houseSaleCount);
        }

        if (!Objects.equals(source.getSaleStatus(), preModify.getSaleStatus())) {
            fieldNames.add(selfBuiltHouse ? CfMaterialPreModifyField.selfBuiltHouseSaleStatus
                    : CfMaterialPreModifyField.houseSaleStatus);
        }

        if (!Objects.equals(source.getTotalValueUserDefined(), preModify.getTotalValueUserDefined())) {
            fieldNames.add(selfBuiltHouse ? CfMaterialPreModifyField.selfBuiltHouseTotalValueUserDefined
                    : CfMaterialPreModifyField.houseTotalValueUserDefined);
        }

        if (!Objects.equals(source.getPureValueUserDefined(), preModify.getPureValueUserDefined())) {
            fieldNames.add(CfMaterialPreModifyField.housePureValueUserDefined);
        }


        if (!Objects.equals(source.getSaleValueUserDefined(), preModify.getSaleValueUserDefined())) {
            fieldNames.add(selfBuiltHouse ? CfMaterialPreModifyField.selfBuiltHouseSaleValueUserDefined
                    : CfMaterialPreModifyField.houseSaleValueUserDefined);
        }

        return fieldNames;
    }

    public static Set<String> getRaiseOnOtherChangeFields(CfPropertyInsuranceInfoModel.RaiseOnOtherPlatform source,
                                                   CfPropertyInsuranceInfoModel.RaiseOnOtherPlatform preModify) {

        if (source == null || preModify == null) {
            return Sets.newHashSet();
        }

        Set<String> fieldNames = Sets.newHashSet();

        if (!Objects.equals(source.getHasRaise(), preModify.getHasRaise())) {
            fieldNames.add(CfMaterialPreModifyField.otherHasRaise);
        }
        if (!Objects.equals(source.getRaiseAmount(), preModify.getRaiseAmount())) {
            fieldNames.add(CfMaterialPreModifyField.otherRaiseAmount);
        }
        if (!Objects.equals(source.getRemainAmount(), preModify.getRemainAmount())) {
            fieldNames.add(CfMaterialPreModifyField.otherRemainAmount);
        }
        if (!Objects.equals(Objects.requireNonNullElse(source.getUseForMedical(), 0),
                Objects.requireNonNullElse(preModify.getUseForMedical(), 0))) {
            fieldNames.add(CfMaterialPreModifyField.otherUseForMedical);
        }
        if (!Objects.equals(source.getMoneyUseFor(), preModify.getMoneyUseFor())) {
            fieldNames.add(CfMaterialPreModifyField.otherMoneyUseFor);
        }

        return fieldNames;
    }

    public static CfRejectPositionFields getBaseInfoChangeFields(InitialAuditCaseDetail.CaseBaseInfo source,
                                                                 InitialAuditCaseDetail.CaseBaseInfo preModify,
                                                                 Set<String> canModifyFields) {

        if (source == null || preModify == null || CollectionUtils.isEmpty(canModifyFields)) {
            return null;
        }

        Map<Integer, Set<String>> rejectPositionForSea = Maps.newHashMap();
        Map<Integer, Set<String>> rejectPositionForC = Maps.newHashMap();

        if (!Objects.equals(source.getTitle(), preModify.getTitle())) {
            rejectPositionForSea.put(InitialAuditItem.EditMaterialType.BASE_INFO_TITLE.getCode(),
                    Sets.newHashSet(CfMaterialPreModifyField.baseInfoTitle));
            rejectPositionForC.put(InitialAuditItem.EditMaterialType.BASE_INFO_TITLE.getCode(),
                    Sets.newHashSet(CfMaterialPreModifyField.baseInfoTitle));
        }
        if (!Objects.equals(source.getContent(), preModify.getContent())) {
            rejectPositionForSea.put(InitialAuditItem.EditMaterialType.BASE_INFO_CONTENT.getCode(),
                    Sets.newHashSet(CfMaterialPreModifyField.baseInfoContent));
            rejectPositionForC.put(InitialAuditItem.EditMaterialType.BASE_INFO_CONTENT.getCode(),
                    Sets.newHashSet(CfMaterialPreModifyField.baseInfoContent));
        }

        if (isImageUrlModify(source.getAttachments(), preModify.getAttachments())) {
            rejectPositionForSea.put(InitialAuditItem.EditMaterialType.BASE_INFO_IMAGE.getCode(),
                    Sets.newHashSet(CfMaterialPreModifyField.baseInfoAttachments));
            rejectPositionForC.put(InitialAuditItem.EditMaterialType.BASE_INFO_IMAGE.getCode(),
                    Sets.newHashSet(CfMaterialPreModifyField.baseInfoAttachments));
        }

        if (source.getTargetAmount() != preModify.getTargetAmount()) {
            rejectPositionForSea.put(InitialAuditItem.EditMaterialType.TARGET_AMOUNT.getCode(),
                    Sets.newHashSet(CfMaterialPreModifyField.targetAmount));
            rejectPositionForC.put(InitialAuditItem.EditMaterialType.TARGET_AMOUNT.getCode(),
                    Sets.newHashSet(CfMaterialPreModifyField.targetAmount));
        }

        return MapUtils.isEmpty(rejectPositionForSea) ? null : new CfRejectPositionFields(rejectPositionForSea, rejectPositionForC);

    }

    private static boolean isImageUrlModify(List<CrowdfundingAttachmentVo> source,  List<CrowdfundingAttachmentVo> preModify) {


        List<String> sourceUrls = getSortImageUrls(source);
        List<String> preModifyUrls = getSortImageUrls(preModify);

        if (sourceUrls.size() != preModifyUrls.size()) {
            return true;
        }

        for (int i = 0; i < sourceUrls.size(); ++i) {
            if (!sourceUrls.get(i).equals(preModifyUrls.get(i))) {
                return true;
            }
        }

        return false;
    }

    private static List<String> getSortImageUrls(List<CrowdfundingAttachmentVo> attachments) {

        List<String> sourceUrls = CollectionUtils.isEmpty(attachments) ? Lists.newArrayList()
                : attachments.stream().map(CrowdfundingAttachmentVo::getUrl).collect(Collectors.toList());
        Collections.sort(sourceUrls);

        return sourceUrls;
    }

    public static <T> CfRejectPositionFields getModifyFields(T source, T target, Set<String> canModifyFields) {

        if (source == null || target == null || CollectionUtils.isEmpty(canModifyFields)) {
            return null;
        }

        Map<Integer, Set<String>> rejectPositionForSea = Maps.newHashMap();
        Map<Integer, Set<String>> rejectPositionForC = Maps.newHashMap();

        Field[] allFields = source.getClass().getDeclaredFields();
        for (Field field : allFields) {
            try {
                PreModifyFieldMapping fieldMapping = field.getAnnotation(PreModifyFieldMapping.class);
                if (fieldMapping == null
                        || fieldMapping.rejectPositions() == null
                        || !canModifyFields.contains(fieldMapping.fieldNameForSea())
                        || NOT_EQUAL.contains(fieldMapping.fieldNameForSea())) {
                    continue;
                }

                field.setAccessible(true);

                if (Objects.equals(getDefaultNull(source, field, fieldMapping.fieldNameForSea()),
                        getDefaultNull(target, field, fieldMapping.fieldNameForSea()))) {
                    continue;
                }

                for (InitialAuditItem.EditMaterialType editPosition : fieldMapping.rejectPositions()) {
                    MaterialCollectionUtils.putValueToSet(rejectPositionForSea,
                            editPosition.getCode(), fieldMapping.fieldNameForSea());
                    MaterialCollectionUtils.putValueToSet(rejectPositionForC,
                            editPosition.getCode(), fieldMapping.fieldNameForC());
                }
            } catch (Exception e) {
                log.error("通过注解拿取对象值错误。field:{}", field, e);
            }
        }

        if (MapUtils.isEmpty(rejectPositionForSea)) {
            return null;
        }
        return new CfRejectPositionFields(rejectPositionForSea, rejectPositionForC);
    }

    private static <T> Object getDefaultNull(T source, Field field, String fieldName) throws IllegalAccessException {

        Object value = field.get(source);
        if (value == null && DEFAULT_STRING_VALUE_FIELD_NAMES.contains(fieldName)) {
            value = "";
        }
        return value;
    }
}
