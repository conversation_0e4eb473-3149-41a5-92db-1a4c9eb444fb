package com.shuidihuzhu.cf.service.report;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.dao.crowdfunding.*;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfReportAddTrustBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfReportFollowCommentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.IAdminCredibleInfoService;
import com.shuidihuzhu.cf.biz.crowdfunding.report.*;
import com.shuidihuzhu.cf.client.adminpure.constants.ReportCons;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.delegate.shorturl.ShortUrlDelegate;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportAddTrustDisposeVo;
import com.shuidihuzhu.cf.model.report.*;
import com.shuidihuzhu.cf.service.FinanceApproveService;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cf.vo.report.CfReportDisposeActionTemplateVO;
import com.shuidihuzhu.cf.vo.report.CfSendProveInfoRecord;
import com.shuidihuzhu.cf.vo.report.ReportInsteadWorkOrderRelateVO;
import com.shuidihuzhu.cf.vo.report.ReportProveInfoVO;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCoreFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.HandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.ext.WorkOrderExtFeignClient;
import com.shuidihuzhu.client.cf.workorder.ext.WorkOrderExtVO;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.msg.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: lixiaoshuang
 * @create: 2020-05-06 16:56
 **/
@Service
@Slf4j
public class CfReportCredibleInfoService {

    @Autowired
    private AdminCfReportAddTrustDao adminCfReportAddTrustDao;
    @Autowired
    private AdminCfReportAddTrustBiz adminCfReportAddTrustBiz;
    @Autowired
    private FinanceApproveService financeApproveService;
    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CfReportFollowCommentBiz cfReportFollowCommentBiz;
    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;
    @Autowired
    private CfReportDisposeActionTemplateDao cfReportDisposeActionTemplateDao;
    @Autowired
    private AdminCfReportDisposeActionBiz adminCfReportDisposeActionBiz;
    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;
    @Autowired
    private CfSendProveTemplateBiz cfSendProveTemplateBiz;
    @Autowired
    private CfSendProveBiz cfSendProveBiz;
    @Autowired
    private IAdminCredibleInfoService adminCredibleInfoService;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Autowired
    private CfSendProveSnapshotDao cfSendProveSnapshotDao;
    @Autowired
    private AdminCfReportProveDisposeActionBiz adminCfReportProveDisposeActionBiz;
    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Autowired
    private CfSendProveRejectedReasonDao cfSendProveRejectedReasonDao;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private ReportInsteadWorkOrderRelateDAO reportInsteadWorkOrderRelateDAO;
    @Autowired
    private AdminCfReportActionClassifyBiz adminCfReportActionClassifyBiz;
    @Autowired
    private AdminCrowdfundingReportDao adminCrowdfundingReportDao;
    @Autowired
    private WorkOrderExtFeignClient workOrderExtFeignClient;
    @Autowired
    private ReportOperationService reportOperationService;
    @Resource
    private ShortUrlDelegate shortUrlDelegate;

    @Resource
    private MsgClientV2Service msgClientV2Service;
    @Resource
    private WorkOrderCoreFeignClient workOrderCoreFeignClient;

    @Autowired
    private MaskUtil maskUtil;


    public void cancelAddTrust(long addTrustId, String reason, int userId) {
        //更新字段
        CfReportAddTrust cfReportAddTrust = adminCfReportAddTrustDao.queryById(addTrustId);
        cfReportAddTrust.setAuditStatus(AddTrustAuditStatusEnum.CANCEL.getCode());
        cfReportAddTrust.setOperatorContent(reason);
        adminCfReportAddTrustBiz.update(cfReportAddTrust);
        //记录操作记录
        String operation = "补充证明撤回";
        AdminUserAccountModel userAccount = this.seaAccountClientV1.getValidUserAccountById(userId).getResult();
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(cfReportAddTrust.getInfoUuid());
        cfReportFollowCommentBiz.save(userId, operation + ":" + reason, CaseReportFollowDealStatusEnum.ADD_TRUST.getValue(),
                crowdfundingInfo.getId(), userAccount.getName());
        financeApproveService.addApprove(crowdfundingInfo, operation, reason, userId);
    }

    public List<CfReportDisposeActionTemplateVO> getActionTemplate(List<Long> actionIds) {
        var cfReportDisposeActionTemplates = cfReportDisposeActionTemplateDao.selectByActionIds(actionIds);
        Map<Long, CfReportDisposeActionTemplate> cfReportDisposeActionTemplateMap = cfReportDisposeActionTemplates.stream()
                .collect(Collectors.toMap(CfReportDisposeActionTemplate::getActionId, Function.identity(), (k1, k2) -> k2));

        List<CfReportDisposeAction> cfReportDisposeActions = adminCfReportDisposeActionBiz.selectByIds(actionIds);
        Map<Long, CfReportDisposeAction> disposeActionMap = cfReportDisposeActions.stream()
                .collect(Collectors.toMap(CfReportDisposeAction::getId, Function.identity(), (k1, k2) -> k2));

        List<CfReportDisposeActionTemplateVO> cfReportDisposeActionTemplateVOS = Lists.newArrayList();
        for (Long actionId : actionIds) {
            CfReportDisposeActionTemplateVO cfReportDisposeActionTemplateVO = new CfReportDisposeActionTemplateVO();
            cfReportDisposeActionTemplateVO.setActionId(actionId);
            cfReportDisposeActionTemplateVO.setTitle("");
            cfReportDisposeActionTemplateVO.setContent("");
            cfReportDisposeActionTemplateVO.setCommitmentContent("");
            CfReportDisposeActionTemplate cfReportDisposeActionTemplate = cfReportDisposeActionTemplateMap.get(actionId);
            var cfReportDisposeAction = disposeActionMap.get(actionId);

            if (Objects.nonNull(cfReportDisposeActionTemplate)) {
                cfReportDisposeActionTemplateVO.setTemplateId(cfReportDisposeActionTemplate.getId());
                cfReportDisposeActionTemplateVO.setId(cfReportDisposeActionTemplate.getId());
                if (cfReportDisposeAction.isHasTemplate()){
                    cfReportDisposeActionTemplateVO.setTitle(cfReportDisposeActionTemplate.getTitle());
                    cfReportDisposeActionTemplateVO.setContent(cfReportDisposeActionTemplate.getContent());
                    cfReportDisposeActionTemplateVO.setCommitmentContent(cfReportDisposeActionTemplate.getCommitmentContent());
                }
            }

            if (Objects.nonNull(cfReportDisposeAction)) {
                cfReportDisposeActionTemplateVO.setHelp(cfReportDisposeAction.isHelp());
                cfReportDisposeActionTemplateVO.setHasTemplate(cfReportDisposeAction.isHasTemplate());
                cfReportDisposeActionTemplateVO.setDisposeAction(cfReportDisposeAction.getDisposeAction());
                //查询处理动作对应分类名
                CfReportActionClassify cfReportActionClassify = adminCfReportActionClassifyBiz.getById(cfReportDisposeAction.getActionClassifyId());
                if (Objects.nonNull(cfReportActionClassify)) {
                    cfReportDisposeActionTemplateVO.setActionClassify(cfReportActionClassify.getActionClassify());
                }
            }
            cfReportDisposeActionTemplateVOS.add(cfReportDisposeActionTemplateVO);
        }
        return cfReportDisposeActionTemplateVOS;
    }

    public Response sendProve(List<CfSendProveTemplate> cfSendProveTemplates, int caseId, int userId) {
        //校验是否可以下发
        Response<WorkOrderVO> response = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, WorkOrderType.REPORT_TYPES);
        WorkOrderVO workOrderVO = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);

        if (Objects.nonNull(workOrderVO)) {
            if (workOrderVO.getOrderType() != WorkOrderType.lost_report.getType()
                    && workOrderVO.getHandleResult() != HandleResultEnum.reach_agree.getType()) {
                return NewResponseUtil.makeFail("工单状态为处理中，无法下发举报代录入");
            }
        }
        List<CfSendProveTemplate> sendProveTemplates = cfSendProveTemplateBiz.findByCaseIdAndAuditStatus(caseId,
                List.of(AddTrustAuditStatusEnum.UN_SUBMITTED.getCode(), AddTrustAuditStatusEnum.REJECTED.getCode(),
                        AddTrustAuditStatusEnum.SUBMITTED.getCode()));
        if (CollectionUtils.isNotEmpty(sendProveTemplates)) {
            return NewResponseUtil.makeFail("存在待审核的补充证明，请先审核");
        }
        //插入补充证明
        CfSendProve cfSendProve = new CfSendProve();
        cfSendProve.setCaseId(caseId);
        cfSendProve.setPictureAuditStatus(AddTrustAuditStatusEnum.UN_SUBMITTED.getCode());
        var count = cfSendProveBiz.insertOne(cfSendProve);
        if (count > 0) {
            //插入可信信息
            CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
            adminCredibleInfoService.insertOne(crowdfundingInfo.getId(), crowdfundingInfo.getUserId(), cfSendProve.getId(),
                    CredibleTypeEnum.HELP_PROVE.getKey(), AddTrustAuditStatusEnum.UN_SUBMITTED.getCode(), userId);
            List<CfSendProveTemplate> cfSendProveTemplateList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(cfSendProveTemplates)) {
                //只下发了不支持待录入的模板
                //插入下发的模板
                cfSendProveTemplateList = cfSendProveTemplates.stream().peek(cfSendProveTemplate -> {
                    cfSendProveTemplate.setProveId(cfSendProve.getId());
                    cfSendProveTemplate.setAuditStatus(AddTrustAuditStatusEnum.UN_SUBMITTED.getCode());
                }).collect(Collectors.toList());
                cfSendProveTemplateBiz.batchInsert(cfSendProveTemplateList);
            }
            //关联举报下发代录入id与工单id
            WorkOrderVO workOrder = getLastWorkOrderByCaseId(caseId);
            ReportInsteadWorkOrderRelateVO reportInsteadWorkOrderRelateVO = ReportInsteadWorkOrderRelateVO.builder()
                    .reportInsteadId(cfSendProve.getId())
                    .workOrderId(workOrder.getWorkOrderId())
                    .build();
            reportInsteadWorkOrderRelateDAO.insert(reportInsteadWorkOrderRelateVO);
            //发送短信
            this.sendSMS(crowdfundingInfo.getInfoId(), crowdfundingInfo.getUserId(), AddTrustModelNumEnum.MHAUEC100500016.name(), 2);
            //发送模板消息
            this.sendTemplateMsg(crowdfundingInfo.getInfoId(), crowdfundingInfo.getUserId(), AddTrustModelNumEnum.LADTAD100502016.name());
            //记录快照
            this.addSnapshot(caseId, cfSendProve, crowdfundingInfo.getInfoId(), cfSendProveTemplateList, AddTrustAuditStatusEnum.UN_SUBMITTED.getCode());
            financeApproveService.addApprove(crowdfundingInfo, "补充证明信息", "发送了补充证明信息", userId);

            reportOperationService.operation(caseId, ReportCons.ActionType.GhostInputSend, userId);
        }
        return NewResponseUtil.makeSuccess(null);
    }

    private void addSnapshot(int caseId, CfSendProve cfSendProve, String infoUuid, List<CfSendProveTemplate> proveTemplates,
                             int auditStatus) {
        CfReportProveDisposeAction cfReportProveDisposeAction = adminCfReportProveDisposeActionBiz.findByInfoUuid(infoUuid);
        List<CfReportAddTrustDisposeVo> cfReportAddTrustDisposeVos = Lists.newArrayList();
        if (Objects.nonNull(cfReportProveDisposeAction)) {
            try {
                cfReportAddTrustDisposeVos = JSONObject.parseObject(cfReportProveDisposeAction.getDisposeAction(), new TypeReference<>() {
                });
            } catch (Exception e) {
                log.error("cfReportAddTrustDisposeVos parse error", e);
            }
        }
        CfSendProveSnapshotBO cfSendProveSnapshotBO = new CfSendProveSnapshotBO();
        cfSendProveSnapshotBO.setCfSendProve(cfSendProve);
        cfSendProveSnapshotBO.setCfReportAddTrustDisposeVos(cfReportAddTrustDisposeVos);
        cfSendProveSnapshotBO.setCfSendProveTemplates(proveTemplates);
        cfSendProveSnapshotBO.setAuditStatus(auditStatus);

        CfSendProveSnapshot cfSendProveSnapshot = new CfSendProveSnapshot();
        cfSendProveSnapshot.setCaseId(caseId);
        cfSendProveSnapshot.setProveId(cfSendProve.getId());
        cfSendProveSnapshot.setProveSnapshot(JSON.toJSONString(cfSendProveSnapshotBO));
        cfSendProveSnapshot.setAuditStatus(auditStatus);
        cfSendProveSnapshotDao.insertOne(cfSendProveSnapshot);
    }

    private void sendTemplateMsg(String infoUuid, long userId, String modelNum) {
        Map<Long, Map<Integer, String>> mapMap = Maps.newHashMap();
        HashMap<Integer, String> params = Maps.newHashMap();
        params.put(1, infoUuid);
        params.put(2, DateUtil.getCurrentDateStr());
        mapMap.put(userId, params);
        msgClientV2Service.sendWxParamsMsg(modelNum, mapMap);
    }

    public void sendSMS(String infoUuid, long userId, String modelNum, int paramKey) {
        UserInfoModel userAccount = userInfoServiceBiz.getUserInfoByUserId(userId);
        String cryptoMobile = userAccount.getCryptoMobile();
        //发送带承诺书短信
        String url = new StringBuilder()
                .append("https://www.shuidichou.com/raise/report/instead-fill?infoUuid=")
                .append(infoUuid)
                .append("&channel=wx_null_msg_jbdxg28270016").toString();

        String shortUrl = shortUrlDelegate.process(url);
        Map<String, Map<Integer, String>> mapMap = Maps.newHashMap();
        mapMap.put(cryptoMobile, Map.of(paramKey, shortUrl));
        msgClientV2Service.sendSmsParamsMsg(modelNum, mapMap, true);
    }

    public ReportProveInfoVO getProveInfo(int caseId, long subId) {
        final ReportProveInfoVO v = new ReportProveInfoVO();
        CfSendProve cfSendProve = cfSendProveBiz.getById(subId);
        if (Objects.isNull(cfSendProve)) {
            return v;
        }
        List<CfSendProveTemplate> cfSendProveTemplates = cfSendProveTemplateBiz.findByCaseIdAndProveId(caseId, cfSendProve.getId());
        CfCredibleInfoDO cfCredibleInfoDO = adminCredibleInfoService.queryBySubId(subId, CredibleTypeEnum.HELP_PROVE.getKey());
        //获取快照
        List<CfSendProveSnapshot> cfSendProveSnapshot = cfSendProveSnapshotDao.getSnapshot(caseId, subId, List.of(AddTrustAuditStatusEnum.UN_SUBMITTED.getCode(),
                AddTrustAuditStatusEnum.REJECTED.getCode()));
        CfSendProveSnapshotBO cfSendProveSnapshotBO = null;
        if (CollectionUtils.isNotEmpty(cfSendProveSnapshot)) {
            var proveSnapshot = cfSendProveSnapshot.get(cfSendProveSnapshot.size() - 1).getProveSnapshot();
            cfSendProveSnapshotBO = JSON.parseObject(proveSnapshot, CfSendProveSnapshotBO.class);
        }

        //获取处理动作
        List<CfReportAddTrustDisposeVo> cfReportAddTrustDisposeVos = getLastActionByCaseId(caseId);
        v.setTemplate(cfSendProveTemplates);
        v.setCfSendProve(cfSendProve);
        v.setSnapshot(cfSendProveSnapshotBO);
        v.setCredibleStatus(cfCredibleInfoDO.getAuditStatus());
        v.setProveDisposeAction(cfReportAddTrustDisposeVos);
        return v;
    }

    public List<CfReportAddTrustDisposeVo> getLastActionByCaseId(int caseId) {
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        CfReportProveDisposeAction cfReportProveDisposeAction = adminCfReportProveDisposeActionBiz.findByInfoUuid(crowdfundingInfo.getInfoId());
        List<CfReportAddTrustDisposeVo> cfReportAddTrustDisposeVos = Lists.newArrayList();
        if (Objects.nonNull(cfReportProveDisposeAction)) {
            try {
                cfReportAddTrustDisposeVos = JSONObject.parseObject(cfReportProveDisposeAction.getDisposeAction(), new TypeReference<>() {
                });
            } catch (Exception e) {
                log.error("cfReportAddTrustDisposeVos parse error", e);
            }
        }
        return cfReportAddTrustDisposeVos;
    }

    public void proveAudit(long proveId, List<CfSendProveTemplate> cfSendProveTemplates, String rejectedReason, String cancelReason,
                           String pictureUrl, int pictureAuditStatus, String pictureRejectedReason, int adminUserId, long workOrderId) {
        CfSendProve cfSendProve = cfSendProveBiz.getById(proveId);

        //审核图片
        if (pictureAuditStatus > 0) {
            cfSendProveBiz.auditPictureUrl(proveId, pictureUrl, pictureAuditStatus, pictureRejectedReason);
        }
        if (StringUtils.isNotBlank(pictureRejectedReason)) {
            CfSendProveRejectedReason cfSendProveRejectedReason = new CfSendProveRejectedReason(cfSendProve.getCaseId(),
                    proveId, pictureRejectedReason, 3);
            cfSendProveRejectedReasonDao.insertOne(cfSendProveRejectedReason);
        }
        //审核模板
        cfSendProveTemplates.forEach(cfSendProveTemplate -> {
            //判断端模板是否是新增
            CfSendProveTemplate proveTemplate = cfSendProveTemplateBiz.findByCaseIdAndProveIdAndTemplateId(cfSendProveTemplate.getCaseId(),
                    proveId, cfSendProveTemplate.getActionId(), cfSendProveTemplate.getTemplateId());
            if (Objects.isNull(proveTemplate)) {
                //审核中新增的模板
                cfSendProveTemplateBiz.insertOne(cfSendProveTemplate);
                return;
            }
            //已有模板，直接审核
            if (cfSendProveTemplate.getAuditStatus() == AddTrustAuditStatusEnum.PASSED.getCode()) {
                cfSendProveTemplateBiz.updateAuditStatus(cfSendProveTemplate.getCaseId(), proveId,
                        cfSendProveTemplate.getTemplateId(), cfSendProveTemplate.getAuditStatus());
            } else if (cfSendProveTemplate.getAuditStatus() == AddTrustAuditStatusEnum.CANCEL.getCode()) {
                cfSendProveTemplateBiz.updateAuditStatus(cfSendProveTemplate.getCaseId(), proveId,
                        cfSendProveTemplate.getTemplateId(), cfSendProveTemplate.getAuditStatus());
                cfSendProveBiz.updateCancelReason(proveId, cancelReason);
            } else if (cfSendProveTemplate.getAuditStatus() == AddTrustAuditStatusEnum.REJECTED.getCode()) {
                cfSendProveTemplateBiz.updateAuditStatusAndContent(cfSendProveTemplate.getCaseId(), proveId,
                        cfSendProveTemplate.getTemplateId(), cfSendProveTemplate.getAuditStatus(), cfSendProveTemplate.getContent());
                cfSendProveBiz.updateRejectedReason(proveId, rejectedReason);
            }
        });
        if (StringUtils.isNotBlank(rejectedReason)) {
            CfSendProveRejectedReason cfSendProveRejectedReason = new CfSendProveRejectedReason(cfSendProve.getCaseId(),
                    proveId, rejectedReason, 2);
            cfSendProveRejectedReasonDao.insertOne(cfSendProveRejectedReason);
        }
        if (StringUtils.isNotBlank(cancelReason)) {
            CfSendProveRejectedReason cfSendProveRejectedReason = new CfSendProveRejectedReason(cfSendProve.getCaseId(),
                    proveId, cancelReason, 1);
            cfSendProveRejectedReasonDao.insertOne(cfSendProveRejectedReason);
        }

        boolean rejected = cfSendProveTemplates.stream().anyMatch(cfSendProveTemplate ->
                cfSendProveTemplate.getAuditStatus() == AddTrustAuditStatusEnum.REJECTED.getCode());

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(cfSendProve.getCaseId());
        int caseId = crowdfundingInfo.getId();
        boolean imageReject = pictureAuditStatus == AddTrustAuditStatusEnum.REJECTED.getCode();

        if (rejected || imageReject) {
            //审核驳回
            adminCredibleInfoService.updateAuditInfo(proveId, AddTrustAuditStatusEnum.REJECTED.getCode(), CredibleTypeEnum.HELP_PROVE.getKey());
            this.sendSMS(crowdfundingInfo.getInfoId(), crowdfundingInfo.getUserId(), AddTrustModelNumEnum.JFQOGT100494036.name(), 3);
            this.sendTemplateMsg(crowdfundingInfo.getInfoId(), crowdfundingInfo.getUserId(), AddTrustModelNumEnum.EXJLAU100498027.name());
            this.addSnapshot(crowdfundingInfo.getId(), cfSendProve, crowdfundingInfo.getInfoId(), cfSendProveTemplates,
                    AddTrustAuditStatusEnum.REJECTED.getCode());
            String comment = "审核驳回，原因：" + rejectedReason + ";" + pictureRejectedReason + ";" + cancelReason;
            financeApproveService.addApprove(crowdfundingInfo, "补充证明信息", comment, adminUserId);
            reportOperationService.operation(caseId, ReportCons.ActionType.AuditReject, adminUserId);
        } else {
            //审核通过
            //更改可信信息状态
            adminCredibleInfoService.updateAuditInfo(proveId, AddTrustAuditStatusEnum.PASSED.getCode(), CredibleTypeEnum.HELP_PROVE.getKey());
            //发送短信
            this.sendSMS(crowdfundingInfo.getInfoId(), crowdfundingInfo.getUserId(), AddTrustModelNumEnum.FQKTUR100496034.name(), 3);
            //给举报人发短信
            userReportSendSMS(proveId, crowdfundingInfo);
            //发送模板消息
            this.sendTemplateMsg(crowdfundingInfo.getInfoId(), crowdfundingInfo.getUserId(), AddTrustModelNumEnum.EYNXTK100494037.name());
            //记录快照
            this.addSnapshot(caseId, cfSendProve, crowdfundingInfo.getInfoId(), cfSendProveTemplates,
                    AddTrustAuditStatusEnum.PASSED.getCode());
            financeApproveService.addApprove(crowdfundingInfo, "补充证明信息", "审核通过", adminUserId);
            String content = this.buildContent(cfSendProveTemplates);
            reportOperationService.operation(caseId, ReportCons.ActionType.AuditPass, adminUserId);

            //发布进展
            CrowdFundingProgress crowdFundingProgress = new CrowdFundingProgress();
            crowdFundingProgress.setImageUrls(pictureUrl);
            crowdFundingProgress.setContent(content);
            crowdFundingProgress.setUserId(crowdfundingInfo.getUserId());
            crowdFundingProgress.setActivityId(caseId);
            crowdFundingProgress.setTitle("");
            crowdFundingProgress.setType(CrowdFundingProgressType.PROGRESS.value());
            crowdfundingDelegate.addProgress(crowdFundingProgress);
        }

        if (workOrderId > 0) {
            HandleOrderParam handleOrderParam = new HandleOrderParam();
            handleOrderParam.setWorkOrderId(workOrderId);
            handleOrderParam.setOrderType(WorkOrderType.report_instead_input.getType());
            handleOrderParam.setHandleResult(rejected || imageReject ? HandleResultEnum.audit_reject.getType() : HandleResultEnum.audit_pass.getType());
            handleOrderParam.setUserId(adminUserId);
            workOrderCoreFeignClient.handle(handleOrderParam);
        }
    }

    private void userReportSendSMS(long proveId, CrowdfundingInfo crowdfundingInfo) {
        ReportInsteadWorkOrderRelateVO reportInsteadWorkOrderRelateVO = reportInsteadWorkOrderRelateDAO.getByReportInsteadId(proveId);
        if (reportInsteadWorkOrderRelateVO == null) {
            return;
        }
        Response<List<WorkOrderExtVO>> resp = workOrderExtFeignClient.getListByName(reportInsteadWorkOrderRelateVO.getWorkOrderId(), "reportId");
        if(NewResponseUtil.isNotOk(resp)){
            log.info("WorkOrderExtVO infos is null");
            return;
        }
        List<WorkOrderExtVO>workOrderExtVOS = resp.getData().
                stream().
                sorted(Comparator.comparing(WorkOrderExtVO::getWorkOrderId).reversed()).
                collect(Collectors.toList());
        for(WorkOrderExtVO workOrderExtVO : workOrderExtVOS){
            CrowdfundingReport crowdfundingReport = adminCrowdfundingReportDao.getById(Integer.parseInt(workOrderExtVO.getValue()));
            Map<Integer, String> msgParams = Maps.newHashMap();
            msgParams.put(1, getReportType(Integer.parseInt(workOrderExtVO.getValue())));
            msgParams.put(2, getTrackUrl(crowdfundingInfo));
            Map<String, Map<Integer, String>> msgMap = Maps.newHashMap();
            msgMap.put(shuidiCipher.decrypt(crowdfundingReport.getEncryptContact()), msgParams);
            msgClientV2Service.sendSmsParamsMsg("ACX7377", msgMap, false);
        }
    }

    public String buildContent(List<CfSendProveTemplate> cfSendProveTemplates) {
        if (CollectionUtils.isEmpty(cfSendProveTemplates)) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder();
        List<CfSendProveTemplate> cfSendProveTemplateList = cfSendProveTemplates.stream().filter(cfSendProveTemplate ->
                cfSendProveTemplate.getAuditStatus() != AddTrustAuditStatusEnum.CANCEL.getCode())
                .collect(Collectors.toList());
        for (CfSendProveTemplate cfSendProveTemplate : cfSendProveTemplateList) {

            // 检查是否应该展示文本内容
            long actionId = cfSendProveTemplate.getActionId();
            CfReportDisposeAction cfReportDisposeAction = adminCfReportDisposeActionBiz.getById(actionId);
            CfReportActionClassify classify = adminCfReportActionClassifyBiz.getById(cfReportDisposeAction.getActionClassifyId());
            if (!classify.isShowProgressTextContent()) {
                continue;
            }
            stringBuilder.append(cfSendProveTemplate.getTitle()).append("<br/>");
            stringBuilder.append(cfSendProveTemplate.getContent()).append("<br/>");
            stringBuilder.append(cfSendProveTemplate.getCommitmentContent()).append("<br/>").append("<br/>");
        }
        return stringBuilder.toString();
    }


    public void cancelCredible(CfCredibleInfoDO cfCredibleInfoDO, String reason, int operatorId) {
        //更新字段
        adminCredibleInfoService.updateAuditStatusById(cfCredibleInfoDO.getId(), AddTrustAuditStatusEnum.CANCEL.getCode());
        cfSendProveBiz.updatePictureAuditStatusById(cfCredibleInfoDO.getSubId(), AddTrustAuditStatusEnum.CANCEL.getCode());
        cfSendProveBiz.updateCancelReason(cfCredibleInfoDO.getSubId(), reason);
        Integer caseId = cfCredibleInfoDO.getCaseId();
        cfSendProveTemplateBiz.updateAllAuditStatus(caseId, cfCredibleInfoDO.getSubId(), AddTrustAuditStatusEnum.CANCEL.getCode());
        //记录操作记录
        String operation = "补充证明信息";
        AdminUserAccountModel userAccount = this.seaAccountClientV1.getValidUserAccountById(operatorId).getResult();
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        cfReportFollowCommentBiz.save(operatorId, operation + ":" + reason, CaseReportFollowDealStatusEnum.ADD_TRUST.getValue(),
                crowdfundingInfo.getId(), userAccount.getName());
        financeApproveService.addApprove(crowdfundingInfo, operation, "已撤回，原因：" + reason, operatorId);

        var cfSendProve = cfSendProveBiz.getById(cfCredibleInfoDO.getSubId());
        List<CfSendProveTemplate> cfSendProveTemplates = cfSendProveTemplateBiz.findByCaseIdAndProveId(crowdfundingInfo.getId(), cfCredibleInfoDO.getSubId());
        this.addSnapshot(crowdfundingInfo.getId(), cfSendProve, crowdfundingInfo.getInfoId(), cfSendProveTemplates, AddTrustAuditStatusEnum.CANCEL.getCode());
        reportOperationService.operation(caseId, ReportCons.ActionType.ActionRecall, operatorId);
    }

    public List<CfSendProveInfoRecord> proveInfoRecord(int caseId, long proveId) {
        var cfCredibleInfoDO = adminCredibleInfoService.queryBySubId(proveId, CredibleTypeEnum.HELP_PROVE.getKey());
        if (Objects.isNull(cfCredibleInfoDO)) {
            return Collections.emptyList();
        }

        //获取快照
        List<CfSendProveSnapshot> cfSendProveSnapshots = cfSendProveSnapshotDao.getSnapshot(caseId, proveId, List.of(
                AddTrustAuditStatusEnum.SUBMITTED.getCode(),AddTrustAuditStatusEnum.REJECTED.getCode()));
        //分组，3个一组
        List<List<CfSendProveSnapshot>> partition = Lists.partition(cfSendProveSnapshots, 2);

        List<CfSendProveInfoRecord> cfSendProveInfoRecords = Lists.newArrayList();
        for (List<CfSendProveSnapshot> sendProveSnapshots : partition) {
            if (sendProveSnapshots.size() < 2) {
                continue;
            }

            List<CfSendProveSnapshotBO> cfSendProveSnapshotBOS = Lists.newArrayList();
            CfSendProveInfoRecord cfSendProveInfoRecord = new CfSendProveInfoRecord();
            cfSendProveInfoRecord.setMobileMask(maskUtil.buildByEncryptPhone(cfCredibleInfoDO.getMobile()));
            cfSendProveInfoRecord.setMobile(null);
            cfSendProveInfoRecord.setSendTime(cfCredibleInfoDO.getSendTime());
            cfSendProveInfoRecord.setSubmitTime(cfCredibleInfoDO.getSubmitTime());
            cfSendProveInfoRecord.setAuditTime(cfCredibleInfoDO.getAuditTime());

            for (CfSendProveSnapshot cfSendProveSnapshot : sendProveSnapshots) {
                //set快照

                String proveSnapshot = cfSendProveSnapshot.getProveSnapshot();
                if (StringUtils.isNotBlank(proveSnapshot)) {
                    CfSendProveSnapshotBO cfSendProveSnapshotBO = JSON.parseObject(proveSnapshot, CfSendProveSnapshotBO.class);
                    cfSendProveSnapshotBO.setAuditStatus(cfSendProveSnapshot.getAuditStatus());
                    cfSendProveSnapshotBOS.add(cfSendProveSnapshotBO);
                }
            }
            cfSendProveInfoRecord.setCfSendProveSnapshotBO(cfSendProveSnapshotBOS);
            cfSendProveInfoRecords.add(cfSendProveInfoRecord);
        }
        return cfSendProveInfoRecords;
    }


    public Map<String, Object> checkSendProve(int caseId) {
        String msg = "";
        boolean result = true;
        var workOrderVoResponse = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, WorkOrderType.REPORT_TYPES);
        if (workOrderVoResponse.ok() && Objects.nonNull(workOrderVoResponse.getData())) {
            WorkOrderVO workOrderVO = workOrderVoResponse.getData();
            if (workOrderVO.getOrderType() != WorkOrderType.lost_report.getType()
                    && workOrderVO.getHandleResult() != HandleResultEnum.reach_agree.getType()) {
                msg = "只有达成一致状态才能下发举报代录入";
                result = false;
            }
        }
        CfCredibleInfoDO cfCredibleInfoDO = adminCredibleInfoService.getLastOneByCaseId(caseId, CredibleTypeEnum.HELP_PROVE.getKey());
        if (Objects.nonNull(cfCredibleInfoDO) && (cfCredibleInfoDO.getAuditStatus() == AddTrustAuditStatusEnum.UN_SUBMITTED.getCode()
                || cfCredibleInfoDO.getAuditStatus() == AddTrustAuditStatusEnum.REJECTED.getCode()
                || cfCredibleInfoDO.getAuditStatus() == AddTrustAuditStatusEnum.SUBMITTED.getCode())) {
            msg = "存在待审核的补充证明，请先审核";
            result = false;
        }
        boolean isCancel = false;
        long subId = 0;
        if (Objects.nonNull(cfCredibleInfoDO) && cfCredibleInfoDO.getAuditStatus() == AddTrustAuditStatusEnum.CANCEL.getCode()) {
            isCancel = true;
            subId = cfCredibleInfoDO.getSubId();
        }
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("result", result);
        resultMap.put("title", msg);
        resultMap.put("isCancel", isCancel);
        resultMap.put("subId", subId);
        return resultMap;
    }

    //获取举报案例最新工单
    @Nullable
    private WorkOrderVO getLastWorkOrderByCaseId(int caseId) {
        final Response<WorkOrderVO> resp = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, WorkOrderType.REPORT_TYPES);
        if (NewResponseUtil.isNotOk(resp)) {
            return null;
        }
        return resp.getData();
    }

    //获取举报类型
    private String getReportType(int reportId){
        List<CrowdfundingReportLabel> crowdfundingReportLabelList =
                adminCrowdfundingReportDao.queryReportLabelByReportId(reportId);
        List<Integer> crowdfundingReportLabels = crowdfundingReportLabelList.stream()
                .map(CrowdfundingReportLabel::getReportLabel)
                .distinct()
                .collect(Collectors.toList());
        List<String> reportLabels = Lists.newArrayList();
        for(int labelId : crowdfundingReportLabels){
            reportLabels.add(CfReportTypeEnum.getDescFromCode(labelId));
        }
        return StringUtils.join(reportLabels,",");
    }

    //短信链接转短链
    private String getTrackUrl(CrowdfundingInfo crowdfundingInfo) {
        List<CrowdfundingReport> crowdfundingReportList = adminCrowdfundingReportDao.getListByInfoId(crowdfundingInfo.getId());
        List<Integer> reportIds = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(crowdfundingReportList)){
            reportIds = crowdfundingReportList.stream().map(CrowdfundingReport::getId).distinct().collect(Collectors.toList());
        }
        int isHight = 0;
        if(reportIds.size() >= 10){
            isHight = 1;
        }
        String url = "https://www.shuidichou.com/mine/report-publicity/" + crowdfundingInfo.getInfoId() + "?type=1&isHigh=" + isHight;
        String shortUrl = shortUrlDelegate.process(url);
        return StringUtils.trimToEmpty(shortUrl);
    }
}
