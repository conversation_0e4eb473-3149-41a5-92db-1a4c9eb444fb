package com.shuidihuzhu.cf.service.record;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.adminpure.model.AdminCaseDisplayRecordVo;
import com.shuidihuzhu.cf.client.adminpure.model.CaseDisplaySetting;
import com.shuidihuzhu.cf.dao.record.AdminCaseDisplayRecordDao;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.delegate.volunteer.IVolunteerDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.record.AdminCaseDisplayRecordDo;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AdminCaseDisplayRecordService {
    @Autowired
    private IVolunteerDelegate volunteerDelegate;
    @Autowired
    private AdminCaseDisplayRecordDao adminCaseDisplayRecordDao;
    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Autowired
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired
    private SeaAccountClientV1 seaAccountDelegate;
    public List<AdminCaseDisplayRecordVo> searchRecordByCaseIdOrUuid(String infoUuid, int caseId) {
        if (StringUtils.isEmpty(infoUuid) && caseId <= 0) {
            log.error("查询案例展示操作记录时，入参错误");
            return new ArrayList<>();
        }

        if (caseId <= 0) {
            CrowdfundingInfo fundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoUuid);
            if (fundingInfo == null) {
                log.warn("查询不到案例，infoUuid: {}", infoUuid);
                return new ArrayList<>();
            }
            caseId = fundingInfo.getId();
        }
        List<AdminCaseDisplayRecordDo> adminCaseDisplayRecordDos = adminCaseDisplayRecordDao.selectByCaseId(caseId);
        // 小鲸鱼的操作人
        List<String> uniqueCodeList = adminCaseDisplayRecordDos.stream()
                .filter(recordDo -> recordDo.getUpdateChannel() == AdminCaseDisplayRecordVo.UPDATE_CHANNEL_TYPE.WHALE.getType())
                .map(AdminCaseDisplayRecordDo::getUniqueCode).distinct().collect(Collectors.toList());
        Map<String, CrowdfundingVolunteer> volunteerMap = Optional.ofNullable(volunteerDelegate.getCfVolunteerDOByUniqueCodes(uniqueCodeList)).orElse(new ArrayList<>())
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(CrowdfundingVolunteer::getUniqueCode, Function.identity()));
        // sea 后台的操作人
        List<Long> seaOperatorIds = adminCaseDisplayRecordDos.stream()
                .filter(recordDo -> recordDo.getUpdateChannel() == AdminCaseDisplayRecordVo.UPDATE_CHANNEL_TYPE.SEA.getType())
                .map(AdminCaseDisplayRecordDo::getOperatorId).distinct().collect(Collectors.toList());
        AuthRpcResponse<List<AdminUserAccountModel>> validUserAccountResponse = seaAccountDelegate.getValidUserAccountByLongIds(seaOperatorIds);
        Map<Integer, AdminUserAccountModel> seaUserMap = Optional.ofNullable(validUserAccountResponse.getResult()).orElse(new ArrayList<>())
                .stream().collect(Collectors.toMap(AdminUserAccountModel::getId, Function.identity()));

        List<AdminCaseDisplayRecordVo> res = new ArrayList<>();
        adminCaseDisplayRecordDos.forEach(adminCaseDisplayRecordDo -> {
            AdminCaseDisplayRecordVo vo = new AdminCaseDisplayRecordVo();
            vo.setBefore(JSON.parseObject(adminCaseDisplayRecordDo.getBefore(), CaseDisplaySetting.class));
            vo.setAfter(JSON.parseObject(adminCaseDisplayRecordDo.getAfter(), CaseDisplaySetting.class));
            vo.setUpdateChannel(adminCaseDisplayRecordDo.getUpdateChannel());
            if (adminCaseDisplayRecordDo.getUpdateChannel() == AdminCaseDisplayRecordVo.UPDATE_CHANNEL_TYPE.WHALE.getType()) {
                vo.setOperatorName(Optional.ofNullable(volunteerMap.get(adminCaseDisplayRecordDo.getUniqueCode())).map(CrowdfundingVolunteer::getVolunteerName).orElse(""));
            } else if (adminCaseDisplayRecordDo.getUpdateChannel() == AdminCaseDisplayRecordVo.UPDATE_CHANNEL_TYPE.SEA.getType()) {
                vo.setOperatorName(Optional.ofNullable(seaUserMap.get((int) adminCaseDisplayRecordDo.getOperatorId())).map(AdminUserAccountModel::getName).orElse(""));
            }
            vo.setUpdateTime(adminCaseDisplayRecordDo.getUpdateTime());
            vo.setReason(adminCaseDisplayRecordDo.getReason());
            res.add(vo);
        });
        return res;
    }

    public List<AdminCaseDisplayRecordVo> searchRecordOfChannel(String infoUuid, int caseId, int udpateChannel) {
        List<AdminCaseDisplayRecordVo> res = searchRecordByCaseIdOrUuid(infoUuid, caseId);
        if (CollectionUtils.isNotEmpty(res)) {
            return res.stream()
                    .filter(caseDisplayRecordVo -> caseDisplayRecordVo.getUpdateChannel() == udpateChannel)
                    .collect(Collectors.toList());
        }
        return res;
    }

    public int insertRecord(AdminCaseDisplayRecordVo record) {
        if (record.getCaseId() <= 0) {
            return 0;
        }
        AdminCaseDisplayRecordDo recordDo = new AdminCaseDisplayRecordDo();
        recordDo.setCaseId(record.getCaseId());
        recordDo.setBefore(JSON.toJSONString(record.getBefore()));
        recordDo.setAfter(JSON.toJSONString(record.getAfter()));
        recordDo.setUpdateChannel(record.getUpdateChannel());
        recordDo.setOperatorId(record.getOperatorId() == null? 0L : record.getOperatorId());
        recordDo.setUniqueCode(record.getUniqueCode());
        recordDo.setReason(record.getReason());
        return adminCaseDisplayRecordDao.addOne(recordDo);
    }
}
