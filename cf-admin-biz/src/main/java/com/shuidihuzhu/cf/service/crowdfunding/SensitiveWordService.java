package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderBiz;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.client.feign.CaseInfoApproveStageFeignClient;
import com.shuidihuzhu.cf.client.feign.CfCommonFeignClient;
import com.shuidihuzhu.cf.client.feign.CfUserInfoFeignClient;
import com.shuidihuzhu.cf.client.feign.CfVerificationFeignClient;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.CfBaseInfoRiskHitVO;
import com.shuidihuzhu.cf.client.ugc.service.RiskControlWordManageClient;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCaseDetailsMsgDao;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.ugc.IUgcDelegate;
import com.shuidihuzhu.cf.domain.CaseRaiseRiskDO;
import com.shuidihuzhu.cf.domain.risk.AiRaiseRiskSnapshotDO;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.cf.enums.verification.VerifyStatusEnum;
import com.shuidihuzhu.cf.facade.AdminApproveFacade;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.risk.verify.RiskUgcVerifyModel;
import com.shuidihuzhu.cf.mq.IAdminCommonMessageHelperService;
import com.shuidihuzhu.cf.risk.client.admin.blacklist.BlacklistClient;
import com.shuidihuzhu.cf.risk.model.admin.blacklist.VerifyAutoAddBlacklistVo;
import com.shuidihuzhu.cf.service.FinanceApproveService;
import com.shuidihuzhu.cf.service.ai.AiImageMaskServiceImpl;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.client.cf.admin.model.AdminCaseDetailsMsg;
import com.shuidihuzhu.client.cf.workorder.CfUgcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.ext.WorkOrderExtFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.UgcHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.constant.CfClientMQTopicCons;
import com.shuidihuzhu.client.model.enums.ImageMaskBizEnum;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.exceptions.ServiceException;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by Ahrievil on 2017/12/3
 * TODO refactor
 */
@Service
@Slf4j
public class SensitiveWordService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CfCaseSensitiveWordService.class);

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Autowired
    private AdminWorkOrderBiz adminWorkOrderBiz;
    @Autowired
    private AdminTaskUgcBiz adminTaskUgcBiz;
    @Autowired
    private CfSensitiveWordRecordBiz cfSensitiveWordRecordBiz;
    @Autowired
    private AdminCrowdfundingOrderBiz adminCrowdfundingOrderBiz;
    @Autowired
    private AdminCrowdFundingProgressBiz adminCrowdFundingProgressBiz;
    @Autowired
    private AdminCrowdFundingVerificationBiz adminCrowdFundingVerificationBiz;
    @Autowired
    private AdminCrowdfundingCommentBiz adminCrowdfundingCommentBiz;
    @Autowired
    private IUgcDelegate ugcDelegate;
    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;
    @Autowired
    private AdminCfInfoExtBiz adminCfInfoExtBiz;

    @Resource
    private FinanceApproveService financeApproveService;

    @Resource
    private IRiskDelegate riskDelegate;

    @Resource
    private AdminApproveFacade adminApproveFacade;

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;



    @Resource
    private IAdminCommonMessageHelperService adminCommonMessageHelperService;

    @Resource
    private UserCommentBiz userCommentBiz;

    @Resource
    private CfUgcWorkOrderClient ugcWorkOrderClient;
    @Resource
    private IUgcOperateRecordService ugcOperateRecordService;
    @Resource
    private CfCommonFeignClient cfCommonFeignClient;

    @Resource
    private CaseInfoApproveStageFeignClient caseInfoApproveStageFeignClient;

    @Resource
    private CfContentImageService cfContentImageService;

    @Resource
    private RiskControlWordManageClient riskControlWordClient;
    @Resource
    private CfWorkOrderClient cfWorkOrderClient;
    @Resource
    private CfSensitiveWordRecordBiz sensitiveWordRecordBiz;
    @Autowired
    private BlacklistClient blacklistClient;
    @Resource
    private AdminCaseDetailsMsgDao adminCaseDetailsMsgDao;
    @Resource
    private ApproveRemarkOldService approveRemarkOldService;

    @Resource
    private CfVerificationFeignClient verificationFeignClient;

    @Resource
    private CfUserInfoFeignClient userInfoFeignClient;

    @Resource
    private WorkOrderExtFeignClient workOrderExtFeignClient;
    @Resource
    private Producer producer;
    @Resource
    private AiImageMaskServiceImpl aiImageMaskService;

    public void handleBaseInfo(AdminWorkOrder adminWorkOrder, Map<String, Object> result, AdminUGCTask.Result handleTypeEnum,
                               String title, String content, String imgUrls, String reason, int userId, String comment, int stopReasonId) {
        long workOrderId = adminWorkOrder.getId();
        AdminTaskUgc adminTaskUgc = adminTaskUgcBiz.selectByWorkOrderId(workOrderId);

        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(adminTaskUgc.getCaseId());
        String infoUuid = crowdfundingInfo.getInfoId();
        CaseRaiseRiskDO caseRaiseRiskDO = riskDelegate.getByInfoUuid(infoUuid);

        int caseId = crowdfundingInfo.getId();

        saveAiRiskSnapshot(handleTypeEnum, title, content, reason, comment, crowdfundingInfo, infoUuid, caseRaiseRiskDO, caseId);

        log.info("handleBaseInfo caseId: {}, infoUuid: {}, action: {}, riskLevel: {}, handleType: {}, title: {}, " +
                        "content: {}, preTitle: {}, preContent: {}",
                caseId,
                infoUuid,
                adminTaskUgc.getAction(),
                caseRaiseRiskDO.getRiskLevel(),
                handleTypeEnum.getCode(),
                title,
                content,
                crowdfundingInfo.getTitle(),
                crowdfundingInfo.getContent());

        switch (handleTypeEnum) {
            case EDIT:

                editCaseBaseInfo(crowdfundingInfo, title, content, imgUrls, userId);
                financeApproveService.addApprove(crowdfundingInfo, "图文-修改基本信息", reason, userId);

                result.put("data", crowdfundingInfo);
                adminTaskUgcBiz.updateResult(adminTaskUgc.getId(), handleTypeEnum.getCode());
                adminWorkOrderBiz.updateChangeable(workOrderId,
                        AdminWorkOrderConst.Status.FINISHED, AdminWorkOrderConst.Result.HANDLE_SUCCESS, reason);

                riskDelegate.handleRiskPassed(infoUuid, userId);

                break;
            case SUGGEST_STOP_CROWDFUNDING:
                adminCfInfoExtBiz.updateSuggestStop(crowdfundingInfo.getInfoId(), true);
                adminTaskUgcBiz.updateResult(adminTaskUgc.getId(), handleTypeEnum.getCode());
                adminWorkOrderBiz.updateChangeable(workOrderId,
                        AdminWorkOrderConst.Status.FINISHED, AdminWorkOrderConst.Result.HANDLE_SUCCESS, reason);

                // 执行停止筹款
                Response stopResponse = adminApproveFacade.stopCase(caseId, userId, "图文-案例结束", stopReasonId, reason, "");
                if (stopResponse.getCode() != AdminErrorCode.SUCCESS.getCode()) {
                    log.error("停止筹款 {}", caseId);
                }

                break;
            case NOT_HANDLE:
                adminTaskUgcBiz.updateResult(adminTaskUgc.getId(), handleTypeEnum.getCode());
                adminWorkOrderBiz.updateChangeable(workOrderId,
                        AdminWorkOrderConst.Status.SHUTDOWN, AdminWorkOrderConst.Result.NO_NEED_HANDLE, reason);
                financeApproveService.addApprove(crowdfundingInfo, "图文-审核通过", reason, userId);

                riskDelegate.handleRiskPassed(infoUuid, userId);
                caseInfoApproveStageFeignClient.commitStage(caseId);

                cfContentImageService.close(userId, crowdfundingInfo);
                break;
            case AUTO_CLOSE:
                adminTaskUgcBiz.updateResult(adminTaskUgc.getId(), handleTypeEnum.getCode());
                adminWorkOrderBiz.updateChangeable(workOrderId,
                        AdminWorkOrderConst.Status.SHUTDOWN, AdminWorkOrderConst.Result.NO_NEED_HANDLE, reason);
                financeApproveService.addApprove(crowdfundingInfo, "图文-自动异常关闭", reason, userId);
                break;
            default:
                break;
        }
    }

    public void editCaseBaseInfo(CrowdfundingInfo crowdfundingInfo, String title, String content, String imgUrls,
                                 int userId) {
        LOGGER.info("handleBaseInfo updateContent crowdfundingInfo:{}, title:{}, content:{}, imgUrls:{}, userId:{}",
                crowdfundingInfo, title, content, imgUrls, userId);
        int caseId = crowdfundingInfo.getId();

        AdminUserAccountModel userAccount = this.seaAccountClientV1.getValidUserAccountById(userId).getResult();
        CfOperatingRecord cfOperatingRecord = this.crowdfundingDelegate.before(crowdfundingInfo, userId,
                userAccount.getName(), CfOperatingRecordEnum.Type.MODIFY_CONTENT, CfOperatingRecordEnum.Role.OPERATOR);

        if (StringUtils.isNotBlank(title) && StringUtils.isNotBlank(content) && imgUrls != null) {
            caseInfoApproveStageFeignClient.saveStage(caseId, title, content, imgUrls);
            caseInfoApproveStageFeignClient.commitStage(caseId);
            adminCrowdfundingInfoBiz.updateContent(title, content, crowdfundingInfo.getId());
        }

        this.crowdfundingDelegate.afterCfOperatingRecord(cfOperatingRecord);
    }

    /**
     * 存储AI审核结果 与人工处理动作对比
     */
    private void saveAiRiskSnapshot(AdminUGCTask.Result handleTypeEnum,
                                    String title,
                                    String content,
                                    String reason,
                                    String comment,
                                    CrowdfundingInfo crowdfundingInfo,
                                    String infoUuid,
                                    CaseRaiseRiskDO caseRaiseRiskDO,
                                    int caseId) {
        try {
            CfBaseInfoTemplateRecord templateRecord = crowdfundingDelegate.selectByInfoUuid(infoUuid);
            boolean isTemplate = templateRecord != null;

            String riskData = caseRaiseRiskDO.getRiskData();
            Integer aiCode = getAiCode(riskData);
            AiRaiseRiskSnapshotDO s = new AiRaiseRiskSnapshotDO(
                    0,
                    caseId,
                    isTemplate ? 1 : 2,
                    0,
                    0,
                    crowdfundingInfo.getTitle(),
                    crowdfundingInfo.getContent(),
                    title == null ? "" : title,
                    content == null ? "" : content,
                    handleTypeEnum.getWord(),
                    handleTypeEnum.getCode(),
                    comment == null ? "" : comment,
                    reason == null ? "" : reason,
                    CaseRaiseRiskLevelEnum.parse(caseRaiseRiskDO.getRiskLevel()).name(),
                    caseRaiseRiskDO.getRiskLevel(),
                    aiCode,
                    riskData
            );

            // 是智能发起的话 记录对应的模板id
            if (isTemplate) {
                s.setTemplateTitleId(templateRecord.getTitleId());
                s.setTemplateContentId(templateRecord.getContentId());
            }
        } catch (Exception e) {
            log.error("saveAiRiskSnapshot {}", caseId, e);
        }
    }

    private Integer getAiCode(String riskData) {
        JSONObject riskJsonObject = JSON.parseObject(riskData);
        if (riskJsonObject == null) {
            return -1;
        }
        JSONObject caseLabel = riskJsonObject.getJSONObject("case_label");
        if (caseLabel == null) {
            return -1;
        }
        Integer code = caseLabel.getInteger("code");
        if (code == null) {
            return -1;
        }
        return code;
    }


    public void newHandleOrder(long wordId,AdminUGCTask.Result handleResultEnum, int adminUserId) {
        CfSensitiveWordRecord cfSensitiveWordRecord = cfSensitiveWordRecordBiz.selectById(wordId);
        long orderId = cfSensitiveWordRecord.getBizId();
        CrowdfundingOrder crowdfundingOrder = adminCrowdfundingOrderBiz.getById(orderId);
        Integer caseId = crowdfundingOrder.getCrowdfundingId();
        switch (handleResultEnum) {
            case ONLY_SELF:
                RiskUgcVerifyModel riskUgcVerifyModel = new RiskUgcVerifyModel(caseId, UgcTypeEnum.ORDER, orderId, "运营操作添加");
                riskDelegate.addVerify(riskUgcVerifyModel);
                ugcOperateRecordService.insertUgcRecord(caseId, UgcBizType.ORDER.getKey(), orderId, UgcManageEnum.ORDER_SEE_ONESELF.getKey(),adminUserId);
                break;
            case PASS_AND_SHOW:
                riskDelegate.deleteVerify(caseId, UgcTypeEnum.ORDER, orderId);
                ugcOperateRecordService.insertUgcRecord(caseId, UgcBizType.ORDER.getKey(), orderId, UgcManageEnum.ORDER_SHOW.getKey(),adminUserId);
                break;
            default:
        }
    }
    @Deprecated
    public void handleOrder(AdminWorkOrder adminWorkOrder, Map<String, Object> result, AdminUGCTask.Result taskResult,
                            String comment, String reason) {
        long workOrderId = adminWorkOrder.getId();
        AdminTaskUgc adminTaskUgc = adminTaskUgcBiz.selectByWorkOrderId(workOrderId);
        long wordId = adminTaskUgc.getWordId();
        CfSensitiveWordRecord cfSensitiveWordRecord = cfSensitiveWordRecordBiz.selectById(wordId);
        long orderId = cfSensitiveWordRecord.getBizId();
        CrowdfundingOrder crowdfundingOrder = adminCrowdfundingOrderBiz.getById(orderId);
        Integer caseId = crowdfundingOrder.getCrowdfundingId();
        int adminUserId = ContextUtil.getAdminUserId();
        switch (taskResult) {
//                没有这个功能了 代码可以先保留
//            case EDIT:
//                adminCrowdfundingOrderBiz.editByIdAndComment(comment, crowdfundingOrder.getId());
//                result.put("data", crowdfundingOrder);
//                adminTaskUgcBiz.updateResult(adminTaskUgc.getId(), taskResult.getCode());
//
//                adminWorkOrderBiz.updateChangeable(workOrderId,
//                        AdminWorkOrderConst.Status.FINISHED, AdminWorkOrderConst.Result.HANDLE_SUCCESS, reason);
//                break;
            case ONLY_SELF:
                adminTaskUgcBiz.updateResult(adminTaskUgc.getId(), taskResult.getCode());
                adminWorkOrderBiz.updateChangeable(workOrderId,
                        AdminWorkOrderConst.Status.SHUTDOWN, AdminWorkOrderConst.Result.NO_NEED_HANDLE, reason);

                RiskUgcVerifyModel riskUgcVerifyModel = new RiskUgcVerifyModel(caseId, UgcTypeEnum.ORDER, orderId, "运营操作添加");
                riskDelegate.addVerify(riskUgcVerifyModel);
                ugcOperateRecordService.insertUgcRecord(caseId, UgcBizType.ORDER.getKey(), orderId, UgcManageEnum.ORDER_SEE_ONESELF.getKey(),adminUserId);

                break;
            case PASS_AND_SHOW:
                riskDelegate.deleteVerify(caseId, UgcTypeEnum.ORDER, orderId);
                adminTaskUgcBiz.updateResult(adminTaskUgc.getId(), taskResult.getCode());
                adminWorkOrderBiz.updateChangeable(workOrderId, AdminWorkOrderConst.Status.SHUTDOWN, AdminWorkOrderConst.Result.NO_NEED_HANDLE, reason);
                ugcOperateRecordService.insertUgcRecord(caseId, UgcBizType.ORDER.getKey(), orderId, UgcManageEnum.ORDER_SHOW.getKey(),adminUserId);
                break;
            default:
        }
    }
    @Deprecated
    public void handleProgress(AdminWorkOrder adminWorkOrder, Map<String, Object> result, AdminUGCTask.Result handleTypeEnum,
                               String title, String content, String imgUrls, String reason, int stopReasonId, int adminUserId) {
        long workOrderId = adminWorkOrder.getId();
        AdminTaskUgc adminTaskUgc = adminTaskUgcBiz.selectByWorkOrderId(workOrderId);
        int caseId = adminTaskUgc.getCaseId();
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        long progressId = adminTaskUgc.getExtId();
        CrowdFundingProgress progress = adminCrowdFundingProgressBiz.getActivityProgressById(progressId);
        switch (handleTypeEnum) {
            case DELETED:
                cfCommonFeignClient.deleteProgress(progress.getActivityId(), (int) progressId);

                riskDelegate.deleteVerify(caseId, UgcTypeEnum.PROGRESS, progressId);

                ugcOperateRecordService.insertUgcRecord(caseId, UgcBizType.PROGRESS.getKey(), progressId, UgcManageEnum.PROGRESS_DEL.getKey(),adminUserId);

                result.put("data", putComment(progress, content));
                adminTaskUgcBiz.updateResult(adminTaskUgc.getId(), handleTypeEnum.getCode());

                adminWorkOrderBiz.updateChangeable(workOrderId,
                        AdminWorkOrderConst.Status.FINISHED, AdminWorkOrderConst.Result.HANDLE_SUCCESS, reason);
                break;
            case EDIT:
                if (StringUtils.isNotBlank(content)) {
                    adminCrowdFundingProgressBiz.updateContent(progressId, content);
                }
                List<String> images = Splitter.on(",").splitToList(imgUrls);
                String urls = images.stream().filter(val -> StringUtils.contains(val, "http"))
                        .collect(Collectors.joining(","));
                adminCrowdFundingProgressBiz.updateImageUrlsByNoIsDelete(urls, (int) progressId, crowdfundingInfo.getId());

                onProgressPass(progress);

                result.put("data", putComment(progress, content));
                adminTaskUgcBiz.updateResult(adminTaskUgc.getId(), handleTypeEnum.getCode());

                adminWorkOrderBiz.updateChangeable(workOrderId,
                        AdminWorkOrderConst.Status.FINISHED, AdminWorkOrderConst.Result.HANDLE_SUCCESS, reason);
                break;
            case NOT_HANDLE:
                adminTaskUgcBiz.updateResult(adminTaskUgc.getId(), handleTypeEnum.getCode());
                adminWorkOrderBiz.updateChangeable(workOrderId,
                        AdminWorkOrderConst.Status.SHUTDOWN, AdminWorkOrderConst.Result.NO_NEED_HANDLE, reason);

                onProgressPass(progress);
                break;
            case SUGGEST_STOP_CROWDFUNDING:
                adminCfInfoExtBiz.updateSuggestStop(crowdfundingInfo.getInfoId(), true);
                adminTaskUgcBiz.updateResult(adminTaskUgc.getId(), handleTypeEnum.getCode());
                adminWorkOrderBiz.updateChangeable(workOrderId,
                        AdminWorkOrderConst.Status.FINISHED, AdminWorkOrderConst.Result.HANDLE_SUCCESS, reason);

                // 执行停止筹款
                Response stopResponse = adminApproveFacade.stopCase(caseId, adminUserId, "动态-案例结束",
                        stopReasonId,
                        reason, "");
                if (stopResponse.getCode() != AdminErrorCode.SUCCESS.getCode()) {
                    log.error("停止筹款 {}", caseId);
                }

                break;
            default:
        }
    }

    /**
     被屏蔽过的动态 在处理后发送动态进展通知，
     未被屏蔽过的动态 在刚发出后就已经发过动态进展通知
      */
    private void onProgressPass(CrowdFundingProgress progress) {
        Integer caseId = progress.getActivityId();
        Integer progressId = progress.getId();
        int adminUserId = ContextUtil.getAdminUserId();
        adminCrowdFundingProgressBiz.reviveProgressById(progressId);
        ugcOperateRecordService.insertUgcRecord(caseId, UgcBizType.PROGRESS.getKey(), progressId, UgcManageEnum.PROGRESS_SHOW.getKey(),adminUserId);
        boolean safe = riskDelegate.isSafeSingle(UgcTypeEnum.PROGRESS, progressId, caseId);
        if (safe) {
            return;
        }
        riskDelegate.deleteVerify(caseId, UgcTypeEnum.PROGRESS, progressId);
        adminCommonMessageHelperService.send(adminCommonMessageHelperService.getCfPublishProgressNoticeMessage(progress));
    }

    public long handleVerification(AdminWorkOrder adminWorkOrder, Map<String, Object> result, AdminUGCTask.Result taskResult,
                                   String comment, String reason) {
        long workOrderId = adminWorkOrder.getId();
        AdminTaskUgc adminTaskUgc = adminTaskUgcBiz.selectByWorkOrderId(workOrderId);
        long wordId = adminTaskUgc.getWordId();
        CfSensitiveWordRecord cfSensitiveWordRecord = cfSensitiveWordRecordBiz.selectById(wordId);
        long verificationId = cfSensitiveWordRecord.getBizId();
        CrowdFundingVerification crowdFundingVerification = adminCrowdFundingVerificationBiz.getById(verificationId);
        int caseId = adminTaskUgc.getCaseId();
        int adminUserId = ContextUtil.getAdminUserId();
        switch (taskResult) {
            case DELETED:
                riskDelegate.deleteVerify(caseId, UgcTypeEnum.VERIFICATION, verificationId);
                riskDelegate.updateValid(0, (int) verificationId);
                crowdFundingVerification.setValid(0);
                result.put("data", this.putComment(crowdFundingVerification, comment));
                adminTaskUgcBiz.updateResult(adminTaskUgc.getId(), taskResult.getCode());

                adminWorkOrderBiz.updateChangeable(workOrderId, AdminWorkOrderConst.Status.FINISHED, AdminWorkOrderConst.Result.HANDLE_SUCCESS, reason);
                ugcOperateRecordService.insertUgcRecord(caseId, UgcBizType.VERIFICATION.getKey(), verificationId, UgcManageEnum.VERIFY_DEL.getKey(),adminUserId);
                break;
//                没有这个功能了 代码可以先保留
//            case EDIT:
//                adminCrowdFundingVerificationBiz.updateDesc(verificationId, comment);
//                result.put("data", this.putComment(crowdFundingVerification, comment));
//                adminTaskUgcBiz.updateResult(adminTaskUgc.getId(), taskResult.getCode());
//
//                adminWorkOrderBiz.updateChangeable(workOrderId,
//                        AdminWorkOrderConst.Status.FINISHED, AdminWorkOrderConst.Result.HANDLE_SUCCESS, reason);
//                break;
            case ONLY_SELF:
                adminTaskUgcBiz.updateResult(adminTaskUgc.getId(), taskResult.getCode());
                adminWorkOrderBiz.updateChangeable(workOrderId,
                        AdminWorkOrderConst.Status.SHUTDOWN, AdminWorkOrderConst.Result.NO_NEED_HANDLE, reason);

                RiskUgcVerifyModel riskUgcVerifyModel = new RiskUgcVerifyModel(caseId, UgcTypeEnum.VERIFICATION, verificationId, "运营操作添加");
                riskDelegate.addVerify(riskUgcVerifyModel);
                ugcOperateRecordService.insertUgcRecord(caseId, UgcBizType.VERIFICATION.getKey(), verificationId, UgcManageEnum.VERIFY_SEE_ONESELF.getKey(),adminUserId);
                break;
            case PASS_AND_SHOW:
                riskDelegate.deleteVerify(caseId, UgcTypeEnum.VERIFICATION, verificationId);
                adminTaskUgcBiz.updateResult(adminTaskUgc.getId(), taskResult.getCode());
                adminWorkOrderBiz.updateChangeable(workOrderId, AdminWorkOrderConst.Status.SHUTDOWN, AdminWorkOrderConst.Result.NO_NEED_HANDLE, reason);
                ugcOperateRecordService.insertUgcRecord(caseId, UgcBizType.VERIFICATION.getKey(), verificationId, UgcManageEnum.VERIFY_SHOW.getKey(),adminUserId);
                break;
            default:
        }
        riskDelegate.deleteCaseVerificationCache(crowdFundingVerification.getCrowdFundingInfoId());
        return verificationId;
    }


    public long newHandleVerification(int caseId, long wordId , AdminUGCTask.Result taskResult, int adminUserId) {
        CfSensitiveWordRecord cfSensitiveWordRecord = cfSensitiveWordRecordBiz.selectById(wordId);
        long verificationId = cfSensitiveWordRecord.getBizId();
        CrowdFundingVerification crowdFundingVerification = adminCrowdFundingVerificationBiz.getById(verificationId);
        switch (taskResult) {
            case ONLY_SELF:
                RiskUgcVerifyModel riskUgcVerifyModel = new RiskUgcVerifyModel(caseId, UgcTypeEnum.VERIFICATION, verificationId, "运营操作添加");
                riskDelegate.addVerify(riskUgcVerifyModel);
                ugcOperateRecordService.insertUgcRecord(caseId, UgcBizType.VERIFICATION.getKey(), verificationId, UgcManageEnum.VERIFY_SEE_ONESELF.getKey(),adminUserId);
                break;
            case PASS_AND_SHOW:
                riskDelegate.deleteVerify(caseId, UgcTypeEnum.VERIFICATION, verificationId);
                ugcOperateRecordService.insertUgcRecord(caseId, UgcBizType.VERIFICATION.getKey(), verificationId, UgcManageEnum.VERIFY_SHOW.getKey(),adminUserId);
                break;
            default:
        }
        riskDelegate.deleteCaseVerificationCache(crowdFundingVerification.getCrowdFundingInfoId());
        return verificationId;
    }
    @Deprecated
    public long handleComment(AdminWorkOrder adminWorkOrder, Map<String, Object> result, AdminUGCTask.Result taskResult, String comment, String reason,AdminUGCTask.Content contentTypeEnum) {
        long workOrderId = adminWorkOrder.getId();
        AdminTaskUgc adminTaskUgc = adminTaskUgcBiz.selectByWorkOrderId(workOrderId);
        int caseId = adminTaskUgc.getCaseId();
        long wordId = adminTaskUgc.getWordId();
        CfSensitiveWordRecord cfSensitiveWordRecord = cfSensitiveWordRecordBiz.selectById(wordId);
        long commentId = cfSensitiveWordRecord.getBizId();
        CrowdfundingComment crowdfundingComment = adminCrowdfundingCommentBiz.getByIdNoCareDeleted(commentId,caseId);

        int adminUserId = ContextUtil.getAdminUserId();
        UgcBizType ugcBizType = contentTypeEnum == AdminUGCTask.Content.COMMENT_ORDER ? UgcBizType.COMMENT_ORDER : UgcBizType.COMMENT_PROGRESS;

        switch (taskResult) {
            case DELETED:
                riskDelegate.deleteVerify(caseId, UgcTypeEnum.COMMENT, commentId);
                ugcDelegate.removeCrowdfundingCommentById(commentId);
                result.put("data", this.putComment(crowdfundingComment, comment));
                adminTaskUgcBiz.updateResult(adminTaskUgc.getId(), taskResult.getCode());

                adminWorkOrderBiz.updateChangeable(workOrderId, AdminWorkOrderConst.Status.FINISHED, AdminWorkOrderConst.Result.HANDLE_SUCCESS, reason);
                UgcManageEnum ugcManageEnum = contentTypeEnum == AdminUGCTask.Content.COMMENT_ORDER ? UgcManageEnum.ORDER_COMMENT_DEL : UgcManageEnum.PROGRESS_COMMENT_DEL;
                ugcOperateRecordService.insertUgcRecord(caseId, ugcBizType.getKey(), commentId, ugcManageEnum.getKey(),adminUserId);

                break;
//                没有这个功能了 代码可以先保留
//            case EDIT:
//                adminCrowdfundingCommentBiz.updateContent(commentId, comment);
//                result.put("data", this.putComment(crowdfundingComment, comment));
//                adminTaskUgcBiz.updateResult(adminTaskUgc.getId(), taskResult.getCode());
//
//                adminWorkOrderBiz.updateChangeable(workOrderId,
//                        AdminWorkOrderConst.Status.FINISHED, AdminWorkOrderConst.Result.HANDLE_SUCCESS, reason);
//                break;
            case ONLY_SELF:
                adminTaskUgcBiz.updateResult(adminTaskUgc.getId(), taskResult.getCode());
                adminWorkOrderBiz.updateChangeable(workOrderId,
                        AdminWorkOrderConst.Status.SHUTDOWN, AdminWorkOrderConst.Result.NO_NEED_HANDLE, reason);

                RiskUgcVerifyModel riskUgcVerifyModel = new RiskUgcVerifyModel(caseId, UgcTypeEnum.COMMENT, commentId, "运营操作添加");
                riskDelegate.addVerify(riskUgcVerifyModel);
                UgcManageEnum ugcManageType = contentTypeEnum == AdminUGCTask.Content.COMMENT_ORDER ? UgcManageEnum.ORDER_COMMENTSEE_ONESELF : UgcManageEnum.PROGRESS_COMMENT_SEE_ONESELF;
                ugcOperateRecordService.insertUgcRecord(caseId, ugcBizType.getKey(), commentId, ugcManageType.getKey(),adminUserId);
                break;
            case PASS_AND_SHOW:
                riskDelegate.deleteVerify(caseId, UgcTypeEnum.COMMENT, commentId);
                adminTaskUgcBiz.updateResult(adminTaskUgc.getId(), taskResult.getCode());
                adminWorkOrderBiz.updateChangeable(workOrderId, AdminWorkOrderConst.Status.SHUTDOWN, AdminWorkOrderConst.Result.NO_NEED_HANDLE, reason);
                UgcManageEnum ugcManage = contentTypeEnum == AdminUGCTask.Content.COMMENT_ORDER ? UgcManageEnum.ORDER_COMMENT_SHOW : UgcManageEnum.PROGRESS_COMMENT_SHOW;
                ugcOperateRecordService.insertUgcRecord(caseId, ugcBizType.getKey(), commentId, ugcManage.getKey(),adminUserId);
                break;
            default:
        }

        return commentId;
    }


    public long newHandleComment(int caseId,long wordId,AdminUGCTask.Result taskResult,AdminUGCTask.Content contentType, int adminUserId) {

        CfSensitiveWordRecord cfSensitiveWordRecord = cfSensitiveWordRecordBiz.selectById(wordId);
        long commentId = cfSensitiveWordRecord.getBizId();
        UgcBizType ugcBizType = contentType == AdminUGCTask.Content.COMMENT_ORDER ? UgcBizType.COMMENT_ORDER : UgcBizType.COMMENT_PROGRESS;
        switch (taskResult) {
            case ONLY_SELF:
                RiskUgcVerifyModel riskUgcVerifyModel = new RiskUgcVerifyModel(caseId, UgcTypeEnum.COMMENT, commentId, "运营操作添加");
                riskDelegate.addVerify(riskUgcVerifyModel);
                UgcManageEnum ugcManageEnum = contentType == AdminUGCTask.Content.COMMENT_ORDER ? UgcManageEnum.ORDER_COMMENTSEE_ONESELF : UgcManageEnum.PROGRESS_COMMENT_SEE_ONESELF;
                ugcOperateRecordService.insertUgcRecord(caseId, ugcBizType.getKey(), commentId, ugcManageEnum.getKey(),adminUserId);
                break;
            case PASS_AND_SHOW:
                riskDelegate.deleteVerify(caseId, UgcTypeEnum.COMMENT, commentId);
                UgcManageEnum ugcManageType = contentType == AdminUGCTask.Content.COMMENT_ORDER ? UgcManageEnum.ORDER_COMMENT_SHOW : UgcManageEnum.PROGRESS_COMMENT_SHOW;
                ugcOperateRecordService.insertUgcRecord(caseId, ugcBizType.getKey(), commentId, ugcManageType.getKey(),adminUserId);
                break;
            default:
        }

        return commentId;
    }



    public Object putComment(Object target, String comment) {
        JSONObject progress = (JSONObject) JSONObject.toJSON(target);
        progress.put("comment", comment);
        return progress;
    }


    // 将controller的方法 抽离到service 可以让其他地方调用
    @Deprecated
    public Response handleUgcWorkId(int handleType,
                                    String workOrderId,
                                    String comment, String reason,
                                    int stopReasonId,
                                    String title, String content,
                                    String imgUrls, int userId) {

        AdminUGCTask.Result handleTypeEnum = AdminUGCTask.Result.getByCode(handleType);
        if (handleTypeEnum == null || handleTypeEnum == AdminUGCTask.Result.NO) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<Long> workOrderIdList = Splitter.on(",").splitToList(workOrderId).stream().map(Long::valueOf).collect(Collectors.toList());
        List<AdminWorkOrder> adminWorkOrderList = adminWorkOrderBiz.selectByIdList(workOrderIdList);
        if (CollectionUtils.isEmpty(adminWorkOrderList)) {
            return NewResponseUtil.makeError(AdminErrorCode.WORK_ORDER_NOT_FOUND);
        }

        List<Long> workOrderList = adminWorkOrderList.stream().map(AdminWorkOrder::getId).collect(Collectors.toList());
        Map<Long, AdminWorkOrder> adminWorkOrderMap = adminWorkOrderList.stream().collect(Collectors.toMap(AdminWorkOrder::getId, Function.identity()));
        List<AdminTaskUgc> adminTaskUgcList = adminTaskUgcBiz.selectByWorkOrderIds(workOrderList);
        Map<String, Object> result = Maps.newHashMap();

        // 如果批量处理中有一个是自动异常关闭的。那这次操作失败。
        boolean hasAutoClose = adminTaskUgcList.stream()
                .anyMatch(v -> v.getResult() == AdminUGCTask.Result.AUTO_CLOSE.getCode());
        if (hasAutoClose) {
            return NewResponseUtil.makeError(AdminErrorCode.ORDER_HAS_AUTO_CLOSE);
        }

        long dealId = 0;
        for (AdminTaskUgc adminTaskUgc : adminTaskUgcList) {
            String desc = "客服操作";
            AdminUGCTask.Content contentType = AdminUGCTask.Content.getByCode(adminTaskUgc.getContentType());
            AdminWorkOrder adminWorkOrder = adminWorkOrderMap.get(adminTaskUgc.getWorkOrderId());

            adminWorkOrderBiz.onHandleOrder(adminWorkOrder);

            switch (contentType) {
                case BASE_INFO:
                    handleBaseInfo(adminWorkOrder, result, handleTypeEnum, title, content,
                            imgUrls, reason, userId, comment, stopReasonId);
                    desc += contentType.getWord();
                    // 图文的处理 同步到 前置审核那里
                    UserComment synFirstApproveUserComment = new UserComment(adminTaskUgc.getCaseId(), UserCommentSourceEnum.UGC,
                            UserCommentSourceEnum.CommentType.UGC_4, userId, reason,
                            getBaseInfoOperateMsg(handleType), desc);
                    userCommentBiz.add(synFirstApproveUserComment);

                    break;
                case ORDER:
                    if (org.apache.commons.lang.StringUtils.isBlank(reason)) {
                        continue;
                    }
                    handleOrder(adminWorkOrder, result, handleTypeEnum, comment, reason);
                    desc += contentType.getWord();
                    break;
                case PROGRESS:
                    if (org.apache.commons.lang.StringUtils.isBlank(reason)) {
                        return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
                    }
                    handleProgress(adminWorkOrder, result, handleTypeEnum, title, content, imgUrls, reason, stopReasonId, userId);
                    desc += contentType.getWord();
                    break;
                case VERIFICATION:
                    if (org.apache.commons.lang.StringUtils.isBlank(reason)) {
                        continue;
                    }
                    dealId = handleVerification(adminWorkOrder, result, handleTypeEnum, comment, reason);
                    desc += contentType.getWord() + ",操作的内容为[" + dealId + "]";
                    break;
                case COMMENT_ORDER:
                case COMMENT_PROGRESS:
                    if (org.apache.commons.lang.StringUtils.isBlank(reason)) {
                        continue;
                    }
                    dealId = handleComment(adminWorkOrder, result, handleTypeEnum, comment, reason, contentType);
                    desc += contentType.getWord() + ",操作的内容为[" + dealId + "]";
                    break;
                default:
            }
            //增加UGC图文审核记录
            UserComment userComment = new UserComment(adminTaskUgc.getCaseId(), UserCommentSourceEnum.UGC,
                    UserCommentSourceEnum.CommentType.getCommetType(contentType), userId, reason, handleTypeEnum.getWord(), desc);
            userCommentBiz.add(userComment);
        }
        return NewResponseUtil.makeSuccess(result);
    }

    public Response handleUgcWorkId(int handleType,
                                    String reason,
                                    int userId,
                                    String ugcWorkOrderId, int adminUserId,Integer verifyStatus,String medicalImageUrl) {

        // 判断是否为医护身份审核逻辑
        if (Objects.nonNull(verifyStatus) && -1 != verifyStatus) {

            List<Integer> verifyStatusList = Arrays.stream(VerifyStatusEnum.values()).map(VerifyStatusEnum::getCode).collect(Collectors.toList());
            if (!verifyStatusList.contains(verifyStatus)) {
                log.info("handleUgcWorkId 未匹配到VerifyStatusEnum verifyStatus: {}" ,verifyStatus);
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }

            Response<List<WorkOrderVO>> response = ugcWorkOrderClient.ugcOrderlistByIds(Collections.singletonList(Long.valueOf(ugcWorkOrderId)));
            if (response == null || response.notOk() || CollectionUtils.isEmpty(response.getData())){
                log.info("handleUgcWorkId 未查询到工单 ugcWorkOrderId: {}" ,ugcWorkOrderId);
                return NewResponseUtil.makeError(AdminErrorCode.WORK_ORDER_NOT_FOUND);
            }

            WorkOrderVO workOrderVO = response.getData().get(0);
            String verificationId = workOrderVO.getVerificationId();
            // 获取证实信息
            if (StringUtils.isNotEmpty(verificationId)) {

                Response<List<CrowdFundingVerification>> verificationReponse = verificationFeignClient.getVerificationList(Collections.singletonList(Long.valueOf(verificationId)));
                if (Objects.nonNull(verificationReponse) && CollectionUtils.isNotEmpty(verificationReponse.getData())){

                    CrowdFundingVerification verification = verificationReponse.getData().get(0);
                    if (Objects.nonNull(verification)) {

                        // 0 更新work_order_ext表快照字段
                        workOrderExtFeignClient.updateByNameValue(workOrderVO.getWorkOrderId(), "medicalStatus", verifyStatus.toString());

                        // 1 更新同步认证状态
                        userInfoFeignClient.updateMedicalStatus(verification.getVerifyUserId(), verifyStatus);

                        // 2 医护证实截图不为空保存
                        if (StringUtils.isNotEmpty(medicalImageUrl)) {
                            userInfoFeignClient.updateMedicalImageUrl(verification.getVerifyUserId(), medicalImageUrl);
                        }
                    }
                }
            }
        }

        return this.handleUgcWorkId(handleType,reason,userId,ugcWorkOrderId,adminUserId);
    }

    public Response handleUgcWorkId(int handleType,
                                    String reason,
                                    int userId,
                                    String ugcWorkOrderId, int adminUserId) {

        AdminUGCTask.Result handleTypeEnum = AdminUGCTask.Result.getByCode(handleType);
        if (handleTypeEnum == null || handleTypeEnum == AdminUGCTask.Result.NO || StringUtils.isEmpty(reason)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<Long> ids = Arrays.stream(ugcWorkOrderId.split(",")).map(Long::valueOf).collect(Collectors.toList());

        Response<List<WorkOrderVO>> response = ugcWorkOrderClient.ugcOrderlistByIds(ids);

        if (response == null || response.notOk() || CollectionUtils.isEmpty(response.getData())){
            return NewResponseUtil.makeError(AdminErrorCode.WORK_ORDER_NOT_FOUND);
        }

        //工单处理为仅自己可见时，异常证实自动添加黑名单
        if (handleTypeEnum == AdminUGCTask.Result.ONLY_SELF) {
            expVerifyAutoAddBlacklist(response.getData());
        }

        Map<String, Object> result = Maps.newHashMap();
        long dealId = 0;
        for (WorkOrderVO workOrderVO : response.getData()) {
            String desc = "客服操作";
            int code = 0;
            if(StringUtils.isNotBlank(workOrderVO.getContentType())){
                code = Integer.parseInt(workOrderVO.getContentType());
            }
            AdminUGCTask.Content contentType = AdminUGCTask.Content.getByCode(code);

            switch (contentType) {

                case ORDER:
                    if (StringUtils.isNotEmpty(workOrderVO.getWordId())) {
                        newHandleOrder(Long.parseLong(workOrderVO.getWordId()),handleTypeEnum, adminUserId);
                        desc += contentType.getWord();
                        break;
                    }
                case VERIFICATION:
                    if (workOrderVO.getHandleResult() == HandleResultEnum.manual_lock.getType()) {
                        log.info("handleUgcWorkId 工单已结束 ugcWorkOrderId: {}" ,ugcWorkOrderId);
                        ids = ids.stream().filter(id -> id != workOrderVO.getWorkOrderId()).collect(Collectors.toList());
                        break;
                    }
                    if (StringUtils.isNotEmpty(workOrderVO.getWordId())) {
                        dealId = newHandleVerification(workOrderVO.getCaseId(), Long.parseLong(workOrderVO.getWordId()), handleTypeEnum, adminUserId);
                        desc += contentType.getWord() + ",操作的内容为[" + dealId + "]";
                        break;
                    }
                case COMMENT_ORDER:
                case COMMENT_PROGRESS:
                    if (StringUtils.isNotEmpty(workOrderVO.getWordId())) {
                        dealId = newHandleComment(workOrderVO.getCaseId(), Long.parseLong(workOrderVO.getWordId()), handleTypeEnum, contentType, adminUserId);
                        desc += contentType.getWord() + ",操作的内容为[" + dealId + "]";
                        break;
                    }
                default:
            }
            //增加UGC图文审核记录
            UserComment userComment = new UserComment(workOrderVO.getCaseId(), UserCommentSourceEnum.UGC,
                    UserCommentSourceEnum.CommentType.getCommetType(contentType), userId, reason, handleTypeEnum.getWord(), desc);
            userCommentBiz.add(userComment);
        }

        //处理新工单
        if (StringUtils.isNotEmpty(ugcWorkOrderId) && CollectionUtils.isNotEmpty(ids)) {
            UgcHandleOrderParam ugcHandleOrderParam = new UgcHandleOrderParam();
            ugcHandleOrderParam.setWorkOrderIds(ids);
            ugcHandleOrderParam.setOrderType(WorkOrderType.ugcpinglun.getType());
            ugcHandleOrderParam.setHandleResult(handleTypeEnum == AdminUGCTask.Result.PASS_AND_SHOW ? HandleResultEnum.pass_show.getType():HandleResultEnum.only_self.getType());
            ugcHandleOrderParam.setOperComment(reason);
            ugcHandleOrderParam.setUserId(userId);
            ugcWorkOrderClient.handleUgc(ugcHandleOrderParam);
        }

        return NewResponseUtil.makeSuccess(result);
    }

    /**
     * 异常证实自动添加黑名单
     */
    private void expVerifyAutoAddBlacklist(List<WorkOrderVO> workOrderVOS){
        List<WorkOrderVO> verifyWorkOrders = workOrderVOS.stream().filter(workOrderVO ->
                Objects.equals(workOrderVO.getContentType(), String.valueOf(CfSensitiveWordRecordEnum.BizType.VERIFICATION.value())))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(verifyWorkOrders)) {
            log.info("verify work order not found, ignore auto add flow");
            return;
        }

        List<Long> verifyWorkOrderIds = verifyWorkOrders.stream().map(WorkOrderVO::getWorkOrderId).collect(Collectors.toList());
        Response<List<WorkOrderExt>> resp = cfWorkOrderClient.listExtInfos(verifyWorkOrderIds, OrderExtName.hitSensitiveStrategyCode.getName());
        if (resp.notOk()) {
            throw new ServiceException(ErrorCode.SYSTEM_ERROR);
        }
        Set<Long> extVerifyWorkOrderIds = resp.getData().stream()
                .filter(workOrderExt -> Objects.equals(workOrderExt.getExtValue(), String.valueOf(AdminWorkOrderConst.Task.EXP_VERIFY_MANY_TIMES.getCode())))
                .map(WorkOrderExt::getWorkOrderId).collect(Collectors.toSet());

        List<Long> expVerifyWordIds = verifyWorkOrders.stream().filter(workOrderVO ->
                extVerifyWorkOrderIds.contains(workOrderVO.getWorkOrderId()))
                .map(workOrderVO -> Long.valueOf(workOrderVO.getWordId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(expVerifyWordIds)) {
            List<CfSensitiveWordRecord> records = sensitiveWordRecordBiz.selectByIds(expVerifyWordIds);
            VerifyAutoAddBlacklistVo verifyAutoAddBlacklistVo = new VerifyAutoAddBlacklistVo(records.stream()
                    .map(CfSensitiveWordRecord::getUserId).collect(Collectors.toList()));
            log.info("异常证实自动添加黑名单，param:{}", verifyAutoAddBlacklistVo);
            Response<Void> response = blacklistClient.verifyAutoAddBlacklist(verifyAutoAddBlacklistVo);
            log.info("异常证实自动添加黑名单，resp:{}", response);
        }
    }

    public Response handleProgress(int handleResult,
                                   String reason,
                                   int userId,
                                   String ugcWorkOrderId,
                                   String content,
                                   String imgUrls) {

        HandleResultEnum handleResultEnum = HandleResultEnum.getFromType(handleResult);

        if ((handleResultEnum != HandleResultEnum.pass_show && handleResultEnum == HandleResultEnum.modify && handleResultEnum == HandleResultEnum.delete)
                || StringUtils.isEmpty(reason) || StringUtils.isEmpty(ugcWorkOrderId)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<Long> ids = Arrays.stream(ugcWorkOrderId.split(",")).map(Long::valueOf).collect(Collectors.toList());

        Response<List<WorkOrderVO>> response = ugcWorkOrderClient.ugcOrderlistByIds(ids);

        if (response == null || response.notOk() || CollectionUtils.isEmpty(response.getData())){
            return NewResponseUtil.makeError(AdminErrorCode.WORK_ORDER_NOT_FOUND);
        }
        long count = response.getData()
                .stream()
                .filter(f -> StringUtils.equals(f.getContentType(), AdminUGCTask.Content.HEAD_IMAGE_URL.getCode() + ""))
                .count();
        if (count > 0) {
            return NewResponseUtil.makeError(AdminErrorCode.BATCH_HANDLE_WORK_ORDER_ERROR);
        }
        long stateCount = response.getData()
                .stream()
                .filter(f -> StringUtils.equals(f.getContentType(), AdminUGCTask.Content.HOPE_TREE_STATE.getCode() + ""))
                .count();
        if (stateCount > 0) {
            return NewResponseUtil.makeError(AdminErrorCode.BATCH_HANDLE_STATE_WORK_ORDER_ERROR);
        }


        for (WorkOrderVO workOrderVO : response.getData()) {

            String desc = "客服操作" + AdminUGCTask.Content.PROGRESS.getWord();
            long progressId = Long.valueOf(workOrderVO.getWordId());
            int caseId = workOrderVO.getCaseId();

            CrowdFundingProgress progress = adminCrowdFundingProgressBiz.getActivityProgressById(progressId);

            switch (handleResultEnum) {
                case delete:
                    cfCommonFeignClient.deleteProgress(progress.getActivityId(), (int) progressId);
                    riskDelegate.deleteVerify(caseId, UgcTypeEnum.PROGRESS, progressId);
                    break;
                case modify:
                    if (StringUtils.isNotBlank(content)) {
                        adminCrowdFundingProgressBiz.updateContent(progressId, content);
                    }
                    List<String> images = Splitter.on(",").splitToList(imgUrls);
                    String urls = images.stream().filter(val -> StringUtils.contains(val, "http"))
                            .collect(Collectors.joining(","));
                    adminCrowdFundingProgressBiz.updateImageUrlsByNoIsDelete(urls, (int) progressId, caseId);

                    onProgressPass(progress);
                    break;
                case pass_show:
                    onProgressPass(progress);
                    break;
                default:

                    //增加UGC图文审核记录
                    UserComment userComment = new UserComment(workOrderVO.getCaseId(), UserCommentSourceEnum.UGC,
                            UserCommentSourceEnum.CommentType.getCommetType(AdminUGCTask.Content.PROGRESS), userId, reason, handleResultEnum.getShowMsg(), desc);

                    userCommentBiz.add(userComment);
            }

            if (handleResultEnum == HandleResultEnum.modify || handleResultEnum == HandleResultEnum.pass_show) {
                // 动态图片掩码发送通知
                log.info("AiImageMaskServiceImpl sendImageMaskMqByBizId {} {}", caseId, ImageMaskBizEnum.CF_PROGRESS_IMAGE.getDesc());
                aiImageMaskService.sendImageMaskMqByBizId(caseId, ImageMaskBizEnum.CF_PROGRESS_IMAGE.getCode(), Long.valueOf(progress.getId()));
            }

        }
        //处理新工单
        UgcHandleOrderParam ugcHandleOrderParam = new UgcHandleOrderParam();
        ugcHandleOrderParam.setWorkOrderIds(ids);
        ugcHandleOrderParam.setOrderType(WorkOrderType.ugcprogress.getType());
        ugcHandleOrderParam.setHandleResult(handleResult);
        ugcHandleOrderParam.setUserId(userId);
        ugcHandleOrderParam.setOperComment(reason);
        ugcWorkOrderClient.handleUgc(ugcHandleOrderParam);

        return NewResponseUtil.makeSuccess("");

    }

    public Response<Void> handleProgressHeadImage(int handleResult, String reason, int userId, String ugcWorkOrderId) {

        HandleResultEnum handleResultEnum = HandleResultEnum.getFromType(handleResult);

        if (StringUtils.isEmpty(reason) || StringUtils.isEmpty(ugcWorkOrderId)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<Long> ids = Splitter.on(",")
                .splitToList(ugcWorkOrderId)
                .stream()
                .map(Long::valueOf)
                .collect(Collectors.toList());

        Response<List<WorkOrderVO>> response = ugcWorkOrderClient.ugcOrderlistByIds(ids);

        if (response == null || response.notOk() || CollectionUtils.isEmpty(response.getData())){
            return NewResponseUtil.makeError(AdminErrorCode.WORK_ORDER_NOT_FOUND);
        }

        for (WorkOrderVO workOrderVO : response.getData()) {

            switch (handleResultEnum) {
                case pass_show:
                    passHeadImageWorkOrder(workOrderVO, handleResultEnum);
                case audit_reject:
                    rejectHeadImageWorkOrder(workOrderVO, handleResultEnum, reason, userId);
                    break;
                default:
                    //增加UGC图文审核记录
                    UserComment userComment = new UserComment(workOrderVO.getCaseId(), UserCommentSourceEnum.UGC,
                            UserCommentSourceEnum.CommentType.getCommetType(AdminUGCTask.Content.HEAD_IMAGE_URL), userId, reason, handleResultEnum.getShowMsg(), "客服操作" + AdminUGCTask.Content.HEAD_IMAGE_URL.getWord());

                    userCommentBiz.add(userComment);
            }
        }
        //处理新工单
        UgcHandleOrderParam ugcHandleOrderParam = new UgcHandleOrderParam();
        ugcHandleOrderParam.setWorkOrderIds(ids);
        ugcHandleOrderParam.setOrderType(WorkOrderType.ugcprogress.getType());
        ugcHandleOrderParam.setHandleResult(handleResult);
        ugcHandleOrderParam.setUserId(userId);
        ugcHandleOrderParam.setOperComment(reason);
        ugcWorkOrderClient.handleUgc(ugcHandleOrderParam);

        return NewResponseUtil.makeSuccess(null);

    }

    private String  getBaseInfoOperateMsg(int handleType) {
        AdminUGCTask.Result handleResult = AdminUGCTask.Result.getByCode(handleType);
       return  "图文-" + (handleResult != AdminUGCTask.Result.NOT_HANDLE ? handleResult.getWord() : "审核通过");
    }

    public Map<Integer, CfBaseInfoRiskHitVO> getBaseInfoHitMapping(List<Integer> caseIds) {

        if (CollectionUtils.isEmpty(caseIds)) {
            return Maps.newHashMap();
        }
        Response<List<CfBaseInfoRiskHitVO>> hitVoResponse = riskControlWordClient.selectHitVoByCaseIds(caseIds);

        Map<Integer, CfBaseInfoRiskHitVO> result = Maps.newHashMap();

        if (hitVoResponse != null && CollectionUtils.isNotEmpty(hitVoResponse.getData())) {
            for (CfBaseInfoRiskHitVO vo : hitVoResponse.getData()) {
                result.put(vo.getCaseId(), vo);
            }
        }

        return result;
    }

    private void passHeadImageWorkOrder(WorkOrderVO workOrderVO, HandleResultEnum handleResultEnum) {
        if (!StringUtils.equals(String.valueOf(AdminUGCTask.Content.HEAD_IMAGE_URL.getCode()), workOrderVO.getContentType())) {
            return;
        }
        if (StringUtils.isEmpty(workOrderVO.getHeadImageUrl())) {
            return;
        }
        if (!Objects.equals(HandleResultEnum.pass_show, handleResultEnum)) {
            return;
        }
        saveCaseHeadPictureUrl(workOrderVO);

    }

    private void rejectHeadImageWorkOrder(WorkOrderVO workOrderVO, HandleResultEnum handleResultEnum, String reason, int userId) {
        if (!StringUtils.equals(String.valueOf(AdminUGCTask.Content.HEAD_IMAGE_URL.getCode()), workOrderVO.getContentType())) {
            return;
        }
        if (StringUtils.isEmpty(workOrderVO.getHeadImageUrl())) {
            return;
        }
        if (!Objects.equals(HandleResultEnum.audit_reject, handleResultEnum)) {
            return;
        }
        String approveComment = "案例头图审核驳回：" + reason;
        if (StringUtils.isNotEmpty(approveComment)) {
            approveRemarkOldService.add(workOrderVO.getCaseId(), userId, approveComment);
        }
    }

    /**
     * 保存案例头图
     *
     * @param workOrderVO
     */
    private void saveCaseHeadPictureUrl(WorkOrderVO workOrderVO) {
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(workOrderVO.getCaseId());
        if (Objects.isNull(crowdfundingInfo)) {
            return;
        }
        String headPictureUrl = workOrderVO.getHeadImageUrl();
        AdminCaseDetailsMsg adminCaseDetailsMsg = adminCaseDetailsMsgDao.getByCaseId(workOrderVO.getCaseId());
        if (Objects.isNull(adminCaseDetailsMsg)) {
            adminCaseDetailsMsgDao.addCaseDetailsMsg(workOrderVO.getCaseId(), crowdfundingInfo.getInfoId(), headPictureUrl, "", "", "", 0);
        } else {
            adminCaseDetailsMsgDao.updateHeadPictureUrl(workOrderVO.getCaseId(), headPictureUrl);
        }
    }

    public Response ugcConsultantEvaluationHandle(int handleType, String reason, int userId, String ugcWorkOrderId, int adminUserId) {

        AdminUGCTask.Result handleTypeEnum = AdminUGCTask.Result.getByCode(handleType);
        if (handleTypeEnum == null || handleTypeEnum == AdminUGCTask.Result.NO || StringUtils.isEmpty(reason)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<Long> ids = Arrays.stream(ugcWorkOrderId.split(",")).map(Long::valueOf).collect(Collectors.toList());

        Response<List<WorkOrderVO>> response = ugcWorkOrderClient.ugcOrderlistByIds(ids);

        if (response == null || response.notOk() || CollectionUtils.isEmpty(response.getData())){
            return NewResponseUtil.makeError(AdminErrorCode.WORK_ORDER_NOT_FOUND);
        }

        Map<String, Object> result = Maps.newHashMap();
        for (WorkOrderVO workOrderVO : response.getData()) {
            String desc = "客服操作";
            AdminUGCTask.Content contentType = AdminUGCTask.Content.getByCode(Integer.valueOf(workOrderVO.getContentType()));
            //增加UGC审核记录
            UserComment userComment = new UserComment(workOrderVO.getCaseId(), UserCommentSourceEnum.UGC,
                    UserCommentSourceEnum.CommentType.getCommetType(contentType), userId, reason, handleTypeEnum.getWord(), desc);
            userCommentBiz.add(userComment);
        }

        //处理新工单
        if (StringUtils.isNotEmpty(ugcWorkOrderId)){
            UgcHandleOrderParam ugcHandleOrderParam = new UgcHandleOrderParam();
            ugcHandleOrderParam.setWorkOrderIds(ids);
            ugcHandleOrderParam.setOrderType(WorkOrderType.consultant_evaluation.getType());
            ugcHandleOrderParam.setHandleResult(handleTypeEnum == AdminUGCTask.Result.PASS_AND_SHOW ? HandleResultEnum.pass_show.getType():HandleResultEnum.only_self.getType());
            ugcHandleOrderParam.setOperComment(reason);
            ugcHandleOrderParam.setUserId(userId);
            ugcWorkOrderClient.handleUgc(ugcHandleOrderParam);
            //发送客户评价工单处理mq
            producer.send(new Message<>(CfClientMQTopicCons.CF,
                    CfClientMQTagCons.CF_HANDLE_CONSULTANT_EVALUATION,
                    CfClientMQTagCons.CF_HANDLE_CONSULTANT_EVALUATION + "-" + System.currentTimeMillis(),
                    ugcHandleOrderParam));
        }
        return NewResponseUtil.makeSuccess(result);
    }
}


