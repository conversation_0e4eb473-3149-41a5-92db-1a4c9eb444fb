package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.admin.AdminCfOperatingRecordBiz;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminUGCTask;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdFundingProgressType;
import com.shuidihuzhu.cf.enums.sona.UGCAction;
import com.shuidihuzhu.cf.facade.AdminApolloCofig;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.client.cf.workorder.CfUgcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.UgcWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: lixuan
 * @date: 2018/3/8 15:04
 */
@Service
@Slf4j
public class CfSensitiveWordService {

    @Autowired
    private AdminCfOperatingRecordBiz adminCfOperatingRecordBiz;
    @Autowired
    private AdminWorkOrderBiz adminWorkOrderBiz;
    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Autowired
    private AdminTaskUgcBiz adminTaskUgcBiz;
    @Autowired
    private AdminCrowdFundingProgressBiz adminCrowdFundingProgressBiz;
    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Resource
    private CfUgcWorkOrderClient ugcWorkOrderClient;
    @Resource
    private AdminCrowdfundingReportBiz reportBiz;

    /**
     * 实时生成
     * @param cfOperatingRecord
     */
    public void addBaseInfoToWorkOrderOne(CfOperatingRecord cfOperatingRecord, UGCAction action){
        if(cfOperatingRecord == null || cfOperatingRecord.getType() != CfOperatingRecordEnum.Type.SUBMIT_BASE.getCode()){
            return;
        }
        //增加图文审核
        addBaseInfo2WorkOrderBase(Lists.newArrayList(cfOperatingRecord), action);
    }

    @Deprecated
    public void addBaseInfoToWorkOrder(Timestamp startTime, Timestamp endTime) {
        List<CfOperatingRecord> cfOperatingRecords = this.adminCfOperatingRecordBiz.getByCreateTime(startTime, endTime,
                ImmutableList.of(CfOperatingRecordEnum.Type.SUBMIT_BASE));
        if(CollectionUtils.isEmpty(cfOperatingRecords)) {
            return;
        }
        addBaseInfo2WorkOrderBase(cfOperatingRecords, null);
    }

    private void addBaseInfo2WorkOrderBase(List<CfOperatingRecord> cfOperatingRecords, UGCAction action) {
        List<String> infoUuids = cfOperatingRecords.stream().map(CfOperatingRecord::getInfoUuid).collect(Collectors
                .toList());
        List<CrowdfundingInfo> crowdfundingInfos = this.adminCrowdfundingInfoBiz.getListByInfoUuIds(infoUuids);
        List<List<CrowdfundingInfo>> infosPartition = Lists.partition(crowdfundingInfos, 1000);
        for (List<CrowdfundingInfo> crowdfundingInfoSubList : infosPartition) {
            List<AdminWorkOrder> adminWorkOrders = this.adminWorkOrderBiz.createBatchAdminWorkOrder
                    (AdminWorkOrderConst.Type.UGC, AdminWorkOrderConst.Task.INFO_BASE_WORD, AdminWorkOrderConst.Role.SYSTEM,
                            -1, 0, "", crowdfundingInfoSubList.size());
            List<AdminTaskUgc> adminTaskUgcs = Lists.newArrayListWithCapacity(crowdfundingInfoSubList.size());
            for(int i = 0; i < crowdfundingInfoSubList.size(); i++) {
                AdminTaskUgc adminTaskUgc = new AdminTaskUgc();
                adminTaskUgc.setModules(AdminUGCTask.Modules.FUNDING.getCode());
                adminTaskUgc.setWorkOrderId(adminWorkOrders.get(i).getId());
                adminTaskUgc.setContentType(AdminUGCTask.Content.BASE_INFO.getCode());
                adminTaskUgc.setExtId(crowdfundingInfoSubList.get(i).getId());
                adminTaskUgc.setWordId(-1);
                adminTaskUgc.setResult(AdminUGCTask.Result.NO.getCode());
                adminTaskUgc.setCaseId(crowdfundingInfoSubList.get(i).getId());
                if (action != null) {
                    adminTaskUgc.setAction(action.getValue());
                }

                adminTaskUgcs.add(adminTaskUgc);
            }
            this.adminTaskUgcBiz.insertList(adminTaskUgcs);
        }
    }


    public void addFirstApprove2Work(String infoUuid ) {
        CrowdfundingInfo fundingInfo = crowdfundingDelegate.getCrowdfundingInfoByInfoId(infoUuid);

        AdminWorkOrder adminWorkOrder = adminWorkOrderBiz.createUgcAdminWorkOrder
                (AdminWorkOrderConst.Type.UGC, AdminWorkOrderConst.Task.FIRST_APPROVE);

        AdminTaskUgc adminTaskUgc = new AdminTaskUgc();
        adminTaskUgc.setModules(AdminUGCTask.Modules.FUNDING.getCode());
        adminTaskUgc.setWorkOrderId(adminWorkOrder.getId());
        adminTaskUgc.setContentType(AdminUGCTask.Content.FIRST_APPROVE.getCode());
        adminTaskUgc.setExtId(fundingInfo.getId());
        adminTaskUgc.setWordId(-1);
        adminTaskUgc.setResult(AdminUGCTask.Result.NO.getCode());
        adminTaskUgc.setCaseId(fundingInfo.getId());

        this.adminTaskUgcBiz.insertOne(adminTaskUgc);

    }

    /**
     * 实时
     * @param crowdFundingProgress
     */
    public void addProcessToWorkOrder(CrowdFundingProgress crowdFundingProgress){
        if(crowdFundingProgress == null){
            return;
        }
        Integer type = crowdFundingProgress.getType();
        // 非用户动态都不生成动态工单
        if (type != CrowdFundingProgressType.PROGRESS.value() && type != CrowdFundingProgressType.PATIENT_PROGRESS.value()){
            return;
        }

        //生成新工单
        UgcWorkOrder workOrder = new UgcWorkOrder();
        workOrder.setCaseId(crowdFundingProgress.getActivityId());
        workOrder.setOrderType(WorkOrderType.ugcprogress.getType());
        workOrder.setContentType(AdminUGCTask.Content.PROGRESS.getCode()+"");
        workOrder.setWordId(crowdFundingProgress.getId()+"");
        workOrder.setOrderlevel(OrderLevel.edium.getType());

        CrowdfundingReport report = reportBiz.getByInfoId(crowdFundingProgress.getActivityId());
        if (report != null){
            workOrder.setOrderlevel(OrderLevel.high.getType());
        }

        Response<Long> response =  ugcWorkOrderClient.createUgc(workOrder);
        log.info("create progress workOrder={},response={}",workOrder, JSON.toJSONString(response));
    }

}
