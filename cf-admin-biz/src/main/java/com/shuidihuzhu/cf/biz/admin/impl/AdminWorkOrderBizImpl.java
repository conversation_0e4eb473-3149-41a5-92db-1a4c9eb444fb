package com.shuidihuzhu.cf.biz.admin.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.admin.util.AdminDateUtil;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderBiz;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderRecordBiz;
import com.shuidihuzhu.cf.biz.admin.exception.ServiceRuntimeException;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.biz.es.CfSearch;
import com.shuidihuzhu.cf.biz.mapper.AdminWorkOrderMapper;
import com.shuidihuzhu.cf.biz.workOrder.ExternalWorkOrderApiBiz;
import com.shuidihuzhu.cf.constants.admin.WorkOrderStatConstant;
import com.shuidihuzhu.cf.dao.admin.AdminWorkOrderDao;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminWorkOrderCaseDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CfFirstApproveOperatorDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CfSensitiveWordRecordDao;
import com.shuidihuzhu.cf.diff.Diff;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminUGCTask;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.admin.errors.WorkOrderErrorEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminWorkOrderCaseConst;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrderRecord;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.enums.crowdfunding.CfSensitiveWordRecordEnum;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminSensitiveVo;
import com.shuidihuzhu.cf.service.search.EsSearchService;
import com.shuidihuzhu.cf.vo.crowdfunding.WorkOrderFirstApprove;
import com.shuidihuzhu.cf.vo.crowdfunding.firstapprove.FirstApproveAccountMap;
import com.shuidihuzhu.cf.vo.crowdfunding.firstapprove.FirstApproveAccountVo;
import com.shuidihuzhu.cf.vo.crowdfunding.firstapprove.FirstApproveConfiguration;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderIndexByAuthorSearchParam;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderIndexSearchParam;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by Ahrievil on 2017/11/30
 */
@Service
@Slf4j
public class AdminWorkOrderBizImpl implements AdminWorkOrderBiz {
    private static final Logger LOGGER = LoggerFactory.getLogger(AdminWorkOrderBizImpl.class);

    @Autowired
    private CfSensitiveWordRecordDao cfSensitiveWordRecordDao;
    @Autowired
    private AdminWorkOrderCaseDao adminWorkOrderCaseDao;
    @Autowired
    private CfFirstApproveOperatorDao cfFirstApproveOperatorDao;
    @Autowired
    private AdminWorkOrderDao adminWorkOrderDao;

    @Autowired
    private AdminWorkOrderRecordBiz adminWorkOrderRecordBiz;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;

    @Autowired
    private MeterRegistry meterRegistry;

    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    private AdminWorkOrderCaseRecordBiz adminWorkOrderCaseRecordBiz;

    @Resource
    private EsSearchService esSearchService;

    @Autowired
    private CfSearch cfSearch;

    @Resource
    private AdminTaskUgcBiz adminTaskUgcBiz;

    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Resource
    private AdminCfInfoExtBiz adminCfInfoExtBiz;

    @Autowired
    private ExternalWorkOrderApiBiz externalWorkOrderApiBiz;

    @Override
    public AdminWorkOrder createUgcAdminWorkOrder(AdminWorkOrderConst.Type orderType, AdminWorkOrderConst.Task orderTask) {
        return createAdminWorkOrder(orderType, orderTask, null, AdminWorkOrderConst.Role.SYSTEM, -1,
                0, "", null);
    }


    @Override
    public AdminWorkOrder createWorkOrderReport(AdminWorkOrderConst.Task taskCode, AdminWorkOrderConst.TaskType taskType,
                                                int creatorId, AdminWorkOrderConst.Level level, String comment) {
        return this.createAdminWorkOrder(AdminWorkOrderConst.Type.CASE_REPORT, AdminWorkOrderConst.Task.CASE_REPORT_DEAL,
                taskType, AdminWorkOrderConst.Role.SYSTEM, creatorId, level.getCode(), "",
                AdminWorkOrderConst.AdminWorkOrderOperateTypeEnum.CREATE_REPORT_ORDER);
    }


    private AdminWorkOrder createAdminWorkOrder(AdminWorkOrderConst.Type orderType, AdminWorkOrderConst.Task orderTask,
                                                AdminWorkOrderConst.TaskType taskType, AdminWorkOrderConst.Role roleType,
                                                int creatorId, int level, String comment,
                                                AdminWorkOrderConst.AdminWorkOrderOperateTypeEnum operateTypeEnum) {
        if (orderType == null || orderTask == null || !AdminWorkOrderConst.Type.isValid(orderType)
                || !AdminWorkOrderConst.Task.isValid(orderTask)) {
            throw new ServiceRuntimeException(WorkOrderErrorEnum.INVALID_TYPE);
        }
        AdminWorkOrder adminWorkOrder = AdminWorkOrder.create(orderType, orderTask, roleType, creatorId, level,
                comment);
        //判断任务类型是否为空
        adminWorkOrder.setTaskType(taskType == null ? 0 : taskType.getCode());
        this.adminWorkOrderDao.insertOne(adminWorkOrder);
        AdminWorkOrderRecord adminWorkOrderRecord = new AdminWorkOrderRecord(adminWorkOrder);
        adminWorkOrderRecord.setOperateType(operateTypeEnum == null ? 0 : operateTypeEnum.getCode());
        this.adminWorkOrderRecordBiz.insertOne(adminWorkOrderRecord);

        //上报grafana
        meterRegistry.counter(WorkOrderStatConstant.ADMIN_WORK_ORDER,
                WorkOrderStatConstant.ADMIN_WORK_ORDER_CATEGORY, orderType.name(),
                WorkOrderStatConstant.ADMIN_WORK_ORDER_SUBTYPE, orderTask.name(),
                WorkOrderStatConstant.ADMIN_WORK_ORDER_TASK_CODE, (taskType == null ? AdminWorkOrderConst.TaskType.CREATED.name() : taskType.name()),
                WorkOrderStatConstant.ADMIN_WORK_ORDER_ROLE, roleType.name()).increment();

        return adminWorkOrder;
    }

    @Override
    public List<AdminWorkOrder> createBatchAdminWorkOrder(AdminWorkOrderConst.Type orderType, AdminWorkOrderConst.Task
            orderTask, AdminWorkOrderConst.Role roleType, int operatorId, int level, String comment, int size) {
        if (orderType == null || orderTask == null || !AdminWorkOrderConst.Type.isValid(orderType)
                || !AdminWorkOrderConst.Task.isValid(orderTask)) {
            throw new ServiceRuntimeException(WorkOrderErrorEnum.INVALID_TYPE);
        }
        List<AdminWorkOrder> adminWorkOrders = Lists.newArrayListWithCapacity(size);
        for (int i = 0; i < size; i++) {
            adminWorkOrders.add(AdminWorkOrder.create(orderType, orderTask, roleType, operatorId, level,
                    comment));
        }
        this.adminWorkOrderDao.insertList(adminWorkOrders);
        List<AdminWorkOrderRecord> adminWorkOrderRecords = Lists.newArrayListWithCapacity(size);
        for (int i = 0; i < size; i++) {
            adminWorkOrderRecords.add(new AdminWorkOrderRecord(adminWorkOrders.get(i)));
        }
        this.adminWorkOrderRecordBiz.insertList(adminWorkOrderRecords);

        //上报grafana
        meterRegistry.counter(WorkOrderStatConstant.ADMIN_WORK_ORDER,
                WorkOrderStatConstant.ADMIN_WORK_ORDER_CATEGORY, orderType.name(),
                WorkOrderStatConstant.ADMIN_WORK_ORDER_SUBTYPE, orderTask.name(),
                WorkOrderStatConstant.ADMIN_WORK_ORDER_TASK_CODE, AdminWorkOrderConst.TaskType.CREATED.name(),
                WorkOrderStatConstant.ADMIN_WORK_ORDER_ROLE, roleType.name()).increment(size);

        return adminWorkOrders;
    }

    @Override
    public List<AdminWorkOrder> assigningTask(AdminWorkOrderConst.Type orderType, AdminWorkOrderConst.Task orderTask, int
            operatorId, int size) {
        return assigningTasks(orderType, Lists.newArrayList(orderTask), operatorId, size);
    }

    @Override
    public List<AdminWorkOrder> assigningTasks(AdminWorkOrderConst.Type orderType, List<AdminWorkOrderConst.Task> orderTask,
                                               int operatorId, int size) {
        String lockName = "admin-work-order-assigning-task-lock";
        String identifier = null;
        try {
            identifier = cfRedissonHandler.tryLock(lockName, 5 * 1000, 60 * 1000);
        } catch (InterruptedException e) {
            log.error("领取工单异常userId:{}, ", operatorId, e);
        }

        if (StringUtils.isBlank(identifier)) {
            return Collections.emptyList();
        }

        List<Integer> tasks = orderTask.stream().map(AdminWorkOrderConst.Task::getCode).collect(Collectors.toList());
        try {

            List<AdminWorkOrder> unHandleTask = this.adminWorkOrderDao.getUnHandleTaskByTasks(orderType.getCode(),
                        tasks, size);

            if (CollectionUtils.isEmpty(unHandleTask)) {
                return unHandleTask;
            }

            this.updateWithOperatorIds(unHandleTask.stream().map(AdminWorkOrder::getId)
                    .collect(Collectors.toList()), operatorId, AdminWorkOrderConst.Status.HANDLING.getCode());
            unHandleTask.forEach(adminWorkOrder -> adminWorkOrder.setOperatorId(operatorId));
            List<AdminWorkOrderRecord> adminWorkOrderRecordList = unHandleTask.stream().map(val -> {
                AdminWorkOrderRecord adminWorkOrderRecord = new AdminWorkOrderRecord(val);
                // 前置审核工单的领取 记录
                if (orderType == AdminWorkOrderConst.Type.UGC && CollectionUtils.isNotEmpty(orderTask) &&
                        orderTask.get(0) == AdminWorkOrderConst.Task.FIRST_APPROVE) {
                    adminWorkOrderRecord.setOperateType(AdminWorkOrderConst.AdminWorkOrderOperateTypeEnum.FIRST_APPROVE_ORDER_ASSIGN.getCode());
                }
                adminWorkOrderRecord.setOperatorId(operatorId);
                return adminWorkOrderRecord;
            }).collect(Collectors.toList());
            this.adminWorkOrderRecordBiz.insertList(adminWorkOrderRecordList);
            return unHandleTask;

        } catch (Exception e) {
            LOGGER.error("", e);
            return Collections.emptyList();
        } finally {
            //没有锁  不用释放  否则会出异常
            if (StringUtils.isNotEmpty(identifier)) {
                cfRedissonHandler.unLock(lockName, identifier);
            }
        }
    }



    @Diff(diffMethod = "selectUgcSensitiveByAnchorFromEs", diffCompare = "com.shuidihuzhu.cf.diff.SelectUgcSensitiveByAnchorCompare")
    @Override
    public List<AdminSensitiveVo> selectUgcSensitiveByAnchor(int anchor, int pageSize, boolean isPre, int operatorId,
                                                             int caseId,
                                                             Integer result, String title, long commentUserId,
                                                             int contentType, int taskType, String hitWords, String startTime,
                                                             String endTime) {

        List<Integer> orderTasks = getOrderTasks(taskType);
        List<Integer> contentTypes = getContentTypes(contentType);

        return selectUgcSensitiveByAnchorV2(anchor, pageSize, isPre, operatorId, caseId, result, title, commentUserId,
                startTime, endTime, orderTasks, contentTypes, hitWords);
    }

    @Override
    public List<AdminSensitiveVo> selectUgcSensitiveByAnchorFromEs(int anchor, int pageSize, boolean isPre, int operatorId,
                                                             int caseId,
                                                             Integer result, String title, long commentUserId,
                                                             int contentType, int taskType, String hitWords, String startTime,
                                                             String endTime) {
        List<Integer> orderTasks = getOrderTasks(taskType);
        List<Integer> contentTypes = getContentTypes(contentType);

        CfWorkOrderIndexByAuthorSearchParam searchParam = new CfWorkOrderIndexByAuthorSearchParam();
        searchParam.setAwoOrderTypes(Lists.newArrayList(AdminWorkOrderConst.Type.UGC.getCode()));
        searchParam.setAwoOrderTasks(orderTasks);
        searchParam.setAtuContentTypes(contentTypes);
        searchParam.setSize(pageSize);
        searchParam.setPre(isPre);
        searchParam.setAnchor(anchor);

        if (operatorId != 0) {
            searchParam.setAwoOperatorIds(Lists.newArrayList((long)operatorId));
        }
        if (caseId > 0) {
            searchParam.setCfCaseIds(Lists.newArrayList((long)caseId));
        }
        if (result != null) {
            searchParam.setAtuResults(Lists.newArrayList(result));
        }
        if (StringUtils.isNotBlank(title)) {
            searchParam.setCfTitle(title);
        }

        if (commentUserId != 0) {
            searchParam.setCswrUserIds(Lists.newArrayList(commentUserId));
        }
        if (StringUtils.isNotBlank(hitWords)) {
            searchParam.setAtuHitWords(Lists.newArrayList(hitWords));
        }
        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            long updateStart = AdminDateUtil.convertToMills(startTime, com.shuidihuzhu.common.util.DateUtil.DATETIME_PATTERN_2);
            long updateEnd = AdminDateUtil.convertToMills(endTime, com.shuidihuzhu.common.util.DateUtil.DATETIME_PATTERN_2);
            searchParam.setAtuUpdateTimeStart(updateStart);
            searchParam.setAtuUpdateTimeEnd(updateEnd);
        }

        Pair<Long, List<AdminWorkOrder>> pair = cfSearch.cfWorkOrderIndexBySearch(searchParam);
        if (CollectionUtils.isEmpty(pair.getRight())) {
            return Lists.newArrayList();
        }

        List<AdminSensitiveVo> list = getAdminSensitiveVos(pair.getRight());
        promoteInfo(list);
        return list;
    }

    private List<AdminSensitiveVo> getAdminSensitiveVos(List<AdminWorkOrder> workOrders) {
        if (CollectionUtils.isEmpty(workOrders)) {
            return Lists.newArrayList();
        }

        List<Long> workOrderIds = workOrders.stream().map(x -> x.getId()).collect(Collectors.toList());

        List<AdminTaskUgc> adminTaskUgcs = adminTaskUgcBiz.selectByWorkOrderIds(workOrderIds);
        Map<Long, AdminTaskUgc> adminTaskUgcMap = Maps.uniqueIndex(adminTaskUgcs, AdminTaskUgc::getWorkOrderId);

        List<Integer> caseIds = adminTaskUgcs.stream().map(x -> x.getCaseId()).collect(Collectors.toList());
        List<CrowdfundingInfo> crowdfundingInfos = adminCrowdfundingInfoBiz.selectByCaseIdList(caseIds);
        Map<Integer, CrowdfundingInfo> crowdfundingInfoMap = Maps.uniqueIndex(crowdfundingInfos, CrowdfundingInfo::getId);

        List<Long> wordIds = adminTaskUgcs.stream().map(x -> x.getWordId()).collect(Collectors.toList());
        List<CfSensitiveWordRecord> wordRecords = selectSensitiveWordRecord(wordIds);
        Map<Long, CfSensitiveWordRecord> wordRecordMap = Maps.uniqueIndex(wordRecords, CfSensitiveWordRecord::getId);


        List<AdminSensitiveVo> sensitiveVos = Lists.newArrayList();
        for (AdminWorkOrder workOrder : workOrders) {
            AdminSensitiveVo sensitiveVo = AdminWorkOrderMapper.INSTANCE.toAdminSensitiveVo(workOrder);

            AdminTaskUgc taskUgc = adminTaskUgcMap.get(workOrder.getId());
            if (taskUgc == null) {
                continue;
            }

            sensitiveVo.setContentType(taskUgc.getContentType());
            sensitiveVo.setHandleResult(String.valueOf(taskUgc.getResult()));
            sensitiveVo.setOperatorTime(taskUgc.getUpdateTime());

            CrowdfundingInfo info = crowdfundingInfoMap.get(taskUgc.getCaseId());
            if (info == null) {
                continue;
            }
            sensitiveVo.setCaseId(info.getId());
            sensitiveVo.setCaseTitle(info.getTitle());
            sensitiveVo.setCaseUUid(info.getInfoId());

            CfSensitiveWordRecord wordRecord = wordRecordMap.get(taskUgc.getWordId());
            if (wordRecord != null) {
                sensitiveVo.setCommentUserId(wordRecord.getUserId());
                sensitiveVo.setContent(wordRecord.getContent());
                sensitiveVo.setSensitiveWords(wordRecord.getSensitiveWord());
            }

            sensitiveVos.add(sensitiveVo);
        }

        return sensitiveVos;
    }
    private List<CfSensitiveWordRecord> selectSensitiveWordRecord(List<Long> list) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return cfSensitiveWordRecordDao.selectByIds(list);
    }
    @Override
    public List<AdminSensitiveVo> searchUgcSensitiveByPage(int anchorId, int pageSize, boolean isPre, int operatorId, Integer result, int contentType, int taskType, String hitWords, String startTime, String endTime) {
        List<Integer> orderTasks = getOrderTasks(taskType);
        List<Integer> contentTypes = getContentTypes(contentType);

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        Date startDate = null;
        Date endDate = null;
        try {
            if (StringUtils.isNotBlank(startTime)) {
                startDate = simpleDateFormat.parse(startTime);
            }
            if (StringUtils.isNotBlank(endTime)) {
                endDate = simpleDateFormat.parse(endTime);
            }
        } catch (ParseException e) {
            log.error("{}, {}", startTime, endTime);
        }

        List<Integer> workOrderIds = esSearchService.searchUgcWordOrder(
                AdminWorkOrderConst.Type.UGC.getCode(),
                orderTasks,
                operatorId,
                contentTypes,
                result,
                startDate,
                endDate,
                hitWords,
                anchorId,
                pageSize,
                isPre
        );
        List<AdminSensitiveVo> list = workOrderIds.stream()
                .map(v -> adminWorkOrderDao.getSensitiveVOByWorkOrderId(v))
                .collect(Collectors.toList());
        promoteInfo(list);
        return list;
    }

    @NotNull
    private List<AdminSensitiveVo> selectUgcSensitiveByPageV2(int current, int pageSize, int operatorId, int caseId, Integer result, String title, long commentUserId, String startTime, String endTime, List<Integer> orderTasks, List<Integer> contentTypes) {
        PageHelper.startPage(current, pageSize);
        List<AdminSensitiveVo> list = adminWorkOrderDao.selectUgcSensitiveByPage(AdminWorkOrderConst.Type.UGC.getCode(),
                orderTasks, operatorId, caseId, result, title, commentUserId,
                contentTypes, StringUtils.trimToNull(startTime), StringUtils.trimToNull(endTime));
        promoteInfo(list);
        return list;
    }

    @NotNull
    private List<AdminSensitiveVo> selectUgcSensitiveByAnchorV2(int anchor, int pageSize, boolean isPre, int operatorId, int caseId,
                                                                Integer result, String title, long commentUserId, String startTime, String endTime, List<Integer> orderTasks, List<Integer> contentTypes, String hitWords) {
        List<AdminSensitiveVo> list = adminWorkOrderDao.selectUgcSensitiveByAnchor(
                AdminWorkOrderConst.Type.UGC.getCode(),
                orderTasks, operatorId, caseId, result, title, commentUserId,
                contentTypes, hitWords, StringUtils.trimToNull(startTime), StringUtils.trimToNull(endTime),
                anchor, pageSize, isPre);
        promoteInfo(list);
        return list;
    }

    private void promoteInfo(List<AdminSensitiveVo> list) {
        //操作人
        List<Integer> userIds = list.stream().map(AdminSensitiveVo::getOperatorId).collect(Collectors.toList());

        List<AdminUserAccountModel> models = seaAccountClientV1.getUserAccountsByIds(userIds).getResult();

        Map<Integer, AdminUserAccountModel> userMap = models.stream().collect(Collectors.toMap(AdminUserAccountModel::getId, Function.identity()));

        list.forEach(r -> {
            r.setContentTypeStr(AdminUGCTask.Content.getByCode(r.getContentType()).getWord());
            r.setOperatorTimeStr(DateFormatUtils.format(r.getOperatorTime(), "yyyy-MM-dd HH:mm:ss"));
            String name = "";
            int user = r.getOperatorId();
            if (userMap.containsKey(user)) {
                name = userMap.get(user).getName();
            }
            r.setOperatorName(name);
            if (r.getSensitiveWords() == null) {
                r.setSensitiveWords("");
            }
        });
    }

    /**
     * 获取要查询的ugc类型
     *
     * @param contentType
     * @return
     */
    private List<Integer> getContentTypes(int contentType) {
        if (contentType == 0) {
            return Collections.emptyList();
        }
        // 订单和订单评论合并为订单评论
        if (contentType == CfSensitiveWordRecordEnum.BizType.COMMENT_ORDER.value()) {
            return Lists.newArrayList(
                    CfSensitiveWordRecordEnum.BizType.COMMENT_ORDER.value(),
                    CfSensitiveWordRecordEnum.BizType.ORDER.value()
            );
        }
        return Lists.newArrayList(contentType);
    }

    @NotNull
    private List<Integer> getOrderTasks(int taskType) {
        List<Integer> orderTasks = Lists.newArrayList();
        // 没传表示同时查询敏感词 禁止词 等
        if (taskType == 0) {
            return AdminWorkOrderConst.Task.UGC_TASK_CODES;
        } else {
            orderTasks.add(taskType);
        }
        return orderTasks;
    }


    @Diff(diffMethod = "selectUgcBaseInfoByPageFromEs", diffCompare = "com.shuidihuzhu.cf.diff.SelectUgcBaseInfoByPageCompare")
    @Override
    public List<AdminWorkOrder> selectUgcBaseInfoByPage(int operatorId, AdminWorkOrderConst.Type orderType, List<AdminWorkOrderConst.Task> orderTasks, int current, int pageSize, Integer caseId, AdminUGCTask.Result result, Integer action, Integer riskLevel, String startTime, String endTime) {
        PageHelper.startPage(current, pageSize);
        Integer orderTypeCode = orderType != null ? orderType.getCode() : null;
        Integer handleResult = result != null ? result.getCode() : null;
        List<Integer> orderTaskCodes = null;
        if (CollectionUtils.isNotEmpty(orderTasks)) {
            orderTaskCodes = orderTasks.stream().map(AdminWorkOrderConst.Task::getCode).collect(Collectors
                    .toList());
        }
        return adminWorkOrderDao.listUgcBaseInfoByPage(operatorId,
                orderTypeCode,
                orderTaskCodes,
                caseId,
                handleResult,
                action,
                riskLevel,
                StringUtils.trimToNull(startTime),
                StringUtils.trimToNull(endTime));
    }

    @Override
    public Pair<Long, List<AdminWorkOrder>> selectUgcBaseInfoByPageFromEs(int operatorId, AdminWorkOrderConst.Type orderType, List<AdminWorkOrderConst.Task> orderTasks, int current, int pageSize, Integer caseId, AdminUGCTask.Result result, Integer action, Integer riskLevel, String startTime, String endTime) {
        CfWorkOrderIndexSearchParam searchParam = new CfWorkOrderIndexSearchParam();

        if (orderType != null) {
            searchParam.setAwoOrderTypes(Lists.newArrayList(orderType.getCode()));
        }

        if (result != null) {
            searchParam.setAtuResults(Lists.newArrayList(result.getCode()));
        }

        if (CollectionUtils.isNotEmpty(orderTasks)) {
            searchParam.setAwoOrderTasks(orderTasks.stream().map(AdminWorkOrderConst.Task::getCode).collect(Collectors.toList()));
        }

        if (caseId != null && caseId > 0) {
            searchParam.setCfCaseIds(Lists.newArrayList((long)caseId));
        }

        if (action != null) {
            searchParam.setAtuActions(Lists.newArrayList(action));
        }

        if (riskLevel != null) {
            searchParam.setCcrrRiskLevels(Lists.newArrayList(riskLevel));
        }

        if (StringUtils.isNotBlank(startTime)) {
            long updateStart = AdminDateUtil.convertToMills(startTime, com.shuidihuzhu.common.util.DateUtil.DATETIME_PATTERN_2);
            searchParam.setAtuUpdateTimeStart(updateStart);
        }
        if (StringUtils.isNotBlank(endTime)) {
            long updateEnd = AdminDateUtil.convertToMills(endTime, com.shuidihuzhu.common.util.DateUtil.DATETIME_PATTERN_2);
            searchParam.setAtuUpdateTimeEnd(updateEnd);
        }

        if (operatorId > 0) {
            searchParam.setAwoOperatorIds(Lists.newArrayList((long)operatorId));
        }
        searchParam.setFrom((current - 1) * pageSize);
        searchParam.setSize(pageSize);


        return cfSearch.cfWorkOrderIndexSearch(searchParam);
    }

    @Override
    public AdminWorkOrder selectById(long id) {
        return adminWorkOrderDao.selectById(id);
    }

    @Override
    public List<AdminWorkOrder> selectByIdList(List<Long> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return adminWorkOrderDao.selectByIdList(list);
    }

    /**
     * 修改level（优先级）、orderStatus（工单状态）、operatorId（处理人id）需要同步给鲸息系统一份数据
     */
    @Override
    public int updateChangeable(long adminWorkOrderId, AdminWorkOrderConst.Status orderStatus,
                                AdminWorkOrderConst.Result handleResult, String comment) {
        AdminWorkOrder adminWorkOrder = this.adminWorkOrderDao.selectById(adminWorkOrderId);
        if (adminWorkOrder == null) {
            throw new ServiceRuntimeException(WorkOrderErrorEnum.UPDATE_TASK_STATUS_FAILED);
        }
        adminWorkOrder.setOrderStatus(orderStatus.getCode());
        adminWorkOrder.setHandleResult(handleResult.getCode());
        if (StringUtils.isNotBlank(comment) && comment.length() >=1000){
            comment = comment.substring(0,999);
        }
        adminWorkOrder.setComment(comment);
        int r = adminWorkOrderDao.updateChangeable(adminWorkOrder);
        this.syncWorkOrderInfoAdminWorkOrder(r, Lists.newArrayList(), adminWorkOrder.getId(), adminWorkOrder.getOrderStatus(), null, null);
        AdminWorkOrderRecord adminWorkOrderRecord = new AdminWorkOrderRecord(adminWorkOrder);
        this.adminWorkOrderRecordBiz.insertOne(adminWorkOrderRecord);
        return 1;
    }

    /**
     * 修改level（优先级）、orderStatus（工单状态）、operatorId（处理人id）需要同步给鲸息系统一份数据
     */
    @Override
    public int resetWorkOrder(List<Long> list, int operatorId, int orderStatus, int taskType) {
        if(CollectionUtils.isEmpty(list)){
            return 0;
        }
        int r = adminWorkOrderDao.resetWorkOrder(list, operatorId, orderStatus, taskType);
        this.syncWorkOrderInfoAdminWorkOrder(r, list, null, orderStatus, null, operatorId);
        return r;
    }

    /**
     * 修改level（优先级）、orderStatus（工单状态）、operatorId（处理人id）需要同步给鲸息系统一份数据
     */
    @Override
    public int recoverStatusAndOperator(List<Long> list, int orderStatus, int operatorId, int orderType, int orderTask) {
        if(CollectionUtils.isEmpty(list)){
            return 0;
        }
        int r = adminWorkOrderDao.recoverStatusAndOperator(list, orderStatus, operatorId, orderType, orderTask);
        this.syncWorkOrderInfoAdminWorkOrder(r, list, null, AdminWorkOrderConst.Status.CREATED.getCode(), null, null);
        return r;
    }

    /**
     * 修改level（优先级）、orderStatus（工单状态）、operatorId（处理人id）需要同步给鲸息系统一份数据
     */
    @Override
    public boolean updateLevel(long workId, int newLevel) {
        if (workId <= 0) {
            return false;
        }
        int r = adminWorkOrderDao.updateLevel(workId, newLevel);
        this.syncWorkOrderInfoAdminWorkOrder(r, Lists.newArrayList(), workId, null, newLevel, null);
        return r == 1;
    }

    /**
     * 仅鲸息系统调用……
     * @param id
     * @param orderStatus
     * @param operatorId
     * @param level
     * @return
     */
    @Override
    public int updateMsg(long id, Integer orderStatus, Integer operatorId, Integer level) {
        if (id <= 0) {
            return 0;
        }
        return adminWorkOrderDao.updateMsg(id, orderStatus, operatorId, level);
    }

    @Override
    public AdminWorkOrder getByOperatorIdAndStatus(Date createTime, long operatorId, int orderStatus) {
        if (Objects.isNull(createTime) || operatorId <= 0L) {
            return null;
        }
        return adminWorkOrderDao.getByOperatorIdAndStatus(createTime, operatorId, orderStatus);
    }

    @Override
    public List<AdminWorkOrder> selectUnHandleTask(Date createTime) {
        if (Objects.isNull(createTime)) {
            return Lists.newArrayList();
        }
        return adminWorkOrderDao.selectUnHandleTask(createTime);
    }

    /**
     * 修改level（优先级）、orderStatus（工单状态）、operatorId（处理人id）需要同步给鲸息系统一份数据
     */
    @Override
    public int updateWithOperatorIds(List<Long> list, int operatorId, int orderStatus) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        int r = adminWorkOrderDao.updateWithOperatorIds(list, operatorId, orderStatus);
        this.syncWorkOrderInfoAdminWorkOrder(r, list, null, orderStatus, null, operatorId);
        return r;
    }

    /**
     * 修改level（优先级）、orderStatus（工单状态）、operatorId（处理人id）需要同步给鲸息系统一份数据
     */
    @Override
    public int updateOperator(long workOrderId, int orignalUserId, int targetUserId) {
        if(workOrderId <= 0 || orignalUserId <= 0 || targetUserId <= 0){
            return 0;
        }
        int r = adminWorkOrderDao.updateOperator(workOrderId, orignalUserId, targetUserId);
        this.syncWorkOrderInfoAdminWorkOrder(r, Lists.newArrayList(), workOrderId, null, null, targetUserId);
        return r;
    }


    @Override
    public int selectUnHandleCount(AdminWorkOrderConst.Type orderType, List<AdminWorkOrderConst.Task> orderTasks) {
        List<Integer> orderTaskCodes = null;
        if (CollectionUtils.isNotEmpty(orderTasks)) {
            orderTaskCodes = orderTasks.stream().map(orderTask -> orderTask.getCode()).collect(Collectors
                    .toList());
        }
        return adminWorkOrderDao.selectUnHandleCount(orderType.getCode(), orderTaskCodes);
    }

    /**
     * 修改level（优先级）、orderStatus（工单状态）、operatorId（处理人id）需要同步给鲸息系统一份数据
     */
    @Override
    public int updateOperatorId(Integer operatorId, long id) {
        if (operatorId == null) {
            return 0;
        }
        AdminWorkOrder adminWorkOrder = this.selectById(id);
        if (adminWorkOrder == null) {
            return 0;
        }
        int r = adminWorkOrderDao.updateRoleOrOperatorId(operatorId, id);
        this.syncWorkOrderInfoAdminWorkOrder(r, Lists.newArrayList(), id, null, null, operatorId);
        return r;
    }

    /**
     * 修改level（优先级）、orderStatus（工单状态）、operatorId（处理人id）需要同步给鲸息系统一份数据
     */
    @Override
    public int updateRoleOrOperatorIdWithIdList(Integer operatorId, List<Long> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        int r = adminWorkOrderDao.updateRoleOrOperatorIdWithIdList(operatorId, list);
        this.syncWorkOrderInfoAdminWorkOrder(r, list, null, null, null, operatorId);
        return r;
    }

    @Override
    public List<AdminWorkOrder> selectByIds(List<Long> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return adminWorkOrderDao.selectByIds(list);
    }

    @Override
    public AdminWorkOrder getWorkOrderByCaseId(int id, int type) {
        return adminWorkOrderDao.getWorkOrderByCaseId(id, type);
    }

    /**
     * 修改level（优先级）、orderStatus（工单状态）、operatorId（处理人id）需要同步给鲸息系统一份数据
     */
    @Override
    public int updateOrderStatus(int orderStatus, int handleResult, int id) {
        int r = adminWorkOrderDao.updateOrderStatus(orderStatus, handleResult, id);
        this.syncWorkOrderInfoAdminWorkOrder(r, Lists.newArrayList(), (long) id, orderStatus, null, null);
        return r;
    }

    /**
     * 修改level（优先级）、orderStatus（工单状态）、operatorId（处理人id）需要同步给鲸息系统一份数据
     */
    @Override
    public int update(AdminWorkOrder adminWorkOrder) {
        //首先保存一条记录再进行真正的更新
        //adminWorkOrderRecordBiz.insertOne(adminWorkOrder);
        int r = adminWorkOrderDao.update(adminWorkOrder);
        this.syncWorkOrderInfoAdminWorkOrder(r, Lists.newArrayList(), adminWorkOrder.getId(), adminWorkOrder.getOrderStatus(), adminWorkOrder.getLevel(), adminWorkOrder.getOperatorId());
        return r;
    }

    @Diff(diffMethod = "getFirstUGCFromEs", diffCompare = "com.shuidihuzhu.cf.diff.GetFirstUGCCompare")
    @Override
    public List<WorkOrderFirstApprove> getFirstUGC(int current, int pageSize, long caseId, int channel, int status, int right, int userId,
                                                   int currOperatorId,
                                                   Date operatorStartDate,
                                                   Date operatorEndDate,
                                                   long raiserId) {
        PageHelper.startPage(current, pageSize);
        return adminWorkOrderDao.getFirstUGC(caseId, channel, status, right, userId, currOperatorId, operatorStartDate, operatorEndDate, raiserId);
    }

    @Override
    public Pair<Long, List<WorkOrderFirstApprove>> getFirstUGCFromEs(int current, int pageSize, long caseId, int channel, int status, int right, int userId, int currOperatorId, Date operatorStartDate, Date operatorEndDate, long raiserId) {

        CfWorkOrderIndexSearchParam searchParam = new CfWorkOrderIndexSearchParam();
        searchParam.setAwoOrderTypes(Lists.newArrayList(AdminWorkOrderConst.Type.UGC.getCode()));
        searchParam.setAwoOrderTasks(Lists.newArrayList(AdminWorkOrderConst.Task.FIRST_APPROVE.getCode()));
        searchParam.setFrom((current - 1) * pageSize);
        searchParam.setSize(pageSize);

        if (right != 1) {
            searchParam.setAwoOperatorIds(Lists.newArrayList((long)userId));
        } else if (currOperatorId != 0) {
            searchParam.setAwoOperatorIds(Lists.newArrayList((long)currOperatorId));
        }

        if (caseId > 0) {
            searchParam.setCfCaseIds(Lists.newArrayList(caseId));
        }

        if (channel == 1) {
            searchParam.setCfChannels(Lists.newArrayList("cf_volunteer"));
        }

        if (status != -1) {
            searchParam.setAtuResults(Lists.newArrayList(status));
        }

        if (raiserId != 0) {
            searchParam.setCfUserIds(Lists.newArrayList(raiserId));
        }

        if (operatorStartDate != null) {
            searchParam.setAwoUpdateTimeStart(operatorStartDate.getTime());
        }

        if (operatorEndDate != null) {
            searchParam.setAwoUpdateTimeEnd(operatorEndDate.getTime());
        }

        Pair<Long, List<AdminWorkOrder>> pair = cfSearch.cfWorkOrderIndexSearch(searchParam);

        if (CollectionUtils.isEmpty(pair.getRight())) {
            return Pair.of(0L, Lists.newArrayList());
        }

        return Pair.of(pair.getLeft(), getWorkOrderFirstApproves(pair.getRight()));
    }


    private List<WorkOrderFirstApprove> getWorkOrderFirstApproves(List<AdminWorkOrder> workOrders) {
        if (CollectionUtils.isEmpty(workOrders)) {
            return Collections.emptyList();
        }

        List<Long> workOrderIds = workOrders.stream().map(x -> x.getId()).collect(Collectors.toList());
        List<AdminTaskUgc> adminTaskUgcs = adminTaskUgcBiz.selectByWorkOrderIds(workOrderIds);
        Map<Long, AdminTaskUgc> adminTaskUgcMap = Maps.uniqueIndex(adminTaskUgcs, AdminTaskUgc::getWorkOrderId);

        List<Integer> caseIds = adminTaskUgcs.stream().map(x -> x.getCaseId()).collect(Collectors.toList());
        List<CrowdfundingInfo> crowdfundingInfos = adminCrowdfundingInfoBiz.selectByCaseIdList(caseIds);
        Map<Integer, CrowdfundingInfo> crowdfundingInfoMap = Maps.uniqueIndex(crowdfundingInfos, CrowdfundingInfo::getId);

        List<String> infoUuids = crowdfundingInfos.stream().map(x -> x.getInfoId()).collect(Collectors.toList());
        List<CfInfoExt> cfInfoExts = adminCfInfoExtBiz.selectByInfoUuidList(infoUuids);
        Map<String, CfInfoExt> infoExtMap = Maps.uniqueIndex(cfInfoExts, CfInfoExt::getInfoUuid);

        List<WorkOrderFirstApprove> approves = Lists.newArrayList();
        for (AdminWorkOrder workOrder : workOrders) {
            WorkOrderFirstApprove approve = AdminWorkOrderMapper.INSTANCE.toWorkOrderFirstApprove(workOrder);
            if (approve == null) {
                continue;
            }

            AdminTaskUgc adminTaskUgc = adminTaskUgcMap.get(workOrder.getId());
            if (adminTaskUgc == null) {
                continue;
            }

            approve.setCaseId(adminTaskUgc.getCaseId());
            approve.setUgcStatus(adminTaskUgc.getResult());

            CrowdfundingInfo info = crowdfundingInfoMap.get(adminTaskUgc.getCaseId());
            if (info == null) {
                continue;
            }
            approve.setChannelStr(info.getChannel());
            approve.setTargetAmount(info.getTargetAmount());

            CfInfoExt cfInfoExt = infoExtMap.get(info.getInfoId());
            if (cfInfoExt != null) {
                approve.setFirstStatus(cfInfoExt.getFirstApproveStatus());
            }

            approves.add(approve);
        }

        return approves;
    }

    @Override
    public int insertOne(AdminWorkOrder adminWorkOrder) {
        return adminWorkOrderDao.insertOne(adminWorkOrder);
    }

    @Override
    public FirstApproveConfiguration getFirstApproveConfiguration(int current, int pageSize, String date, int operatorId) {
        Timestamp start = DateUtil.getPGDetailImeStampFromString(date + " 00:00:00");
        Timestamp end = DateUtil.addDays(start, 1);
        int unhandleCount = adminWorkOrderDao.getFirstApproveCountByStatusAndTime(AdminWorkOrderConst.Status.CREATED.getCode(), start, end);
        int handlingCount = adminWorkOrderDao.getFirstApproveCountByStatusAndTime(AdminWorkOrderConst.Status.HANDLING.getCode(), start, end);
        int finishedCount = adminWorkOrderDao.getFirstApproveCountByStatusAndTime(AdminWorkOrderConst.Status.SHUTDOWN.getCode(), start, end);
        FirstApproveConfiguration firstApproveConfiguration = new FirstApproveConfiguration();
        firstApproveConfiguration.setUnhandleTaskCount(unhandleCount);
        firstApproveConfiguration.setHandlingTaskCount(handlingCount);
        firstApproveConfiguration.setFinishedTaskCount(finishedCount);
        List<FirstApproveAccountMap> firstApproveAccounts = adminWorkOrderDao.getFirstApproveAccounts(start, end, operatorId);
        List<FirstApproveAccountVo> firstApproveAccountVos = Lists.newArrayList();
        Map<Integer, List<FirstApproveAccountMap>> accountsByOperatorId = firstApproveAccounts.parallelStream().collect(Collectors.groupingBy(FirstApproveAccountMap::getOperatorId));
        accountsByOperatorId.forEach((account, firstApproves) -> {
            String userName = seaAccountClientV1.getValidUserAccountById(account).getResult().getName();
            int total = 0;
            for (FirstApproveAccountMap firstApproveAccountMap : firstApproves) {
                total += firstApproveAccountMap.getCount();
            }
            CfFirstApproveOperator operatorConfig = this.getFirstApproveOperatorById(account);
            int limit = operatorConfig.getCount();
            FirstApproveAccountVo firstApproveAccountVo = new FirstApproveAccountVo(
                    account,
                    userName,
                    total,
                    getFirstApproveCountByOperatorAndHandleType(FirstApproveStatusEnum.APPLYING.getCode(),
                            firstApproves),
                    getFirstApproveCountByOperatorAndHandleType(FirstApproveStatusEnum.APPLY_FAIL.getCode(),
                            firstApproves),
                    getFirstApproveCountByOperatorAndHandleType(FirstApproveStatusEnum.APPLY_SUCCESS.getCode(),
                            firstApproves),
                    date,
                    limit
            );
            firstApproveAccountVos.add(firstApproveAccountVo);
        });

        Page page = new Page(current, pageSize);
        int total = firstApproveAccountVos.size();
        page.setTotal(total);
        List<FirstApproveAccountVo> subPage;
        if (total <= pageSize && 1 == current) {
            subPage = firstApproveAccountVos;
        } else if (total < current * pageSize) {
            subPage = firstApproveAccountVos.subList((current - 1) * pageSize, total);
        } else {
            subPage = firstApproveAccountVos.subList((current - 1) * pageSize, current * pageSize);
        }
        firstApproveConfiguration.setFirstApproveAccounts(subPage);
        firstApproveConfiguration.setPagination(PageUtil.transform2PageMap(page));
        return firstApproveConfiguration;
    }

    private CfFirstApproveOperator getFirstApproveOperatorById(int oporatorId) {
        CfFirstApproveOperator cfFirstApproveOperator = cfFirstApproveOperatorDao.getCfFirstApproveOperatorCountById(oporatorId);
        if (null == cfFirstApproveOperator) {
            cfFirstApproveOperatorDao.insertCfFirstApproveOperator(oporatorId, 10);
            return cfFirstApproveOperatorDao.getCfFirstApproveOperatorCountById(oporatorId);
        }
        return cfFirstApproveOperator;
    }

    @Override
    public void cancelWorkOrders(List<AdminWorkOrder> adminWorkOrders, int userId) {
        if (CollectionUtils.isEmpty(adminWorkOrders)) {
            return;
        }

        List<Long> workOrderIds = adminWorkOrders.stream().map(AdminWorkOrder::getId).collect(Collectors.toList());
        //将工单更新为已创建状态
        updateWithOperatorIds(workOrderIds, 0, AdminWorkOrderConst.Status.CREATED.getCode());

        // 删除以前的领取记录
        List<AdminWorkOrderRecord> assignRecords = adminWorkOrderRecordBiz.selectByWorkIdAndOperateTypes(workOrderIds,
                Arrays.asList(AdminWorkOrderConst.AdminWorkOrderOperateTypeEnum.FIRST_APPROVE_ORDER_ASSIGN.getCode()));
        for (AdminWorkOrderRecord record : assignRecords) {
            adminWorkOrderRecordBiz.deleteFirstUgcById(record.getId());
        }

        //添加工单撤回日志
        List<AdminWorkOrderRecord> recordList = adminWorkOrders.stream().map(val -> {
            val.setOrderStatus(AdminWorkOrderConst.Status.FIRST_APPROVE_CANCEL.getCode());
            val.setOperatorId(userId);
            return new AdminWorkOrderRecord(val);
        }).collect(Collectors.toList());
        adminWorkOrderRecordBiz.insertList(recordList);
        //更新工单案例
        List<AdminWorkOrderCase> adminWorkOrderCases = this.selectByCaseIdList(workOrderIds);
        List<Long> workOrderCaseIds = adminWorkOrderCases.stream().map(AdminWorkOrderCase::getId).collect(Collectors.toList());
        this.updateStatusByIdList(AdminWorkOrderCaseConst.Status.CREATE, workOrderCaseIds);
        //添加工单案例映射表日志
        List<AdminWorkOrderCaseRecord> caseRecordList = adminWorkOrderCases.stream().map(val -> {
            val.setStatus(AdminWorkOrderConst.Status.CREATED.getCode());
            return new AdminWorkOrderCaseRecord(val, userId);
        }).collect(Collectors.toList());
        adminWorkOrderCaseRecordBiz.insertList(caseRecordList);
    }

    private List<AdminWorkOrderCase> selectByCaseIdList(List<Long> workOrderIds) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(workOrderIds)) {
            return Lists.newArrayList();
        }
        return adminWorkOrderCaseDao.selectByCaseIdList(workOrderIds);
    }
    private int updateStatusByIdList(AdminWorkOrderCaseConst.Status status, List<Long> list) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(list)) {
            return 0;
        }
        return adminWorkOrderCaseDao.updateStatusByIdList(status.getCode(), list);
    }
    @Override
    public List<AdminWorkOrder> selectApplyingFirstApprovesByOperator(int oporatorId, int count, String date) {
        Timestamp start = DateUtil.getPGDetailImeStampFromString(date + " 00:00:00");
        Timestamp end = DateUtil.addDays(start, 1);
        List<AdminWorkOrder> adminWorkOrders = adminWorkOrderDao.selectApplyingFirstApprovesByOperator(oporatorId, count, start, end);
        return adminWorkOrders;
    }

    private int getFirstApproveCountByOperatorAndHandleType(int handleType, List<FirstApproveAccountMap> oporatorFirstApproves) {
        if (CollectionUtils.isEmpty(oporatorFirstApproves)) {
            return 0;
        }
        List<FirstApproveAccountMap> finished = oporatorFirstApproves.stream().filter(firstApprove -> firstApprove.getFirstApproveStatus() == handleType).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(finished)) {
            return finished.get(0).getCount();
        }
        return 0;
    }

    /**
     * 权限高的运营 操作其他人领取的工单时 自动转换为自己的工单
     *
     * @param adminWorkOrder
     */
    @Override
    public void onHandleOrder(AdminWorkOrder adminWorkOrder) {
        int currentOperatorId = ContextUtil.getAdminUserId();
        int oldOperatorId = adminWorkOrder.getOperatorId();
        if (oldOperatorId == currentOperatorId) {
            return;
        }
        long orderId = adminWorkOrder.getId();
        log.info("orderId: {}, oldOperatorId: {}, currentOperatorId: {}", orderId, oldOperatorId, currentOperatorId);

        // 修改工单所有者
        updateWithOperatorIds(Lists.newArrayList(orderId), currentOperatorId, adminWorkOrder.getOrderStatus());

        // 增加领取记录 防止出现某人 领取2个工单 但处理了10个工单的情况
        adminWorkOrder.setOperatorId(currentOperatorId);
        AdminWorkOrderRecord adminWorkOrderRecord = new AdminWorkOrderRecord(adminWorkOrder);
        adminWorkOrderRecordBiz.insertOne(adminWorkOrderRecord);
    }

    private void syncWorkOrderInfoAdminWorkOrder(int res, List<Long> workOrderIdList, Long workOrderId, Integer orderStatus, Integer level, Integer operatorId) {
        if (res <= 0) {
            return;
        }

        if (Objects.nonNull(workOrderId)) {
            externalWorkOrderApiBiz.syncWorkOrderInfoAdminWorkOrder(workOrderId, orderStatus, level, operatorId);
        } else {
            if (CollectionUtils.isNotEmpty(workOrderIdList)) {
                for (Long id : workOrderIdList) {
                    externalWorkOrderApiBiz.syncWorkOrderInfoAdminWorkOrder(id, orderStatus, level, operatorId);
                }
            }
        }
    }

}
