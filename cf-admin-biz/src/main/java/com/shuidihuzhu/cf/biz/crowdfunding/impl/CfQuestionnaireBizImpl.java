package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.CfQuestionnaireBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.dao.crowdfunding.CfQuestionnaireDao;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.facade.AdminApolloCofig;
import com.shuidihuzhu.cf.model.QuestionnaireRecord;
import com.shuidihuzhu.cf.model.admin.CfQuestionnaire;
import com.shuidihuzhu.cf.model.admin.CfQuestionnaireVo;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.repository.CfQuestionnaireRepository;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.model.CfUserInvitedLaunchCaseRecordModel;
import com.shuidihuzhu.client.cf.clewtrack.model.ChannelRefine;
import com.shuidihuzhu.client.cf.clewtrack.param.ChannelRefineDTO;
import com.shuidihuzhu.client.cf.growthtool.client.CfChannelFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.MobileUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2019/12/12
 */
@Service
@Slf4j
public class CfQuestionnaireBizImpl implements CfQuestionnaireBiz {

    @Autowired
    private CfQuestionnaireDao questionnaireDao;

    @Autowired
    private CfQuestionnaireRepository cfQuestionnaireRepository;

    @Autowired
    private SeaAccountClientV1 clientV1;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private IRiskDelegate riskDelegate;

    @Autowired
    private CfChannelFeignClient channelFeignClient;

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Autowired(required = false)
    private Producer producer;

    @Autowired
    private AdminApproveService adminApproveService;


    @Override
    public String getQid() {
        return AdminApolloCofig.getValueFromApollo(AdminApolloCofig.questionnaire_id, "f426f5fa-022f-41e5-a1d5-61e124a1e86b");
    }

    @Override
    public String getQurl() {
        return AdminApolloCofig.getValueFromApollo(AdminApolloCofig.questionnaire_url, "https://www.shuidichou.com/qs/detail/f426f5fa-022f-41e5-a1d5-61e124a1e86b");
    }

    @Override
    public String getUserQurl() {
        return AdminApolloCofig.getValueFromApollo(AdminApolloCofig.questionnaire_user_url, "https://www.shuidichou.com/qs/detail/bf6e210a-1c15-4f56-8cd7-195eceb8ade3");
    }

    @Override
    public CfQuestionnaire getById(long id) {
        return questionnaireDao.getById(id);
    }

    @Override
    public List<CfQuestionnaire> getByIds(List<Long> ids) {
        return questionnaireDao.getByIds(ids);
    }

    @Override
    public int save(CfQuestionnaire cfQuestionnaire) {
        return questionnaireDao.save(cfQuestionnaire);
    }

    @Override
    public String getByCard(String card) {
        return questionnaireDao.getByCard(card);
    }

    @Override
    public List<CfQuestionnaire> getListByCard(String card) {
        return questionnaireDao.getListByCard(card);
    }

    @Override
    public int updateContentByUserId(long questionnaireId, String content, int status, String qname, String startAnsweringTime, String endTime) {
        return questionnaireDao.updateContentByUserId(questionnaireId, content, status, qname, startAnsweringTime, endTime);
    }

    @Override
    public List<CfQuestionnaire> getList(String qid, long userId, String qname, String name, String channel, String source, int status, String q_mobile, String startTime, String endTime, int pageSize, boolean isPre, long anchor, long recordId) {

        String encryptMobile = MobileUtil.mask(oldShuidiCipher.aesEncrypt(q_mobile));
        List<CfQuestionnaire> list = cfQuestionnaireRepository.getList(qid, userId, qname, name, channel, source, status, encryptMobile, startTime, endTime, pageSize, isPre, anchor, recordId, 0, 0);

        List<Integer> ids = list.stream().filter(r-> CfQuestionnaireBiz.chuci_source.equals(r.getSource()) || CfQuestionnaireBiz.cailiao_source.equals(r.getSource()))
            .map(CfQuestionnaire::getCaseId).collect(Collectors.toList());

        Map<Integer, CfUserInvitedLaunchCaseRecordModel> caseRecordMap = adminApproveService
                .getCaseChannelRecordMap(ids);


        list.stream().forEach(cfQuestionnaire -> {

            String mobile = cfQuestionnaire.getMobile();
            cfQuestionnaire.setMobile(MobileUtil.mask(shuidiCipher.decrypt(mobile)));

            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            if (cfQuestionnaire.getQStatus() == CfQuestionnaire.q_status_submit) {
                cfQuestionnaire.setSubmitTimeStr(simpleDateFormat.format(cfQuestionnaire.getSubmitTime()));
            }

            cfQuestionnaire.setSendTimeStr(simpleDateFormat.format(cfQuestionnaire.getSendTime()));
            if (cfQuestionnaire.getOperatorId() != 0) {
                AuthRpcResponse<AdminUserAccountModel> response = clientV1.getValidUserAccountById(cfQuestionnaire.getOperatorId());
                if (response.getResult() != null) {
                    cfQuestionnaire.setOperatorName(response.getResult().getName());
                }
                cfQuestionnaire.setUpdateTimeStr(simpleDateFormat.format(cfQuestionnaire.getUpdateTime()));
            }
            if (caseRecordMap.get(cfQuestionnaire.getCaseId()) != null){
                cfQuestionnaire.setName(caseRecordMap.get(cfQuestionnaire.getCaseId()).getServiceUserInfo(shuidiCipher));
            }
            
        });

        return list;
    }

    @Override
    public List<CfQuestionnaire> getExcelList(String qid, long userId, String qname, String name, String channel, String source, int status, String mobile, String startTime, String endTime, long recordId) {

        List<CfQuestionnaire> list = Lists.newArrayList();
        Map<Integer, CfUserInvitedLaunchCaseRecordModel> caseRecordMap = Maps.newHashMap();
        String encryptMobile = MobileUtil.mask(oldShuidiCipher.aesEncrypt(mobile));
        int size = 500;
        Long cfQuestionnaireId = Long.MAX_VALUE;
        do {
            try {
                CfQuestionnaireVo cfQuestionnaires = this.getMoietyExcelList(qid, userId, qname, name, channel, source, status, encryptMobile, startTime, endTime, recordId, cfQuestionnaireId, size);
                if (null == cfQuestionnaires) {
                    break;
                }
                cfQuestionnaireId = cfQuestionnaires.getCfQid();
                list.addAll(cfQuestionnaires.getCfQuestionnaireList());
                List<Integer> ids = cfQuestionnaires.getCfQuestionnaireList().stream().filter(r-> CfQuestionnaireBiz.chuci_source.equals(r.getSource()) || CfQuestionnaireBiz.cailiao_source.equals(r.getSource()))
                        .map(CfQuestionnaire::getCaseId).collect(Collectors.toList());
                 caseRecordMap.putAll(adminApproveService.getCaseChannelRecordMap(ids));
            } catch (Exception e) {
                log.error("getExcelList", e);
            }

        } while (true);

        if (CollectionUtils.isNotEmpty(list)) {
            list.stream().forEach(cfQuestionnaire -> {

                String q_mobile = cfQuestionnaire.getMobile();
                cfQuestionnaire.setMobile(MobileUtil.mask(shuidiCipher.decrypt(q_mobile)));

                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                if (cfQuestionnaire.getQStatus() == CfQuestionnaire.q_status_submit) {
                    cfQuestionnaire.setSubmitTimeStr(simpleDateFormat.format(cfQuestionnaire.getSubmitTime()));
                }

                cfQuestionnaire.setSendTimeStr(simpleDateFormat.format(cfQuestionnaire.getSendTime()));
                if (cfQuestionnaire.getOperatorId() != 0) {
                    AuthRpcResponse<AdminUserAccountModel> response = clientV1.getValidUserAccountById(cfQuestionnaire.getOperatorId());
                    cfQuestionnaire.setOperatorName(Optional.ofNullable(response.getResult()).map(AdminUserAccountModel::getName).orElse(""));
                    cfQuestionnaire.setUpdateTimeStr(simpleDateFormat.format(cfQuestionnaire.getUpdateTime()));
                }

                if (caseRecordMap.get(cfQuestionnaire.getCaseId()) != null){
                    cfQuestionnaire.setName(caseRecordMap.get(cfQuestionnaire.getCaseId()).getServiceUserInfo(shuidiCipher));
                }

            });
        }

        return list;
    }

    @Override
    public int saveCaseId(long recordId, int caseId) {
        return questionnaireDao.saveCaseId(recordId, caseId);
    }

    @Override
    public int saveOperatorIdAndComment(long recordId, int operatorId, String comment) {
        return questionnaireDao.saveOperatorIdAndComment(recordId, operatorId, comment);
    }

    @Override
    public int total(String qid, long userId, String qname, String name, String channel, String source, int status, String q_mobile, String startTime, String endTime, long recordId) {
        return cfQuestionnaireRepository.total(qid, userId, qname, name, channel, source, status, q_mobile, startTime, endTime, recordId);
    }


    @Override
    public QuestionnaireRecord canSend(int caseId, String source) {

        QuestionnaireRecord qr = new QuestionnaireRecord();

        CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getCaseInfoById(caseId);

        if (crowdfundingInfo == null){
            return qr;
        }
        ChannelRefineDTO refineDTO = new ChannelRefineDTO();
        refineDTO.setInfoId((long)caseId);
        refineDTO.setChannel(crowdfundingInfo.getChannel());
        refineDTO.setUserId(crowdfundingInfo.getUserId());

        Response<String> response = channelFeignClient.getChannelByInfoIdWithUserIdAndOldChannel(refineDTO);
        log.info("channel refineDTO={} response={}",refineDTO,response);
        String channel = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse("");

        ChannelRefine.ChannelRefineResuleEnum channelRefineResuleEnum = ChannelRefine.ChannelRefineResuleEnum.parse(channel);

        /**
         * v1:
         * 线下bd和微信1v1 才发问卷
         * v2:
         * 1）线上筹款顾问引导发起的案例：发送预审服务问卷
         * 2）线下筹款顾问引导发起的案例：发送预审服务问卷+材审服务问卷
         */
        if (ChannelRefine.ChannelRefineResuleEnum.XIANXIA_BD.equals(channelRefineResuleEnum) ||
                (ChannelRefine.ChannelRefineResuleEnum.WEIXIN_1V1.equals(channelRefineResuleEnum)
                && chuci_source.equals(source))){

            CfFirsApproveMaterial material = riskDelegate.getCfFirsApproveMaterialByInfoId(caseId);

            String card = material.getPatientCryptoIdcard();

            if (StringUtils.isEmpty(card)){
                card = material.getPatientBornCard();
            }

            List<CfQuestionnaire> list = getListByCard(card);
            log.info("getListByCard card={} size={}",card,list.size());
            if (CollectionUtils.isNotEmpty(list)){
                Optional<CfQuestionnaire> optional = list.stream().filter(r->r.getSource().equals(source)).findAny();
                //存在代表发送过   不再发送了
                if (optional.isPresent()){
                    log.info("canSend no CfQuestionnaire={}",optional.get().getId());
                    return qr;
                }
            }
            //保存信息
            UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(crowdfundingInfo.getUserId());
            String mobile = Optional.ofNullable(userInfoModel).map(UserInfoModel::getCryptoMobile).orElse("-");

            qr.setFlag(true);
            qr.setCard(card);
            qr.setSource(source);
            qr.setChannel(channelRefineResuleEnum.getChannelDesc());
            qr.setMobile(mobile);
            qr.setUserId(crowdfundingInfo.getUserId());
            qr.setList(list);
        }
        return qr;
    }

    @Override
    public long save(long userId, int caseId, String channel, String card, String mobile,String source, Date sendTime) {

        CfQuestionnaire q = new CfQuestionnaire();

        q.setRecordId(0);
        q.setSource(source);
        q.setChannel(channel);
        q.setSendTime(sendTime);
        q.setCaseId(caseId);
        q.setOrg("");
        q.setName("");
        q.setMobile(mobile);
        q.setCard(card);
        q.setUserId(userId);
        q.setQid("");

        save(q);

        return q.getId();
    }

    @Override
    public void sendDelayMQForMsg(QuestionnaireRecord qr, long delaySecs) {
        Message message = Message.ofDelay(MQTopicCons.CF,
                MQTagCons.record_1h_check, MQTagCons.record_1h_check + "_" + qr.getId(), qr, delaySecs);

        producer.send(message);
    }

    public CfQuestionnaireVo getMoietyExcelList(String qid, long userId, String qname, String name, String channel, String source, int status, String encryptMobile, String startTime, String endTime, long recordId, Long cfQuestionnaireId, int size) {

        if (null == cfQuestionnaireId || cfQuestionnaireId <= 0) {
            return null;
        }

        List<CfQuestionnaire> cfQuestionnaireList = cfQuestionnaireRepository.getList(qid, userId, qname, name, channel, source, status, encryptMobile, startTime, endTime, 0, true, 0, recordId, cfQuestionnaireId, size);

        if (CollectionUtils.isEmpty(cfQuestionnaireList)) {
            return null;
        }

        CfQuestionnaireVo cfQuestionnaireVo = new CfQuestionnaireVo();
        cfQuestionnaireVo.getCfQuestionnaireList().addAll(cfQuestionnaireList);

        Optional<CfQuestionnaire> optional = cfQuestionnaireList.stream().min(Comparator.comparing(CfQuestionnaire::getId));
        cfQuestionnaireVo.setCfQid(optional.orElse(new CfQuestionnaire()).getId() - 1);
        return cfQuestionnaireVo;
    }

}
