package com.shuidihuzhu.cf.biz.workOrder.impl;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.workOrder.ExternalWorkOrderApiBiz;
import com.shuidihuzhu.cs.work.order.client.dto.*;
import com.shuidihuzhu.cs.work.order.client.external.workorder.ExternalWorkOrderApiClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/7/13  2:05 下午
 */
@Service
@Slf4j
public class ExternalWorkOrderApiBizImpl implements ExternalWorkOrderApiBiz {

    @Autowired
    private ExternalWorkOrderApiClient externalWorkOrderApiClient;

    @Autowired
    private SeaAccountClientV1 accountClientV1;

    private static final String SYSTEM_CODE = "cf";

    @Override
    public Boolean syncWorkOrderInfoAdminWorkOrder(Long workOrderId, Integer orderStatus, Integer level, Integer operatorId) {
        if (Objects.isNull(workOrderId) || workOrderId <= 0) {
            return null;
        }
        try {
            SyncWorkOrderInfoRequest syncWorkOrderInfoRequest = new SyncWorkOrderInfoRequest();
            syncWorkOrderInfoRequest.setSystemCode(SYSTEM_CODE);
            syncWorkOrderInfoRequest.setWorkOrderNumber(String.valueOf(workOrderId));
            syncWorkOrderInfoRequest.setStatus(Objects.isNull(orderStatus) ? null : String.valueOf(orderStatus));
            syncWorkOrderInfoRequest.setPriority(Objects.isNull(level) ? null : String.valueOf(level));
            if (Objects.nonNull(operatorId)) {
                AuthRpcResponse<AdminUserAccountModel> model = accountClientV1.getValidUserAccountById(operatorId);
                String mis = model != null && model.getResult() != null ? model.getResult().getMis() : "";
                syncWorkOrderInfoRequest.setOrgUserMis(mis);
            }
            ResponseDto<Boolean> responseDto = externalWorkOrderApiClient.syncWorkOrderInfo(syncWorkOrderInfoRequest);
            return Optional.ofNullable(responseDto).filter(ResponseDto::isSuccess).map(ResponseDto::getData).orElse(null);
        } catch (Exception e) {
            log.error("调用鲸息系统失败 ", e);
        }
        return null;
    }

    @Override
    public Boolean syncWorkOrderInfoAdminWorkOrderFlow(Long workOrderId, Integer problemType) {
        if (Objects.isNull(workOrderId) || workOrderId <= 0) {
            return null;
        }
        try {
            SyncWorkOrderInfoRequest syncWorkOrderInfoRequest = new SyncWorkOrderInfoRequest();
            syncWorkOrderInfoRequest.setSystemCode(SYSTEM_CODE);
            syncWorkOrderInfoRequest.setWorkOrderNumber(String.valueOf(workOrderId));
            syncWorkOrderInfoRequest.setOrgCode(Objects.isNull(problemType) ? null : String.valueOf(problemType));
            ResponseDto<Boolean> responseDto = externalWorkOrderApiClient.syncWorkOrderInfo(syncWorkOrderInfoRequest);
            return Optional.ofNullable(responseDto).filter(ResponseDto::isSuccess).map(ResponseDto::getData).orElse(null);
        } catch (Exception e) {
            log.error("调用鲸息系统失败 ", e);
        }
        return null;
    }

    @Override
    public SystemFieldsMappingRule mappingRuleFields(String fieldCode) {
        try {
            ResponseDto<SystemFieldsMappingRule> responseDto = externalWorkOrderApiClient.mappingRuleFields(SYSTEM_CODE, fieldCode);
            return Optional.ofNullable(responseDto).filter(ResponseDto::isSuccess).map(ResponseDto::getData).orElse(null);
        } catch (Exception e) {
            log.error("调用鲸息系统失败 ", e);
        }
        return null;
    }

    @Override
    public CascadeRuleFields fieldsCascade(long parentId) {
        try {
            ResponseDto<CascadeRuleFields> responseDto = externalWorkOrderApiClient.fieldsCascade(SYSTEM_CODE, "fieldsCascade", parentId);
            return Optional.ofNullable(responseDto).filter(ResponseDto::isSuccess).map(ResponseDto::getData).orElse(null);
        } catch (Exception e) {
            log.error("调用鲸息系统失败 ", e);
        }
        return null;
    }

    @Override
    public CascadeRuleFields queryAllFeldsCascade() {
        try {
            ResponseDto<CascadeRuleFields> responseDto = externalWorkOrderApiClient.queryAllfieldsCascade(SYSTEM_CODE, "fieldsCascade");
            return Optional.ofNullable(responseDto).filter(ResponseDto::isSuccess).map(ResponseDto::getData).orElse(null);
        } catch (Exception e) {
            log.error("调用鲸息系统失败 ", e);
        }
        return null;
    }

    @Override
    public Map<String, String> getAccessImages(List<String> images) {
        try {
            QueryAccessImagesRequest queryAccessImagesRequest = new QueryAccessImagesRequest();
            queryAccessImagesRequest.setSystemCode(SYSTEM_CODE);
            queryAccessImagesRequest.setImages(images);
            ResponseDto<Map<String, String>> responseDto = externalWorkOrderApiClient.getAccessImages(queryAccessImagesRequest);
            return Optional.ofNullable(responseDto).filter(ResponseDto::isSuccess).map(ResponseDto::getData).orElse(Maps.newHashMap());
        } catch (Exception e) {
            log.error("调用鲸息系统失败 ", e);
        }
        return null;
    }
}
