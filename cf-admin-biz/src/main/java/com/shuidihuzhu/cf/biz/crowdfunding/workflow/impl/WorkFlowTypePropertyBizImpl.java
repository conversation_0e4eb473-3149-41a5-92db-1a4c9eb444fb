package com.shuidihuzhu.cf.biz.crowdfunding.workflow.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.workflow.WorkFlowTypePropertyBiz;
import com.shuidihuzhu.cf.dao.admin.workorder.flowWorkOrder.WorkFlowTypePropertyDAO;
import com.shuidihuzhu.cf.model.crowdfunding.vo.workflow.WorkFlowTypePropertyVo;
import com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowTypeProperty;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WorkFlowTypePropertyBizImpl implements WorkFlowTypePropertyBiz {
    @Autowired
    private WorkFlowTypePropertyDAO flowTypePropertyDAO;
    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @Override
    public void addOrgNoHandleLimit(int lowestId, int limit) {

        log.info("添加未处理的工单数量的限制.orgId:{} limit:{}", lowestId, limit);

        flowTypePropertyDAO.deletePropertyList(WorkFlowTypeProperty.FlowTypeEnum.LOWEST_ORDER_ID.getCode(),
                lowestId, WorkFlowTypeProperty.PropertyTypeEnum.MAX_NO_HANDLE_COUNT.getCode());

        WorkFlowTypeProperty typeProperty = new WorkFlowTypeProperty();
        typeProperty.setFlowType(WorkFlowTypeProperty.FlowTypeEnum.LOWEST_ORDER_ID.getCode());
        typeProperty.setTypeId(lowestId);
        typeProperty.setPropertyType(WorkFlowTypeProperty.PropertyTypeEnum.MAX_NO_HANDLE_COUNT.getCode());
        typeProperty.setPropertyValue(String.valueOf(limit));

        flowTypePropertyDAO.insertPropertys(Lists.newArrayList(typeProperty));

        log.info("添加组织下用户最大未处理工单的限制.orgId:{} limit:{}", lowestId, limit);
    }

    @Override
    @Async("flowOrderAutoAssignExecutor")
    public void addAutoAssignRecordList(Map<Integer, Long> assignList) {
        if (MapUtils.isEmpty(assignList)) {
            return;
        }

        for (Map.Entry<Integer, Long> entry : assignList.entrySet()) {
            addAutoAssignRecord(entry.getValue(), entry.getKey());
        }
    }

    @Override
    @Async("flowOrderAutoAssignExecutor")
    public void addAutoAssignRecord(long flowId, int userId) {

        log.info("信息传递工单自动分配到用户flowId:{} userId:{}", flowId, userId);

        // 先判断下 是不是自动分配的工单， 只有自动分配的工单，才记录
        if (CollectionUtils.isNotEmpty(flowTypePropertyDAO.selectPropertyList(WorkFlowTypeProperty.FlowTypeEnum.WORK_FLOW_ID.getCode(),
                flowId, WorkFlowTypeProperty.PropertyTypeEnum.AUTO_ASSIGN_TIME.getCode()))) {
            log.info("当前工单已经自动分配过，不记录log. flowId:{} userId:{}", flowId, userId);
            return;
        }

        WorkFlowTypeProperty timeProperty = new WorkFlowTypeProperty();
        timeProperty.setFlowType(WorkFlowTypeProperty.FlowTypeEnum.WORK_FLOW_ID.getCode());
        timeProperty.setTypeId(flowId);
        timeProperty.setPropertyType(WorkFlowTypeProperty.PropertyTypeEnum.AUTO_ASSIGN_TIME.getCode());
        timeProperty.setPropertyValue(String.valueOf(System.currentTimeMillis()));

        WorkFlowTypeProperty userProperty = new WorkFlowTypeProperty();
        userProperty.setFlowType(WorkFlowTypeProperty.FlowTypeEnum.WORK_FLOW_ID.getCode());
        userProperty.setTypeId(flowId);
        userProperty.setPropertyType(WorkFlowTypeProperty.PropertyTypeEnum.AUTO_ASSIGN_USER.getCode());
        userProperty.setPropertyValue(String.valueOf(userId));

        Integer result = flowTypePropertyDAO.insertPropertys(Lists.newArrayList(userProperty, timeProperty));

        log.info("信息传递工单自动分配到用户flowId:{} userId:{} result:{}", flowId, userId, result);
    }

    // 用户点击 处理中
    @Override
    @Async("flowOrderAutoAssignExecutor")
    public void addFlowBeginHandleRecord(long flowId, int userId) {
        log.info("信息传递工单用户点击处理中flowId:{} userId:{}", flowId, userId);

        // 先判断下 是不是自动分配的工单， 只有自动分配的工单，才记录
        if (CollectionUtils.isEmpty(flowTypePropertyDAO.selectPropertyList(WorkFlowTypeProperty.FlowTypeEnum.WORK_FLOW_ID.getCode(),
                flowId, WorkFlowTypeProperty.PropertyTypeEnum.AUTO_ASSIGN_TIME.getCode()))) {
            log.info("当前工单不是自动分配的，开始处理时，不记录log. flowId:{} userId:{}", flowId, userId);
            return;
        }

        WorkFlowTypeProperty timeProperty = new WorkFlowTypeProperty();
        timeProperty.setFlowType(WorkFlowTypeProperty.FlowTypeEnum.WORK_FLOW_ID.getCode());
        timeProperty.setTypeId(flowId);
        timeProperty.setPropertyType(WorkFlowTypeProperty.PropertyTypeEnum.AUTO_FIRST_HANDLE_TIME.getCode());
        timeProperty.setPropertyValue(String.valueOf(System.currentTimeMillis()));

        WorkFlowTypeProperty userProperty = new WorkFlowTypeProperty();
        userProperty.setFlowType(WorkFlowTypeProperty.FlowTypeEnum.WORK_FLOW_ID.getCode());
        userProperty.setTypeId(flowId);
        userProperty.setPropertyType(WorkFlowTypeProperty.PropertyTypeEnum.AUTO_FIRST_HANDLE_USER.getCode());
        userProperty.setPropertyValue(String.valueOf(userId));

        Integer result = flowTypePropertyDAO.insertPropertys(Lists.newArrayList(timeProperty, userProperty));

        log.info("信息传递工单用户点击处理中flowId:{} userId:{} result:{}", flowId, userId, result);
    }

    // 用户点击 '处理'
    @Override
    @Async("flowOrderAutoAssignExecutor")
    public void addFlowHandleRecord(long flowId, int userId) {
        log.info("信息传递工单用户点击 处理 flowId:{} userId:{}", flowId, userId);
        WorkFlowTypeProperty timeProperty = new WorkFlowTypeProperty();
        timeProperty.setFlowType(WorkFlowTypeProperty.FlowTypeEnum.WORK_FLOW_ID.getCode());
        timeProperty.setTypeId(flowId);
        timeProperty.setPropertyType(WorkFlowTypeProperty.PropertyTypeEnum.AUTO_CLICK_HANDLE_TIME.getCode());
        timeProperty.setPropertyValue(String.valueOf(System.currentTimeMillis()));

        WorkFlowTypeProperty userProperty = new WorkFlowTypeProperty();
        userProperty.setFlowType(WorkFlowTypeProperty.FlowTypeEnum.WORK_FLOW_ID.getCode());
        userProperty.setTypeId(flowId);
        userProperty.setPropertyType(WorkFlowTypeProperty.PropertyTypeEnum.AUTO_CLICK_HANDLE_USER_ID.getCode());
        userProperty.setPropertyValue(String.valueOf(userId));

        Integer result = flowTypePropertyDAO.insertPropertys(Lists.newArrayList(timeProperty, userProperty));

        log.info("信息传递工单用户点击 处理 flowId:{} userId:{} result:{}", flowId, userId, result);
    }

    @Override
    public Map<Long, WorkFlowTypePropertyVo> getFlowAutoAssignData(List<Integer> flowIds) {
        Map<Long, WorkFlowTypePropertyVo> flowDateMappings = Maps.newHashMap();
        if (CollectionUtils.isEmpty(flowIds)) {
            return flowDateMappings;
        }

        List<WorkFlowTypeProperty> typePropertyList = flowTypePropertyDAO.selectPropertyListsV2(
                WorkFlowTypeProperty.FlowTypeEnum.WORK_FLOW_ID.getCode(),
                flowIds.stream().map(Integer::longValue).collect(Collectors.toList()),
                Lists.newArrayList(WorkFlowTypeProperty.PropertyTypeEnum.AUTO_ASSIGN_TIME.getCode(),
                        WorkFlowTypeProperty.PropertyTypeEnum.AUTO_ASSIGN_USER.getCode(),
                        WorkFlowTypeProperty.PropertyTypeEnum.AUTO_CLICK_HANDLE_TIME.getCode()));
        if (CollectionUtils.isEmpty(typePropertyList)) {
            return flowDateMappings;
        }

        List<Integer> userIdList = typePropertyList.stream()
                .filter(item -> item.getPropertyType() == WorkFlowTypeProperty.PropertyTypeEnum.AUTO_ASSIGN_USER.getCode())
                .map(WorkFlowTypeProperty::getPropertyValue)
                .distinct()
                .map(Integer::parseInt)
                .collect(Collectors.toList());
        List<AdminUserAccountModel> userAccountModelList = seaAccountClientV1.getUserAccountsByIds(userIdList).getResult();
        if (null == userAccountModelList) {
            userAccountModelList = Lists.newArrayList();
        }
        Map<Integer, AdminUserAccountModel> userAccountModelMap = userAccountModelList.stream()
                .collect(Collectors.toMap(AdminUserAccountModel::getId, Function.identity(), (a, b) -> b));

        Map<Long, List<WorkFlowTypeProperty>> typePropertyListMap = typePropertyList.stream()
                .collect(Collectors.groupingBy(WorkFlowTypeProperty::getTypeId));
        for (Map.Entry<Long, List<WorkFlowTypeProperty>> entry : typePropertyListMap.entrySet()) {
            List<WorkFlowTypeProperty> typePropertyItem = entry.getValue();
            Long typeId = entry.getKey();
            // 首次系统分配处理人
            String firstSystemAssignsUser = StringUtils.EMPTY;
            WorkFlowTypeProperty itemWork = typePropertyItem.stream()
                    .filter(item -> item.getPropertyType() == WorkFlowTypeProperty.PropertyTypeEnum.AUTO_ASSIGN_USER.getCode())
                    .sorted(Comparator.comparing(WorkFlowTypeProperty::getCreateTime))
                    .findFirst().orElse(null);
            if (itemWork != null) {
                AdminUserAccountModel adminUserAccountModel = userAccountModelMap.get(Integer.valueOf(itemWork.getPropertyValue()));
                if (adminUserAccountModel != null) {
                    firstSystemAssignsUser = adminUserAccountModel.getName();
                }
            }

            //首次处理时间
            String firstHandleTime = StringUtils.EMPTY;
            itemWork = typePropertyItem.stream()
                    .filter(item -> item.getPropertyType() == WorkFlowTypeProperty.PropertyTypeEnum.AUTO_CLICK_HANDLE_TIME.getCode())
                    .sorted(Comparator.comparing(WorkFlowTypeProperty::getCreateTime))
                    .findFirst().orElse(null);
            if (itemWork != null) {
                firstHandleTime = DateUtil.getYmdhmsFromTimestamp(Long.valueOf(itemWork.getPropertyValue()));
            }

            // 系统分配时间
            String systemAllocationTime = StringUtils.EMPTY;
            for (WorkFlowTypeProperty property : typePropertyItem.stream()
                    .filter(item -> item.getPropertyType() == WorkFlowTypeProperty.PropertyTypeEnum.AUTO_ASSIGN_TIME.getCode())
                    .collect(Collectors.toList())) {
                systemAllocationTime = DateUtil.getYmdhmsFromTimestamp(Long.valueOf(property.getPropertyValue()));
            }

            WorkFlowTypePropertyVo workFlowTypePropertyVo = new WorkFlowTypePropertyVo(typeId, systemAllocationTime,
                    firstSystemAssignsUser, firstHandleTime);
            flowDateMappings.put(typeId, workFlowTypePropertyVo);
        }
        return flowDateMappings;
    }

    @Override
    public List<WorkFlowTypeProperty> selectPropertyList(int flowType, int typeId, int propertyType) {
        return flowTypePropertyDAO.selectPropertyList(flowType, typeId, propertyType);
    }

    @Override
    public Map<Integer, Integer> getNoHandleOrgMapping(List<Long> orgIds) {

        Map<Integer, Integer> orgIdDelayCountMapping = Maps.newHashMap();
        if (CollectionUtils.isEmpty(orgIds)) {
            return orgIdDelayCountMapping;
        }

        List<WorkFlowTypeProperty> propertyList = flowTypePropertyDAO.selectPropertyLists(WorkFlowTypeProperty.FlowTypeEnum
                .LOWEST_ORDER_ID.getCode(), orgIds, WorkFlowTypeProperty.PropertyTypeEnum.MAX_NO_HANDLE_COUNT.getCode());

        for (WorkFlowTypeProperty property : propertyList) {
            orgIdDelayCountMapping.put(Long.valueOf(property.getTypeId()).intValue(), Integer.valueOf(property.getPropertyValue()));
        }

        return orgIdDelayCountMapping;
    }


}
