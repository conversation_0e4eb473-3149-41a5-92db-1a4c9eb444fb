package com.shuidihuzhu.cf.biz.risk.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.risk.IUserBehaviorService;
import com.shuidihuzhu.cf.client.feign.CfCommonFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.dao.admin.AdminWorkOrderDao;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminWorkOrderReportDao;
import com.shuidihuzhu.cf.enums.crowdfunding.UserBehaviorEnum;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportView;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @time 2019/10/12 上午11:54
 * @desc 举报行为
 */
@Service
public class UserBehaviorReportServiceImpl implements IUserBehaviorService {
    @Autowired
    private CfCommonFeignClient cfCommonFeignClient;

    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;

    @Autowired
    private AdminWorkOrderReportDao adminWorkOrderReportDao;

    @Autowired
    private AdminWorkOrderDao adminWorkOrderDao;

    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @Override
    public UserBehaviorEnum getBehavior() {
        return UserBehaviorEnum.REPORT;
    }

    @Override
    public List<AdminUserBehaviorDetail> buildBehaviorDetail(String mobile, long userId, String name, String identity, UserInfoModel userInfo, UserBehaviorEnum behaviorEnum) {
        List<CrowdfundingReport> reports = cfCommonFeignClient.queryReportByUser(userId).getData();
        if(CollectionUtils.isEmpty(reports)){
            return Lists.newArrayList();
        }

        UserInfoDetail userInfoDetail = new UserInfoDetail();
        userInfoDetail.setUserId(userId);
        userInfoDetail.setNickName(Objects.nonNull(userInfo) ? userInfo.getNickname() : "");


        List<AdminUserBehaviorDetail> reportDetails = Lists.newArrayList();

        for (CrowdfundingReport report : reports){
            int caseId = report.getActivityId();
            CrowdfundingInfo caseInfo = crowdfundingFeignClient.getCaseInfoById(caseId).getData();

            StringBuilder sb = new StringBuilder();
            sb.append("案例id:").append(caseId).append(REDEX);
            sb.append("案例标题:").append(Objects.nonNull(caseInfo) ? caseInfo.getTitle() : "").append(REDEX);
            sb.append("举报内容:").append(report.getContent());

            AdminUserBehaviorDetail reportDetail = new AdminUserBehaviorDetail();
            reportDetail.setTime(report.getCreateTime());
            reportDetail.setBehaviorType(UserBehaviorEnum.REPORT.getKey());
            reportDetail.setUserInfoDetail(userInfoDetail);
            reportDetail.setUrl(Lists.newArrayList());
            reportDetail.setBehavoir(Lists.newArrayList(sb.toString().split(REDEX)));
            reportDetail.setSubBehavoirDetails(buildReportView(report));

            reportDetails.add(reportDetail);
        }

        return reportDetails;
    }

    private List<Object> buildReportView(CrowdfundingReport report){
        List<CrowdfundingReportLabel> reportLabels = cfCommonFeignClient.queryReportLabelByReportId(report.getId()).getData();

        List<String> labelTypes = Lists.newArrayList();
        for (CrowdfundingReportLabel reportLabel : reportLabels){
            labelTypes.add(String.valueOf(reportLabel.getReportLabel()));
        }

        AdminWorkReportMap reportWorkMap = adminWorkOrderReportDao.getAdminWorkReportMapByReportId(report.getId());
        long workOrderId = Objects.nonNull(reportWorkMap) ? reportWorkMap.getWorkOrderId() : 0L;

        AdminWorkOrderReport workOrderReport = adminWorkOrderReportDao.selectWorkOrderReportById(report.getId());

        AdminWorkOrder adminWorkOrder = adminWorkOrderDao.selectById(workOrderId);
        int operatorId = Objects.nonNull(adminWorkOrder) ? adminWorkOrder.getOperatorId() : 0;

        CfReportView cfReportView = new CfReportView();
        BeanUtils.copyProperties(report, cfReportView);
        cfReportView.setReportTypes(labelTypes);
        cfReportView.setWorkId(workOrderId);
        AuthRpcResponse<AdminUserAccountModel> account = seaAccountClientV1.getValidUserAccountById(operatorId);
        String name = Optional.ofNullable(account).map(AuthRpcResponse::getResult).map(AdminUserAccountModel::getName).orElse("");
        cfReportView.setOperator(name);
        int handleResult = Optional.ofNullable(workOrderReport).map(AdminWorkOrderReport::getDealResult).orElse(0);
        cfReportView.setReportWorkOrderHandleResult(handleResult);

        return Lists.newArrayList(cfReportView);
    }
}
