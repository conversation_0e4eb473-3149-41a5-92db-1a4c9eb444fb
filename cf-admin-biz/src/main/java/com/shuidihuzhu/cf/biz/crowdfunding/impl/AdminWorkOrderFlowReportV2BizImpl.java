package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.*;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.admin.util.Arith;
import com.shuidihuzhu.cf.biz.admin.AdminOrganizationBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowReportBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowReportV2Biz;
import com.shuidihuzhu.cf.biz.crowdfunding.workflow.WorkFlowFreeRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.workflow.WorkFlowTypePropertyBiz;
import com.shuidihuzhu.cf.biz.es.CfSearch;
import com.shuidihuzhu.cf.dao.admin.AdminWorkOrderFlowRemindDao;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminWorkOrderFlowConst;
import com.shuidihuzhu.cf.enums.export.CfWorkOrderFlowV2DownloadField;
import com.shuidihuzhu.cf.enums.export.CfWorkOrderFlowV3DownloadField;
import com.shuidihuzhu.cf.export.impl.CfWorkOrderFlowV2DataExportStrategy;
import com.shuidihuzhu.cf.export.impl.CfWorkOrderFlowV3DataExportStrategy;
import com.shuidihuzhu.cf.finance.enums.CfOrderDownloadEnum;
import com.shuidihuzhu.cf.finance.model.CfReminderWord;
import com.shuidihuzhu.cf.model.admin.AdminOrganization;
import com.shuidihuzhu.cf.model.admin.AdminOrganizationUserMap;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowParam;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowStatistics;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView;
import com.shuidihuzhu.cf.model.crowdfunding.vo.workflow.WorkFlowTypePropertyVo;
import com.shuidihuzhu.cf.service.workorder.WorkFlowOrderCommonService;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderIndexSearchParam;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst.Status.*;

/**
 * <AUTHOR>
 * @date 2019/7/3
 */
@Slf4j
@Service
public class AdminWorkOrderFlowReportV2BizImpl implements AdminWorkOrderFlowReportV2Biz {

    @Autowired
    private CfSearch cfSearch;

    @Autowired
    private AdminOrganizationBiz orgIdBiz;

    @Autowired
    private AdminWorkOrderFlowRecordBiz adminWorkOrderFlowRecordBiz;

    @Autowired
    private AdminWorkOrderFlowBiz orderFlowBiz;

    @Autowired
    private WorkFlowOrderCommonService flowOrderCommonService;

    @Autowired
    private AdminWorkOrderFlowRemindDao remindDao;

    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    private WorkFlowFreeRecordBiz freeRecordBiz;

    @Autowired
    private WorkFlowTypePropertyBiz typePropertyBiz;

    @Resource
    private CfWorkOrderFlowV2DataExportStrategy cfWorkOrderFlowV2DataExportStrategy;

    @Resource
    private CfWorkOrderFlowV3DataExportStrategy cfWorkOrderFlowV3DataExportStrategy;

    @Override
    public boolean downloadDataDetailExcelV3(HttpServletResponse response, int orgId, Date beginTime, Date endTime, int level, List<Long> secondLevelClassifyIds, int memberType, int staffId, int dataType, long userId) {

        List<AdminOrganizationUserMap> userOrgMap = orgIdBiz.getOrgUserByOrgIds(Lists.newArrayList(orgId));
        List<Long> userIds = userOrgMap.stream().map(AdminOrganizationUserMap::getUserId).map(Integer::longValue).collect(Collectors.toList());

        Set<Long> workOrderIds;
        if (dataType == 9) {
            Pair<Long, List<AdminWorkOrder>> pair = countNoHandleMoreThan48Hour(orgId, level, secondLevelClassifyIds);
            workOrderIds = pair.getRight().stream().map(AdminWorkOrder::getId).collect(Collectors.toSet());
        } else {
            Pair<Integer, List<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView>> viewPair = countFlowReport(orgId, beginTime, endTime, level, secondLevelClassifyIds, memberType, 1, Integer.MAX_VALUE, false);
            workOrderIds = getWorkOrderIds(viewPair.getRight(), memberType, staffId, dataType);
        }

        if (CollectionUtils.isEmpty(workOrderIds)) {
            return false;
        }
        List<List<Long>> workOrderIdPartition = Lists.partition(Lists.newArrayList(workOrderIds), 200);
        List<Map<CfWorkOrderFlowV3DownloadField, String>> data = Lists.newArrayList();
        for (List<Long> tmp : workOrderIdPartition) {

            AdminWorkOrderFlowParam.SearchParam param = new AdminWorkOrderFlowParam.SearchParam();
            param.setWorkOrderIds(Sets.newHashSet(tmp));
            PageInfo<AdminWorkOrderFlowView> pageInfo = orderFlowBiz.selectAdminWorkOrderByParam(param);
            List<AdminWorkOrderFlowView> dataViews = pageInfo.getList();
            List<Integer> flowIds = pageInfo.getList().stream()
                    .map(item -> (int) item.getId())
                    .collect(Collectors.toList());
            Map<Integer, Long> flowIdTRemindTimes = remindDao.listByFlowIds(flowIds).stream().collect(Collectors.groupingBy(AdminWorkOrderFlowRemindRecord::getFlowId, Collectors.counting()));
            Map<Long, WorkFlowTypePropertyVo> flowAutoAssignData = typePropertyBiz.getFlowAutoAssignData(flowIds);
            for (AdminWorkOrderFlowView view : dataViews) {
                Map<CfWorkOrderFlowV3DownloadField, String> map = Maps.newHashMap();
                map.put(CfWorkOrderFlowV3DownloadField.WORK_ORDER_ID, view.getWorkOrderFlowId() + "");
                map.put(CfWorkOrderFlowV3DownloadField.CASE_ID, view.getCaseId() == 0 ? "" : view.getCaseId() + "");
                map.put(CfWorkOrderFlowV3DownloadField.CASE_TITLE, view.getCaseTitle() == null ? "" : view.getCaseTitle());
                map.put(CfWorkOrderFlowV3DownloadField.QUESTION_ONE_LEVE, view.getFirstClassifyDesc());
                map.put(CfWorkOrderFlowV3DownloadField.QUESTION_TWO_LEVE, view.getSecondClassifyDesc());
                map.put(CfWorkOrderFlowV3DownloadField.QUESTION_DES, view.getProblemContent());
                map.put(CfWorkOrderFlowV3DownloadField.DEGREE_EMERGENCY, flowOrderCommonService.queryPriorityLevelDesc(view.getLevel()));
                map.put(CfWorkOrderFlowV3DownloadField.STATUS, flowOrderCommonService.queryOrderStatusDesc(view.getLevel()));

                if (view.getWorkOrderStatus() == AdminWorkOrderConst.Status.HANDLE_SUCCESS.getCode() && userIds.contains((long) view.getOperatorId())) {
                    map.put(CfWorkOrderFlowV3DownloadField.HANDLE_ACTION, "内部处理");
                } else {
                    map.put(CfWorkOrderFlowV3DownloadField.HANDLE_ACTION, "");
                }

                map.put(CfWorkOrderFlowV3DownloadField.HANDLE_RESULT, view.getComment() == null ? "" : view.getComment());
                map.put(CfWorkOrderFlowV3DownloadField.LAST_OPERATOR, view.getOperatorName());
                map.put(CfWorkOrderFlowV3DownloadField.OPERATOR_ORG, view.getOperatorRoleName());
                map.put(CfWorkOrderFlowV3DownloadField.CREATOR, view.getCreatorName());
                map.put(CfWorkOrderFlowV3DownloadField.CREATOR_ORG, view.getCreatorRoleName());
                map.put(CfWorkOrderFlowV3DownloadField.CREATE_TIME, com.shuidihuzhu.common.util.DateUtil.getDate2LStr(view.getCreateTime()));

                if (view.getWorkOrderStatus() == AdminWorkOrderConst.Status.HANDLE_SUCCESS.getCode()) {
                    LocalDateTime createTime = LocalDateTime.ofInstant(view.getCreateTime().toInstant(), ZoneId.systemDefault());
                    LocalDateTime updateTime = LocalDateTime.ofInstant(view.getUpdateTime().toInstant(), ZoneId.systemDefault());
                    long millis = Duration.between(createTime, updateTime).toMillis();
                    map.put(CfWorkOrderFlowV3DownloadField.HANDLE_TIME, String.format("%.1f", millis * 1.0 / 60000));

                } else {
                    map.put(CfWorkOrderFlowV3DownloadField.HANDLE_TIME, StringUtils.EMPTY);
                }
                map.put(CfWorkOrderFlowV3DownloadField.LAST_OPERATE_TIME, com.shuidihuzhu.common.util.DateUtil.getDate2LStr(view.getUpdateTime()));
                map.put(CfWorkOrderFlowV3DownloadField.NUMBER_REMINDERS, flowIdTRemindTimes.getOrDefault((int) (view.getId()), 0L) + "");
                map.put(CfWorkOrderFlowV3DownloadField.REPEAT_WORK_ORDER_COUNT, orderFlowBiz.relateFlowNums(view) + "");

                WorkFlowTypePropertyVo workFlowTypePropertyVo = flowAutoAssignData.get(view.getId());
                if (null == workFlowTypePropertyVo) {
                    workFlowTypePropertyVo = new WorkFlowTypePropertyVo();
                }

                map.put(CfWorkOrderFlowV3DownloadField.SYS_ASS_TIME, workFlowTypePropertyVo.getSystemAllocationTime());
                map.put(CfWorkOrderFlowV3DownloadField.FIRST_SYS_ASS_OPERATOR, workFlowTypePropertyVo.getFirstSystemAssignsUser());
                map.put(CfWorkOrderFlowV3DownloadField.FIRST_HANDLE_TIME, workFlowTypePropertyVo.getFirstHandleTime());
                data.add(map);
            }
        }
        CfReminderWord<Void> export = cfWorkOrderFlowV3DataExportStrategy.export(userId, data, Arrays.stream(CfWorkOrderFlowV3DownloadField.values()).map(CfWorkOrderFlowV3DownloadField::name).collect(Collectors.toList()));
        return export.isSuccessFlag();
    }


    @Override
    public boolean downloadDataSummaryExcelV2(HttpServletResponse response, int orgId, Date beginTime, Date endTime, int level, List<Long> secondLevelClassifyIds, int memberType, long userId) {


        List<String> headers = CfWorkOrderFlowV2DownloadField.getHeaderNameByMemberType(memberType);

        List<Map<CfWorkOrderFlowV2DownloadField, String>> list = new ArrayList<>();

        int current = 1;
        int size = 100;

        while (true) {
            Pair<Integer, List<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView>> pair = countFlowReport(orgId, beginTime, endTime, level, secondLevelClassifyIds, memberType, current, size, true);
            List<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> date2StatisticsView = pair.getRight();
            for (AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView view : date2StatisticsView) {

                Map<CfWorkOrderFlowV2DownloadField, String> map = Maps.newHashMap();
                map.put(CfWorkOrderFlowV2DownloadField.DATE, view.getDate() + "");
                map.put(CfWorkOrderFlowV2DownloadField.MEMBER, view.getMemberName() + "");
                map.put(CfWorkOrderFlowV2DownloadField.TODAY_CREATE_WORK_ORDER, view.getCreate() + "");
                map.put(CfWorkOrderFlowV2DownloadField.TODAY_ADD_WORK_ORDER, view.getProcessingWorkOrder() + "");
                map.put(CfWorkOrderFlowV2DownloadField.WORK_ORDER_ACTUAL_HANDLE_COUNT, view.getAllTo() + "");

                if (memberType != 1) {
                    map.put(CfWorkOrderFlowV2DownloadField.WORK_ORDER_RECEIVED, view.getNoAssign() + "");
                }

                map.put(CfWorkOrderFlowV2DownloadField.NO_HANDLE, view.getNoStartHandle() + "");
                map.put(CfWorkOrderFlowV2DownloadField.DOING, view.getHanding() + "");
                map.put(CfWorkOrderFlowV2DownloadField.DONE_ALL_TODAY, view.getFinish() + "");
                map.put(CfWorkOrderFlowV2DownloadField.DONE_CREATE_TODAY, view.getFinishOnTheSameDay() + "");
                map.put(CfWorkOrderFlowV2DownloadField.NO_DONE_CREATE_TODAY, view.getUnprocessedOnTheSameDay() + "");
                map.put(CfWorkOrderFlowV2DownloadField.NOT_NEED_HANDLE, view.getNoNeedHandle() + "");
                map.put(CfWorkOrderFlowV2DownloadField.THROUGH_WORK_ORDER, view.getFlowPassing() + "");

                if (memberType == 1) {
                    map.put(CfWorkOrderFlowV2DownloadField.WORK_ORDER_24_HOUR_DONE_RATE, view.getFinishRateOnTheSameDay() + "");
                }
                map.put(CfWorkOrderFlowV2DownloadField.AVERAGE_DURATION_ORDER_TIME, view.getAverageOrderTime() + "");
                map.put(CfWorkOrderFlowV2DownloadField.AVERAGE_FIRST_RESPONSE_TIME, view.getAverageFirstHandleTime() + "");
                map.put(CfWorkOrderFlowV2DownloadField.AVERAGE_PROCESSING_TIME, view.getAverageFinishTime() + "");
                map.put(CfWorkOrderFlowV2DownloadField.URGED_SIMPLEX_SINGULAR, view.getRemindOrderNums() + "");

                if (memberType != 1) {
                    map.put(CfWorkOrderFlowV2DownloadField.MOVING_WORKER_SINGULAR, view.getReAllotCount() + "");
                }

                if (memberType == 1) {
                    map.put(CfWorkOrderFlowV2DownloadField.NUMBER_ASSIGNED_GROUP, view.getSameOrgAllotCount() + "");
                    map.put(CfWorkOrderFlowV2DownloadField.FLOW_OUT_GROUP_WORKER_SINGULAR, view.getDiffOrgAllotCount() + "");
                }

                if (memberType != 1) {
                    map.put(CfWorkOrderFlowV2DownloadField.AVERAGE_NUMBER_TURNOVER_WORK_ORDERS, view.getAverageReAllotCount() + "");
                    map.put(CfWorkOrderFlowV2DownloadField.REPEAT_WORK_ORDER_COUNT, view.getSameFlowCount() + "");
                }

                map.put(CfWorkOrderFlowV2DownloadField.FREE_TIME, view.getFreeTime() + "");
                list.add(map);
            }

            if (pair.getRight().size() < size) {
                break;
            } else {
                ++current;
            }
        }
        CfReminderWord<Void> export = cfWorkOrderFlowV2DataExportStrategy.export(userId, list, headers);
        return export.isSuccessFlag();
    }

    @Override
    public Pair<Integer, List<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView>> countFlowReport(int orgId, Date beginTime, Date endTime, int level, List<Long> secondLevelClassifyIds, int memberType, int current, int pageSize, boolean averageOrderTime) {

        if (current < 1) {
            current = 1;
        }

        if (pageSize < 1) {
            pageSize = 20;
        }

        if (memberType == 1) {
            return countFlowReportByUserIds(orgId, beginTime, endTime, level, secondLevelClassifyIds, current, pageSize, averageOrderTime);
        } else {
            return countFlowReportByOrgId(orgId, beginTime, endTime, level, secondLevelClassifyIds, averageOrderTime);
        }
    }

    @Override
    public Pair<Integer, List<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView>> countFlowReportByUserIds(int orgId, Date beginTime, Date endTime, int level, List<Long> secondLevelClassifyIds, int current, int pageSize, boolean averageOrderTime) {
        List<AdminOrganizationUserMap> userOrgMap = orgIdBiz.getOrgUserByOrgIds(Lists.newArrayList(orgId));
        if (CollectionUtils.isEmpty(userOrgMap)) {
            return Pair.of(0, Lists.newArrayList());
        }
        List<Long> userIds = userOrgMap.stream().map(AdminOrganizationUserMap::getUserId).map(Integer::longValue).collect(Collectors.toList());

        //日期 - userId - view
        //人员统计暂时没有按照日期划分啦，为了保证结构完整统一，日期：统一写成当天
        Table<Integer, Integer, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> tables = initTable(userIds);

        // 创建工单数量
        findCreateTaskByUserIds(orgId, userIds, AdminWorkOrderFlowReportBiz.FlowReportType.USER, tables, beginTime, endTime, level, secondLevelClassifyIds);

        // 待领取、未处理、处理中、内部处理
        findHandingTask(orgId, userIds, AdminWorkOrderFlowReportBiz.FlowReportType.USER, tables, beginTime, endTime, level, secondLevelClassifyIds);

        // 工单总量、处理完成、不需要处
        findHandleEndTask(orgId, userIds, AdminWorkOrderFlowReportBiz.FlowReportType.USER, tables, beginTime, endTime, level, secondLevelClassifyIds);

        // 当天创建、当天处理完成的工单数量
        findFinishTask(orgId, userIds, AdminWorkOrderFlowReportBiz.FlowReportType.USER, tables, beginTime, endTime, level, secondLevelClassifyIds);

        // 当天创建、当天未处理的工单数量 and 当天创建，非当天完成（算在当天创建，当天未处理里）
        findUnprocessedTask(orgId, userIds, AdminWorkOrderFlowReportBiz.FlowReportType.USER, tables, beginTime, endTime, level, secondLevelClassifyIds);


        List<AdminUserAccountModel> accountModels = seaAccountClientV1.getUserAccountsByIds(userIds.stream().map(Math::toIntExact).collect(Collectors.toList())).getResult();
        Map<Integer, AdminUserAccountModel> accountModelMap = Maps.uniqueIndex(accountModels, AdminUserAccountModel::getId);

        tables.values().stream().forEach(view -> {
            // 未领取 是组织维度的， 个人没有未领取
            view.setNoAssign(0);
            view.setNoAssignIds(Lists.newArrayList());
            view.removeRepeat();

            AdminUserAccountModel model = accountModelMap.get(view.getMemberId());
            if (model != null) {
                view.setMemberName(model.getName());
            }

        });

        List<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> views = tables.values().stream().sorted(
                Comparator.comparing(AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView::getDate).reversed()
                        .thenComparing(AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView::getMemberId))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(views)) {
            return Pair.of(0, Lists.newArrayList());
        }

        int from = (current - 1) * pageSize;
        int toIndex = from + pageSize < views.size() ? from + pageSize : views.size();

        List<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> viewListResult = views.subList(from, toIndex);

        //计算平均领单时长
        if (averageOrderTime) {
            findAverageOrderTime(orgId, beginTime, endTime, views);
        }

        return Pair.of(views.size(), viewListResult);
    }


    private void findAverageOrderTime(int orgId, Date beginTime, Date endTime, List<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> views) {
        if (CollectionUtils.isEmpty(views)) {
            return;
        }
        for (AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView view : views) {
            FlowUserStat flowUserStat = findAverageOrderTimeByUserId(orgId, beginTime, endTime, view.getMemberId());
            if (flowUserStat.getOrderCount() > 0) {
                view.setAverageOrderTime(Arith.div(flowUserStat.getOrderTime(), flowUserStat.getOrderCount() * 60.0));
            }
            if (flowUserStat.getFirstHandleCount() > 0) {
                view.setAverageFirstHandleTime(Arith.div(flowUserStat.getFirstHandleTime(), flowUserStat.getFirstHandleCount() * 60.0));
            }
            if (flowUserStat.getFinishCount() > 0) {
                view.setAverageFinishTime(Arith.div(flowUserStat.getFinishTime(), flowUserStat.getFinishCount() * 60.0));
            }
            view.setReAllotCount(flowUserStat.getReAllotCount());
            view.setRemindOrderNums(flowUserStat.getRemindOrderNums());
            view.setSameOrgAllotCount(flowUserStat.getSameOrgAllotCount());
            view.setDiffOrgAllotCount(flowUserStat.getDiffOrgAllotCount());
            view.setFreeTime(flowUserStat.getFreeTime());
        }
    }

    private void findAverageOrderTime(int orgId, List<Long> userIds, List<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> views) {
        if (CollectionUtils.isEmpty(views)) {
            return;
        }

        for (AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView view : views) {
            Date begin = com.shuidihuzhu.common.util.DateUtil.getStrToDate("yyyyMMdd", String.valueOf(view.getDate()));
            if (begin == null) {
                continue;
            }

            Date end = DateUtils.addSeconds(DateUtils.addDays(begin, 1), -1);
            List<FlowUserStat> userStatList = findAverageOrderTimeByUserIds(orgId, begin, end, userIds, view);
            int orderCount = userStatList.stream().mapToInt(FlowUserStat::getOrderCount).sum();
            long orderTime = userStatList.stream().mapToLong(FlowUserStat::getOrderTime).sum();
            if (orderCount > 0) {
                view.setAverageOrderTime(Arith.div(orderTime, orderCount * 60.0));
            }

            int firstFinishCount = userStatList.stream().mapToInt(FlowUserStat::getFirstHandleCount).sum();
            long firstFinishTime = userStatList.stream().mapToLong(FlowUserStat::getFirstHandleTime).sum();
            if (firstFinishCount > 0) {
                view.setAverageFirstHandleTime(Arith.div(firstFinishTime, firstFinishCount * 60.0));
            }

            int finishCount = userStatList.stream().mapToInt(FlowUserStat::getFinishCount).sum();
            long finishTime = userStatList.stream().mapToLong(FlowUserStat::getFinishTime).sum();
            if (finishCount > 0) {
                view.setAverageFinishTime(Arith.div(finishTime, finishCount * 60.0));
            }
            view.setReAllotCount(userStatList.stream().mapToInt(FlowUserStat::getReAllotCount).sum());
            view.setRemindOrderNums(userStatList.stream().mapToInt(FlowUserStat::getRemindOrderNums).sum());
            view.setSameOrgAllotCount(userStatList.stream().mapToInt(FlowUserStat::getSameOrgAllotCount).sum());
            view.setDiffOrgAllotCount(userStatList.stream().mapToInt(FlowUserStat::getDiffOrgAllotCount).sum());
            if (view.getSameOrgAllotCount() > 0) {
                DecimalFormat df = new DecimalFormat("#.00");
                view.setAverageReAllotCount(df.format((view.getSameOrgAllotCount() + view.getDiffOrgAllotCount()) * 1.00 / view.getSameOrgAllotCount()));
            } else {
                view.setAverageReAllotCount("0.00");
            }
            view.setFreeTime(userStatList.stream().mapToInt(FlowUserStat::getFreeTime).sum());
        }
    }

    @Data
    class FlowUserStat {
        //平均领单时长(s)
        long orderTime;
        int orderCount;

        //平均首次响应时长
        long firstHandleTime;
        int firstHandleCount;


        //平均处理时长
        long finishTime;
        int finishCount;

        //催单次数（对同一工单去重）
        int remindOrderNums;

        //组内+组外工单流转次数
        int reAllotCount;

        //同组流转工单数
        int sameOrgAllotCount;

        //组外流转工单数
        int diffOrgAllotCount;

        //空闲时间
        int freeTime;

    }


    /**
     * 一段时间内，所有人的领单时长信息
     *
     * @param beginTime
     * @param endTime
     * @param userIds
     * @return left：领单时长总和 单位：s，right：领单次数总和
     */
    private List<FlowUserStat> findAverageOrderTimeByUserIds(int orgId, Date beginTime, Date endTime, List<Long> userIds, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView view) {
        if (orgId <= 0 || beginTime == null || endTime == null || CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }

        List<FlowUserStat> userStatList = Lists.newArrayList();
        for (long userId : userIds) {
            userStatList.add(findAverageOrderTimeByUserId(orgId, beginTime, endTime, userId));
        }

        return userStatList;
    }

    /**
     * 一段时间内，一个人的领单时长信息
     *
     * @param beginTime
     * @param endTime
     * @param userId
     * @return left：领单时长总和 单位：s，right：领单次数总和
     */
    private FlowUserStat findAverageOrderTimeByUserId(int orgId, Date beginTime, Date endTime, long userId) {
        List<AdminWorkOrderFlowRecord> records = adminWorkOrderFlowRecordBiz.selectWithAssignRecords(beginTime, endTime, Math.toIntExact(userId));
        FlowUserStat flowUserStat = new FlowUserStat();
        if (CollectionUtils.isEmpty(records)) {
            return flowUserStat;
        }

        int assignCount = 0;
        long assignTimes = 0;
        int firstHandleCount = 0;
        long firstHandleTimes = 0;
        int finishCount = 0;
        long finishTimes = 0;
        int diffOrgAllotCount = 0;
        int sameOrgAllotCount = 0;
        Map<Long, List<AdminWorkOrderFlowRecord>> workOrderId2RecordsMap = records.stream().collect(Collectors.groupingBy(AdminWorkOrderFlowRecord::getWorkOrderId));
        for (Map.Entry<Long, List<AdminWorkOrderFlowRecord>> entry : workOrderId2RecordsMap.entrySet()) {
            List<AdminWorkOrderFlowRecord> tmpRecords = entry.getValue();

            // 创建、分配、领取
            if (tmpRecords.size() < 2) {
                continue;
            }

            List<AdminWorkOrderFlowRecord> sortedWorkFlows = tmpRecords.stream().sorted(Comparator.comparing(AdminWorkOrderFlowRecord::getCreateTime)).collect(Collectors.toList());

            Date assignCreateTime = null;
            for (AdminWorkOrderFlowRecord tmpRecord : sortedWorkFlows) {
                if (tmpRecord.getOperateType() == AdminWorkOrderFlowConst.OperateTypeEnum.CREATE_WORK_FLOW.getCode()
                        && tmpRecord.getCurrentProblemType() == orgId
                        && tmpRecord.getOrderOperatorId() == 0) {
                    assignCreateTime = tmpRecord.getCreateTime();
                    // 分配
                } else if (tmpRecord.getOperateType() == AdminWorkOrderFlowConst.OperateTypeEnum.ALLOT_WORK_FLOW.getCode()
                        && tmpRecord.getCurrentProblemType() == orgId
                        && tmpRecord.getOrderOperatorId() == 0) {
                    assignCreateTime = tmpRecord.getCreateTime();
                    // 系统分配
                } else if (tmpRecord.getOperateType() == AdminWorkOrderFlowConst.OperateTypeEnum.SYSTEM_ALLOT_WORK_FLOW.getCode()
                        && assignCreateTime != null) {
                    assignCount++;
                    assignTimes += (tmpRecord.getCreateTime().getTime() / 1000 - assignCreateTime.getTime() / 1000);
                    assignCreateTime = null;
                    // 领取
                } else if (tmpRecord.getOperateType() == AdminWorkOrderFlowConst.OperateTypeEnum.ASSIGN_WORK_FLOW.getCode()
                        && assignCreateTime != null) {
                    assignCount++;
                    assignTimes += (tmpRecord.getCreateTime().getTime() / 1000 - assignCreateTime.getTime() / 1000);
                    assignCreateTime = null;
                }

                if (tmpRecord.getOperateType() == AdminWorkOrderFlowConst.OperateTypeEnum.ALLOT_WORK_FLOW.getCode()) {
                    //判断是分给组内还是组外
                    if (orgId == tmpRecord.getCurrentProblemType()) {
                        sameOrgAllotCount++;
                    } else {
                        diffOrgAllotCount++;
                    }
                }
            }

            //求首次处理时间
            Optional<Date> firstAssignOpt = sortedWorkFlows.stream()
                    .filter(item -> item.getOperateType() == AdminWorkOrderFlowConst.OperateTypeEnum.ASSIGN_WORK_FLOW.getCode()
                            || item.getOperateType() == AdminWorkOrderFlowConst.OperateTypeEnum.SYSTEM_ALLOT_WORK_FLOW.getCode())
                    .map(AdminWorkOrderFlowRecord::getCreateTime).findFirst();
            Optional<Date> firstHandledOpt = sortedWorkFlows.stream()
                    .filter(item -> AdminWorkOrderFlowConst.OperateTypeEnum.getHasHandledOperateCode().contains(item.getOperateType())
                            || item.getOperateType() == AdminWorkOrderFlowConst.OperateTypeEnum.HANDLE_WORK_FLOW.getCode())
                    .map(AdminWorkOrderFlowRecord::getCreateTime).findFirst();
            if (firstAssignOpt.isPresent() && firstHandledOpt.isPresent() && firstAssignOpt.get().before(firstHandledOpt.get())) {
                firstHandleCount++;
                firstHandleTimes += firstHandledOpt.get().getTime() / 1000 - firstAssignOpt.get().getTime() / 1000;
            }

            //平均处理时长
//            Optional<Date> firstCreateOpt = sortedWorkFlows.stream()
//                    .filter(item -> item.getOperateType() == AdminWorkOrderFlowConst.OperateTypeEnum.CREATE_WORK_FLOW.getCode())
//                    .map(AdminWorkOrderFlowRecord::getCreateTime).findFirst();
            // 处理完成 （不再处理、处理完成）  减去  分配时间
            Optional<Date> endStatusOpt = sortedWorkFlows.stream().filter(item -> AdminWorkOrderFlowConst.finishedOptList.contains(item.getOperateType()))
                    .map(AdminWorkOrderFlowRecord::getCreateTime).findFirst();
            if (firstAssignOpt.isPresent() && endStatusOpt.isPresent() && firstAssignOpt.get().before(endStatusOpt.get())) {
                finishCount++;
                finishTimes += endStatusOpt.get().getTime() / 1000 - firstAssignOpt.get().getTime() / 1000;
            }
        }
        //被催单数量,同一工单去重
        List<AdminWorkOrderFlowRemindRecord> recordList = remindDao.listByUserIdsAndTime(Lists.newArrayList((int) userId), beginTime, endTime);
        long remindCount = recordList.stream().map(AdminWorkOrderFlowRemindRecord::getFlowId).distinct().count();


        flowUserStat.setOrderTime(assignTimes);
        flowUserStat.setOrderCount(assignCount);
        flowUserStat.setFirstHandleTime(firstHandleTimes);
        flowUserStat.setFirstHandleCount(firstHandleCount);
        flowUserStat.setFinishTime(finishTimes);
        flowUserStat.setFinishCount(finishCount);
        flowUserStat.setRemindOrderNums((int) remindCount);
        flowUserStat.setSameOrgAllotCount(sameOrgAllotCount);
        flowUserStat.setDiffOrgAllotCount(diffOrgAllotCount);
        flowUserStat.setReAllotCount(diffOrgAllotCount + sameOrgAllotCount);

        int count = freeRecordBiz.count(Lists.newArrayList(userId), null, beginTime, endTime);
        flowUserStat.setFreeTime(count * 3);
        return flowUserStat;
    }


    Table<Integer, Integer, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> initTable(List<Long> userIds) {
        Set<Long> currentUserIds = Sets.newHashSet(userIds);
        Table<Integer, Integer, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> table = HashBasedTable.create();
        int start = getYmdFromDate(new Date());

        for (long userId : currentUserIds) {
            if (!table.contains(start, (int) userId)) {
                AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView view = new AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView();
                view.setDate(start);
                view.setMemberId((int) userId);
                table.put(start, (int) userId, view);
            }
        }
        return table;
    }

    Table<Integer, Integer, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> initTable(Date beginTime, Date endTime, int orgId) {

        Table<Integer, Integer, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> table = HashBasedTable.create();

        Date beginTimeTmp = DateUtils.truncate(beginTime, 10);

        for (; beginTimeTmp.before(endTime); beginTimeTmp = DateUtils.addDays(beginTimeTmp, 1)) {
            int start = getYmdFromDate(beginTimeTmp);
            if (!table.contains(start, orgId)) {
                AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView view = new AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView();
                view.setDate(start);
                view.setMemberId(orgId);
                table.put(start, orgId, view);
            }
        }
        return table;
    }

    @Override
    public Pair<Integer, List<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView>> countFlowReportByOrgId(int orgId, Date beginTime, Date endTime, int level, List<Long> secondLevelClassifyIds, boolean averageOrderTime) {

        AdminOrganization adminOrganization = orgIdBiz.getAdminOrganizationById(orgId);
        if (adminOrganization == null) {
            return Pair.of(0, Lists.newArrayList());
        }

        //按照每天初始化对象
        //日期 - userId - view
        Table<Integer, Integer, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> tables = initTable(beginTime, endTime, orgId);

        List<AdminOrganizationUserMap> userOrgMap = orgIdBiz.getOrgUserByOrgIds(Lists.newArrayList(orgId));
        List<Long> userIds = userOrgMap.stream().map(AdminOrganizationUserMap::getUserId).map(Integer::longValue).collect(Collectors.toList());

        // 创建工单数量
        findCreateTaskByUserIds(orgId, userIds, AdminWorkOrderFlowReportBiz.FlowReportType.ORG, tables, beginTime, endTime, level, secondLevelClassifyIds);

        // 待领取、未处理、处理中、内部处理
        findHandingTask(orgId, userIds, AdminWorkOrderFlowReportBiz.FlowReportType.ORG, tables, beginTime, endTime, level, secondLevelClassifyIds);

        // 工单总量、处理完成、不需要处
        findHandleEndTask(orgId, userIds, AdminWorkOrderFlowReportBiz.FlowReportType.ORG, tables, beginTime, endTime, level, secondLevelClassifyIds);

        // 当天创建、当天处理完成的工单数量
        findFinishTask(orgId, userIds, AdminWorkOrderFlowReportBiz.FlowReportType.ORG, tables, beginTime, endTime, level, secondLevelClassifyIds);

        // 当天创建、当天未处理的工单数量 and 当天创建，非当天完成（算在当天创建，当天未处理里）
        findUnprocessedTask(orgId, userIds, AdminWorkOrderFlowReportBiz.FlowReportType.ORG, tables, beginTime, endTime, level, secondLevelClassifyIds);

        tables.values().stream().forEach(view -> {
            view.setMemberId(orgId);
            view.setMemberName(adminOrganization.getName());
            view.removeRepeat();
            DateTime startTime = parseDateFromInt(view.getDate());
            int freeCount = freeRecordBiz.count(userIds, null, startTime.toDate(), startTime.plusDays(1).toDate());
            view.setFreeTime(freeCount * 3);

        });

        //倒序
        List<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> views = tables.values().stream().
                sorted(Comparator.comparing(AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView::getDate).reversed()).collect(Collectors.toList());

        if (averageOrderTime) {
            findAverageOrderTime(orgId, userIds, views);
        }

        return Pair.of(views.size(), views);
    }

    // 当天创建、当天未处理的
    private void findUnprocessedTask(int orgId, List<Long> userIds, AdminWorkOrderFlowReportBiz.FlowReportType reportType,
                                     Table<Integer, Integer, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> tables,
                                     Date beginTime, Date endTime, int level, List<Long> secondLevelClassifyIds) {

        CfWorkOrderIndexSearchParam searchParam = buildBaseSearchParam(beginTime, endTime, level, secondLevelClassifyIds);
        searchParam.setAwoOrderStatuses(Lists.newArrayList(AdminWorkOrderConst.Status.CREATED.getCode()));
        if (reportType == AdminWorkOrderFlowReportBiz.FlowReportType.ORG) {
            searchParam.setAwofProblemTypes(Lists.newArrayList(orgId));
        } else {
            searchParam.setAwoOperatorIds(userIds);
        }

        int from = 0;
        int size = 1000;
        searchParam.setSize(size);
        while (true) {
            searchParam.setFrom(from);
            Pair<Long, List<AdminWorkOrder>> pair = cfSearch.cfWorkOrderIndexSearch(searchParam);
            pair.getRight().stream().forEach(adminWorkOrder -> {
                int columnKey = reportType == AdminWorkOrderFlowReportBiz.FlowReportType.ORG ? orgId : adminWorkOrder.getOperatorId();
                Date date = reportType == AdminWorkOrderFlowReportBiz.FlowReportType.USER ? new Date() : adminWorkOrder.getCreateTime();

                int begin = getYmdFromDate(adminWorkOrder.getCreateTime());
                int end = getYmdFromDate(adminWorkOrder.getUpdateTime());
                //当天创建，当天未处理的,
                if (begin == end) {
                    AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView view = getAdminWorkOrderFlowStatisticsView(date, columnKey, tables);
                    view.setUnprocessedOnTheNextDay(view.getUnprocessedOnTheNextDay() + 1);
                    view.getUnprocessedOnTheNextDayIds().add(adminWorkOrder.getId());
                }
            });
            if (pair.getRight().size() < size) {
                break;
            } else {
                from += size;
            }
        }
    }


    // 当天创建、当天处理完成的工单数量 and 当天创建，非当天完成（算在当天创建，当天未处理里）
    private void findFinishTask(int orgId, List<Long> userIds, AdminWorkOrderFlowReportBiz.FlowReportType reportType,
                                Table<Integer, Integer, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> tables,
                                Date beginTime, Date endTime, int level, List<Long> secondLevelClassifyIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }

        CfWorkOrderIndexSearchParam searchParam = buildBaseSearchParam(beginTime, endTime, level, secondLevelClassifyIds);
        searchParam.setAwoOrderStatuses(Lists.newArrayList(AdminWorkOrderConst.Status.HANDLE_SUCCESS.getCode()));
        if (reportType == AdminWorkOrderFlowReportBiz.FlowReportType.ORG) {
            searchParam.setAwofProblemTypes(Lists.newArrayList(orgId));
        } else {
            searchParam.setAwoOperatorIds(userIds);
        }

        int from = 0;
        int size = 1000;
        searchParam.setSize(size);
        while (true) {
            searchParam.setFrom(from);
            Pair<Long, List<AdminWorkOrder>> pair = cfSearch.cfWorkOrderIndexSearch(searchParam);

            pair.getRight().stream().forEach(adminWorkOrder -> {
                int columnKey = reportType == AdminWorkOrderFlowReportBiz.FlowReportType.ORG ? orgId : adminWorkOrder.getOperatorId();
                Date date = reportType == AdminWorkOrderFlowReportBiz.FlowReportType.USER ? new Date() : adminWorkOrder.getCreateTime();

                int begin = getYmdFromDate(adminWorkOrder.getCreateTime());
                int end = getYmdFromDate(adminWorkOrder.getUpdateTime());
                //如果begin=end ，当天创建当天完成 ， 否则 当天创建，非当天完成（算在当天创建，当天未处理里）
                if (begin == end) {
                    AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView view = getAdminWorkOrderFlowStatisticsView(date, columnKey, tables);
                    view.setFinishOnTheSameDay(view.getFinishOnTheSameDay() + 1);
                    view.getFinishOnTheSameDayIds().add(adminWorkOrder.getId());
                } else {
                    AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView view = getAdminWorkOrderFlowStatisticsView(date, columnKey, tables);
                    view.setTheFinishOnNextDay(view.getTheFinishOnNextDay() + 1);
                    view.getTheFinishOnNextDayIds().add(adminWorkOrder.getId());
                }
            });
            if (pair.getRight().size() < size) {
                break;
            } else {
                from += size;
            }
        }
    }


    // 创建工单数量
    private void findCreateTaskByUserIds(int orgId, List<Long> userIds, AdminWorkOrderFlowReportBiz.FlowReportType reportType,
                                         Table<Integer, Integer, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> tables,
                                         Date beginTime, Date endTime, int level, List<Long> secondLevelClassifyIds) {

        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }

        CfWorkOrderIndexSearchParam searchParam = buildBaseSearchParam(beginTime, endTime, level, secondLevelClassifyIds);
        searchParam.setAwoCreatorIds(userIds);

        int from = 0;
        int size = 1000;
        searchParam.setSize(size);
        while (true) {
            searchParam.setFrom(from);
            Pair<Long, List<AdminWorkOrder>> pair = cfSearch.cfWorkOrderIndexSearch(searchParam);

            pair.getRight().stream().forEach(adminWorkOrder -> {
                int columnKey = reportType == AdminWorkOrderFlowReportBiz.FlowReportType.ORG ? orgId : adminWorkOrder.getCreatorId();
                Date date = reportType == AdminWorkOrderFlowReportBiz.FlowReportType.USER ? new Date() : adminWorkOrder.getCreateTime();
                AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView view = getAdminWorkOrderFlowStatisticsView(date, columnKey, tables);
                view.setCreate(view.getCreate() + 1);
                view.getCreateIds().add(adminWorkOrder.getId());
                //判断工单是否有相同的工单
                AdminWorkOrderFlow adminWorkOrderFlow = orderFlowBiz.selectByWorkOrderId(adminWorkOrder.getId());
                Response<String> relateFlowResponse = orderFlowBiz.existRelateFlow(adminWorkOrderFlow);
                if (relateFlowResponse.notOk()) {
                    view.setSameFlowCount(view.getSameFlowCount() + 1);
                }

            });
            if (pair.getRight().size() < size) {
                break;
            } else {
                from += size;
            }
        }
    }

    // 异常工单（48小时未结束）：是指到目前为止（当前时间），超过48小时还未结束的工单数量
    @Override
    public Pair<Long, List<AdminWorkOrder>> countNoHandleMoreThan48Hour(int orgId, int level, List<Long> secondLevelClassifyIds) {
        CfWorkOrderIndexSearchParam searchParam = buildBaseSearchParam(null, DateUtils.addHours(new Date(), -48), level, secondLevelClassifyIds);
        searchParam.setAwoOrderStatuses(Lists.newArrayList(AdminWorkOrderConst.Status.CREATED.getCode(), AdminWorkOrderConst.Status.HANDLING.getCode()));
        searchParam.setAwofProblemTypes(Lists.newArrayList(orgId));
        return cfSearch.cfWorkOrderIndexSearch(searchParam);
    }

    // 待领取、未处理、处理中
    private void findHandingTask(int orgId, List<Long> userIds, AdminWorkOrderFlowReportBiz.FlowReportType reportType,
                                 Table<Integer, Integer, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> tables,
                                 Date beginTime, Date endTime, int level, List<Long> secondLevelClassifyIds) {
        CfWorkOrderIndexSearchParam searchParam = buildBaseSearchParam(beginTime, endTime, level, secondLevelClassifyIds);
        searchParam.setAwoOrderStatuses(Lists.newArrayList(AdminWorkOrderConst.Status.CREATED.getCode(), AdminWorkOrderConst.Status.HANDLING.getCode()));

        if (reportType == AdminWorkOrderFlowReportBiz.FlowReportType.ORG) {
            searchParam.setAwofProblemTypes(Lists.newArrayList(orgId));
        } else {
            searchParam.setAwoOperatorIds(userIds);
        }
        int from = 0;
        int size = 1000;
        searchParam.setSize(size);
        while (true) {
            searchParam.setFrom(from);

            Pair<Long, List<AdminWorkOrder>> pair = cfSearch.cfWorkOrderIndexSearch(searchParam);
            pair.getRight().stream().forEach(adminWorkOrder -> {
                int columnKey = reportType == AdminWorkOrderFlowReportBiz.FlowReportType.ORG ? orgId : adminWorkOrder.getOperatorId();
                Date date = reportType == AdminWorkOrderFlowReportBiz.FlowReportType.USER ? new Date() : adminWorkOrder.getCreateTime();
                AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView view = getAdminWorkOrderFlowStatisticsView(date, columnKey, tables);

                if (adminWorkOrder.getOrderStatus() == CREATED.getCode()) {
                    if (adminWorkOrder.getOperatorId() != 0) {
                        view.setNoStartHandle(view.getNoStartHandle() + 1);
                        view.getNoStartHandleIds().add(adminWorkOrder.getId());
                    } else {
                        view.setNoAssign(view.getNoAssign() + 1);
                        view.getNoAssignIds().add(adminWorkOrder.getId());
                    }
                } else if (adminWorkOrder.getOrderStatus() == HANDLING.getCode()) {
                    if (adminWorkOrder.getOperatorId() != 0) {
                        view.setHanding(view.getHanding() + 1);
                        view.getHandingIds().add(adminWorkOrder.getId());
                    } else {
                        view.setNoAssign(view.getNoAssign() + 1);
                        view.getNoAssignIds().add(adminWorkOrder.getId());
                    }
                }
            });

            if (pair.getRight().size() < size) {
                break;
            } else {
                from += size;
            }
        }
    }

    // 工单总量、处理完成、不需要处理
    private void findHandleEndTask(int orgId, List<Long> userIds, AdminWorkOrderFlowReportBiz.FlowReportType reportType,
                                   Table<Integer, Integer, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> tables,
                                   Date beginTime, Date endTime, int level, List<Long> secondLevelClassifyIds) {
        AdminWorkOrderFlowStatistics.searchParam param = new AdminWorkOrderFlowStatistics.searchParam();
        if (reportType == AdminWorkOrderFlowReportBiz.FlowReportType.ORG) {
            param.setProblemTypeCodes(Lists.newArrayList(orgId));
        } else {
            param.setOperatorIds(userIds.stream().map(Long::intValue).collect(Collectors.toList()));
        }
        param.setOperateTypeCodes(Arrays.asList(AdminWorkOrderFlowConst.OperateTypeEnum.HANDLE_SUCCESS_WORK_FLOW.getCode(),
                AdminWorkOrderFlowConst.OperateTypeEnum.NO_HANDLE_WORK_FLOW.getCode(),
                AdminWorkOrderFlowConst.OperateTypeEnum.ALLOT_WORK_FLOW.getCode()));
        param.setBeginTime(beginTime);
        param.setEndTime(endTime);
        param.setLevel(level);
        param.setSecondLevelClassifyIds(secondLevelClassifyIds);

        List<AdminWorkOrderFlowRecord> flowRecords = adminWorkOrderFlowRecordBiz.selectByOperatorIdsAndTime(param);

        flowRecords.stream().forEach(flowRecord -> {
            int columnKey = reportType == AdminWorkOrderFlowReportBiz.FlowReportType.ORG ? orgId : flowRecord.getOperatorId();
            Date date = reportType == AdminWorkOrderFlowReportBiz.FlowReportType.USER ? new Date() : flowRecord.getCreateTime();
            AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView view = getAdminWorkOrderFlowStatisticsView(date, columnKey, tables);
            switch (AdminWorkOrderFlowConst.OperateTypeEnum.getEnumByCode(flowRecord.getOperateType())) {
                case ALLOT_WORK_FLOW:
                    view.setFlowPassing(view.getFlowPassing() + 1);
                    view.getFlowPassingIds().add(flowRecord.getWorkOrderId());
                    break;
                case HANDLE_SUCCESS_WORK_FLOW:
                    view.setFinish(view.getFinish() + 1);
                    view.getFinishIds().add(flowRecord.getWorkOrderId());
                    break;
                case NO_HANDLE_WORK_FLOW:
                    view.setNoNeedHandle(view.getNoNeedHandle() + 1);
                    view.getNoNeedHandleIds().add(flowRecord.getWorkOrderId());
                    break;
                default:
                    break;
            }
        });
    }

    private CfWorkOrderIndexSearchParam buildBaseSearchParam(Date beginTime, Date endTime, int level, List<Long> secondLevelClassifyIds) {
        CfWorkOrderIndexSearchParam searchParam = new CfWorkOrderIndexSearchParam();
        searchParam.setAwoOrderTypes(Lists.newArrayList(AdminWorkOrderConst.Type.FLOW.getCode()));
        searchParam.setFrom(0);
        // 不限制条数，设置个稍大的值
        searchParam.setSize(10000);
        if (level != -1) {
            searchParam.setAwoLevels(Lists.newArrayList(level));
        }

        if (CollectionUtils.isNotEmpty(secondLevelClassifyIds)) {
            searchParam.setAwofSecondClassifyIds(secondLevelClassifyIds);
        }

        if (beginTime != null) {
            searchParam.setAwoCreateTimeStart(beginTime.getTime());
        }

        if (endTime != null) {
            searchParam.setAwoCreateTimeEnd(endTime.getTime());
        }

        return searchParam;
    }

    private AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView getAdminWorkOrderFlowStatisticsView(Date CreateTime, Integer columnKey, Table<Integer, Integer, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> tables) {
        int rowKey = getYmdFromDate(CreateTime);
        AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView view = tables.get(rowKey, columnKey);
        if (view == null) {
            view = new AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView();
            view.setDate(rowKey);
            view.setMemberId(columnKey);
            tables.put(rowKey, columnKey, view);
        }
        return view;
    }

    private int getYmdFromDate(Date createTime) {
        String ymd = DateUtil.getYMDStringByDate(createTime);
        int key = Integer.parseInt(ymd);
        return key;
    }

    private DateTime parseDateFromInt(int date) {
        DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyyMMdd");
        return DateTime.parse(String.valueOf(date), formatter);
    }

    private Set<Long> getWorkOrderIds(List<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> views, int memberType, int staffId, int dataType) {
        if (CollectionUtils.isEmpty(views)) {
            return Sets.newHashSet();
        }

        Set<Long> workOrderIds = Sets.newHashSet();
        for (AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView view : views) {
            if (memberType == 1 && staffId != 0 && view.getMemberId() != staffId) {
                continue;
            }

            switch (dataType) {
                case 1:
                    workOrderIds.addAll(view.getAllToIds());
                    break;
                case 2:
                    workOrderIds.addAll(view.getCreateIds());
                    break;
                case 3:
                    workOrderIds.addAll(view.getNoAssignIds());
                    break;
                case 4:
                    workOrderIds.addAll(view.getNoStartHandleIds());
                    break;
                case 5:
                    workOrderIds.addAll(view.getHandingIds());
                    break;
                case 6:
                    workOrderIds.addAll(view.getFinishIds());
                    break;
                case 7:
                    workOrderIds.addAll(view.getNoNeedHandleIds());
                    break;
                case 8:
                    workOrderIds.addAll(view.getFlowPassingIds());
                    break;
                case 10:
                    workOrderIds.addAll(view.getFinishOnTheSameDayIds());
                    break;
                case 11:
                    workOrderIds.addAll(view.getUnprocessedOnTheSameDayIds());
                    break;
                case 12:
                    workOrderIds.addAll(view.getProcessingWorkOrderIds());
                    break;
                default:
                    break;
            }
        }

        return workOrderIds;
    }

}
