package com.shuidihuzhu.cf.biz.risk.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.shuidihuzhu.cf.biz.admin.AdminCfOperatingRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.biz.risk.ISimpleExportService;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfOperatingRecordDao;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICfInfoXXXRecordDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.ugc.IUgcDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.finance.client.feign.CfFinancePauseFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.CfDrawCashPauseRecord;
import com.shuidihuzhu.cf.model.crowdfunding.*;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

/**
 * Created by sven on 2019/6/28.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class RiskSimpleExportServiceImpl implements ISimpleExportService {

    private final static DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd HH:mm:ss");

    private final String VERIFY_COUNT = "证实次数";
    private final String SHARE_COUNT = "转发次数";
    private final String REPORT_count = "举报次数";
    private final String NON_REPORT_COUNT = "点击不需要处理举报条数";
    private final String CASE_PAGE_VISIT_COUNT = "案例详情页访问次数";
    private final String REFUSE_DETAIL = "案例审核驳回次数";
    private final String PAUSE_TIMES = "暂停次数";
    private final String PAUSE_DRAW_TIMES = "暂停打款次数";
    private final String STRONG_LABEL = "后台强提示";
    private final String OPERATOR_SEND_SMS_COUNT = "运营发送短信次数";


    @Resource
    private IRiskDelegate riskDelegate;

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Resource
    private IUgcDelegate ugcDelegate;

    @Autowired
    private AdminCfOperatingRecordBiz adminCfOperatingRecordBiz;

    @Resource
    private AdminCrowdfundingOperationBiz adminCrowdfundingOperationBiz;

    @Resource
    private AdminCfOperatingRecordDao cfOperatingRecordDao;

    @Autowired
    private CfFinancePauseFeignClient cfFinancePauseFeignClient;

    @Autowired
    private ICfInfoXXXRecordDelegate cfInfoXXXRecordDelegate;

    @Override
    public Map<String, String> getDetail(CfInfoSimpleModel cfInfoSimpleModel) {

        int verifyTimes = ugcDelegate.countCrowdFundingVerificationByInfoUuid(cfInfoSimpleModel.getInfoId());
        long shareCount = cfInfoXXXRecordDelegate.getCountByInfoId(cfInfoSimpleModel.getId());
        List<CrowdfundingReport> reportList = riskDelegate.getCrowdfundingReportListByInfoId(cfInfoSimpleModel.getId());
        int reportCount = CollectionUtils.isEmpty(reportList)?0:reportList.size();


        CrowdfundingOperation crowdfundingOperation = adminCrowdfundingOperationBiz.getByInfoId(cfInfoSimpleModel.getInfoId());

        Map<String,String> map = Maps.newHashMap();
        map.put(VERIFY_COUNT, verifyTimes+"");
        map.put(SHARE_COUNT, shareCount+"");
        map.put(REPORT_count, reportCount+"");
        map.put(NON_REPORT_COUNT,UNKOWN);
        map.put(CASE_PAGE_VISIT_COUNT, UNKOWN);
        map.put(REFUSE_DETAIL, UNKOWN);

        if(crowdfundingOperation != null){
            map.put(REFUSE_DETAIL, crowdfundingOperation.getRefuseCount() + "");
        }

        map.put(PAUSE_TIMES, buildDrawCashStop(cfInfoSimpleModel.getId()));
        map.put(STRONG_LABEL, buildStrongTip(cfInfoSimpleModel.getInfoId()));
        map.put(OPERATOR_SEND_SMS_COUNT, UNKOWN);

        return map;
    }

    public String getRefuseDetail(CfInfoSimpleModel cfInfoSimpleModel) {

        CrowdfundingOperation crowdfundingOperation = adminCrowdfundingOperationBiz.getByInfoId(cfInfoSimpleModel.getInfoId());

        List<CfOperatingRecord> operatingRecords = cfOperatingRecordDao.getAllOperateByInfoUuidAndTypes(cfInfoSimpleModel.getInfoId(), Lists.newArrayList(201));
        if (crowdfundingOperation == null || CollectionUtils.isEmpty(operatingRecords)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        sb.append("驳回次数：" + crowdfundingOperation.getRefuseCount() + "\n");
        int i = 1;
        for (CfOperatingRecord cfOperatingRecord : operatingRecords) {
            String userName = cfOperatingRecord.getUserName();
            String date = dateFormat.format(cfOperatingRecord.getDateCreated());
            sb.append("第" + i + "次：" + userName + " " + date + "\n");
            i++;
        }
        return sb.toString();
    }

    private String buildDrawCashStop(int infoId) {
        // 获取暂停打款次数
        List<CfDrawCashPauseRecord> drawCashPauseRecords = Lists.newArrayList();
        FeignResponse<List<CfDrawCashPauseRecord>> response = cfFinancePauseFeignClient.getPauseDrawCashByCaseId(infoId);
        if (!response.notOk() && CollectionUtils.isNotEmpty(response.getData())) {
            drawCashPauseRecords.addAll(response.getData());
        }
        List<CfDrawCashPauseRecord> pauseLaunch = drawCashPauseRecords.stream()
                // 10: 打款， 20：退款
                .filter(p -> p.getStateProgress() == 10)
                .collect(Collectors.toList());
        List<CfDrawCashPauseRecord> pauseRefund = drawCashPauseRecords.stream()
                // 10: 打款， 20：退款
                .filter(p -> p.getStateProgress() == 20)
                .collect(Collectors.toList());
        String result = "";
        if (CollectionUtils.isEmpty(drawCashPauseRecords)) {
            return "没有暂停打款记录";
        }
        result = String.format("暂停打款次数:%d 暂停退款次数：%d 整体暂停次数:%d",
                pauseLaunch.size(),
                pauseRefund.size(),
                drawCashPauseRecords.size()
                );
        result += "暂停明细 ";
        for (CfDrawCashPauseRecord pauseRecord : drawCashPauseRecords) {
            result += String.format("暂停的原因:%s 暂停的时间:%s 暂停人姓名：%s 恢复备注：%s 恢复人姓名:%s 恢复时间:%s",
                    pauseRecord.getPauseComment(),
                    dateFormat.format(pauseRecord.getCreateTime()),
                    pauseRecord.getPauseName(),
                    pauseRecord.getRecoverComment(),
                    pauseRecord.getRecoverName(),
                    dateFormat.format(pauseRecord.getRecoverTime()));
        }
        return result;
    }

    private String buildStrongTip(String infoUuid) {
        String result = "";
        List<CfOperatingRecord> operatingRecordList = adminCfOperatingRecordBiz.getAllOperateByInfoUuidAndType(
                infoUuid,
                CfOperatingRecordEnum.Type.ADD_REMINDER.getCode());
        if (CollectionUtils.isEmpty(operatingRecordList)) {
            return result;
        }
        for (CfOperatingRecord cfOperatingRecord : operatingRecordList) {
            result += String.format("强提示操作人：%s \n强提示内容：%s \n强提示标记时间:%s",
                    operatingRecordList.get(0).getUserName(),
                    operatingRecordList.get(0).getComment(),
                    dateFormat.format(operatingRecordList.get(0).getDateCreated())
            );
        }

        return result;
    }

    @Override
    public String getCategory() {
        return "案例风险反证";
    }

    @Override
    public int getOrder() {
        return 5;
    }
}
