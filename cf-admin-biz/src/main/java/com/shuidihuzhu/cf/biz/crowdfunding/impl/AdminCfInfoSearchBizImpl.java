package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.admin.util.CurrencyUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.biz.mapper.CrowdfundingInfoMapper;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfInfoExtDao;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingInfoSlaveDao;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingOperationDao;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.diff.Diff;
import com.shuidihuzhu.cf.enums.admin.ApproveListOrderByWhat;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CaseSearchVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoVo;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.search.client.CfSearchClient;
import com.shuidihuzhu.client.cf.search.model.CfCaseIndexSearchParam;
import com.shuidihuzhu.client.cf.search.model.CfCaseIndexSearchResult;
import com.shuidihuzhu.client.cf.search.model.CfCaseModel;
import com.shuidihuzhu.client.cf.search.model.SearchRpcResult;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.util.admin.BasicExample;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @date 2019/2/28
 */

@Slf4j
@Service
public class AdminCfInfoSearchBizImpl implements AdminCfInfoSearchBiz {

    @Autowired
    private AdminCrowdfundingAuthorBiz crowdfundingAuthorBiz;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Autowired
    private AdminCrowdfundingInfoSlaveDao crowdfundingInfoSlaveDao;

    @Autowired
    private CfSearchClient cfSearchClient;

    @Autowired
    private AdminCrowdfundingInfoSlaveDao adminCrowdfundingInfoSlaveDao;

    @Autowired
    private AdminCfInfoExtDao adminCfInfoExtDao;

    @Autowired
    private AdminCrowdfundingOperationDao adminCrowdfundingOperationDao;

    @Autowired
    private AdminApproveService adminApproveService;

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private CfRepeatUserIdRecordBiz cfRepeatUserIdRecordBiz;

    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Override
    public List<CrowdfundingInfoVo> approveSearch(String mobile, String name, Integer id,
                                                  String title, String caseUserId, Integer refuseCountHandle,
                                                  Integer status, Integer isContact, Integer finished,
                                                  Integer operationStatus, Integer dataStatus, Integer sortHandle,
                                                  String startTime, String endTime, Integer handle,
                                                  Integer pageNum, Integer pageSize) {


        //处理name
        List<Integer> idListByName = null;
        if (!StringUtils.isEmpty(name)) {
            List<CrowdfundingAuthor> authors = crowdfundingAuthorBiz.getByName(name);
            if (CollectionUtils.isNotEmpty(authors)) {
                idListByName = authors.stream().
                        mapToInt(CrowdfundingAuthor::getCrowdfundingId).boxed().collect(toList());
            }
        }

        //处理CaseUserID
        long userId = 0;
        if (StringUtils.isNotBlank(caseUserId)) {
            try {
                userId = Long.valueOf(caseUserId);
            } catch (NumberFormatException e) {
                log.warn("caseUserId valueOf error", caseUserId);
            } catch (Exception e) {
                log.error("AdminApproveService approveSearch error", e);
            }
            //如果不成功走解密路线
            if (userId <= 0) {
                userId = shuidiCipher.decryptUserId(caseUserId);
            }
        }

        String userMobile = null;
        if (StringUtils.isNotEmpty(mobile)) {
            //如果userId为空并且mobile不为空 对mobile进行处理
            UserInfoModel userInfoModel = this.userInfoServiceBiz.getUserInfoByMobile(mobile);
            if (userInfoModel != null && StringUtils.isNotBlank(userInfoModel.getCryptoMobile())) {
                userId = userInfoModel.getUserId();
                userMobile = null;
            } else {
                userMobile = oldShuidiCipher.aesEncrypt(mobile);
            }
        }

        if (status != null && status >= 0 && status < CrowdfundingStatus.values().length) {
            CrowdfundingStatus statusEnum = CrowdfundingStatus.values()[status];
            status = statusEnum.value();
        }

        userMobile = StringUtils.trimToNull(userMobile);
        title = StringUtils.trimToNull(title);

        PageHelper.startPage(pageNum, pageSize);
        return crowdfundingInfoSlaveDao.selectByPage(userMobile, id, idListByName,
                title, userId, refuseCountHandle, status, isContact, finished, new Date(),
                operationStatus, dataStatus, startTime, endTime, handle, getBySortHandle(sortHandle));
    }

    @Override
    public List<CrowdfundingInfoVo> reserveApproveSearch(String mobile, String name, Integer id,
                                                         String title, Integer pageNum, Integer pageSize) {

        //处理name
        List<Integer> idListByName = null;
        if (!StringUtils.isEmpty(name)) {
            List<CrowdfundingAuthor> authors = crowdfundingAuthorBiz.getByName(name);
            if (CollectionUtils.isNotEmpty(authors)) {
                idListByName = authors.stream().
                        mapToInt(CrowdfundingAuthor::getCrowdfundingId).boxed().collect(toList());
            }
        }

        long userId = 0;
        String userMobile = null;

        if (StringUtils.isNotEmpty(mobile)) {
            //如果userId为空并且mobile不为空 对mobile进行处理
            UserInfoModel userInfoModel = this.userInfoServiceBiz.getUserInfoByMobile(mobile);
            if (userInfoModel != null && StringUtils.isNotBlank(userInfoModel.getCryptoMobile())) {
                userId = userInfoModel.getUserId();
                userMobile = null;
            } else {
                userMobile = oldShuidiCipher.aesEncrypt(mobile);
            }
        }

        userMobile = StringUtils.trimToNull(userMobile);
        title = StringUtils.trimToNull(title);

        PageHelper.startPage(pageNum, pageSize);

        List<CrowdfundingInfoVo> crowdfundingInfoVoList = crowdfundingInfoSlaveDao.reserveSelectByPage(userMobile, id, idListByName, userId, title);
        List<Integer> caseIds = crowdfundingInfoVoList.stream().map(CrowdfundingInfoVo::getId).collect(toList());
        List<CrowdfundingOperation> crowdfundingOperationList = adminCrowdfundingOperationDao.getCrowdfundingOperation(caseIds);

        Map<Integer,CrowdfundingOperation> crowdfundingOperationMap = Maps.uniqueIndex(crowdfundingOperationList,CrowdfundingOperation::getCaseId);

        for (CrowdfundingInfoVo crowdfundingInfoVo : crowdfundingInfoVoList) {
            CrowdfundingOperation crowdfundingOperation = crowdfundingOperationMap.get(crowdfundingInfoVo.getId());

            if (crowdfundingOperation == null) {
                continue;
            }
            crowdfundingInfoVo.setOperation(crowdfundingOperation.getOperation());
            crowdfundingInfoVo.setAuditCommitTime(crowdfundingOperation.getAuditCommitTime());
            crowdfundingInfoVo.setCallCount(crowdfundingOperation.getCallCount());
            crowdfundingInfoVo.setCallStatus(crowdfundingOperation.getCallStatus());
            crowdfundingInfoVo.setOperatorId(crowdfundingOperation.getOperatorId());
            crowdfundingInfoVo.setUserRefuseCount(crowdfundingOperation.getUserRefuseCount());
            crowdfundingInfoVo.setRefuseCount(crowdfundingOperation.getRefuseCount());
        }

        return crowdfundingInfoVoList;
    }

    @Override
    public Pair<Long, List<CrowdfundingInfoVo>> approveSearchFromEs(String mobile, String name, Integer id,
                                                                    String title, String caseUserId, Integer refuseCountHandle,
                                                                    Integer status, Integer isContact, Integer finished,
                                                                    Integer operationStatus, Integer dataStatus, Integer sortHandle,
                                                                    String startTime, String endTime, Integer handle,
                                                                    Integer pageNum, Integer pageSize, String content,int creditStatus, int deferContactReasonType,
                                                                    int fuwuType) {


        CfCaseIndexSearchParam searchParam = convertApproveSearchParam(mobile, name, id, title, caseUserId, refuseCountHandle,
                status, isContact, finished, operationStatus, dataStatus, startTime, endTime, handle,
                pageNum, pageSize, content,creditStatus,deferContactReasonType,fuwuType);

        Pair<Long, List<CfCaseModel>> pair = searchFromEs(searchParam);

        long total = pair.getLeft();
        List<CfCaseModel> cfCaseModels = pair.getRight();

        List<CrowdfundingInfoVo> infoVos = convert(cfCaseModels);
        return Pair.of(total, infoVos.stream().sorted(Comparator.comparing(CrowdfundingInfoVo::getCreateTime).reversed()).collect(Collectors.toList()));
    }

    @Override
    public Pair<Long, List<CrowdfundingInfoVo>> caseSearchByEs(CaseSearchVo caseSearchVo) {
        CfCaseIndexSearchParam searchParam = convertCaseQuerySearchParam(caseSearchVo);

        Pair<Long, List<CfCaseModel>> pair = searchFromEs(searchParam);

        long total = pair.getLeft();
        List<CfCaseModel> cfCaseModels = pair.getRight();

        List<CrowdfundingInfoVo> infoVos = convert(cfCaseModels);
        return Pair.of(total, infoVos.stream().sorted(Comparator.comparing(CrowdfundingInfoVo::getCreateTime).reversed()).collect(Collectors.toList()));
    }

    private CfCaseIndexSearchParam convertCaseQuerySearchParam(CaseSearchVo caseSearchVo) {
        //处理CaseUserID
        long userId = 0;
        String caseUserId = caseSearchVo.getCaseUserId();
        if (StringUtils.isNotBlank(caseUserId)) {
            try {
                userId = Long.valueOf(caseUserId);
            } catch (NumberFormatException e) {
                log.warn("caseUserId valueOf error", caseUserId);
            } catch (Exception e) {
                log.error("AdminApproveService approveSearch error", e);
            }
            //如果不成功走解密路线
            if (userId <= 0) {
                userId = shuidiCipher.decryptUserId(caseUserId);
            }
        }

        String userMobile = null;
        String mobile = caseSearchVo.getMobile();
        if (StringUtils.isNotEmpty(mobile)) {
            //如果userId为空并且mobile不为空 对mobile进行处理
            UserInfoModel userInfoModel = this.userInfoServiceBiz.getUserInfoByMobile(mobile);
            if (userInfoModel != null && StringUtils.isNotBlank(userInfoModel.getCryptoMobile())) {
                userId = userInfoModel.getUserId();
                userMobile = null;
            } else {
                userMobile = oldShuidiCipher.aesEncrypt(mobile);
            }
        }

        Integer status = caseSearchVo.getStatus();
        if (status != null && status >= 0 && status < CrowdfundingStatus.values().length) {
            CrowdfundingStatus statusEnum = CrowdfundingStatus.values()[status];
            status = statusEnum.value();
        }

        CfCaseIndexSearchParam searchParam = new CfCaseIndexSearchParam();
        //先复制通用的
        BeanUtils.copyProperties(caseSearchVo, searchParam);

        //处理金额元转分
        searchParam.setTargetAmountStart(caseSearchVo.getTargetAmountStart() == null ? null : CurrencyUtil.yuan2Fen(new BigDecimal(caseSearchVo.getTargetAmountStart())));
        searchParam.setTargetAmountEnd(caseSearchVo.getTargetAmountEnd() == null ? null : CurrencyUtil.yuan2Fen(new BigDecimal(caseSearchVo.getTargetAmountEnd())));
        searchParam.setAmountStart(caseSearchVo.getAmountStart() == null ? null : CurrencyUtil.yuan2Fen(new BigDecimal(caseSearchVo.getAmountStart())));
        searchParam.setAmountEnd(caseSearchVo.getAmountEnd() == null ? null : CurrencyUtil.yuan2Fen(new BigDecimal(caseSearchVo.getAmountEnd())));

        searchParam.setTypes(Lists.newArrayList(0));
        searchParam.setTitle(StringUtils.trimToNull(caseSearchVo.getTitle()));
        searchParam.setMobile(StringUtils.trimToNull(userMobile));

        if (userId > 0) {
            searchParam.setUserIds(Lists.newArrayList(userId));
        }

        Integer id = caseSearchVo.getId();
        if (id != null) {
            searchParam.setIds(Lists.newArrayList(id));
        } else if (CollectionUtils.isNotEmpty(caseSearchVo.getExtCaseIds())) {
            searchParam.setIds(caseSearchVo.getExtCaseIds());
        }
        if (status != null && status >= 0) {
            searchParam.setStatuses(Lists.newArrayList(status));
        }

        String startTime = StringUtils.trimToNull(caseSearchVo.getStartTime());
        String endTime = StringUtils.trimToNull(caseSearchVo.getEndTime());
        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            long auditCommitTimeStart = convertToMills(startTime, DateUtil.DATETIME_PATTERN_2);
            long auditCommitTimeEnd = convertToMills(endTime, DateUtil.DATETIME_PATTERN_2);
            if (auditCommitTimeStart > 0 && auditCommitTimeEnd > 0) {
                searchParam.setAuditCommitTimeStart(auditCommitTimeStart);
                searchParam.setAuditCommitTimeEnd(auditCommitTimeEnd);
            }
        }

        /**
         * handle取值范围：0，1，2
         * operationStatus取值范围：3，4，5
         * 参考CrowdfundingOperationEnum
         */
        Integer handle = caseSearchVo.getHandle();
        Integer operationStatus = caseSearchVo.getOperationStatus();
        if (handle != null) {
            searchParam.setOperations(Lists.newArrayList(handle));
        } else if (operationStatus != null) {
            searchParam.setOperations(Lists.newArrayList(operationStatus));
        } else {
            searchParam.setOperations(Arrays.stream(CrowdfundingOperationEnum.values()).map(CrowdfundingOperationEnum::value).collect(Collectors.toList()));
        }

        Integer refuseCountHandle = caseSearchVo.getRefuseCountHandle();
        if (refuseCountHandle != null) {
            if (refuseCountHandle == 0) {
                //原逻辑是 > 3
                searchParam.setRefuseCounts(Stream.iterate(4, item -> item + 1).limit(100).collect(Collectors.toList()));
            } else if (refuseCountHandle == 1) {
                searchParam.setRefuseCounts(Lists.newArrayList(1, 2, 3));
            } else if (refuseCountHandle == 2) {
                searchParam.setRefuseCounts(Lists.newArrayList(0));
            }
        }

        /**
         * finished == 0，代表未结束，查询结束时间大于此刻时间
         * finished == 0，代表已结束，查询结束时间小于此刻时间
         */
        Integer finished = caseSearchVo.getFinished();
        if (finished != null) {
            if (finished == 0) {
                searchParam.setEndTimeStart(System.currentTimeMillis());
            } else if (finished == 1) {
                searchParam.setEndTimeEnd(System.currentTimeMillis());
            }
        }

        //设置患者年龄条件,出生日期=当前年份-年龄
        int start = caseSearchVo.getPatientAgeEnd() != null ? DateTime.now().getYear()-caseSearchVo.getPatientAgeEnd() : 0;
        int end = caseSearchVo.getPatientAgeStart() != null ? DateTime.now().getYear()-caseSearchVo.getPatientAgeStart() : 0;
        if (start > 0) {
            searchParam.setPatientAgeStart(start);
        }
        if (end > 0) {
            searchParam.setPatientAgeEnd(end);
        }

        searchParam.setFrom((caseSearchVo.getCurrent() - 1) * caseSearchVo.getPageSize());
        searchParam.setSize(caseSearchVo.getPageSize());

        return searchParam;
    }


    @Diff(diffMethod = "contactSearchFromEs", diffCompare = "com.shuidihuzhu.cf.diff.ContactSearchCompare")
    @Override
    public List<CrowdfundingInfoVo> contactSearch(String mobile,
                                                  String name, Integer id,
                                                  String title, String caseUserId,
                                                  Integer callStatus, Integer isContact,
                                                  Integer sortHandle, String startTime,
                                                  String endTime, Integer handle,
                                                  Integer current, Integer pageSize) {

        BasicExample basicExample = new BasicExample();
        BasicExample.Criteria criteria = basicExample.or();

        if (isContact != null) {
            adminApproveService.contactHandle(isContact, criteria);
        }
        if (callStatus != null) {
            criteria.andEqualTo("cfo.call_status", CfCallOutConditionTypeEnum.getByValue(callStatus).getValue());
        }
        if (StringUtil.isNotBlank(startTime) && StringUtil.isNotBlank(endTime)) {
            criteria.andGreaterThanOrEqualTo("ci.create_time", startTime);
            criteria.andLessThanOrEqualTo("ci.create_time", endTime);
        }
        List<Integer> allNormalList = adminApproveService.getNormalOperationList();
        boolean isClearFindHandle = StringUtil.isNotBlank(title) || id != null || StringUtil.isNotBlank(name)
                || StringUtil.isNotBlank(mobile) || StringUtils.isNotBlank(caseUserId);
        if (isClearFindHandle) {
            basicExample = new BasicExample();
            criteria = basicExample.or();
            adminApproveService.findInText(criteria, title.trim(), id, name.trim(), mobile.trim(), caseUserId.trim());
        } else if (handle != null) {// 垃圾案例筛选
            adminApproveService.handleFields(handle, CrowdfundingType.SERIOUS_ILLNESS, allNormalList, basicExample,
                    criteria);
        } else {
            criteria.andIn("cfo.operation", allNormalList);
        }

        if (sortHandle != null) {
            basicExample.setOrderByClause(ApproveListOrderByWhat.getValue(sortHandle));
        } else {
            basicExample.setOrderByClause(ApproveListOrderByWhat.CREATE_TIME.getValue());
        }
        criteria.andEqualTo("type", CrowdfundingType.SERIOUS_ILLNESS.value());

        // 排除掉首次审核未通过的案例
        criteria.andIn("cie.first_approve_status", Arrays.asList(FirstApproveStatusEnum.DEFAULT.getCode(),
                FirstApproveStatusEnum.APPLY_SUCCESS.getCode()));

        return crowdfundingInfoBiz.selectByExampleJoin(basicExample, current, pageSize);
    }

    @Override
    public Pair<Long, List<CrowdfundingInfoVo>> contactSearchFromEs(String mobile,
                                                                    String name, Integer id,
                                                                    String title, String caseUserId,
                                                                    Integer callStatus, Integer isContact,
                                                                    Integer sortHandle, String startTime,
                                                                    String endTime, Integer handle,
                                                                    Integer current, Integer pageSize, String content) {

        CfCaseIndexSearchParam searchParam = convertContactSearchParam(mobile, name, id, title, caseUserId, callStatus,
                isContact, startTime, endTime, handle, current, pageSize, content);
        Pair<Long, List<CfCaseModel>> pair = searchFromEs(searchParam);

        long total = pair.getLeft();
        List<CfCaseModel> cfCaseModels = pair.getRight();

        List<CrowdfundingInfoVo> infoVos = convert(cfCaseModels);
        return Pair.of(total, infoVos.stream().sorted(Comparator.comparing(CrowdfundingInfoVo::getCreateTime).reversed()).collect(Collectors.toList()));
    }

    /**
     * 查询es
     *
     * @param searchParam
     * @return
     */
    private Pair<Long, List<CfCaseModel>> searchFromEs(CfCaseIndexSearchParam searchParam) {
        if (searchParam == null) {
            return Pair.of(0L, Lists.newArrayList());
        }
        SearchRpcResult<CfCaseIndexSearchResult> searchRpcResult = cfSearchClient.cfCaseIndexSearch(searchParam);

        if (searchRpcResult == null || searchRpcResult.getCode() != 0
                || searchRpcResult.getData() == null
                || CollectionUtils.isEmpty(searchRpcResult.getData().getModels())) {
            return Pair.of(0L, Lists.newArrayList());
        }

        return Pair.of(searchRpcResult.getData().getTotal(), searchRpcResult.getData().getModels());
    }

    private CfCaseIndexSearchParam convertContactSearchParam(String mobile, String name, Integer id, String title,
                                                             String caseUserId, Integer callStatus, Integer isContact,
                                                             String startTime, String endTime, Integer handle,
                                                             Integer current, Integer pageSize,
                                                             String content) {

        //处理CaseUserID
        long userId = 0;
        if (StringUtils.isNotBlank(caseUserId)) {
            try {
                userId = Long.valueOf(caseUserId);
            } catch (NumberFormatException e) {
                log.warn("caseUserId valueOf error", caseUserId);
            } catch (Exception e) {
                log.error("AdminApproveService approveSearch error", e);
            }
            //如果不成功走解密路线
            if (userId <= 0) {
                userId = shuidiCipher.decryptUserId(caseUserId);
            }
        }

        String userMobile = null;
        if (StringUtils.isNotEmpty(mobile)) {
            //如果userId为空并且mobile不为空 对mobile进行处理
            UserInfoModel userInfoModel = this.userInfoServiceBiz.getUserInfoByMobile(mobile);
            if (userInfoModel != null && StringUtils.isNotBlank(userInfoModel.getCryptoMobile())) {
                userId = userInfoModel.getUserId();
                userMobile = null;
            } else {
                userMobile = oldShuidiCipher.aesEncrypt(mobile);
            }
        }


        CfCaseIndexSearchParam searchParam = new CfCaseIndexSearchParam();
        searchParam.setTypes(Lists.newArrayList(CrowdfundingType.SERIOUS_ILLNESS.value()));
        searchParam.setMobile(StringUtils.trimToNull(userMobile));
        searchParam.setTitle(StringUtils.trimToNull(title));
        searchParam.setName(name);

        if (StringUtils.isNotBlank(content)) {
            searchParam.setContent(content);
        }

        if (userId > 0) {
            searchParam.setUserIds(Lists.newArrayList(userId));
        }

        /**
         * isContact == 0:代表没有沟通
         * isContact == 1:代表已沟通，包含未呼通
         */
        if (isContact != null) {
            if (isContact == 0) {
                searchParam.setCallStatuses(Lists.newArrayList(0));
            } else if (isContact == 1) {
                searchParam.setCallStatuses(Arrays.stream(CfCallOutConditionTypeEnum.values()).filter(x -> x.getValue() != 0).map(x -> x.getValue()).collect(Collectors.toList()));
            }
        }

        if (callStatus != null) {
            searchParam.setCallStatuses(Lists.newArrayList(callStatus));
        }

        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            long createTimeStart = convertToMills(startTime, DateUtil.DATETIME_PATTERN_2);
            long createTimeEnd = convertToMills(endTime, DateUtil.DATETIME_PATTERN_2);
            if (createTimeStart > 0 && createTimeEnd > 0) {
                searchParam.setCreateTimeStart(createTimeStart);
                searchParam.setCreateTimeEnd(createTimeEnd);
            }
        }


        if (id != null && id > 0) {
            searchParam.setIds(Lists.newArrayList(id));
        }

        if (handle != null) {
            CfApproveCaseHandleEnum caseHandleEnum = CfApproveCaseHandleEnum.getByValue(handle);
            switch (caseHandleEnum) {
                case OLD_REPEAT:
                    List<Integer> userIds = cfRepeatUserIdRecordBiz.selectUserId();
                    if (CollectionUtils.isNotEmpty(userIds)) {
                        searchParam.setUserIds(userIds.stream().map(x -> x.longValue()).collect(Collectors.toList()));
                    }
                    searchParam.setEndTimeStart(System.currentTimeMillis());
                    break;
                case GARBAGE:
                    searchParam.setTargetAmountEnd(100 * 100);
                    searchParam.setEndTimeStart(System.currentTimeMillis());
                    //TODO: 与原逻辑差amount比对
                    break;
                case DELAY_APPROVE:
                    searchParam.setOperations(Lists.newArrayList(CrowdfundingOperationEnum.DEFER_APPROVE.value()));
                    break;
                case DELAY_CONTACT:
                    searchParam.setOperations(Lists.newArrayList(CrowdfundingOperationEnum.DEFER_CONTACT.value()));
                    break;
                case NO_LONGER_HANDLE:
                    searchParam.setOperations(Lists.newArrayList(CrowdfundingOperationEnum.NEVER_PROCESSING.value()));
                    break;
                case REPEAT_CASE:
                    searchParam.setRepeatSummaries(AdminCfRepeatView.repeatSummaryCodeSet);
                    searchParam.setEndTimeStart(System.currentTimeMillis());
                    break;
                case TWICE_CASE:
                    searchParam.setRepeatSummaries(AdminCfRepeatView.twiceSummaryCodeSet);
                    searchParam.setEndTimeStart(System.currentTimeMillis());
                    break;
                default:
                    break;
            }
        }

        searchParam.setFirstApproveStatuses(Arrays.asList(FirstApproveStatusEnum.DEFAULT.getCode(), FirstApproveStatusEnum.APPLY_SUCCESS.getCode()));

        searchParam.setFrom((current - 1) * pageSize);
        searchParam.setSize(pageSize);

        return searchParam;
    }

    private CfCaseIndexSearchParam convertApproveSearchParam(String mobile, String name, Integer id,
                                                             String title, String caseUserId, Integer refuseCountHandle,
                                                             Integer status, Integer isContact, Integer finished,
                                                             Integer operationStatus, Integer dataStatus,
                                                             String startTime, String endTime, Integer handle,
                                                             Integer pageNum, Integer pageSize, String content,int creditStatus,int deferContactReasonType,
                                                             int fuwuType) {
        //处理CaseUserID
        long userId = 0;
        if (StringUtils.isNotBlank(caseUserId)) {
            try {
                userId = Long.valueOf(caseUserId);
            } catch (NumberFormatException e) {
                log.warn("caseUserId valueOf error", caseUserId);
            } catch (Exception e) {
                log.error("AdminApproveService approveSearch error", e);
            }
            //如果不成功走解密路线
            if (userId <= 0) {
                userId = shuidiCipher.decryptUserId(caseUserId);
            }
        }

        String userMobile = null;
        if (StringUtils.isNotEmpty(mobile)) {
            //如果userId为空并且mobile不为空 对mobile进行处理
            UserInfoModel userInfoModel = this.userInfoServiceBiz.getUserInfoByMobile(mobile);
            if (userInfoModel != null && StringUtils.isNotBlank(userInfoModel.getCryptoMobile())) {
                userId = userInfoModel.getUserId();
                userMobile = null;
            } else {
                userMobile = oldShuidiCipher.aesEncrypt(mobile);
            }
        }

        if (status != null && status >= 0 && status < CrowdfundingStatus.values().length) {
            CrowdfundingStatus statusEnum = CrowdfundingStatus.values()[status];
            status = statusEnum.value();
        }

        CfCaseIndexSearchParam searchParam = new CfCaseIndexSearchParam();
        searchParam.setName(name);
        searchParam.setTypes(Lists.newArrayList(0));
        searchParam.setTitle(StringUtils.trimToNull(title));
        searchParam.setMobile(StringUtils.trimToNull(userMobile));

        if (StringUtils.isNotBlank(content)) {
            searchParam.setContent(content);
        }

        if (userId > 0) {
            searchParam.setUserIds(Lists.newArrayList(userId));
        }

        if (id != null) {
            searchParam.setIds(Lists.newArrayList(id));
        }
        if (status != null && status >= 0) {
            searchParam.setStatuses(Lists.newArrayList(status));
        }
        if (dataStatus != null) {
            searchParam.setDataStatuses(Lists.newArrayList(dataStatus));
        }
        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            long auditCommitTimeStart = convertToMills(startTime, DateUtil.DATETIME_PATTERN_2);
            long auditCommitTimeEnd = convertToMills(endTime, DateUtil.DATETIME_PATTERN_2);
            if (auditCommitTimeStart > 0 && auditCommitTimeEnd > 0) {
                searchParam.setAuditCommitTimeStart(auditCommitTimeStart);
                searchParam.setAuditCommitTimeEnd(auditCommitTimeEnd);
            }
        }

        /**
         * handle取值范围：3，4，5
         * operationStatus取值范围：0，1，2
         * 参考CrowdfundingOperationEnum
         */
        if (handle != null) {
            searchParam.setOperations(Lists.newArrayList(handle));
        } else if (operationStatus != null) {
            searchParam.setOperations(Lists.newArrayList(operationStatus));
        }

        if (handle != null && handle == CrowdfundingOperationEnum.DEFER_CONTACT.value() && deferContactReasonType > 0) {
            searchParam.setDeferContactReasonTypes(Lists.newArrayList(deferContactReasonType));
        }

        if (refuseCountHandle != null) {
            if (refuseCountHandle == 0) {
                //原逻辑是 > 3
                searchParam.setRefuseCounts(Stream.iterate(4, item -> item + 1).limit(100).collect(Collectors.toList()));
            } else if (refuseCountHandle == 1) {
                searchParam.setRefuseCounts(Lists.newArrayList(1, 2, 3));
            } else if (refuseCountHandle == 2) {
                searchParam.setRefuseCounts(Lists.newArrayList(0));
            }
        }

        /**
         * isContact == 0:代表没有沟通
         * isContact == 1:代表已沟通，包含未呼通
         */
        if (isContact != null) {
            if (isContact == 0) {
                searchParam.setCallStatuses(Lists.newArrayList(0));
            } else if (isContact == 1) {
                searchParam.setCallStatuses(Arrays.stream(CfCallOutConditionTypeEnum.values()).filter(x -> x.getValue() != 0).map(x -> x.getValue()).collect(Collectors.toList()));
            }
        }

        /**
         * finished == 0，代表未结束，查询结束时间大于此刻时间
         * finished == 0，代表已结束，查询结束时间小于此刻时间
         */
        if (finished != null) {
            if (finished == 0) {
                searchParam.setEndTimeStart(System.currentTimeMillis());
            } else if (finished == 1) {
                searchParam.setEndTimeEnd(System.currentTimeMillis());
            }
        }
        if (creditStatus > 0){
            searchParam.setCreditStatuses(Lists.newArrayList(creditStatus));
        }

        if (fuwuType > 0){
            searchParam.setFuwuType(fuwuType);
        }

        searchParam.setFrom((pageNum - 1) * pageSize);
        searchParam.setSize(pageSize);

        return searchParam;
    }

    private List<CrowdfundingInfoVo> convert(List<CfCaseModel> cfCaseModels) {
        if (CollectionUtils.isEmpty(cfCaseModels)) {
            return Lists.newArrayList();
        }
        List<Integer> ids = cfCaseModels.stream().map(CfCaseModel::getId).collect(Collectors.toList());
        List<String> infoIds = cfCaseModels.stream().map(CfCaseModel::getInfoId).collect(Collectors.toList());

        List<CrowdfundingInfo> infos = adminCrowdfundingInfoSlaveDao.getFundingInfoByIds(ids);
        List<CfInfoExt> cfInfoExts = adminCfInfoExtDao.selectByInfoUuidList(infoIds);
        List<AdminCrowdfundingOperation> operations = adminCrowdfundingOperationDao.selectByInfoUuids(infoIds);

        Map<String, CfInfoExt> infoExtMap = Maps.uniqueIndex(cfInfoExts, CfInfoExt::getInfoUuid);
        Map<String, AdminCrowdfundingOperation> operationMap = Maps.uniqueIndex(operations, AdminCrowdfundingOperation::getInfoId);

        List<CrowdfundingInfoVo> infoVos = CrowdfundingInfoMapper.INSTANCE.toCrowdfundingInfoVos(infos);

        for (CrowdfundingInfoVo infoVo : infoVos) {
            CfInfoExt cfInfoExt = infoExtMap.get(infoVo.getInfoId());
            if (cfInfoExt != null) {
                infoVo.setCryptoRegisterMobile(cfInfoExt.getCryptoRegisterMobile());
            }

            AdminCrowdfundingOperation operation = operationMap.get(infoVo.getInfoId());
            if (operation != null) {
                infoVo.setOperation(operation.getOperation());
                infoVo.setAuditCommitTime(operation.getAuditCommitTime());
                infoVo.setRefuseCount(operation.getRefuseCount());
                infoVo.setCallCount(operation.getCallCount());
                infoVo.setCallStatus(operation.getCallStatus());
                infoVo.setOperatorId(operation.getOperatorId());
                infoVo.setUserRefuseCount(operation.getUserRefuseCount());
            }
        }
        return infoVos;
    }

    private String getBySortHandle(Integer sortHandle) {
        if (sortHandle == null) {
            return ApproveListOrderByWhat.CREATE_TIME.getValue();
        } else {
            switch (ApproveListOrderByWhat.getByCode(sortHandle)) {
                case CREATE_TIME:
                    return ApproveListOrderByWhat.CREATE_TIME.getValue();
                case AMOUNT:
                    return ApproveListOrderByWhat.AMOUNT.getValue();
                case OPERATION_TIME:
                    return ApproveListOrderByWhat.OPERATION_TIME.getValue();
                case AUDIT_COMMIT_TIME:
                    return ApproveListOrderByWhat.AUDIT_COMMIT_TIME.getValue();
                case CALL_COUNT:
                    return ApproveListOrderByWhat.CALL_COUNT.getValue();
                case REFUSE_COUNT:
                    return ApproveListOrderByWhat.REFUSE_COUNT.getValue();
            }
        }
        return ApproveListOrderByWhat.CREATE_TIME.getValue();
    }

    private long convertToMills(String str, String format) {
        long mills = 0L;
        Date date = DateUtil.getStrToDate(format, str);
        if (date != null) {
            mills = date.getTime();
        }
        return mills;
    }
}
