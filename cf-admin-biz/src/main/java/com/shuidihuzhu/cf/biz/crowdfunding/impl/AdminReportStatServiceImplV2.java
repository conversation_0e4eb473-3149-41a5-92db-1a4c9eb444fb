package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.delegate.saas.AdminOrganization;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.OrganizationClientV1;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.admin.AdminOperationRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingReportBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminReportStatServiceV2;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminReportStatDao;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminWorkOrderReportDao;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminWorkOrderReportConst;
import com.shuidihuzhu.cf.enums.crowdfunding.CaseReportDealStatus;
import com.shuidihuzhu.cf.model.crowdfunding.AdminReportDataVo;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkReportMap;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport;
import com.shuidihuzhu.cf.model.report.EntryStatNum;
import com.shuidihuzhu.cf.model.report.ReportStatTotal;
import com.shuidihuzhu.common.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.util.CellRangeAddress;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AdminReportStatServiceImplV2 implements AdminReportStatServiceV2 {

    @Autowired
    private AdminReportStatDao adminReportStatDao;
    @Autowired
    private AdminOperationRecordBiz adminOperationRecordBiz;
    @Autowired
    private AdminCrowdfundingReportBiz crowdfundingReportBiz;
    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;
    @Autowired
    private OrganizationClientV1 orgClientV1;

    private final List<String> REPORT_ORG_NAME_LIST = Lists.newArrayList("水滴筹", "筹款顾问", "北京顾问团队", "举报组");

    private AtomicLong fromId = new AtomicLong(0);

    // 查询用户
    @Override
    public ReportStatTotal.ReportWorkOrderTotalCount queryReportTotalCount()  {

        ReportStatTotal.ReportWorkOrderTotalCount reportTotalCount = new  ReportStatTotal.ReportWorkOrderTotalCount();
        Date todayDate = new DateTime().withHourOfDay(0).withMinuteOfHour(0).withSecondOfMinute(0).toDate();
        Date now = new Date();

        reportTotalCount.setAssignWorkOrderUserNum(getAssignOrderOperatorIds(todayDate, now).size());
        reportTotalCount.setDutyUserNum(adminOperationRecordBiz.selectByDateAndOrg(Integer.valueOf(com.shuidihuzhu.msg.util.DateUtil.getDate2YMDStr(new Date())),
                REPORT_ORG_NAME_LIST).size());

        reportTotalCount.setNewlyAddReportWorkOrder(buildNewlyAddReportWorkOrder(todayDate));
        reportTotalCount.setFinishedReportWorkOrder(buildFinishedReportWorkOrder(todayDate));
        reportTotalCount.setRemainReportWorkOrder(buildRemainWorkOrder(todayDate));

        return reportTotalCount;
    }

    private Set<Integer> getAssignOrderOperatorIds(Date from, Date to) {
        List<ReportStatTotal.SimpleWorkIdAndOperatorId> assignOperatorAndWorkIds = adminReportStatDao.getHandlerUserByAssignTime(from, to);
        Set<Integer> userIds = Sets.newHashSet();
        assignOperatorAndWorkIds.forEach((item)->{userIds.add(item.getOperatorId());});
        return userIds;
    }

    private ReportStatTotal.NewlyAddReportWorkOrder buildNewlyAddReportWorkOrder(Date todayDate) {

        ReportStatTotal.NewlyAddReportWorkOrder newlyAddReportWorkOrder = new ReportStatTotal.NewlyAddReportWorkOrder();
        List<ReportStatTotal.SimpleWorkIdAndOperatorId> todayReportWork = adminReportStatDao.queryWorkOrderIdAndOperatorId(todayDate);

        if (CollectionUtils.isEmpty(todayReportWork)) {
            return  newlyAddReportWorkOrder;
        }

        List<Long> assignedWorkIds = Lists.newArrayList();
        List<Long> noAssignedWorkIds = Lists.newArrayList();

        for (ReportStatTotal.SimpleWorkIdAndOperatorId workIdAndOperatorId : todayReportWork) {
            if (workIdAndOperatorId.getOperatorId() != 0)  {
                assignedWorkIds.add(workIdAndOperatorId.getWorkOrderId());
            }  else {
                noAssignedWorkIds.add(workIdAndOperatorId.getWorkOrderId());
            }
        }

        int assignedReportEntry = queryReportIdsNumByPartition(assignedWorkIds).size();
        int noAssignReportEntry = queryReportIdsNumByPartition(noAssignedWorkIds).size();

        newlyAddReportWorkOrder.setTotalWorkOrderNum(todayReportWork.size());
        newlyAddReportWorkOrder.setTotalReportEntry(assignedReportEntry + noAssignReportEntry);
        newlyAddReportWorkOrder.setAssignedWorkOrderNum(assignedWorkIds.size());
        newlyAddReportWorkOrder.setAssignedReportEntry(assignedReportEntry);
        newlyAddReportWorkOrder.setNoAssignWorkOrderNum(noAssignedWorkIds.size());
        newlyAddReportWorkOrder.setNoAssignReportEntry(noAssignReportEntry);
        return newlyAddReportWorkOrder;
    }

    private List<Integer> queryReportIdsNumByPartition(List<Long> totalWorkOrderIds) {

        if (CollectionUtils.isEmpty(totalWorkOrderIds)) {
            return Lists.newArrayList();
        }

        Set<Integer> totalReportIds = Sets.newHashSet();
        List<List<Long>> totalPartition = Lists.partition(totalWorkOrderIds, 100);
        for (List<Long> workIds : totalPartition) {
            totalReportIds.addAll(adminReportStatDao.queryReportIdByWorkIds(workIds));
        }

        return Lists.newArrayList(totalReportIds);
    }


    private ReportStatTotal.FinishedReportWorkOrder buildFinishedReportWorkOrder(Date todayDate) {

        ReportStatTotal.FinishedReportWorkOrder finishReportWork = new ReportStatTotal.FinishedReportWorkOrder();
        List<ReportStatTotal.SimpleOrderHandleResult> orderHandleResult = adminReportStatDao.queryOrderHandleResult(todayDate,
                Lists.newArrayList(AdminWorkOrderReportConst.DealResult.DEAL_COMPLETE.getCode(),
                    AdminWorkOrderReportConst.DealResult.NO_DEAL.getCode()));

        if (CollectionUtils.isEmpty(orderHandleResult)) {
            return finishReportWork;
        }

        List<Long> finishedWorkOrderIds = Lists.newArrayList();
        List<Long> noNeedHandleWorkOrderIds = Lists.newArrayList();
        int newAddWorkOrderNum = 0;
        for (ReportStatTotal.SimpleOrderHandleResult orderHandle : orderHandleResult) {
            if (orderHandle.getDealResult() == AdminWorkOrderReportConst.DealResult.DEAL_COMPLETE.getCode()) {
                finishedWorkOrderIds.add(orderHandle.getWorkOrderId());
            } else {
                noNeedHandleWorkOrderIds.add(orderHandle.getWorkOrderId());
            }

            if (orderHandle.getCreateTime().after(todayDate)) {
                ++newAddWorkOrderNum;
            }
        }

        List<Integer> finishedReportEntryIds = queryReportIdsNumByPartition(finishedWorkOrderIds);
        List<Integer> noNeedHandleReportEntryIds = queryReportIdsNumByPartition(noNeedHandleWorkOrderIds);

        finishReportWork.setTotalWorkOrderNum(orderHandleResult.size());
        finishReportWork.setTotalReportEntry(finishedReportEntryIds.size() + noNeedHandleReportEntryIds.size());
        finishReportWork.setNewAddWorkOrderNum(newAddWorkOrderNum);
        finishReportWork.setLeftOverWorkOrderNum(orderHandleResult.size() - newAddWorkOrderNum);

        // 处理完成
        finishReportWork.setFinishedWorkOrderNum(finishedWorkOrderIds.size());
        finishReportWork.setFinishedReportEntry(finishedReportEntryIds.size());
        finishReportWork.setFinishedReportEntryStats(buildReportEntryStat(finishedReportEntryIds));

        // 不需要处理
        finishReportWork.setNoNeedHandleWorkOrderNum(noNeedHandleWorkOrderIds.size());
        finishReportWork.setNoNeedHandleReportEntry(noNeedHandleReportEntryIds.size());
        finishReportWork.setNoNeedHandleEntryStats(buildReportEntryStat(noNeedHandleReportEntryIds));

        return finishReportWork;
    }

    private List<EntryStatNum> buildReportEntryStat(List<Integer> reportIds) {

        List<EntryStatNum> entryStatNumList = Lists.newArrayList();
        List<CrowdfundingReport> reportList = crowdfundingReportBiz.getListByReportIds(reportIds);
        if (CollectionUtils.isEmpty(reportList)) {
            return entryStatNumList;
        }
        logNotExistReport(reportIds, reportList);

        Map<Integer, Integer> statNumMap = Maps.newTreeMap();
        for (CrowdfundingReport report : reportList) {
            Integer dealStatus = report.getDealStatus();
            Integer num = statNumMap.get(dealStatus);
            statNumMap.put(dealStatus, num == null ? 1 : num + 1);

        }

        for (Map.Entry<Integer, Integer> entry : statNumMap.entrySet()) {
            entryStatNumList.add(new EntryStatNum(entry.getKey(), entry.getValue()));
        }

        return entryStatNumList;
    }

    private static void logNotExistReport(List<Integer> reportIds,  List<CrowdfundingReport> reportList) {
        Set<Integer> existIds = Sets.newHashSet();
        reportList.forEach((item)->{existIds.add(item.getId());});
        Set<Integer> notExistIds = Sets.newHashSet();

        reportIds.forEach((id)->{if (!existIds.contains(id)) {notExistIds.add(id);} });

        log.info("举报条目的查找。原id.size:{}, 找出id.size:{}, 差值是：{}", reportIds.size(), existIds.size(), notExistIds);
    }

    // 剩余工单
    private ReportStatTotal.RemainReportWorkOrder buildRemainWorkOrder(Date todayDate) {

        ReportStatTotal.RemainReportWorkOrder reportWorkOrder = new ReportStatTotal.RemainReportWorkOrder();
        List<ReportStatTotal.SimpleOrderHandleResult> noHandleWorks = adminReportStatDao.queryNoHandleWorks(fromId.get(),
                Lists.newArrayList(AdminWorkOrderReportConst.DealResult.DEFAULT.getCode(),
                AdminWorkOrderReportConst.DealResult.REPORT_FOLLOW.getCode()), Lists.newArrayList());

        if (CollectionUtils.isEmpty(noHandleWorks)) {
            return reportWorkOrder;
        }
        fromId.set(noHandleWorks.get(0).getId());

        int newlyAddWorkOrder = 0;
        List<Long> handingWorkIds = Lists.newArrayList();
        List<Long> noAssignedWorkIds = Lists.newArrayList();
        List<Long> noBeginHandleWorkIds = Lists.newArrayList();

        for (ReportStatTotal.SimpleOrderHandleResult handleResult : noHandleWorks) {

            if (handleResult.getCreateTime().after(todayDate)) {
                ++newlyAddWorkOrder;
            }

            if (handleResult.getOperatorId() > 0) {
                if (handleResult.getDealResult() ==  AdminWorkOrderReportConst.DealResult.REPORT_FOLLOW.getCode()) {
                    handingWorkIds.add(handleResult.getWorkOrderId());
                } else {
                    noBeginHandleWorkIds.add(handleResult.getWorkOrderId());
                }
            } else {
                noAssignedWorkIds.add(handleResult.getWorkOrderId());
            }
        }

        List<Integer> handingReportEntryIds = queryReportIdsNumByPartition(handingWorkIds);
        List<Integer> noAssignedReportEntryIds = queryReportIdsNumByPartition(noAssignedWorkIds);
        List<Integer> noBeginHandleReportEntryIds = queryReportIdsNumByPartition(noBeginHandleWorkIds);

        reportWorkOrder.setTotalWorkOrderNum(noHandleWorks.size());
        reportWorkOrder.setTotalReportEntry(handingReportEntryIds.size() + noAssignedReportEntryIds.size()
                                + noBeginHandleReportEntryIds.size());

        reportWorkOrder.setNewAddWorkOrderNum(newlyAddWorkOrder);
        reportWorkOrder.setLeftOverWorkOrderNum(noHandleWorks.size() - newlyAddWorkOrder);

        reportWorkOrder.setHandingWorkOrderNum(handingWorkIds.size());
        reportWorkOrder.setHandingReportEntry(handingReportEntryIds.size());
        reportWorkOrder.setHandingReportEntryStats(buildReportEntryStat(handingReportEntryIds));

        reportWorkOrder.setNoAssignedWorkOrderNum(noAssignedWorkIds.size());
        reportWorkOrder.setNoAssignedReportEntry(noAssignedReportEntryIds.size());
        reportWorkOrder.setNoAssignedReportEntryStats(buildReportEntryStat(noAssignedReportEntryIds));

        reportWorkOrder.setNoBeginHandleWorkOrderNum(noBeginHandleWorkIds.size());
        reportWorkOrder.setNoBeginHandleReportEntry(noBeginHandleReportEntryIds.size());
        reportWorkOrder.setNoBeginHandleEntryStats(buildReportEntryStat(noBeginHandleReportEntryIds));

        return reportWorkOrder;
    }

    @Override
    public List<ReportStatTotal.ReportWorkOrderUserProcess> queryReportUserCount() {

        Date todayDate = new DateTime().withHourOfDay(0).withMinuteOfHour(0).withSecondOfMinute(0).toDate();
        Date now = new Date();

        List<ReportStatTotal.SimpleWorkIdAndOperatorId> assignOperatorAndWorkIds = adminReportStatDao.getHandlerUserByAssignTime(todayDate, now);
        if (CollectionUtils.isEmpty(assignOperatorAndWorkIds)) {
            return Lists.newArrayList();
        }

        Map<Integer, ReportStatTotal.ReportWorkOrderUserProcess> userId2WorkOrderUser = buildReportWorkOrderUserProcessMap(assignOperatorAndWorkIds);

        fillFinishWorkData(userId2WorkOrderUser,  todayDate);
        fillNotHandle(userId2WorkOrderUser,  todayDate);
        fillLostOfContact(userId2WorkOrderUser, todayDate, now);
        return Lists.newArrayList(userId2WorkOrderUser.values());
    }

    // 已领取工单数
    private Map<Integer, ReportStatTotal.ReportWorkOrderUserProcess> buildReportWorkOrderUserProcessMap(List<ReportStatTotal.SimpleWorkIdAndOperatorId>
                                                                                                                assignOperatorAndWorkIds) {
        Map<Integer, ReportStatTotal.ReportWorkOrderUserProcess> userId2WorkOrderUser = Maps.newHashMap();

        if (CollectionUtils.isEmpty(assignOperatorAndWorkIds)) {
            return userId2WorkOrderUser;
        }

        for (ReportStatTotal.SimpleWorkIdAndOperatorId assign : assignOperatorAndWorkIds) {
            ReportStatTotal.ReportWorkOrderUserProcess userProcess = userId2WorkOrderUser.get(assign.getOperatorId());
            if (userProcess == null) {
                userProcess = new  ReportStatTotal.ReportWorkOrderUserProcess();
                userId2WorkOrderUser.put(assign.getOperatorId(), userProcess);
            }
            userProcess.getAssignWorkOrderIds().add(assign.getWorkOrderId());
        }

        Map<Integer,String> userMap = buildUserId2NameMap(Lists.newArrayList(userId2WorkOrderUser.keySet()));
        Map<Integer,String> orgMap = buildUserId2OrgNameExcludeReportOrg(Lists.newArrayList(userId2WorkOrderUser.keySet()));

        for (Map.Entry<Integer, ReportStatTotal.ReportWorkOrderUserProcess> entry : userId2WorkOrderUser.entrySet()) {
            ReportStatTotal.ReportWorkOrderUserProcess current = entry.getValue();

            current.setUserId(entry.getKey());
            current.setUserName(userMap.get(entry.getKey()));
            // 组织
            current.setOrgName(StringUtils.trimToEmpty(orgMap.get(entry.getKey())));
            current.setAssignWorkOrderNum(current.getAssignWorkOrderIds().size());
            current.setAssignReportEntry(queryReportIdsNumByPartition(current.getAssignWorkOrderIds()).size());
        }
        return userId2WorkOrderUser;
    }

    private Map<Integer, String> buildUserId2NameMap(List<Integer> userIds) {

        AuthRpcResponse<List<AdminUserAccountModel>> rpcResponse = seaAccountClientV1.getUserAccountsByIds(userIds);

        Map<Integer,String> userMap = Maps.newHashMap();
        if (rpcResponse != null && rpcResponse.isSuccess()){
            List<AdminUserAccountModel> accountModels = rpcResponse.getResult();
            userMap.putAll(accountModels.stream().collect(Collectors.toMap(AdminUserAccountModel::getId, AdminUserAccountModel::getName)));
        }
        return userMap;
    }

    private Map<Integer, String> buildUserId2OrgNameExcludeReportOrg(List<Integer> userIds) {

        Map<Integer, String> orgMap = Maps.newHashMap();
        AuthRpcResponse<Map<Integer, List<AdminOrganization>>> userRoleModel = orgClientV1.getUserOrgs(userIds);
        if (userRoleModel == null || userRoleModel.getResult() == null ) {
            return orgMap;
        }

        String reportOrgName = Joiner.on("-").join(REPORT_ORG_NAME_LIST);
        for (Map.Entry<Integer, List<AdminOrganization>> entry : userRoleModel.getResult().entrySet()) {
            List<String> orgNameList = Lists.newArrayList();
            for (AdminOrganization org : entry.getValue()) {
                orgNameList.add(org.getName());
            }
            String orgName = Joiner.on("-").join(orgNameList);

            if (!reportOrgName.equals(orgName)) {
                orgMap.put(entry.getKey(), Joiner.on("-").join(orgNameList));
            }
        }

        return orgMap;
    }
    // 处理完成
    private void fillFinishWorkData(Map<Integer, ReportStatTotal.ReportWorkOrderUserProcess> userId2WorkOrderUser, Date todayDate) {
        List<ReportStatTotal.SimpleOrderHandleResult> orderHandleResult = adminReportStatDao.queryOrderHandleResult(todayDate,
                Lists.newArrayList(AdminWorkOrderReportConst.DealResult.DEAL_COMPLETE.getCode(),
                        AdminWorkOrderReportConst.DealResult.NO_DEAL.getCode()));

        if (CollectionUtils.isEmpty(orderHandleResult)) {
            return;
        }

        for (ReportStatTotal.SimpleOrderHandleResult handleResult : orderHandleResult) {
            ReportStatTotal.ReportWorkOrderUserProcess userProcess = userId2WorkOrderUser.get(handleResult.getOperatorId());
            if (userProcess == null) {
                log.info("举报报表，当前用户没有领取工单的记录。 userId:{}", handleResult.getOperatorId());
                continue;
            }
            fillNewAddOrLeftOver(todayDate, handleResult, decideOrderProcessByDealResult(handleResult, userProcess));
        }

        for (Map.Entry<Integer, ReportStatTotal.ReportWorkOrderUserProcess> entry : userId2WorkOrderUser.entrySet()) {
            ReportStatTotal.ReportWorkOrderUserProcess userProcess = entry.getValue();
            fillReportEntryStat(userProcess.getFinish());
            fillReportEntryStat(userProcess.getNoNeedHandle());
        }
    }

    private ReportStatTotal.ReportWorkOrderProcess decideOrderProcessByDealResult(ReportStatTotal.SimpleOrderHandleResult handleResult,
                                                                                  ReportStatTotal.ReportWorkOrderUserProcess userProcess) {

        if (handleResult.getDealResult() != AdminWorkOrderReportConst.DealResult.DEAL_COMPLETE.getCode()
            && handleResult.getDealResult() != AdminWorkOrderReportConst.DealResult.NO_DEAL.getCode()) {
            throw new IllegalArgumentException("工单的状态必须是处理完成或不需要处理");
        }

        return handleResult.getDealResult() == AdminWorkOrderReportConst.DealResult.DEAL_COMPLETE.getCode()
                ? userProcess.getFinish() : userProcess.getNoNeedHandle();
    }

    private void fillNewAddOrLeftOver(Date now, ReportStatTotal.SimpleOrderHandleResult handleResult,
                      ReportStatTotal.ReportWorkOrderProcess orderProcess) {

        orderProcess.getTotalWorkOrderIds().add(handleResult.getWorkOrderId());
        if (handleResult.getCreateTime().after(now)) {
            orderProcess.setNewAddWorkOrderNum(orderProcess.getNewAddWorkOrderNum() + 1);
        } else {
            orderProcess.setLeftOverWorkOrderNum(orderProcess.getLeftOverWorkOrderNum() + 1);
        }
    }

    private void fillReportEntryStat(ReportStatTotal.ReportWorkOrderProcess orderProcess) {
        orderProcess.setTotalWorkOrderNum(orderProcess.getTotalWorkOrderIds().size());
        List<Integer> handingReportEntryIds = queryReportIdsNumByPartition(orderProcess.getTotalWorkOrderIds());

        orderProcess.setTotalReportEntry(handingReportEntryIds.size());
        orderProcess.setReportEntryStats(buildReportEntryStat(handingReportEntryIds));
    }

    // 标记失联
    private void fillLostOfContact(Map<Integer, ReportStatTotal.ReportWorkOrderUserProcess> userId2WorkOrderUser,
                                   Date today, Date now) {

        for (Map.Entry<Integer, ReportStatTotal.ReportWorkOrderUserProcess> entry :  userId2WorkOrderUser.entrySet()) {
            entry.getValue().setLostCaseNum( adminReportStatDao.getUserLostNumByDate(today,
                    now, entry.getKey()));
        }
    }

    // 处理中的
    private void fillNotHandle(Map<Integer, ReportStatTotal.ReportWorkOrderUserProcess> userId2WorkOrderUser, Date todayDate) {
        List<ReportStatTotal.SimpleOrderHandleResult> noHandleWorks = adminReportStatDao.queryNoHandleWorks(fromId.get(),
                Lists.newArrayList(AdminWorkOrderReportConst.DealResult.DEFAULT.getCode(),
                        AdminWorkOrderReportConst.DealResult.REPORT_FOLLOW.getCode()), userId2WorkOrderUser.keySet());

        if (CollectionUtils.isEmpty(noHandleWorks)) {
            return;
        }

        for (ReportStatTotal.SimpleOrderHandleResult handleResult : noHandleWorks) {
            fillHanding(handleResult, todayDate, userId2WorkOrderUser.get(handleResult.getOperatorId()));
        }

        for (Map.Entry<Integer, ReportStatTotal.ReportWorkOrderUserProcess> entry : userId2WorkOrderUser.entrySet()) {

            ReportStatTotal.ReportWorkOrderUserProcess userProcess = entry.getValue();
            fillReportEntryStat(userProcess.getHanding());

            //未处理的数据
            userProcess.setNoBeginHandleWorkOrder(userProcess.getNoBeginHandleWorkOrderIds().size());
            userProcess.setNoBeginHandleReportEntry(queryReportIdsNumByPartition(userProcess
                    .getNoBeginHandleWorkOrderIds()).size());
        }
    }

    private void fillHanding(ReportStatTotal.SimpleOrderHandleResult handleResult, Date todayDate,
                             ReportStatTotal.ReportWorkOrderUserProcess userProcess) {

        if (handleResult.getDealResult() == AdminWorkOrderReportConst.DealResult.DEFAULT.getCode()) {
            userProcess.getNoBeginHandleWorkOrderIds().add(handleResult.getWorkOrderId());
        } else {
            userProcess.getHanding().getTotalWorkOrderIds().add(handleResult.getWorkOrderId());
            if (handleResult.getCreateTime().after(todayDate)) {
                userProcess.getHanding().setNewAddWorkOrderNum(userProcess.getHanding().getNewAddWorkOrderNum() + 1);
            } else {
                userProcess.getHanding().setLeftOverWorkOrderNum(userProcess.getHanding().getLeftOverWorkOrderNum() + 1);
            }
        }
    }

}













