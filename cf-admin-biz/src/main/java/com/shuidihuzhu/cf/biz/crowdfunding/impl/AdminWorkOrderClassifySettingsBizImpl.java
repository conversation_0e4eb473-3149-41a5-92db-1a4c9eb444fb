package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderClassifySettingsBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminWorkOrderClassifySettingsDao;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminWorkOrderClassifySettingsRecordDao;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderClassifySettings;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderClassifySettingsRecord;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderClassifySettingsVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AdminWorkOrderClassifySettingsBizImpl implements AdminWorkOrderClassifySettingsBiz {

    @Autowired
    SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    private AdminWorkOrderClassifySettingsDao classifySettingsDao;

    @Autowired
    private AdminWorkOrderClassifySettingsRecordDao recordDao;

    private static List<String> forbidAbort = Lists.newArrayList("水滴筹-提现&退款", "水滴筹-发起相关", "线索流转至1V1服务", "提现失败", "退款失败", "单笔退款");

    private static final Long FIRST_LEVEL_PARENT_ID = 0L;
    public static final int IS_DELETE = 1;
    public static final int NO_DELETE = 0;
    @Override
    public List<AdminWorkOrderClassifySettingsVo> queryAllValidSettings(Integer available) {

        List<AdminWorkOrderClassifySettingsVo> settingsVos = new ArrayList<>();

        List<AdminWorkOrderClassifySettings> allValidSettings = classifySettingsDao.selectAllClassifySettingsByDelStatus(0);
        if (CollectionUtils.isEmpty(allValidSettings)) {
            return settingsVos;
        }

        Map<Long, List<AdminWorkOrderClassifySettings>> parentIdToSettings = allValidSettings.stream()
                .collect(Collectors.groupingBy(AdminWorkOrderClassifySettings::getParentId));

        log.info("parentIdToSettings: {}", parentIdToSettings);

        // 第一层的分类  其partentId为0
        List<AdminWorkOrderClassifySettings> firstLevelSettings = parentIdToSettings.get(FIRST_LEVEL_PARENT_ID);
        if (CollectionUtils.isEmpty(firstLevelSettings)) {
            return settingsVos;
        }
        //一级问题,需要按照available返回,二级返回全部
        List<AdminWorkOrderClassifySettings> filterParentClassify = firstLevelSettings.stream().filter(item -> {
            if (available == null) {
                return true;
            } else {
                return item.getAvailable() == available;
            }
        }).collect(Collectors.toList());
        firstLevelSettings = filterParentClassify;
        //第一层级排序,启动态>弃用态,同一个状态按权重排序,同权重按照id排序
        sortByMultiCondition(firstLevelSettings);
        // 在第一层分类上挂载二级分类
        firstLevelSettings.forEach(item-> {
            AdminWorkOrderClassifySettingsVo currVo = AdminWorkOrderClassifySettingsVo.valueOf(item);
            List<AdminWorkOrderClassifySettings> secondLevelSettings = parentIdToSettings.get(item.getId());
            currVo.setNext(generatorSettingsVoList(secondLevelSettings));
            //标记问题是否可以废弃
            if (forbidAbort.contains(item.getAllText())) {
                currVo.setForbidAbort(true);
            }
            settingsVos.add(currVo);

        });

        return settingsVos;
    }


    private void sortByMultiCondition(List<AdminWorkOrderClassifySettings> classifySettings) {
        Comparator<AdminWorkOrderClassifySettings> comparator = new Comparator<AdminWorkOrderClassifySettings>() {
            @Override
            public int compare(AdminWorkOrderClassifySettings o1, AdminWorkOrderClassifySettings o2) {
                //启动态>弃用态
                if (o1.getAvailable() != o2.getAvailable()) {
                    return o1.getAvailable() - o2.getAvailable();
                } else if (o1.getWeight() != o2.getWeight()) {
                    return o1.getWeight() - o2.getWeight();
                }
                return (int) (o1.getId() - o2.getId());
            }
        };
        classifySettings.sort(comparator);
    }

    private List<AdminWorkOrderClassifySettingsVo> generatorSettingsVoList(List<AdminWorkOrderClassifySettings> settings) {
        List<AdminWorkOrderClassifySettingsVo> voList = new ArrayList<>();
        if (CollectionUtils.isEmpty(settings)) {
            return voList;
        }
        sortByMultiCondition(settings);
        settings.forEach(item->{
            AdminWorkOrderClassifySettingsVo vo = AdminWorkOrderClassifySettingsVo.valueOf(item);
            if (forbidAbort.contains(vo.getAllText())) {
                vo.setForbidAbort(true);
            }
            voList.add(vo);
        });
        return voList;
    }

    @Override
    public Response insert(long parentId, int userId, String allText, boolean autoTriger) {
        //判断是否存在
        List<AdminWorkOrderClassifySettings> classifySettingsList = selectClassifySettingsByText(allText);
        if (CollectionUtils.isNotEmpty(classifySettingsList)) {
            List<Long> parentIds = classifySettingsList.stream().map(AdminWorkOrderClassifySettings::getParentId).collect(Collectors.toList());
            if (parentId == FIRST_LEVEL_PARENT_ID || parentIds.contains(parentId)) {
                return NewResponseUtil.makeFail("该分类名称已存在");
            }
        }
        // 非第一层目录，需要判断父目录现在是否存在
        AdminWorkOrderClassifySettings parentClassify = null;
        if (parentId != FIRST_LEVEL_PARENT_ID) {
            List<AdminWorkOrderClassifySettings> parentSettings = classifySettingsDao.selectClassifyByIdsAndDels(
                    Arrays.asList(parentId), Arrays.asList(NO_DELETE));
            if (CollectionUtils.isEmpty(parentSettings)) {
                return NewResponseUtil.makeFail(AdminErrorCode.CLASSIFY_SETTINGS_NOT_EXISTS.getMsg());
            }
            parentClassify = parentSettings.stream().findFirst().orElse(null);
        }

        long maxId = Optional.ofNullable(classifySettingsDao.selectMaxId()).orElse(0L);

        AdminWorkOrderClassifySettings settings = new AdminWorkOrderClassifySettings();
        settings.setParentId(parentId);
        settings.setOperatorId(userId);
        settings.setAllText(allText);
        settings.setAutoTrigger(autoTriger);
        settings.setWeight((int) maxId + 1);
        settings.setAvailable(AdminWorkOrderClassifySettings.AvailableEnum.not_activity.getCode());
        classifySettingsDao.insert(settings);
        if (settings.getId() > 0) {
            AuthRpcResponse<AdminUserAccountModel> accountResponse = seaAccountClientV1.getValidUserAccountById((int) userId);
            String operatorName = Optional.ofNullable(accountResponse.getResult()).map(AdminUserAccountModel::getName).orElse("");
            int level = (parentClassify != null) ? 2 : 1;
            String text = parentClassify != null ? parentClassify.getAllText() + "-" + allText : allText;
            AdminWorkOrderClassifySettingsRecord record = AdminWorkOrderClassifySettingsRecord.populateRecord(level, AdminWorkOrderClassifySettingsRecord.OperateTypeEnum.create,
                    text, operatorName, settings.getId());
            recordDao.insert(record);
        } else {
            log.warn("insert AdminWorkOrderClassifySettings error!");
        }
        return NewResponseUtil.makeSuccess("");
    }

    // 由于需要保存目录名称的快照（为了查看），因此不能直接在记录上修改。需要让原数据失效，在插入新数据。
    // 然后在保持
    @Override
    public Response update(long id, long parentId, int userId, String allText, boolean autoTriger) {

        // 先判断数据是否有过改动
        List<AdminWorkOrderClassifySettings> parentSettings = classifySettingsDao.selectClassifyByIdsAndDels(
                Arrays.asList(id, parentId), Arrays.asList(NO_DELETE));
        int settingsCnt = 1 + (parentId != 0 ? 1 : 0);
        if (parentSettings.size() != settingsCnt) {
            return NewResponseUtil.makeFail(AdminErrorCode.CLASSIFY_SETTINGS_HAS_MODIFY.getMsg());
        }


        AdminWorkOrderClassifySettings settings = new AdminWorkOrderClassifySettings();
        settings.setParentId(parentId);
        settings.setOperatorId(userId);
        settings.setAllText(allText);
        settings.setAutoTrigger(autoTriger);
        classifySettingsDao.insert(settings);
        log.info("AdminWorkOrderClassifySettings对象:{}", settings);
        classifySettingsDao.updateDelStatusById(Arrays.asList(id), userId, IS_DELETE);

        // 一级分类被修改, 需要重建目录关系
        if (parentId == 0) {
            List<AdminWorkOrderClassifySettings> childClassify = classifySettingsDao.selectChildClassifySettings(id, NO_DELETE);
            if (!CollectionUtils.isEmpty(childClassify)) {
                List<Long> secondOldIds = new ArrayList<>();
                childClassify.forEach(item->{secondOldIds.add(item.getId());});
                childClassify.forEach(item->{item.setParentId(settings.getId());
                                             item.setAutoTrigger(autoTriger);
                    classifySettingsDao.insert(item);});
                classifySettingsDao.updateDelStatusById(secondOldIds, userId, IS_DELETE);
            }
        }

        return NewResponseUtil.makeSuccess("");
    }

    // 一级目录下如果挂载有二级目录 则不能删除
    // 二级目录可以直接删除
    @Override
    public Response delete(long id, long parentId, int userId) {

        if (parentId == FIRST_LEVEL_PARENT_ID) {
            List<AdminWorkOrderClassifySettings> childClassify = classifySettingsDao.selectChildClassifySettings(id, NO_DELETE);
            if (!CollectionUtils.isEmpty(childClassify)) {
                log.info("id: {} 下有有效的二级目录，不能删除", id);
                return NewResponseUtil.makeFail(AdminErrorCode.ROOT_CLASSIFY_SETTINGS_CANNT_DEL.getMsg());
            }
        }

        classifySettingsDao.updateDelStatusById(Arrays.asList(id), userId, IS_DELETE);
        return NewResponseUtil.makeSuccess("");
    }

    @Override
    public List<AdminWorkOrderClassifySettings> queryAllSettings() {
        return classifySettingsDao.selectAllClassifySettingsByDelStatus(null);
    }


    @Override
    public List<AdminWorkOrderClassifySettings> selectChildClassifySettings(long parentId, int deleteStatus) {
        return classifySettingsDao.selectChildClassifySettings(parentId, deleteStatus);
    }

    @Override
    public AdminWorkOrderClassifySettings selectClassifySettingsById(long id) {
        return classifySettingsDao.selectClassifySettingsById(id);
    }

    @Override
    public List<AdminWorkOrderClassifySettings> selectClassifySettingsByText(String text) {

        return classifySettingsDao.selectClassifySettingsByText(text);

    }

    @Override
    public String joinClassifyTextBySecondId(long secondId) {

        String text = "";
        AdminWorkOrderClassifySettings secondSettings = selectClassifySettingsById(secondId);
        if (secondSettings != null) {
            AdminWorkOrderClassifySettings firstSettings = selectClassifySettingsById(secondSettings.getParentId());
            text = (firstSettings != null ? firstSettings.getAllText() : "") + "-" + secondSettings.getAllText();
        }

        log.info("信息流转工单拼接问题分类。secondId: {} allText:{}", secondId, text);

        return text;
    }

    @Override
    public Long querySecondClassifyIdByText(List<String> problemClassifys) {

        if (CollectionUtils.isEmpty(problemClassifys) || problemClassifys.size() != 2) {
            return null;
        }

        String firstClassifys = problemClassifys.get(0);
        String secondClassifys = problemClassifys.get(1);
        if (firstClassifys == null || secondClassifys == null) {
            return null;
        }

        List<AdminWorkOrderClassifySettings> allSecondSettings = selectClassifySettingsByText(secondClassifys);
        List<AdminWorkOrderClassifySettings> allFirstSettings = selectClassifySettingsByText(firstClassifys);

        for (AdminWorkOrderClassifySettings second : allSecondSettings)  {
            for (AdminWorkOrderClassifySettings first : allFirstSettings) {
                if (second.getParentId() == first.getId()) {
                    return second.getId();
                }
            }
        }

        return null;
    }

    @Override
    public List<AdminWorkOrderClassifySettingsRecord> showRecords(long problemClassify) {
        return recordDao.listRecords(problemClassify);
    }

    @Override
    public boolean changeClassify(long classifyId, int changeAction, int upId, int downId, int adminUserId) {
        AdminWorkOrderClassifySettingsRecord.OperateTypeEnum operateTypeEnum = AdminWorkOrderClassifySettingsRecord.OperateTypeEnum.findByCode(changeAction);
        AuthRpcResponse<AdminUserAccountModel> account = seaAccountClientV1.getValidUserAccountById(adminUserId);
        String operatorName = Optional.ofNullable(account.getResult()).map(AdminUserAccountModel::getName).orElse("");
        switch (operateTypeEnum) {
            case qi_yong:
            case fei_qi:
                return changeAvailableStatus(classifyId, operateTypeEnum, operatorName);
            case shang_yi:
                return swapWeight(classifyId, upId, operateTypeEnum, operatorName);
            case xia_yi:
                return swapWeight(classifyId, downId, operateTypeEnum, operatorName);
            default:
                break;
        }
        return false;
    }


    private boolean swapWeight(long classifyId, long destClassifyId, AdminWorkOrderClassifySettingsRecord.OperateTypeEnum operateTypeEnum, String operatorName) {
        AdminWorkOrderClassifySettings classifySetting = classifySettingsDao.selectClassifySettingsById(classifyId);
        AdminWorkOrderClassifySettings destClassifySetting = classifySettingsDao.selectClassifySettingsById(destClassifyId);
        if (classifySetting == null || destClassifySetting == null) {
            log.warn("changeClassify error cannot find classify:{} and destClassify:{} info", classifyId, destClassifyId);
            return false;
        }
        int weight = classifySetting.getWeight();
        int destWeight = destClassifySetting.getWeight();
        if (weight < destWeight && operateTypeEnum == AdminWorkOrderClassifySettingsRecord.OperateTypeEnum.shang_yi) {
            log.warn("问题id:{}的排序:{}高于:{}的排序:{},无法上移", classifyId, weight, destClassifyId, destWeight);
            return false;
        }
        if (weight > destWeight && operateTypeEnum == AdminWorkOrderClassifySettingsRecord.OperateTypeEnum.xia_yi) {
            log.warn("问题id:{}的排序:{}低于:{}的排序:{},无法下移", classifyId, weight, destClassifyId, destWeight);
            return false;
        }
        long parentId = classifySetting.getParentId();
        int level = 1;
        if (parentId > 0) {
            level = 2;
        }
        AdminWorkOrderClassifySettingsRecord record = AdminWorkOrderClassifySettingsRecord.populateRecord(level, operateTypeEnum, classifySetting.getAllText(), operatorName, classifyId);
        recordDao.insert(record);
        classifySettingsDao.changeWeight(classifyId, destWeight);
        classifySettingsDao.changeWeight(destClassifyId, weight);
        return true;
    }


    private boolean changeAvailableStatus(long classifyId, AdminWorkOrderClassifySettingsRecord.OperateTypeEnum operateTypeEnum, String operatorName) {
        int available = (operateTypeEnum == AdminWorkOrderClassifySettingsRecord.OperateTypeEnum.qi_yong) ? 0 : 1;
        String comment = "";
        int level = 1;
        AdminWorkOrderClassifySettings classifySettings = classifySettingsDao.selectClassifySettingsById(classifyId);
        if (classifySettings == null) {
            return false;
        }

        long parentId = classifySettings.getParentId();
        if (parentId > 0) {
            //此问题为二级问题
            AdminWorkOrderClassifySettings parentSettings = classifySettingsDao.selectClassifySettingsById(parentId);
            level = 2;
            comment = Optional.ofNullable(parentSettings).map(AdminWorkOrderClassifySettings::getAllText).orElse("") + "-"
                    + classifySettings.getAllText();

            Integer parentAvailable = Optional.ofNullable(parentSettings).map(AdminWorkOrderClassifySettings::getAvailable).orElse(0);
            //一级问题为废弃，启用二级问题直接返回false
            if (parentAvailable > 0 && operateTypeEnum == AdminWorkOrderClassifySettingsRecord.OperateTypeEnum.qi_yong) {
                return false;
            }
        } else {
            //此问题为一级
            comment = classifySettings.getAllText();
            List<AdminWorkOrderClassifySettings> childClassifySettings = classifySettingsDao.selectChildClassifySettings(classifyId, 0);
            List<AdminWorkOrderClassifySettings> needDisableList = childClassifySettings.stream().filter(item -> item.getAvailable() == 0).collect(Collectors.toList());
            //一级问题废弃先废弃对应的二级问题
            if (operateTypeEnum == AdminWorkOrderClassifySettingsRecord.OperateTypeEnum.fei_qi) {
                for (AdminWorkOrderClassifySettings needDisableItem : needDisableList) {
                    String childComment = comment + "-" + needDisableItem.getAllText();
                    AdminWorkOrderClassifySettingsRecord record = AdminWorkOrderClassifySettingsRecord.populateRecord(2, operateTypeEnum, childComment, operatorName, needDisableItem.getId());
                    changeSingleClassifyStatus(record, needDisableItem.getId(), available);
                }
            }
        }
        AdminWorkOrderClassifySettingsRecord record = AdminWorkOrderClassifySettingsRecord.populateRecord(level, operateTypeEnum, comment, operatorName, classifyId);
        return changeSingleClassifyStatus(record, classifyId, available);
    }


    private boolean changeSingleClassifyStatus(AdminWorkOrderClassifySettingsRecord record, long classifyId, int availableStatus) {
        classifySettingsDao.changeAvailableStatus(classifyId, availableStatus);
        recordDao.insert(record);
        return true;
    }

}
