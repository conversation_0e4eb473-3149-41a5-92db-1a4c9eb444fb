package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.model.admin.AdminCreditSupplement;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Created by ahrievil on 2017/2/26.
 */
public interface AdminCrowdfundingInfoStatusBiz {

    int add(CrowdfundingInfoStatus crowdfundingInfoStatus);
    int updateByInfoId(String infoUuId, int type, CrowdfundingInfoStatusEnum status);
    int updateByInfoId(String infoUuId, CrowdfundingInfoStatusEnum status);
    List<CrowdfundingInfoStatus> getByInfoUuid(String infoUuid);
    List<CrowdfundingInfoStatus> getByCaseId(int caseId);
    CrowdfundingInfoStatus getByInfoUuidAndType(String infoUuid, int type);
    int changeTypeAndInfoUuid(CrowdfundingInfoStatus crowdfundingInfoStatus);
    int updateByTypes(String infoUuid, CrowdfundingInfoStatusEnum crowdfundingInfoStatusEnum, Set<Integer> set);

    int updateByInfoUuidsAndType(int type, Set<String> infoUuidSet, int originalStatus, int targetStatus);

    List<CrowdfundingInfoStatus> getInfoUuidList(CrowdfundingInfoStatusEnum statusEnum,
                                 CrowdfundingInfoDataStatusTypeEnum dataStatusTypeEnum, int offset, int size);

    int countByTypeAndStatus(CrowdfundingInfoStatusEnum statusEnum,
                             CrowdfundingInfoDataStatusTypeEnum dataStatusTypeEnum);

    List<String> selectByAttributes(AdminCreditSupplement houseCredit, AdminCreditSupplement carCredit,
                                    CrowdfundingInfoStatusEnum statusEnum, Date statTime,
                                    Date endTime, int offset, int size);

    int getCountByAttributes(AdminCreditSupplement houseCredit, AdminCreditSupplement carCredit,
                             CrowdfundingInfoStatusEnum statusEnum, Date statTime, Date endTime);

    /**
     * 批量更新状态
     * @param infoUuIdSet
     * @param type
     * @param status
     * @return
     */
    int updateByInfoIdSet(Set<String> infoUuIdSet, CrowdfundingInfoDataStatusTypeEnum type, CrowdfundingInfoStatusEnum status);

    /**
     * 批量获取数据的案例的数据状态
     * @param infoUuidList
     * @param typeEnum
     * @return
     */
    List<CrowdfundingInfoStatus> getByInfoUuidsAndType(List<String> infoUuidList, CrowdfundingInfoDataStatusTypeEnum typeEnum);

    /**
     * 批量获取
     */
    List<CrowdfundingInfoStatus> getByInfoUuids(List<String> infoUuidList);

    List<CrowdfundingInfoStatus> getByInfoUuidsAndTypes(List<String> infoUuidList, List<CrowdfundingInfoDataStatusTypeEnum> typeEnums);
}