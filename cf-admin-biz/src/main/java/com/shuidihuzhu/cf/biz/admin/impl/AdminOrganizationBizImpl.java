package com.shuidihuzhu.cf.biz.admin.impl;

import com.google.common.base.Joiner;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.OrgMembersResult;
import com.shuidihuzhu.cf.delegate.saas.OrganizationClientV1;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.admin.AdminOrganizationBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowBiz;
import com.shuidihuzhu.cf.dao.admin.AdminOrganizationDao;
import com.shuidihuzhu.cf.dao.admin.AdminOrganizationUserMapDao;
import com.shuidihuzhu.cf.delegate.saas.SimpleOrgVo;
import com.shuidihuzhu.cf.model.admin.AdminOrganization;
import com.shuidihuzhu.cf.model.admin.AdminOrganizationUserMap;
import com.shuidihuzhu.cf.vo.crowdfunding.OrganizationEmployeesVo;
import com.shuidihuzhu.cf.vo.crowdfunding.account.SeaUserAccountVo;
import com.shuidihuzhu.client.auth.saas.model.dto.SimpleUserVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: WangYing on 2018/9/17
 */
@Service
@Slf4j
public class AdminOrganizationBizImpl implements AdminOrganizationBiz {
    @Autowired
    private AdminOrganizationDao adminOrganizationDao;
    @Autowired
    private AdminOrganizationUserMapDao adminOrganizationUserMapDao;
    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    private OrganizationClientV1 organizationClientV1;

    private LoadingCache<Integer,List<OrgMembersResult>> orgMembersResultsCache = CacheBuilder
            .newBuilder()
            .maximumSize(50)
            .expireAfterAccess(30, TimeUnit.MINUTES)
            .build(new CacheLoader<Integer, List<OrgMembersResult>>() {
                @Override
                public List<OrgMembersResult> load(Integer orgId) throws Exception {
                    List<OrgMembersResult> orgMembersResults = getOrgMembersResultByOrgId(orgId);
                    return orgMembersResults;
                }
            });

    /**
     * 查询 某个人对应的 分组、区域    比如  吉林&东北区
     */
    private LoadingCache<String,String> misOrgIdsCache = CacheBuilder
            .newBuilder()
            .maximumSize(50)
            .expireAfterAccess(5, TimeUnit.HOURS)
            .build(new CacheLoader<String, String>() {
                @Override
                public String load(String mis) throws Exception {
                    return getMisOrg(mis);
                }
            });


    private String getMisOrg(String mis){
        List<OrgMembersResult> orgMembersResultByOrgId = this.getAllOrgMembersResultByOrgId(57);
        OrgMembersResult topOrg = orgMembersResultByOrgId.stream().filter(s -> s.getOrgId() == 57).collect(Collectors.toList()).get(0);
        List<SimpleOrgVo> subOrgs = topOrg.getSubOrgs();
        // 对应的 区
        for (SimpleOrgVo areaOrg : subOrgs) {
            List<OrgMembersResult> areaOrgMemberReuslt = orgMembersResultByOrgId.stream().filter(s -> s.getOrgId() == areaOrg.getOrgId()).collect(Collectors.toList());
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(areaOrgMemberReuslt)){
                continue;
            }
            List<SimpleOrgVo> groupOrgList = areaOrgMemberReuslt.get(0).getSubOrgs();
            // 对应的组
            for (SimpleOrgVo groupOrg : groupOrgList) {
                // 拿出对应组 中的 成员
                List<OrgMembersResult> groupOrgMembersResult = orgMembersResultByOrgId.stream().filter(s -> s.getOrgId() == groupOrg.getOrgId()).collect(Collectors.toList());
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(groupOrgMembersResult)){
                    continue;
                }
                OrgMembersResult orgMembersResult = groupOrgMembersResult.get(0);
                // 查询当前查询的mis 是否在 此层级 成员里
                List<SimpleUserVo> simpleUserVos = orgMembersResult.getMembers().stream().filter(simpleUserVo -> simpleUserVo.getMis().equals(mis)).collect(Collectors.toList());
                // 如果存在 则直接return
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(simpleUserVos)){
                    return groupOrg.getOrgName()+"&"+areaOrg.getOrgName();
                }
            }
        }

        return "";
    }
    /**
     * 根据传入的 orgid 获取所有下面的节点
     * @param orgId
     * @return
     */
    private List<OrgMembersResult> getAllOrgMembersResultByOrgId(int orgId){
        List<OrgMembersResult> orgMembersResults = Lists.newArrayList();
        try {
            orgMembersResults = orgMembersResultsCache.get(orgId);
        } catch (ExecutionException e) {
            log.error(this.getClass().getSimpleName()+"  orgMembersResultsCache.get({})",orgId,e);
        }
        return orgMembersResults;
    }
    private OrgMembersResult getOrgMebmbers(int orgId) {
        AuthRpcResponse<OrgMembersResult> authRpcResponse = organizationClientV1.getOrgMebmbers(orgId);
        if (authRpcResponse.isSuccess()) {
            return authRpcResponse.getResult();
        }
        return new OrgMembersResult();
    }
    private List<OrgMembersResult> getOrgMembersResultByOrgId(int orgId){
        OrgMembersResult orgMebmbers = this.getOrgMebmbers(orgId);
        List<OrgMembersResult> orgMembersResults = Lists.newArrayList();
        orgMembersResults = this.getOrgMembersResultByOrgId(orgMebmbers.getOrgId(), orgMembersResults);
        return this.handleOrgMemberResult(orgMembersResults);
    }

    private List<OrgMembersResult> getOrgMembersResultByOrgId(int orgId,List<OrgMembersResult> orgMembersResults){
        OrgMembersResult orgMebmbers = this.getOrgMebmbers(orgId);
        if (orgMebmbers.getOrgName()!=null){
            orgMembersResults.add(orgMebmbers);
            for (SimpleOrgVo simpleOrgVo:orgMebmbers.getSubOrgs()){
                this.getOrgMembersResultByOrgId(simpleOrgVo.getOrgId(),orgMembersResults);
            }
        }
        return orgMembersResults;
    }
    /**
     * 把 当前节点下所有的节点的 人员 都添加到父级节点上
     * @param orgMembersResults
     * @return
     */
    private List<OrgMembersResult> handleOrgMemberResult(List<OrgMembersResult> orgMembersResults){
        Map<Integer, OrgMembersResult> orgIdMap = orgMembersResults.parallelStream()
                .collect(Collectors.toMap(OrgMembersResult::getOrgId, Function.identity()));
        Map<Integer, List<SimpleOrgVo>> orgIdMapSubOrgInfo = orgMembersResults.parallelStream()
                .collect(Collectors.toMap(OrgMembersResult::getOrgId, OrgMembersResult::getSubOrgs));
        for (Map.Entry<Integer,List<SimpleOrgVo>> entrySet : orgIdMapSubOrgInfo.entrySet()){
            orgIdMap.get(entrySet.getKey()).getMembers().addAll(getAllSubOrgMemberByOrgId(entrySet.getKey(),orgIdMap,orgIdMapSubOrgInfo));
        }
        // 去重userId 后  封装到 list中
        List<OrgMembersResult> orgMembersResults1 = Lists.newArrayList();
        for (Map.Entry<Integer,OrgMembersResult> entrySet : orgIdMap.entrySet()){
            entrySet.getValue().setMembers(entrySet.getValue().getMembers().parallelStream().distinct().collect(Collectors.toList()));
            orgMembersResults1.add(entrySet.getValue());
        }
        return orgMembersResults1;
    }

    /**
     *
     * @param orgId
     * @param orgIdMap
     * @param orgIdMapSubOrgInfo
     * @return
     */
    private List<SimpleUserVo> getAllSubOrgMemberByOrgId(int orgId,
                                                         Map<Integer, OrgMembersResult> orgIdMap,
                                                         Map<Integer, List<SimpleOrgVo>> orgIdMapSubOrgInfo){
        for (SimpleOrgVo simpleOrgVo : orgIdMapSubOrgInfo.get(orgId)) {
            orgIdMap.get(orgId).getMembers().addAll(orgIdMap.get(simpleOrgVo.getOrgId()).getMembers());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(orgIdMap.get(simpleOrgVo.getOrgId()).getSubOrgs())) {
                orgIdMap.get(orgId).getMembers().addAll(this.getAllSubOrgMemberByOrgId(simpleOrgVo.getOrgId(), orgIdMap, orgIdMapSubOrgInfo));
            }
        }
        return orgIdMap.get(orgId).getMembers();
    }

    @Override
    public int addOrganizationNode(String organizationName, int parentOrganizationId) {
        AdminOrganization organization = new AdminOrganization();
        organization.setName(organizationName);
        organization.setParentOrgId(parentOrganizationId);
        return adminOrganizationDao.addOrganizationNode(organization);
    }

    @Override
    public void editOrganizationNode(int organizationId, String newOrganizationName) {
        AdminOrganization adminOrganization = adminOrganizationDao.getAdminOrganization(organizationId);
        adminOrganization.setName(newOrganizationName);
        adminOrganizationDao.editOrganizationNode(adminOrganization);
    }

    @Override
    public void setManagerOfNode(int uid, int organizationId) {
        if (null == adminOrganizationUserMapDao.getByUserIdAndOrganizationId(uid, organizationId)) {
            adminOrganizationUserMapDao.addEmployeeToNode(uid, organizationId);
        }
        adminOrganizationUserMapDao.setManagerOfNode(uid, organizationId);
    }

    @Override
    public void deleteEmployeeFromNode(int uid, int organizationId) {

        adminOrganizationUserMapDao.deleteEmployeeFromNode(uid, organizationId);
    }

    @Override
    public int addEmployeeToNode(int uid, int organizationId) {
        return adminOrganizationUserMapDao.addEmployeeToNode(uid, organizationId);
    }

    @Override
    public void deleteOrganizationNode(int organizationId) {
        adminOrganizationDao.deleteOrganizationNode(organizationId);
    }

    @Override
    public AdminOrganization getAdminOrganizationById(int id) {
        return adminOrganizationDao.getAdminOrganization(id);
    }

    @Override
    public AdminOrganizationUserMap getAdminOrganizationUserMap(int uid, int organizationId) {
        return adminOrganizationUserMapDao.getByUserIdAndOrganizationId(uid, organizationId);
    }

    @Override
    public List<AdminOrganization> getOrganizationTree() {
        List<AdminOrganization> parentOrgNodes = adminOrganizationDao.getAdminOrganizationByParentOrgId(0);
       setChildrenOrgs(parentOrgNodes);

        return parentOrgNodes;
    }

    @Override
    public OrganizationEmployeesVo getOrganizationEmployees(int organizationId, int current, int pageSize) {
        List<AdminOrganizationUserMap> adminOrganizationUserMaps = adminOrganizationUserMapDao.getByOrgId(organizationId);
        List<Integer> userIds = adminOrganizationUserMaps.stream().map(AdminOrganizationUserMap::getUserId).collect(Collectors.toList());
        List<AdminUserAccountModel> adminUserAccountModels = seaAccountClientV1.getUserAccountsByIds(userIds).getResult();
        List<SeaUserAccountVo> userAccountVos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(adminUserAccountModels)) {
            for (AdminUserAccountModel adminUserAccountModel : adminUserAccountModels) {
                SeaUserAccountVo userAccountVo = new SeaUserAccountVo();
                userAccountVo.setId(adminUserAccountModel.getId());
                userAccountVo.setMis(adminUserAccountModel.getMis());
                userAccountVo.setName(adminUserAccountModel.getName());
                userAccountVos.add(userAccountVo);
            }
        }
        OrganizationEmployeesVo organizationEmployeesVo = new OrganizationEmployeesVo();
        organizationEmployeesVo.setUserAccount(userAccountVos);
        adminOrganizationDao.getAdminOrganization(organizationId);
        organizationEmployeesVo.setOrganizationRelation(getParentOrganization(organizationId));
        return organizationEmployeesVo;
    }

    private String getParentOrganization(int organizationId) {
        AdminOrganization organization = adminOrganizationDao.getAdminOrganization(organizationId);
        if (null != organization && 0 != organization.getParentOrgId()) {
            String parentOrgName = getParentOrganization(organization.getParentOrgId());
            return StringUtils.join(new String[]{ parentOrgName, organization.getName() }, "-");

        }
        return null != organization ? organization.getName() : "";
    }

    @Override
    public List<AdminUserAccountModel> getOrganizationManagers(int organizationId) {
        List<AdminOrganizationUserMap> adminOrganizationUserMaps = adminOrganizationUserMapDao.getByOrgId(organizationId);
        if (CollectionUtils.isNotEmpty(adminOrganizationUserMaps)) {
            List<Integer> userIds = adminOrganizationUserMaps.stream().filter(adminOrganizationUserMap -> 1 == adminOrganizationUserMap.getIsManager())
                    .map(AdminOrganizationUserMap::getUserId).collect(Collectors.toList());
            return seaAccountClientV1.getUserAccountsByIds(userIds).getResult();
        }
        return Lists.newArrayList();
    }

    @Override
    public List<AdminUserAccountModel> searchEmployeesByMis(int organizationId, String mis) {
        mis = mis.trim();
        List<Integer> organizationUserIds = adminOrganizationUserMapDao.getByOrgId(organizationId).stream().map(AdminOrganizationUserMap::getUserId).collect(Collectors.toList());
        AuthRpcResponse<List<AdminUserAccountModel>> userAccountsByMisLike = seaAccountClientV1.getUserAccountsByMisLike(mis,1);

        if (CollectionUtils.isEmpty(userAccountsByMisLike.getResult())){
            return Collections.emptyList();
        }

        return userAccountsByMisLike.getResult().stream().filter(adminUserAccountModel -> organizationUserIds.contains(adminUserAccountModel.getId())).collect(Collectors.toList());
    }

    @Override
    public List<AdminOrganization> getAdminOrganizationByParentOrgId(int organizationId) {
        return adminOrganizationDao.getAdminOrganizationByParentOrgId(organizationId);
    }

    @Override
    public boolean hasSameNameAndParentOrg(String organizationName, int organizationId) {
        if (0 == organizationId && CollectionUtils.isNotEmpty(adminOrganizationDao.getAdminOrganizationByParentOrgId(0))) {
            return adminOrganizationDao.hasSameNameAndParentOrg(organizationName, adminOrganizationDao.getAdminOrganizationByParentOrgId(0).stream().map(AdminOrganization::getId).collect(Collectors.toList()));
        }
        int parentOrgId = getParentOrganizationId(organizationId);
        List<Integer> childrenOrgIds = getChildrenOrgIds(Lists.newArrayList(parentOrgId));
        return adminOrganizationDao.hasSameNameAndParentOrg(organizationName, childrenOrgIds);
    }

    @Override
    public List<Integer> getChildrenOrgIds(List<Integer> orgIds) {
        List<Integer> childrenOrgIds = Lists.newArrayList();
        childrenOrgIds.addAll(orgIds);
        if (CollectionUtils.isNotEmpty(orgIds)) {
            orgIds.forEach(orgId -> {
                List<AdminOrganization> organizations = adminOrganizationDao.getAdminOrganizationByParentOrgId(orgId);
                if (CollectionUtils.isNotEmpty(organizations)) {
                    childrenOrgIds.addAll(getChildrenOrgIds(organizations.stream().map(AdminOrganization::getId).collect(Collectors.toList())));
                }
            });
        }
        return childrenOrgIds;
    }

    @Override
    public List<AdminOrganization> getChildrenOrgs(List<AdminOrganization> orgs) {
        List<AdminOrganization> childrenOrgIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(orgs)) {
            orgs.forEach(org -> {
                List<AdminOrganization> organizations = adminOrganizationDao.getAdminOrganizationByParentOrgId(org.getId());
                if (CollectionUtils.isNotEmpty(organizations)) {
                    childrenOrgIds.addAll(getChildrenOrgs(organizations));
                }
            });
            childrenOrgIds.addAll(orgs);
        }
        return childrenOrgIds;
    }

    private int getParentOrganizationId(int organizationId) {
        AdminOrganization organization = adminOrganizationDao.getAdminOrganization(organizationId);
        if (null != organization && 0 != organization.getParentOrgId()) {
           return getParentOrganizationId(organization.getParentOrgId());
        }
        return null != organization ? organization.getId() : 0;
    }

    private void setChildrenOrgs(List<AdminOrganization> parentOrgNodes) {
        if (CollectionUtils.isNotEmpty(parentOrgNodes)) {
            parentOrgNodes.forEach(parentOrgNode -> {
                List<AdminOrganization> children = adminOrganizationDao.getAdminOrganizationByParentOrgId(parentOrgNode.getId());
                if (CollectionUtils.isNotEmpty(children)) {
                    setChildrenOrgs(children);
                }
                parentOrgNode.setChidrenOrgs(children);
            });
        }
    }

    // 得到用户所属组织
    @Override
    public Map<Integer, List<List<AdminOrganization>>>  getOrgIdsByUserIds(List<Integer> userIds) {
        Map<Integer, List<List<AdminOrganization>>> res = Maps.newHashMap();
        List<AdminOrganizationUserMap> userAllOrgList = getLowestOrgByUserIds(userIds);

        if (CollectionUtils.isEmpty(userAllOrgList)) {
            return res;
        }

        List<AdminOrganization> orgList = adminOrganizationDao.getAllAdminOrganization();
        Map<Integer, AdminOrganization> id2OrgMap = orgList.stream().collect(Collectors.toMap(AdminOrganization::getId, Function.identity()));
        for (AdminOrganizationUserMap userOrg : userAllOrgList) {
            List<AdminOrganization> userCurOrgList = getOrgListByIds(userOrg.getOrgId(), id2OrgMap);
            if (CollectionUtils.isEmpty(userCurOrgList)) {
                continue;
            }
            List<List<AdminOrganization>> userOrgList = res.get(userOrg.getUserId());
            if (CollectionUtils.isEmpty(userOrgList)) {
                userOrgList = Lists.newArrayList();
            }
            userOrgList.add(userCurOrgList);
            res.put(userOrg.getUserId(), userOrgList);
        }

        return res;
    }

    private List<AdminOrganization> getOrgListByIds(int id,  Map<Integer, AdminOrganization> id2OrgMap) {

        ArrayList<AdminOrganization> orgList = Lists.newArrayList();

        AdminOrganization value = id2OrgMap.get(id);
        while (value != null) {
            orgList.add(value);
            value = id2OrgMap.get(value.getParentOrgId());
        }

        return Lists.reverse(orgList);
    }

    // 用户所属的组织
    @Override
    public  List<AdminOrganizationUserMap> getLowestOrgByUserIds(List<Integer> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }
        return adminOrganizationUserMapDao.getLowestOrgByUserIds(userIds);
    }

    /**
     * 得到当前组织的最低一级的组织
     *
     * @param parentId
     * @return
     */
    @Override
    public List<AdminOrganization> getLowestOrgByParentId(int parentId) {

        List<AdminOrganization> organizations = getAdminOrganizationByParentOrgId(parentId);

        List<AdminOrganization> lowestOrgs = Lists.newArrayList();
        if (CollectionUtils.isEmpty(organizations)) {
            AdminOrganization currOrg = adminOrganizationDao.getAdminOrganization(parentId);
            if (currOrg != null) {
                lowestOrgs.add(currOrg);
            }
        } else {
            // 得到最低一级的组织
            for (AdminOrganization currOrg : organizations) {
                lowestOrgs.addAll(getLowestOrgByParentId(currOrg.getId()));
            }
        }

        return lowestOrgs;
    }

    @Override
    public void getOrgByLowestId(int lowestId, List<AdminOrganization> result) {
        AdminOrganization organization = getAdminOrganizationById(lowestId);
        if (organization == null) {
            return;
        }
        result.add(organization);
        getOrgByLowestId(organization.getParentOrgId(), result);
    }

    @Override
    public List<AdminOrganizationUserMap> getOrgUserByOrgIds(List<Integer> organizationIds) {
        if (CollectionUtils.isEmpty(organizationIds)) {
            return Lists.newArrayList();
        }

        return adminOrganizationUserMapDao.getByOrgIds(organizationIds);
    }

    @Override
    public List<AdminOrganization> getAdminOrganizationByName(String name) {

        if (StringUtils.isBlank(name)) {
            return Lists.newArrayList();
        }

        return  adminOrganizationDao.getAdminOrganizationByName(name);
    }

    @Override
    public String joinOrgDescByNextId(int nextId) {
        List<String> orgNames = Lists.newArrayList();

        while (nextId > 0) {
            AdminOrganization org = getAdminOrganizationById(nextId);
            if (org == null) {
                break;
            }
            orgNames.add(org.getName());
            nextId = org.getParentOrgId();
        }

        return Joiner.on(AdminWorkOrderFlowBiz.ORG_NAME_SPLIT).join(Lists.reverse(orgNames));
    }


    @Override
    public Integer queryLowestIdByText(List<String> orgTexts) {

        if (CollectionUtils.isEmpty(orgTexts)) {
            return null;
        }

        List<AdminOrganization> lowestOrgs = getAdminOrganizationByName(orgTexts.get(orgTexts.size() - 1));

        for (AdminOrganization curOrgs : lowestOrgs) {
            if (isSequenceOrg(curOrgs, orgTexts, orgTexts.size() - 1)) {
                return curOrgs.getId();
            }
        }

        return null;
    }


    @Override
    public String getGroupWithAreaNameByMis(String mis){
        try {
            return misOrgIdsCache.get(mis);
        } catch (ExecutionException e) {
            log.error(this.getClass().getSimpleName()+" getGroupWithAreaNameByMis  get :{}  err",mis);
        }
        return "";
    }

    @Override
    public int countOrganizationManagers(List<Integer> organizationIds) {
        if (CollectionUtils.isEmpty(organizationIds)) {
            return 0;
        }
        return (int)(adminOrganizationUserMapDao.countByOrgIds(organizationIds));
    }

    private boolean isSequenceOrg(AdminOrganization org,  List<String> orgTexts, int pos) {
        if (org == null || pos < 0 || !org.getName().equals(orgTexts.get(pos))) {
            return false;
        }

        if (org.getParentOrgId() == 0 ) {
            return pos == 0 ? true : false;
        }

        return isSequenceOrg(adminOrganizationDao.getAdminOrganization(org.getParentOrgId()), orgTexts, --pos);
    }

}
