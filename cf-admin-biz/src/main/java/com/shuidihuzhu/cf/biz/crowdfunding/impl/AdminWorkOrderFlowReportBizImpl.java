package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.admin.AdminOrganizationBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderClassifySettingsBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowReportBiz;
import com.shuidihuzhu.cf.dao.admin.AdminWorkOrderFlowDao;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminWorkOrderFlowConst;
import com.shuidihuzhu.cf.model.admin.AdminOrganization;
import com.shuidihuzhu.cf.model.admin.AdminOrganizationUserMap;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderClassifySettings;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlowRecord;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowParam;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowStatistics;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView;
import com.shuidihuzhu.cf.service.workorder.WorkFlowOrderCommonService;
import com.shuidihuzhu.common.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst.Status.CREATED;

/*
 *
 * 信息传递工单 报表相关的需求
 * */
// 1.1  https://wiki.shuiditech.com/pages/viewpage.action?pageId=********
@Service
@Slf4j
public class AdminWorkOrderFlowReportBizImpl implements AdminWorkOrderFlowReportBiz {

    @Autowired
    private AdminOrganizationBiz orgIdBiz;
    @Autowired
    private AdminWorkOrderClassifySettingsBiz classifySettingsBiz;
    @Autowired
    private AdminWorkOrderFlowDao adminWorkOrderFlowDao;
    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;
    @Autowired
    private AdminWorkOrderFlowRecordBiz adminWorkOrderFlowRecordBiz;
    @Autowired
    private AdminWorkOrderFlowBiz flowBiz;
    @Autowired
    private WorkFlowOrderCommonService flowOrderCommonService;


    @Override
    public PageInfo<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> countFlowReport(int current, int size,
                                                                                                   AdminWorkOrderFlowStatistics.searchParam param) {

        // todo  参数检验
        if (param.getBeginTime() == null || param.getEndTime() == null || (param.getOrgId() == 0 && param.getStaffId() == 0)) {
            log.info("报表查询 参数错误");
            return new PageInfo<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView>();
        }

        if (param.getFirstLevelClassifyId() != 0) {
            List<AdminWorkOrderClassifySettings> classifySettings = classifySettingsBiz
                    .selectChildClassifySettings(param.getFirstLevelClassifyId(), 0);
            if (CollectionUtils.isEmpty(classifySettings)) {
                return new PageInfo<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView>();
            }

            param.setSecondLevelClassifyIds(classifySettings.stream()
                    .map(AdminWorkOrderClassifySettings::getId).collect(Collectors.toList()));
        }

        if (param.getStaffId() != 0 || param.getMemberType() == 1) {
            return countFlowReportByUserIds(current, size, param);
        }

        return countFlowReportByOrgIds(current, size, param);
    }

    private PageInfo<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> countFlowReportByOrgIds(int current,
                                                                                                            int pageSize, AdminWorkOrderFlowStatistics.searchParam param) {

        PageInfo<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> result = new PageInfo<>();

        List<AdminOrganization> nextLevelOrgs = orgIdBiz.getAdminOrganizationByParentOrgId(param.getOrgId());
        // 如果没有下一级的组织 就查询当前的组织
        if (CollectionUtils.isEmpty(nextLevelOrgs)) {
            AdminOrganization orgs = orgIdBiz.getAdminOrganizationById(param.getOrgId());
            if (orgs == null) {
                return result;
            }
            nextLevelOrgs.add(orgs);
        }
        nextLevelOrgs.sort(Comparator.comparing(AdminOrganization::getId));

        // 排序
        int fromIndex = (current - 1) * pageSize;
        int toIndex = fromIndex + pageSize < nextLevelOrgs.size() ? fromIndex + pageSize : nextLevelOrgs.size();
        if (fromIndex >= toIndex) {
            log.warn("信息流转工单报表查询异常.nextLevelOrgs.size:{}, current:{}, pageSize:{}， fromIndex:{}," +
                            "toIndex:{}", nextLevelOrgs.size(),
                    current, pageSize, fromIndex, toIndex);
            return result;
        }

        List<AdminOrganization> currPageOrgs = nextLevelOrgs.subList(fromIndex, toIndex);

        Map<Integer, Integer> orgId2PIdRelative = Maps.newHashMap();
        List<AdminOrganization> childOrgs = orgIdBiz.getChildrenOrgs(currPageOrgs);
        Set<Integer> firstLevelIds = currPageOrgs.stream().map(AdminOrganization::getId).collect(Collectors.toSet());
        for (AdminOrganization childOrg : childOrgs) {
            if (!firstLevelIds.contains(childOrg.getId())) {
                orgId2PIdRelative.put(childOrg.getId(), childOrg.getParentOrgId());
            }
        }

        List<Integer> currOrgIds = childOrgs.stream().map(AdminOrganization::getId).collect(Collectors.toList());
        Map<Integer, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> orgId2StatisViews = Maps.newHashMap();
        // 未处理 和 处理中
        findHandingTask(currOrgIds, param, orgId2StatisViews, FlowReportType.ORG, false);
        findHandingTask(currOrgIds, param, orgId2StatisViews, FlowReportType.ORG, true);
        // 指派 处理结束， 不需要处理
        findHandleEndTask(currOrgIds, param, orgId2StatisViews, FlowReportType.ORG);

        Map<Integer, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> resMap = aggregateByOrgId(
                orgId2StatisViews, firstLevelIds, orgId2PIdRelative);

        // 创建工单的需要特殊处理下
        List<AdminOrganizationUserMap> userOrgMap = orgIdBiz.getOrgUserByOrgIds(currOrgIds);
        userOrgMap.stream().map(AdminOrganizationUserMap::getUserId)
                .collect(Collectors.toList());
        Map<Integer, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> userId2StatisViews = Maps.newHashMap();
        findCreateTaskByUserIds(userOrgMap.stream().map(AdminOrganizationUserMap::getUserId)
                .collect(Collectors.toList()), param, userId2StatisViews);
        for (AdminOrganizationUserMap user : userOrgMap) {
            int parentId = getParentOrgId(firstLevelIds, user.getOrgId(), orgId2PIdRelative);
            AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView aggreView = resMap.get(parentId);
            if (aggreView == null) {
                aggreView = new AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView(parentId);
                resMap.put(parentId, aggreView);
            }
            AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView userIdStaticView = userId2StatisViews.get(user.getUserId());
            if (userIdStaticView != null) {
                aggreView.setCreate(aggreView.getCreate() + userIdStaticView.getCreate());
                aggreView.getCreateIds().addAll(userIdStaticView.getCreateIds());
            }
        }

        for (AdminOrganization org : currPageOrgs) {
            AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView currentView = resMap.get(org.getId());
            if (currentView == null) {
                currentView = new AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView(org.getId());
                resMap.put(org.getId(), currentView);
            }
            currentView.setMemberName(org.getName());
            currentView.removeRepeat();
            currentView.sortByIdDesc();
        }
        result.setTotal(nextLevelOrgs.size());
        result.setList(resMap.values().stream().collect(Collectors.toList()));

        return result;
    }

    // 按结果聚合
    private Map<Integer, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView>
    aggregateByOrgId(Map<Integer, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> orgId2StatisViews,
                     Set<Integer> firstLevelIds, Map<Integer, Integer> orgId2PIdRelative) {
        Map<Integer, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> resMap = Maps.newHashMap();
        // 所有结果需要按组聚合
        for (Map.Entry<Integer, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> entry : orgId2StatisViews.entrySet()) {
            int orgId = entry.getKey();
            AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView currView = entry.getValue();
            int pageOrgId = getParentOrgId(firstLevelIds, orgId, orgId2PIdRelative);
            AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView aggreView = resMap.get(pageOrgId);
            if (aggreView == null) {
                aggreView = new AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView(pageOrgId);
                resMap.put(pageOrgId, aggreView);
            }

            aggreView.setNoAssign(aggreView.getNoAssign() + currView.getNoAssign());
            aggreView.getNoAssignIds().addAll(currView.getNoAssignIds());

            aggreView.setNoStartHandle(aggreView.getNoStartHandle() + currView.getNoStartHandle());
            aggreView.getNoStartHandleIds().addAll(currView.getNoStartHandleIds());

            aggreView.setHanding(aggreView.getHanding() + currView.getHanding());
            aggreView.getHandingIds().addAll(currView.getHandingIds());

            aggreView.getAllToIds().addAll(currView.getAllToIds());
            aggreView.setTotalAllTo(aggreView.getTotalAllTo() + currView.getTotalAllTo());

            aggreView.setFinish(aggreView.getFinish() + currView.getFinish());
            aggreView.getFinishIds().addAll(currView.getFinishIds());

            aggreView.setNoNeedHandle(aggreView.getNoNeedHandle() + currView.getNoNeedHandle());
            aggreView.getNoNeedHandleIds().addAll(currView.getNoNeedHandleIds());

            aggreView.setNoHandleMoreThan48Hour(aggreView.getNoHandleMoreThan48Hour() + currView.getNoHandleMoreThan48Hour());
            aggreView.getNoHandleMoreThan48HourIds().addAll(currView.getNoHandleMoreThan48HourIds());
        }
        return resMap;
    }

    private Integer getParentOrgId(Set<Integer> firstLevelIds, Integer currOrgId, Map<Integer, Integer> orgId2PIdRelative) {

        if (currOrgId == null) {
            log.error("信息流转工单报表查询getParentOrgId异常, base:{} curr:{}, relative:{}", firstLevelIds,
                    currOrgId, orgId2PIdRelative);
            return 0;
        }

        if (firstLevelIds.contains(currOrgId)) {
            return currOrgId;
        }

        return getParentOrgId(firstLevelIds, orgId2PIdRelative.get(currOrgId), orgId2PIdRelative);
    }

    // 查询这个组织下所有的人
    private PageInfo<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> countFlowReportByUserIds(int current,
                                                                                                             int pageSize, AdminWorkOrderFlowStatistics.searchParam param) {

        List<Integer> currPageUserIds = Lists.newArrayList();
        PageInfo<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> result = new PageInfo<>();
        // 单个用户查询
        if (param.getStaffId() != 0) {
            currPageUserIds.add(param.getStaffId());
            result.setTotal(1);
        } else {
            AdminOrganization adminOrg = orgIdBiz.getAdminOrganizationById(param.getOrgId());
            if (adminOrg == null) {
                return result;
            }
            // 某个组织下->所有的组织
            List<AdminOrganization> lowestOrgByParentIds = orgIdBiz.getChildrenOrgs(Arrays.asList(adminOrg));
            if (CollectionUtils.isEmpty(lowestOrgByParentIds)) {
                return new PageInfo<>();
            }

            List<AdminOrganizationUserMap> userMapLists = Lists.newArrayList();
            List<AdminOrganizationUserMap> repeatUserMapLists = orgIdBiz.getOrgUserByOrgIds(lowestOrgByParentIds.stream()
                    .map(AdminOrganization::getId).collect(Collectors.toList()));
            Set<Integer> hasUserIds = Sets.newHashSet();
            // 需要按userId去重
            for (AdminOrganizationUserMap user : repeatUserMapLists) {
                if (!hasUserIds.contains(user.getUserId())) {
                    userMapLists.add(user);
                    hasUserIds.add(user.getUserId());
                }
            }

            userMapLists.sort(Comparator.comparing(AdminOrganizationUserMap::getUserId));
            result.setTotal(userMapLists.size());

            int fromIndex = (current - 1) * pageSize;
            int toIndex = fromIndex + pageSize < userMapLists.size() ? fromIndex + pageSize : userMapLists.size();
            if (fromIndex >= toIndex) {
                log.warn("信息流转工单报表查询异常.userMapLists.size:{}, current:{}, pageSize:{}， fromIndex:{}," +
                                "toIndex:{}", userMapLists.size(),
                        current, pageSize, fromIndex, toIndex);
                return result;
            }

            List<AdminOrganizationUserMap> subList = userMapLists.subList(fromIndex, toIndex);

            currPageUserIds.addAll(subList.stream().map(AdminOrganizationUserMap::getUserId).collect(Collectors.toList()));
        }
        result.setList(countFlowReportByUserIds(currPageUserIds, param));

        return result;
    }

    private List<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView>
    countFlowReportByUserIds(List<Integer> currPageUserIds, AdminWorkOrderFlowStatistics.searchParam param) {

        Map<Integer, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> userId2StatisViews = Maps.newHashMap();

        if (CollectionUtils.isEmpty(currPageUserIds)) {
            return Lists.newArrayList();
        }

        // 创建
        findCreateTaskByUserIds(currPageUserIds, param, userId2StatisViews);
        // 未处理 和 处理中
        findHandingTask(currPageUserIds, param, userId2StatisViews, FlowReportType.USER, false);
        // 超过48小时没有处理
        findHandingTask(currPageUserIds, param, userId2StatisViews, FlowReportType.USER, true);
        // 流转过的工单， 处理完成，不需要处理
        findHandleEndTask(currPageUserIds, param, userId2StatisViews, FlowReportType.USER);

        // 填充useName
        List<AdminUserAccountModel> accountModels = seaAccountClientV1.getUserAccountsByIds(currPageUserIds).getResult();
        for (Integer userId : currPageUserIds) {

            AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView view = userId2StatisViews.get(userId);
            if (view == null) {
                view = new AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView(userId);
                userId2StatisViews.put(userId, view);
            }
            // 未领取 是组织维度的， 个人没有未领取
            view.setNoAssign(0);
            view.setNoAssignIds(Lists.newArrayList());
            view.removeRepeat();
            view.sortByIdDesc();
            fillUserName(accountModels, view);
        }

        return userId2StatisViews.values().stream().collect(Collectors.toList());
    }

    // 用户名
    private void fillUserName(List<AdminUserAccountModel> accountModels, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView view) {
        if (CollectionUtils.isEmpty(accountModels)) {
            return;
        }
        for (AdminUserAccountModel acModel : accountModels) {
            if (acModel.getId() == view.getMemberId()) {
                view.setMemberName(acModel.getName());
                return;
            }
        }
    }

    // 查找创建的task
    private void findCreateTaskByUserIds(List<Integer> currPageUserIds, AdminWorkOrderFlowStatistics.searchParam param,
                                         Map<Integer, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> userId2StatisViews) {
        if (CollectionUtils.isEmpty(currPageUserIds)) {
            return;
        }

        param.setCreateIds(currPageUserIds);
        List<AdminWorkOrderFlowView> createFlowOrders = adminWorkOrderFlowDao.selectByCreateTimeAndCreateIds(param);
        for (AdminWorkOrderFlowView currFlowOrder : createFlowOrders) {
            AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView view
                    = userId2StatisViews.get(currFlowOrder.getCreatorId());
            if (view == null) {
                view = new AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView(currFlowOrder.getCreatorId());
                userId2StatisViews.put(currFlowOrder.getCreatorId(), view);
            }
            view.setCreate(view.getCreate() + 1);
            view.getCreateIds().add(currFlowOrder.getWorkOrderId());
        }
    }

    private void findHandingTask(List<Integer> ids, AdminWorkOrderFlowStatistics.searchParam param,
                                 Map<Integer, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> dataMap,
                                 FlowReportType reportType, boolean moreThan48) {
        if (reportType == null || CollectionUtils.isEmpty(ids)) {
            return;
        }

        if (reportType == FlowReportType.USER) {
            param.setOperatorIds(ids);
        } else {
            param.setProblemTypeCodes(ids);
        }

        if (moreThan48) {
            param.setMoreThanTimeNoHandle(DateUtils.addHours(new Date(), -48));
        }

        param.setOrderStatusSet(Arrays.asList(CREATED.getCode(),
                AdminWorkOrderConst.Status.HANDLING.getCode()));

        param.setCreateTime(getDate());
        Stopwatch stopwatch = Stopwatch.createStarted();
        List<AdminWorkOrderFlowView> noHandleSucFlows = adminWorkOrderFlowDao.selectByOrderStatusAndOperators(param);
        stopwatch.stop();
        log.info("findHandingTask data:{}", JSON.toJSONString(noHandleSucFlows));
        log.info("findHandingTask 耗时:{}", stopwatch);

        for (AdminWorkOrderFlowView flow : noHandleSucFlows) {
            int key = (reportType == FlowReportType.USER ? flow.getOperatorId() : flow.getProblemType());
            AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView view
                    = getDataViewOrCreate(dataMap, key);
            // 超过48小时 还没有处理完成的
            if (moreThan48) {
                view.setNoHandleMoreThan48Hour(view.getNoHandleMoreThan48Hour() + 1);
                view.getNoHandleMoreThan48HourIds().add(flow.getWorkOrderId());
                continue;
            }
            // 到目前时间 都还没有处理完成的
            if (flow.getWorkOrderStatus() == CREATED.getCode()) {
                if (flow.getOperatorId() != 0) {
                    view.setNoStartHandle(view.getNoStartHandle() + 1);
                    view.getNoStartHandleIds().add(flow.getWorkOrderId());
                } else {
                    view.setNoAssign(view.getNoAssign() + 1);
                    view.getNoAssignIds().add(flow.getWorkOrderId());
                }
            } else {
                if (flow.getOperatorId() != 0) {
                    view.setHanding(view.getHanding() + 1);
                    view.getHandingIds().add(flow.getWorkOrderId());
                } else {
                    view.setNoAssign(view.getNoAssign() + 1);
                    view.getNoAssignIds().add(flow.getWorkOrderId());
                }
            }
        }
    }

    private void findHandleEndTask(List<Integer> ids, AdminWorkOrderFlowStatistics.searchParam param,
                                   Map<Integer, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> dataMap,
                                   FlowReportType reportType) {
        if (reportType == null || CollectionUtils.isEmpty(ids)) {
            return;
        }

        if (reportType == FlowReportType.USER) {
            param.setOperatorIds(ids);
        } else {
            param.setProblemTypeCodes(ids);
        }

        param.setOperateTypeCodes(Arrays.asList(AdminWorkOrderFlowConst.OperateTypeEnum.HANDLE_SUCCESS_WORK_FLOW.getCode(),
                AdminWorkOrderFlowConst.OperateTypeEnum.NO_HANDLE_WORK_FLOW.getCode(),
                AdminWorkOrderFlowConst.OperateTypeEnum.ALLOT_WORK_FLOW.getCode()));
        List<AdminWorkOrderFlowRecord> flowRecords = adminWorkOrderFlowRecordBiz.selectByOperatorIdsAndTime(param);
        for (AdminWorkOrderFlowRecord flowRecord : flowRecords) {
            int key = (reportType == FlowReportType.USER ? flowRecord.getOperatorId() : flowRecord.getProblemType());

            AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView view
                    = getDataViewOrCreate(dataMap, key);

            switch (AdminWorkOrderFlowConst.OperateTypeEnum.getEnumByCode(flowRecord.getOperateType())) {
                case ALLOT_WORK_FLOW:
                    view.setTotalAllTo(view.getTotalAllTo() + 1);
                    view.getAllToIds().add(flowRecord.getWorkOrderId());
                    break;
                case HANDLE_SUCCESS_WORK_FLOW:
                    view.setFinish(view.getFinish() + 1);
                    view.getFinishIds().add(flowRecord.getWorkOrderId());
                    break;
                case NO_HANDLE_WORK_FLOW:
                    view.setNoNeedHandle(view.getNoNeedHandle() + 1);
                    view.getNoNeedHandleIds().add(flowRecord.getWorkOrderId());
                    break;
                default:
                    break;
            }
        }
    }

    private AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView getDataViewOrCreate(
            Map<Integer, AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> dataMap, int key) {
        AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView view = dataMap.get(key);
        if (view == null) {
            view = new AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView(key);
            dataMap.put(key, view);
        }
        return view;
    }

    private Date getDate() {
        LocalDate localDate = LocalDate.now().minusDays(7);

        ZoneId zoneId = ZoneId.systemDefault();

        ZonedDateTime zdt = localDate.atStartOfDay(zoneId);

        return Date.from(zdt.toInstant());
    }
}
