package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCaseVisitConfigBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfCaseVisitConfigDao;
import com.shuidihuzhu.cf.delegate.commonservice.ICommonServiceDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdFundingProgressType;
import com.shuidihuzhu.cf.enums.visitconfig.VisitConfigLogActionInfoEnum;
import com.shuidihuzhu.cf.enums.visitconfig.VisitConfigSourceEnum;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCfCaseVisitConfig;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminOperatorVo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.risk.client.CfRiskClient;
import com.shuidihuzhu.client.cf.risk.client.CfRiskPlatformClient;
import com.shuidihuzhu.client.cf.risk.model.CfRiskOperateDetailParam;
import com.shuidihuzhu.client.cf.risk.model.CfRiskOperateLimitExtParam;
import com.shuidihuzhu.client.cf.risk.model.CfRiskOperateLimitParam;
import com.shuidihuzhu.client.cf.risk.model.enums.RiskOperateSourceEnum;
import com.shuidihuzhu.client.cf.risk.model.enums.UserOperationEnum;
import com.shuidihuzhu.client.cf.risk.model.result.RiskRpcResponse;
import com.shuidihuzhu.client.param.RiskOperateLimitParam;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 *
 * Created by wangsf on 18/5/20.
 */
@Service
@Slf4j
public class AdminCfCaseVisitConfigBizImpl implements AdminCaseVisitConfigBiz {

	@Autowired
	private AdminCfCaseVisitConfigDao cfCaseVisitConfigDao;

	@Autowired
	private ICrowdfundingDelegate crowdfundingDelegate;

	@Resource
	private ICommonServiceDelegate commonServiceDelegate;
	@Resource
	private SeaAccountClientV1 seaAccountClientV1;
	@Autowired
	private CfRiskClient cfRiskClient;
	@Autowired
	private CfRiskPlatformClient cfRiskPlatformClient;

	@Override
	public int add(AdminCfCaseVisitConfig config) {
		if(config == null) {
			return 0;
		}
		onConfigUpdate(config);
		return this.cfCaseVisitConfigDao.insert(config);
	}

	@Override
	public int update(AdminCfCaseVisitConfig config) {
		if(config == null) {
			return 0;
		}
		onConfigUpdate(config);
		return this.cfCaseVisitConfigDao.update(config);
	}

	@Override
	public AdminCfCaseVisitConfig get(int caseId) {
		return this.cfCaseVisitConfigDao.getByCaseId(caseId);
	}

	@Override
	public List<AdminCfCaseVisitConfig> getList(int current, int pageSize) {
		PageHelper.startPage(current, pageSize);
		return this.cfCaseVisitConfigDao.getList();
	}

	@Override
	public List<AdminCfCaseVisitConfig> getList(int current, int pageSize, Integer caseId, Integer type, Integer operator, String start, String end) {

		PageHelper.startPage(current, pageSize);

		Timestamp s = null;
		Timestamp e = null;

		if (StringUtils.isNotEmpty(start) && StringUtils.isNotEmpty(end)){
			s = Timestamp.valueOf(start);
			e = Timestamp.valueOf(end);
		}

		return this.cfCaseVisitConfigDao.getListByType(caseId,type,operator,s,e);
	}

	@Override
	public int add(AdminCfCaseVisitConfig config,boolean dynamicFlag,boolean titleFlag) {
		if(config == null) {
			return 0;
		}
		onConfigUpdate(config);
		int caseId = config.getCaseId();
		int result = cfCaseVisitConfigDao.insert(config);
		if (result > 0){
			chageTitle(caseId, titleFlag);
			officialDynamic(caseId, dynamicFlag);
		}

		return result;
	}

	@Override
	public int update(AdminCfCaseVisitConfig config,boolean dynamicFlag,boolean titleFlag) {
		if(config == null) {
			return 0;
		}
		onConfigUpdate(config);
		int result = cfCaseVisitConfigDao.update(config);
		if (result>0){
			chageTitle(config.getCaseId(),titleFlag);
			officialDynamic(config.getCaseId(),dynamicFlag);
		}
		return result;
	}

	@Override
	public boolean saveAbnormalHidden(int caseId, Boolean hidden, String selfTitle, String otherTitle, int userId) {
		onHiddenUpdate(caseId, hidden, userId);
		if (hidden == null && selfTitle == null && otherTitle == null) {
			return true;
		}

		Integer hiddenInteger = hidden == null ? null : hidden ? 1 : 0;
		return cfCaseVisitConfigDao.updateAbnormalHiddenAndHiddenTitle(caseId, hiddenInteger, selfTitle, otherTitle) > 0;
	}

	private void onHiddenUpdate(int caseId, Boolean hidden, int userId) {
		if (hidden == null) {
			return;
		}
		AdminCfCaseVisitConfig oldConfig = cfCaseVisitConfigDao.getByCaseId(caseId);
		boolean oldHidden = oldConfig == null ? false : oldConfig.getAbnormalHidden();
		if (hidden.equals(oldHidden)) {
			return;
		}
		VisitConfigLogActionInfoEnum actionInfoEnum = hidden ?
				VisitConfigLogActionInfoEnum.LOCK_INFO_PAGE :
				VisitConfigLogActionInfoEnum.UNLOCK_INFO_PAGE;

		commonServiceDelegate.pin(caseId,
				VisitConfigSourceEnum.RISK_CONTROL,
				ImmutableList.of(actionInfoEnum),
				userId);
	}

	private void onConfigUpdate(AdminCfCaseVisitConfig config) {
		int caseId = config.getCaseId();
		AdminCfCaseVisitConfig oldConfig = cfCaseVisitConfigDao.getByCaseId(caseId);

		// 若有改动记录日志
		boolean sharable = oldConfig == null || oldConfig.isSharable();
		if (sharable != config.isSharable()) {
			VisitConfigLogActionInfoEnum actionInfoEnum = config.isSharable() ?
					VisitConfigLogActionInfoEnum.UNLOCK_SHARE :
					VisitConfigLogActionInfoEnum.LOCK_SHARE;

			commonServiceDelegate.pin(caseId,
					VisitConfigSourceEnum.RISK_CONTROL,
					ImmutableList.of(actionInfoEnum),
					ContextUtil.getAdminUserId());
		}

		// 若有改动记录日志
		boolean showBanner = oldConfig != null && oldConfig.isShowBanner();
		if (showBanner != config.isShowBanner()) {
			VisitConfigLogActionInfoEnum actionInfoEnum = config.isShowBanner() ?
					VisitConfigLogActionInfoEnum.RUMOUR_BANNER :
					VisitConfigLogActionInfoEnum.NO_RUMOUR_BANNER;

			commonServiceDelegate.pin(caseId,
					VisitConfigSourceEnum.RISK_CONTROL,
					ImmutableList.of(actionInfoEnum),
					ContextUtil.getAdminUserId());
		}
	}

	private void chageTitle(int caseId,boolean titleFlag){

		if (!titleFlag){
			return;
		}

		CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getFundingInfoById(caseId);
		if (crowdfundingInfo == null) {
			return ;
		}
		String officalTitle = "【官方声明：转发次数不影响提现】";
		if (StringUtils.startsWith(crowdfundingInfo.getTitle(), officalTitle)){
			return;
		}
		crowdfundingInfo.setTitle("【官方声明：转发次数不影响提现】 "+crowdfundingInfo.getTitle());

		if (StringUtils.length(crowdfundingInfo.getTitle()) >= 60) {
			return;
		}

		crowdfundingDelegate.updateCrowdfundingInfo(crowdfundingInfo);
		log.info("chageTitle caseId={},titleFlag={}",caseId,titleFlag);

		commonServiceDelegate.pin(caseId,
				VisitConfigSourceEnum.RISK_CONTROL,
				ImmutableList.of(
						VisitConfigLogActionInfoEnum.CHANGE_TITLE
				),
				ContextUtil.getAdminUserId());
	}

	private void officialDynamic(int caseId,boolean dynamicFlag){

		if (!dynamicFlag){
			return;
		}

		CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getFundingInfoById(caseId);
		if (crowdfundingInfo == null) {
			return ;
		}

		String content = "感谢各位爱心人士的关注、转发与支持，近期出现的水滴筹个人求助项目“因转发次数不够，钱无法到账”的传言属于虚假信息。患者在疾病证明等相关资料通过专业审核后，即可申请提现。是否能提现与转发次数无任何关联。建议大家在遇到类似传言时，可以通过“水滴筹”微信公众号或官方服务热线（400-686-1179）联系我们，我们将第一时间进行妥善处理。再次感谢！";
		List<CrowdFundingProgress> progresses = crowdfundingDelegate.queryByType(caseId, CrowdFundingProgressType.OFFICIAL.value());
		for (CrowdFundingProgress progress : progresses){
			if("官方动态".equals(progress.getTitle()) && content.equals(progress.getContent())){
				return;
			}
		}
		CrowdFundingProgress crowdFundingProgress = new CrowdFundingProgress();
		crowdFundingProgress.setActivityId(caseId);
		crowdFundingProgress.setUserId(-1);
		crowdFundingProgress.setTitle("官方动态");
		crowdFundingProgress.setContent(content);
		crowdFundingProgress.setImageUrls("");
		crowdFundingProgress.setType(CrowdFundingProgressType.OFFICIAL.value());
		this.crowdfundingDelegate.addProgress(crowdFundingProgress);
		log.info("officialDynamic caseId={},titleFlag={}",caseId,dynamicFlag);

		commonServiceDelegate.pin(caseId,
				VisitConfigSourceEnum.RISK_CONTROL,
				ImmutableList.of(VisitConfigLogActionInfoEnum.OFFICIAL_PROGRESS),
				ContextUtil.getAdminUserId());

	}

	@Override
	public List<AdminOperatorVo> getOperatorList(){
		return cfCaseVisitConfigDao.getOperatorList();
	}

	@Override
	public OpResult updateCanShowByCaseId(Integer caseId, Integer canShow) {
        int updateResult = cfCaseVisitConfigDao.updateCanShowByCaseId(caseId,canShow);
        if (updateResult > 0) {
            return OpResult.createSucResult();}
		return OpResult.createFailResult(CfErrorCode.SYSTEM_PARAM_ERROR);
	}

	@Override
	public OpResult updateBannerTextAndStartEndTime(Integer caseId, String bannerText,Date startTime, Date endTime) {
	    int updateResult = cfCaseVisitConfigDao.updateBannerTextAndStartEndTime(caseId, bannerText, startTime, endTime);
	    if (updateResult > 0){
            return OpResult.createSucResult();
        }
		return OpResult.createFailResult(CfErrorCode.SYSTEM_PARAM_ERROR);
	}

	@Override
	public Response<Void> judeRiskCase(int caseId, int adminUserId, boolean action, long riskUserId, UserOperationEnum userOpEnum, RiskOperateSourceEnum operateSourceEnum, CfRiskOperateLimitExtParam extParam) {
		AdminUserAccountModel model = seaAccountClientV1.getValidUserAccountById(adminUserId).getResult();
		String adminUserName = Objects.nonNull(model) ? model.getName() : null;
		CfRiskOperateDetailParam detailParam = new CfRiskOperateDetailParam();
		detailParam.setUserOperate(userOpEnum.getCode());
		detailParam.setAction(action);
		detailParam.setReason("风控管理-风控动作查询");
		detailParam.setExtParam(extParam);

		CfRiskOperateLimitParam riskParam = new CfRiskOperateLimitParam();
		riskParam.setCaseId(caseId);
		riskParam.setUserId(riskUserId);
		riskParam.setOperateSource(operateSourceEnum.getCode());
		riskParam.setOperatorId(adminUserId);
		riskParam.setOperator(StringUtils.isEmpty(adminUserName) ? null : adminUserName);
		riskParam.setDetailParams(Lists.newArrayList(detailParam));

		Response<String> response = cfRiskClient.writeRiskOperate(riskParam);
		if(0 != response.getCode()){
			return NewResponseUtil.makeResponse(response.getCode(), response.getMsg(), null);
		}

		//TODO ###请勿改动、待删除####
		Map<String, Boolean> paramMap = Maps.newHashMap();
		paramMap.put(String.valueOf(userOpEnum.getCode()), action);

		RiskOperateLimitParam param = new RiskOperateLimitParam();
		param.setUserId(riskUserId);
		param.setCaseId(caseId);
		param.setLimits(paramMap);
		param.setOperateSource(operateSourceEnum.getCode());
		param.setOperatorId(Integer.valueOf(String.valueOf(adminUserId)));
		param.setOperator(StringUtils.isEmpty(adminUserName) ? null : adminUserName);

		RiskRpcResponse riskRpcResponse = cfRiskPlatformClient.addLimit(param);

		if(Objects.isNull(riskRpcResponse) || 0 != riskRpcResponse.getCode()){
			return NewResponseUtil.makeResponse(riskRpcResponse.getCode(), riskRpcResponse.getMsg(), null);
		}
		return NewResponseUtil.makeSuccess(null);
	}

}
