package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.github.pagehelper.PageHelper;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.admin.util.AdminDateUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfReportAddTrustBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingReportBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderReportBiz;
import com.shuidihuzhu.cf.biz.es.CfSearch;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminWorkOrderReportDao;
import com.shuidihuzhu.cf.delegate.finance.IFinanceDelegate;
import com.shuidihuzhu.cf.diff.Diff;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminCfCaseStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminCfDrawCashConstant;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminWorkOrderReportConst;
import com.shuidihuzhu.cf.enums.crowdfunding.CfDrawCashConstant;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.NewCfRefundConstant;
import com.shuidihuzhu.cf.finance.model.po.CfCashPauseBooleanPo;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.crowdfunding.AdminReportDataVo;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderReport;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkReportMap;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport;
import com.shuidihuzhu.cf.model.crowdfunding.ReportWorkStat;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderDataVo;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminWorkOrderReportVo;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderIndexSearchParam;
import com.shuidihuzhu.client.cf.search.model.enums.CfWorkOrderIndexSortEnum;
import com.shuidihuzhu.common.web.model.Response;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by lixiaoshuang on 2018/5/10.
 */
@Service
public class AdminWorkOrderReportBizImpl implements AdminWorkOrderReportBiz {
    @Autowired
    private AdminWorkOrderReportDao adminWorkOrderReportDao;
    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    private CfSearch cfSearch;

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Autowired
    private AdminCrowdfundingReportBiz adminCrowdfundingReportBiz;

    @Autowired
    private IFinanceDelegate financeDelegate;

    @Autowired
    private AdminCfReportAddTrustBiz adminCfReportAddTrustBiz;

    @Override
    public int insertAdminWorkOrderReport(AdminWorkOrderReport adminWorkOrderReport) {
        return adminWorkOrderReportDao.insertAdminWorkOrderReport(adminWorkOrderReport);
    }

    @Override
    public AdminWorkOrderReport getAdminWorkOrderReportByCaseId(int caseId) {
        return adminWorkOrderReportDao.getAdminWorkOrderReportByCaseId(caseId);
    }

    @Override
    public List<AdminWorkOrderReport> getAdminWorkOrderReportByCount(int orderType, int orderTask, int count) {
        return adminWorkOrderReportDao.getAdminWorkOrderReportByCount(orderType, orderTask, count);
    }

    @Diff(diffMethod = "getAdminWorkOrderReportListFromEs", diffCompare = "com.shuidihuzhu.cf.diff.GetAdminWorkOrderReportListCompare")
    @Override
    public List<AdminWorkOrderReportVo> getAdminWorkOrderReportList(int userId,
                                                                    int lostContact,
                                                                    Integer caseStatus,
                                                                    Integer addTrustStatus,
                                                                    Integer followStatus,
                                                                    Integer infoId,
                                                                    int current,
                                                                    int pageSize,
                                                                    Integer reprotType,
                                                                    Integer realName,
                                                                    String appointStartTime,
                                                                    String appointEndTime) {
        Integer apporoveStatus = null;
        Integer drawStatus = null;
        Integer drawApplyStatus = null;
        Integer refundStatus = null;
        if (null != caseStatus) {
            if (caseStatus == AdminCfCaseStatus.APPROVE_NO.getValue()) {
                //1.审核未通过
            } else if (caseStatus == AdminCfCaseStatus.APPROVE_FINISH.getValue()) {
                //2.审核通过
                apporoveStatus = CrowdfundingStatus.CROWDFUNDING_STATED.value();

            } else if (caseStatus == AdminCfCaseStatus.DRAWCASH_SUBMIT.getValue()) {
                //3.已申请提现
                apporoveStatus = CrowdfundingStatus.CROWDFUNDING_STATED.value();
                drawApplyStatus = AdminCfDrawCashConstant.ApplyStatus.UNSUBMIT.getCode();

            } else if (caseStatus == AdminCfCaseStatus.DRAWCASH_APPROVE.getValue()) {
                //4.体现审核通过还未生成
                apporoveStatus = CrowdfundingStatus.CROWDFUNDING_STATED.value();
                drawApplyStatus = AdminCfDrawCashConstant.ApplyStatus.APPROVE_SUCCESS.getCode();
                drawStatus = CfDrawCashConstant.DrawStatus.UNBUILD.getCode();

            } else if (caseStatus == AdminCfCaseStatus.DRAWCASH_BUILD.getValue()) {
                //5.已生成&打款失败
                apporoveStatus = CrowdfundingStatus.CROWDFUNDING_STATED.value();
                drawApplyStatus = AdminCfDrawCashConstant.ApplyStatus.APPROVE_SUCCESS.getCode();
                drawStatus = CfDrawCashConstant.DrawStatus.HANDLE_FAILED.getCode();

            } else if (caseStatus == AdminCfCaseStatus.DRAWCASH_SUCCESS.getValue()) {
                //6.已打款&部分打款成功
                apporoveStatus = CrowdfundingStatus.CROWDFUNDING_STATED.value();
                drawApplyStatus = AdminCfDrawCashConstant.ApplyStatus.APPROVE_SUCCESS.getCode();
                drawStatus = CfDrawCashConstant.DrawStatus.HANDLE_SUCCESS.getCode();

            } else if (caseStatus == AdminCfCaseStatus.REFUND_SUBMIT.getValue()) {
                //7.已申请退款 >不提交
                apporoveStatus = CrowdfundingStatus.CROWDFUNDING_STATED.value();
                refundStatus = NewCfRefundConstant.ApplyStatus.UNSUBMIT.getCode();
            }
        }
        PageHelper.startPage(current, pageSize);
        return adminWorkOrderReportDao.getAdminWorkOrderReportList(userId, lostContact, addTrustStatus, followStatus,
                apporoveStatus, drawStatus, drawApplyStatus, refundStatus, infoId, reprotType, realName, appointStartTime, appointEndTime);

    }

    @Override
    public Pair<Long, List<AdminWorkOrderReportVo>> getAdminWorkOrderReportListFromEs(int userId,
                                                                                int lostContact,
                                                                                Integer caseStatus,
                                                                                Integer addTrustStatus,
                                                                                Integer followStatus,
                                                                                Integer infoId,
                                                                                int current,
                                                                                int pageSize,
                                                                                Integer reprotType,
                                                                                Integer realName,
                                                                                String appointStartTime,
                                                                                String appointEndTime) {

        CfWorkOrderIndexSearchParam searchParam = new CfWorkOrderIndexSearchParam();
        searchParam.setAwoOrderTypes(Lists.newArrayList(AdminWorkOrderConst.Type.CASE_REPORT.getCode()));
        searchParam.setAwoOrderTasks(Lists.newArrayList(AdminWorkOrderConst.Task.CASE_REPORT_DEAL.getCode()));

        if (userId > 0) {
            searchParam.setAwoOperatorIds(Lists.newArrayList((long)userId));
        }
        if (infoId != null && infoId > 0) {
            searchParam.setCfCaseIds(Lists.newArrayList((long)infoId));
        }

        if (followStatus != null) {
            searchParam.setAworDealResults(Lists.newArrayList(followStatus.intValue()));
        }else {
            searchParam.setAworDealResults(Lists.newArrayList(0, 1));
        }

        if (reprotType != null) {
            searchParam.setCrlReportlabels(Lists.newArrayList(reprotType.intValue()));
        }

        if (lostContact != 0) {
            searchParam.setAclcLosts(Lists.newArrayList(lostContact));
        }

        if (addTrustStatus != null) {
            searchParam.setCratAuditStatuses(Lists.newArrayList(addTrustStatus.intValue()));
        }


        Integer approveStatus = null;
        Integer drawStatus = null;
        Integer drawApplyStatus = null;
        Integer refundStatus = null;
        if (null != caseStatus) {
            if (caseStatus == AdminCfCaseStatus.APPROVE_NO.getValue()) {
                //1.审核未通过
            } else if (caseStatus == AdminCfCaseStatus.APPROVE_FINISH.getValue()) {
                //2.审核通过
                approveStatus = CrowdfundingStatus.CROWDFUNDING_STATED.value();

            } else if (caseStatus == AdminCfCaseStatus.DRAWCASH_SUBMIT.getValue()) {
                //3.已申请提现
                approveStatus = CrowdfundingStatus.CROWDFUNDING_STATED.value();
                drawApplyStatus = AdminCfDrawCashConstant.ApplyStatus.UNSUBMIT.getCode();

            } else if (caseStatus == AdminCfCaseStatus.DRAWCASH_APPROVE.getValue()) {
                //4.体现审核通过还未生成
                approveStatus = CrowdfundingStatus.CROWDFUNDING_STATED.value();
                drawApplyStatus = AdminCfDrawCashConstant.ApplyStatus.APPROVE_SUCCESS.getCode();
                drawStatus = CfDrawCashConstant.DrawStatus.UNBUILD.getCode();

            } else if (caseStatus == AdminCfCaseStatus.DRAWCASH_BUILD.getValue()) {
                //5.已生成&打款失败
                approveStatus = CrowdfundingStatus.CROWDFUNDING_STATED.value();
                drawApplyStatus = AdminCfDrawCashConstant.ApplyStatus.APPROVE_SUCCESS.getCode();
                drawStatus = CfDrawCashConstant.DrawStatus.HANDLE_FAILED.getCode();

            } else if (caseStatus == AdminCfCaseStatus.DRAWCASH_SUCCESS.getValue()) {
                //6.已打款&部分打款成功
                approveStatus = CrowdfundingStatus.CROWDFUNDING_STATED.value();
                drawApplyStatus = AdminCfDrawCashConstant.ApplyStatus.APPROVE_SUCCESS.getCode();
                drawStatus = CfDrawCashConstant.DrawStatus.HANDLE_SUCCESS.getCode();

            } else if (caseStatus == AdminCfCaseStatus.REFUND_SUBMIT.getValue()) {
                //7.已申请退款 >不提交
                approveStatus = CrowdfundingStatus.CROWDFUNDING_STATED.value();
                refundStatus = NewCfRefundConstant.ApplyStatus.UNSUBMIT.getCode();
            }
        }

        if (approveStatus != null) {
            searchParam.setCfStatuses(Lists.newArrayList(approveStatus.intValue()));
        }

        if (drawStatus == null && drawApplyStatus != null && drawApplyStatus == 0) {
            searchParam.setCdcApplyStatuses(Lists.newArrayList(1, 3));
        }

        if (drawStatus != null && drawStatus == -1 && drawApplyStatus != null && drawApplyStatus == 2) {
            searchParam.setCdcApplyStatuses(Lists.newArrayList(drawApplyStatus));
            searchParam.setCdcDrawStatuses(Lists.newArrayList(drawStatus));
        }

        if (drawStatus != null && drawStatus == 3 && drawApplyStatus != null && drawApplyStatus == 2) {
            searchParam.setCdcApplyStatuses(Lists.newArrayList(drawApplyStatus));
            searchParam.setCdcDrawStatuses(Lists.newArrayList(0, 3));
        }

        if (drawStatus != null && drawStatus == 2 && drawApplyStatus != null && drawApplyStatus == 2) {
            searchParam.setCdcApplyStatuses(Lists.newArrayList(drawApplyStatus));
            searchParam.setCdcDrawStatuses(Lists.newArrayList(1, 2, 4, 5));
        }

        if (refundStatus != null) {
            final int refundStatusFinal = refundStatus.intValue();
            List<Integer> applyStatuses = Arrays.stream(NewCfRefundConstant.ApplyStatus.values()).filter(x -> x.getCode() > refundStatusFinal).map(x -> x.getCode()).collect(Collectors.toList());
            searchParam.setCrApplyStatuses(applyStatuses);
        }

        if (realName != null) {
            searchParam.setCrRealNameReport(Lists.newArrayList(realName));
        }

        if (StringUtils.isNotEmpty(appointStartTime) && StringUtils.isNotEmpty(appointEndTime)){
            long startTime = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss").parseDateTime(appointStartTime).getMillis();
            long endTime = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss").parseDateTime(appointEndTime).getMillis();
            searchParam.setAtuUpdateTimeStart(startTime);
            searchParam.setAwoAppointTimeEnd(endTime);
        }

        searchParam.setSortEnums(Lists.newArrayList(CfWorkOrderIndexSortEnum.AWOR_CASE_RISK_DESC, CfWorkOrderIndexSortEnum.AWOR_ID_ASC));
        searchParam.setFrom((current - 1) * pageSize);
        searchParam.setSize(pageSize);

        Pair<Long, List<AdminWorkOrder>> pair = cfSearch.cfWorkOrderIndexSearch(searchParam);

        return Pair.of(pair.getLeft(), getAdminWorkOrderReportVos(pair.getRight()));
    }

    private List<AdminWorkOrderReportVo> getAdminWorkOrderReportVos(List<AdminWorkOrder> workOrders) {
        if (CollectionUtils.isEmpty(workOrders)) {
            return Collections.emptyList();
        }

        Map<Long, AdminWorkOrder> workOrderMap = Maps.uniqueIndex(workOrders, AdminWorkOrder::getId);

        List<Long> workOrderIds = workOrders.stream().map(x -> x.getId()).collect(Collectors.toList());
        List<AdminWorkOrderReport> reports = adminWorkOrderReportDao.getByWorkOrderIds(workOrderIds);

        List<Integer> caseIds = reports.stream().map(x -> x.getCaseId()).collect(Collectors.toList());
        List<CrowdfundingInfo> infos = adminCrowdfundingInfoBiz.selectByCaseIdList(caseIds);
        Map<Integer, CrowdfundingInfo> infoMap = Maps.uniqueIndex(infos, CrowdfundingInfo::getId);

        List<Integer> crowdfundingReportIds = reports.stream().map(x -> x.getReportId()).collect(Collectors.toList());
        List<CrowdfundingReport> crowdfundingReports = adminCrowdfundingReportBiz.getListByReportIds(crowdfundingReportIds);
        Map<Integer, CrowdfundingReport> crowdfundingReportMap = Maps.uniqueIndex(crowdfundingReports, CrowdfundingReport::getId);

        Response<Map<Integer, CfCashPauseBooleanPo>> pauseDrawCashResponse = financeDelegate.checkPauseDrawCash(caseIds);
        Map<Integer, CfCashPauseBooleanPo> pauseBooleanPoMap = pauseDrawCashResponse.getData();
        List<String> infoUuidList = infos.stream().map(x -> x.getInfoId()).collect(Collectors.toList());
        Map<String, Integer> addTrustAuditStatusMap = adminCfReportAddTrustBiz.getAuditStausMapByInfoUuids(infoUuidList);

        List<AdminWorkOrderReportVo> reportVos = Lists.newArrayList();
        AdminWorkOrderReportVo reportVo = null;
        for (AdminWorkOrderReport report : reports) {
            reportVo = new AdminWorkOrderReportVo();
            reportVo.setId(report.getId());
            reportVo.setCaseId(report.getCaseId());
            reportVo.setWorkOrderId(report.getWorkOrderId());
            reportVo.setCaseRisk(report.getCaseRisk());
            reportVo.setDealResult(report.getDealResult());
            reportVo.setReportId(report.getReportId());
            reportVo.setAppointTime(report.getAppointTime());

            AdminWorkOrder adminWorkOrder = workOrderMap.get((long)report.getWorkOrderId());
            reportVo.setOperatorId(adminWorkOrder.getOperatorId());
            reportVo.setLastOperationTime(adminWorkOrder.getUpdateTime());

            CrowdfundingInfo info = infoMap.get(report.getCaseId());
            if (info == null) {
                continue;
            } else {
                reportVo.setAmount(info.getAmount());
            }

            CrowdfundingReport crowdfundingReport = crowdfundingReportMap.get(report.getReportId());
            if (crowdfundingReport == null) {
                continue;
            }else {
                reportVo.setIsNewReport(crowdfundingReport.getIsNewreport());

            }

            CfCashPauseBooleanPo pauseBooleanPo = pauseBooleanPoMap.get(report.getCaseId());
            if (pauseBooleanPo != null) {
                reportVo.setPauseState(pauseBooleanPo.isPause() ? 1 :0 );
            }

            if (MapUtils.isNotEmpty(addTrustAuditStatusMap) && addTrustAuditStatusMap.containsKey(info.getInfoId())) {
                reportVo.setAddTrustAuditStatus(addTrustAuditStatusMap.getOrDefault(info.getInfoId(), -1));
            }else {
                reportVo.setAddTrustAuditStatus(-1);
            }

            reportVos.add(reportVo);
        }

        //排序
        reportVos.sort((o1, o2) -> {
            if (o1.getCaseRisk() == o2.getCaseRisk()) {
                return o1.getId() - o2.getId();
            } else {
                return o2.getCaseRisk() - o1.getCaseRisk();
            }
        });

        return reportVos;
    }

    @Override
    public List<AdminWorkOrderReport> getAdminWorkOrderReportById(List<Integer> ids) {
        return adminWorkOrderReportDao.getAdminWorkOrderReportById(ids);
    }

    @Override
    public int getAdminWorkOrderReportCount(int orderType, int orderTask, String startTime, String endTime) {
        return adminWorkOrderReportDao.getAdminWorkOrderReportCount(orderType, orderTask, startTime, endTime);
    }

    @Override
    public int updateCaseRisk(int id, int caseRisk) {
        return adminWorkOrderReportDao.updateCaseRisk(id, caseRisk);
    }

    @Diff(diffMethod = "selectAdminWorkOrderReportFromEs", diffCompare = "com.shuidihuzhu.cf.diff.SelectAdminWorkOrderReportCompare")
    @Override
    public List<AdminWorkOrderReportVo> selectAdminWorkOrderReport(int lostContact, Integer addTrustStatus, Integer
            followStatus, Integer caseRisk, String startTime, String endTime,
            int current, int pageSize, Integer isDrawCash, String reportFollowOperator, Integer realName) {

        reportFollowOperator = StringUtils.trimToNull(reportFollowOperator);
        List<Integer> operatorIds = null;
        if (StringUtils.isNotBlank(reportFollowOperator)){
            List<AdminUserAccountModel> adminUserAccountModels = seaAccountClientV1.getUserAccountsByNameLike(reportFollowOperator).getResult();
            if (CollectionUtils.isNotEmpty(adminUserAccountModels)) {
                operatorIds = adminUserAccountModels.stream().map(AdminUserAccountModel::getId).collect(Collectors.toList());
            }
        }
        PageHelper.startPage(current, pageSize);

        return adminWorkOrderReportDao.selectAdminWorkOrderReport(lostContact, addTrustStatus, followStatus,
                caseRisk,
                startTime,
                endTime,
                isDrawCash , operatorIds, realName);
    }

    @Override
    public Pair<Long, List<AdminWorkOrderReportVo>> selectAdminWorkOrderReportFromEs(int lostContact, Integer addTrustStatus, Integer followStatus
            , Integer caseRisk, String startTime, String endTime, int current, int pageSize, Integer isDrawCash, String reportFollowOperator, Integer realName) {

        reportFollowOperator = StringUtils.trimToNull(reportFollowOperator);
        List<Integer> operatorIds = null;
        if (StringUtils.isNotBlank(reportFollowOperator)){
            List<AdminUserAccountModel> adminUserAccountModels = seaAccountClientV1.getUserAccountsByNameLike(reportFollowOperator).getResult();
            if (CollectionUtils.isNotEmpty(adminUserAccountModels)) {
                operatorIds = adminUserAccountModels.stream().map(AdminUserAccountModel::getId).collect(Collectors.toList());
            }
        }

        CfWorkOrderIndexSearchParam searchParam = new CfWorkOrderIndexSearchParam();
        searchParam.setAwoOrderTypes(Lists.newArrayList(AdminWorkOrderConst.Type.CASE_REPORT.getCode()));
        searchParam.setAwoOrderTasks(Lists.newArrayList(AdminWorkOrderConst.Task.CASE_REPORT_DEAL.getCode()));

        if (addTrustStatus != null) {
            searchParam.setCratAuditStatuses(Lists.newArrayList(addTrustStatus));
        }

        if (followStatus != null) {
            searchParam.setAworDealResults(Lists.newArrayList(followStatus));
        }

        if (caseRisk != null) {
            searchParam.setAworCaseRisks(Lists.newArrayList(caseRisk));
        }

        if (StringUtils.isNotBlank(startTime)) {
            long createStart = AdminDateUtil.convertToMills(startTime, com.shuidihuzhu.common.util.DateUtil.DATETIME_PATTERN_2);
            searchParam.setAworCreateTimeStart(createStart);
        }
        if (StringUtils.isNotBlank(endTime)) {
            long createEnd = AdminDateUtil.convertToMills(endTime, com.shuidihuzhu.common.util.DateUtil.DATETIME_PATTERN_2);
            searchParam.setAworCreateTimeEnd(createEnd);
        }

        if (isDrawCash != null && isDrawCash == 0) {
            searchParam.setCsPauseStates(Lists.newArrayList(1));
        } else if (isDrawCash != null && isDrawCash == 1) {
            searchParam.setCsPauseStates(Lists.newArrayList(0));
        }

        if (lostContact != 0) {
            searchParam.setAclcLosts(Lists.newArrayList(lostContact));
        }

        if (CollectionUtils.isNotEmpty(operatorIds)) {
            searchParam.setAwoOperatorIds(operatorIds.stream().map(x -> x.longValue()).collect(Collectors.toList()));
        }

        if (realName != null) {
            searchParam.setCrRealNameReport(Lists.newArrayList(realName));
        }

        searchParam.setFrom((current - 1) * pageSize);
        searchParam.setSize(pageSize);
        searchParam.setSortEnums(Lists.newArrayList(CfWorkOrderIndexSortEnum.AWOR_CASE_RISK_DESC, CfWorkOrderIndexSortEnum.AWOR_ID_ASC));

        Pair<Long, List<AdminWorkOrder>> pair = cfSearch.cfWorkOrderIndexSearch(searchParam);
        return Pair.of(pair.getLeft(), getAdminWorkOrderReportVos(pair.getRight()));
    }

    @Override
    public int updateDealResultById(int id, int dealResult) {
        return adminWorkOrderReportDao.updateDealResultById(id, dealResult);
    }

    @Override
    public int getCount(int orderType, int orderTask, String startTime, String endTime) {
        return adminWorkOrderReportDao.getCount(orderType, orderTask, startTime, endTime);
    }

    @Override
    public int getDealCount(int orderType, int orderTask, String startTime, String endTime, int dealResult) {
        return adminWorkOrderReportDao.getDealCount(orderType, orderTask, startTime, endTime, dealResult);
    }

    @Override
    public int getOldFollowCount(int orderType, int orderTask, String startTime, int dealResult) {
        return adminWorkOrderReportDao.getOldFollowCount(orderType, orderTask, startTime, dealResult);
    }

    @Override
    public List<AdminWorkOrderDataVo> getNoNeedDealCountByUserIds(int orderType, int orderTask, String startTime, String endTime, int dealResult, List<Integer> userIds) {
        return adminWorkOrderReportDao.getNoNeedDealCountByUserIds(orderType, orderTask, startTime, endTime, dealResult, userIds);
    }

    @Override
    public List<AdminWorkOrderDataVo> getOldFollowCountByUserIds(int orderType, int orderTask, String startTime, int dealResult, List<Integer> userIds) {
        return adminWorkOrderReportDao.getOldFollowCountByUserIds(orderType, orderTask, startTime, dealResult, userIds);
    }

    @Override
    public List<AdminWorkOrderReport> selectWorkOrderReportByCaseIds(List<Integer> caseIds) {
        return adminWorkOrderReportDao.selectWorkOrderReportByCaseIds(caseIds);
    }

    @Override
    public  List<AdminWorkOrderReportVo> getWorkOrderOperatorIdByCaseIds(List<Integer> caseIds) {
        return adminWorkOrderReportDao.getWorkOrderOperatorIdByCaseIds(caseIds);
    }

    @Override
    public int updateFollowTypeById(int followType, int id) {
        return adminWorkOrderReportDao.updateFollowTypeById(followType,id);
    }

    @Override
    public List<AdminWorkOrderDataVo> getCountByUserIds(int orderType, int orderTask, String startTime, String endTime, List<Integer> userIds) {
        return adminWorkOrderReportDao.getCountByUserIds(orderType,orderTask,startTime,endTime,userIds);
    }

    @Override
    public AdminWorkOrderReport selectWorkOrderReportById(int id) {
        return adminWorkOrderReportDao.selectWorkOrderReportById(id);
    }

    @Override
    public Map<String, Object> getReportWorkStat(Date today, Date yesterday, Set<Integer> userIds) {

        int newNum = adminWorkOrderReportDao.getNewNum(today,yesterday);

        int newActivityNum = adminWorkOrderReportDao.getNewActivityNum(today,yesterday);

        int allActivityNum = adminWorkOrderReportDao.getAllActivityNum(today,yesterday);

        int doingNum = adminWorkOrderReportDao.getDoingNum();

        int finishNum =adminWorkOrderReportDao.getFinishNum(today,yesterday);

        int finishNewNum =adminWorkOrderReportDao.getFinishNewNum(today,yesterday);

        int lostNum =adminWorkOrderReportDao.getLostNum(today,yesterday);

        int risktNum =adminWorkOrderReportDao.getRisktNum(today,yesterday);

        List<ReportWorkStat> list = countReportWorkStat(today,yesterday, Lists.newArrayList(userIds));

        Map<String, Object> map = Maps.newHashMap();

        map.put("newNum",newNum);
        map.put("newActivityNum",newActivityNum);
        map.put("allActivityNum",allActivityNum+doingNum);
        map.put("finishNum",finishNum);
        map.put("finishNewNum",finishNewNum);
        map.put("lostNum",lostNum);
        map.put("risktNum",risktNum);
        map.put("list", list);

        return map;
    }


    private List<ReportWorkStat> countReportWorkStat(Date today, Date yesterday, List<Integer> userIds){


        List<AdminUserAccountModel> adminUserAccountModels = seaAccountClientV1.getUserAccountsByIds(userIds).getResult();

        List<ReportWorkStat> newNum = adminWorkOrderReportDao.getNewNumByUser(today,yesterday,userIds);
        Map<Integer,Integer> newNumMap = newNum.stream().collect(Collectors.toMap(ReportWorkStat::getOperatorId,ReportWorkStat::getNewNum));


        List<ReportWorkStat> activityNum = adminWorkOrderReportDao.getNewActivityNumByUser(today,yesterday,userIds);
        Map<Integer,Integer> newActivityNumMap = activityNum.stream().collect(Collectors.toMap(ReportWorkStat::getOperatorId,ReportWorkStat::getNewActivityNum));

        List<ReportWorkStat> doingNum = adminWorkOrderReportDao.getDoingNumByUser(userIds);
        Map<Integer,Integer> doingNumNumMap = doingNum.stream().collect(Collectors.toMap(ReportWorkStat::getOperatorId,ReportWorkStat::getAllActivityNum));

        List<ReportWorkStat> allActivityNum = adminWorkOrderReportDao.getAllActivityNumByUser(today,yesterday,userIds);
        Map<Integer,Integer> allActivityNumMap = allActivityNum.stream().collect(Collectors.toMap(ReportWorkStat::getOperatorId,ReportWorkStat::getAllActivityNum));

        List<ReportWorkStat> finishNum = adminWorkOrderReportDao.getFinishNumByUser(today,yesterday,userIds);
        Map<Integer,Integer> finishNumMap = finishNum.stream().collect(Collectors.toMap(ReportWorkStat::getOperatorId,ReportWorkStat::getFinishNum));


        List<ReportWorkStat> finishNewNum = adminWorkOrderReportDao.getFinishNewNumByUser(today,yesterday,userIds);
        Map<Integer,Integer> finishNewNumMap = finishNewNum.stream().collect(Collectors.toMap(ReportWorkStat::getOperatorId,ReportWorkStat::getFinishNewNum));


        List<ReportWorkStat> lostNum = adminWorkOrderReportDao.getLostNumByUser(today,yesterday,userIds);
        Map<Integer,Integer> lostNumMap = lostNum.stream().collect(Collectors.toMap(ReportWorkStat::getOperatorId,ReportWorkStat::getLostNum));

        List<ReportWorkStat> risktNum = adminWorkOrderReportDao.getRisktNumByUser(today,yesterday,userIds);
        Map<Integer,Integer> risktNumMap = risktNum.stream().collect(Collectors.toMap(ReportWorkStat::getOperatorId,ReportWorkStat::getRisktNum));

        List<ReportWorkStat> list  = adminUserAccountModels.stream().map(u -> {

            ReportWorkStat reportWorkStat = new ReportWorkStat();

            int userId = u.getId();
            reportWorkStat.setOperatorId(userId);
            reportWorkStat.setOperatorName(u.getName());

            if ( newNumMap!=null && newNumMap.containsKey(userId)){
                reportWorkStat.setNewNum(newNumMap.get(userId));
            }
            if ( newActivityNumMap!=null && newActivityNumMap.containsKey(userId)){
                reportWorkStat.setNewActivityNum(newActivityNumMap.get(userId));
            }

            if ( allActivityNumMap!=null && allActivityNumMap.containsKey(userId)){
                reportWorkStat.setAllActivityNum(allActivityNumMap.get(userId));
            }

            if ( doingNumNumMap!=null && doingNumNumMap.containsKey(userId)){
                reportWorkStat.setAllActivityNum(reportWorkStat.getAllActivityNum() + doingNumNumMap.get(userId));
            }

            if ( finishNumMap!=null && finishNumMap.containsKey(userId)){
                reportWorkStat.setFinishNum(finishNumMap.get(userId));
            }
            if ( finishNewNumMap!=null && finishNewNumMap.containsKey(userId)){
                reportWorkStat.setFinishNewNum(finishNewNumMap.get(userId));
            }
            if ( lostNumMap!=null && lostNumMap.containsKey(userId)){
                reportWorkStat.setLostNum(lostNumMap.get(userId));
            }
            if ( risktNumMap!=null && risktNumMap.containsKey(userId)){
                reportWorkStat.setRisktNum(risktNumMap.get(userId));
            }

            return reportWorkStat;

        }).collect(Collectors.toList());

        return list;

    }


    @Override
    public List<AdminReportDataVo> getAdminReportDataVo( int current, int pageSize,Long workId, Integer status, Long operator, Long caseId, String startDealTime,String endDealTime,
                                                         String startHandleTime,String endHandleTime,String startCreateTime,String endCreateTime) {

        PageHelper.startPage(current, pageSize);
        List<AdminReportDataVo> list = adminWorkOrderReportDao.getAdminReportDataVo(workId,status,operator,caseId,startDealTime,endDealTime,
                startHandleTime,endHandleTime,startCreateTime,endCreateTime);

        if (CollectionUtils.isEmpty(list)){
            return null;
        }


        List<Integer> userIds = list.stream().filter(l->l.getOperatorId()>0).map(AdminReportDataVo::getOperatorId).collect(Collectors.toList());
        List<AdminUserAccountModel> adminUserAccountModels = seaAccountClientV1.getUserAccountsByIds(userIds).getResult();
        Map<Integer,String> userMap = adminUserAccountModels.stream().collect(Collectors.toMap(AdminUserAccountModel::getId,AdminUserAccountModel::getName));


        List<Long> workIds = list.stream().map(v -> v.getWorkId()).collect(Collectors.toList());
        List<AdminReportDataVo> counts = adminWorkOrderReportDao.getAdminReportCount(workIds);
        Map<Long,Integer> map = counts.stream().collect(Collectors.toMap(AdminReportDataVo::getWorkId,AdminReportDataVo::getReportCount));


        List<AdminWorkReportMap> adminWorkReportMaps =  adminWorkOrderReportDao.getWorkReportMapList(workIds);
        Map<Long,List<AdminWorkReportMap>> WorkReportMap = adminWorkReportMaps.stream().collect(Collectors.groupingBy(AdminWorkReportMap::getWorkOrderId));


        list.forEach(v -> {
            if (map.containsKey(v.getWorkId())){
                v.setReportCount(map.get(v.getWorkId()));
            }
            if (userMap.containsKey(v.getOperatorId())){
                v.setName(userMap.get(v.getOperatorId()));
            }
            v.setStatusStr(AdminWorkOrderConst.Result.getByCode(v.getStatus()).getWord());

            if (WorkReportMap.containsKey(v.getWorkId())){
                AdminWorkReportMap reportMap = WorkReportMap.get(v.getWorkId()).get(0);
                v.setPic(reportMap.getPic());
                v.setContent(reportMap.getContent());
                v.setTime(reportMap.getCreateTime());
            }

        });

        return list;
    }


    @Override
    public int addAdminWorkReportMap(AdminWorkReportMap map) {
        return adminWorkOrderReportDao.addAdminWorkReportMap(map);
    }

    @Override
    public List<AdminWorkReportMap> getLastAdminWorkReportMap(long workOrderId) {
        return adminWorkOrderReportDao.getLastAdminWorkReportMap(workOrderId);
    }


    @Override
    public int updateHandkeTime(List<Long> workOrderIdList) {
        return adminWorkOrderReportDao.updateHandkeTime(workOrderIdList);
    }

    @Override
    public AdminWorkReportMap getAdminWorkReportMapByReportId(long reportId) {
        return adminWorkOrderReportDao.getAdminWorkReportMapByReportId(reportId);
    }

    @Override
    public boolean isSafe(int caseId) {
        AdminWorkOrderReport report = adminWorkOrderReportDao.getOneByCaseIdAndStatusList(caseId,
                AdminWorkOrderReportConst.DealResult.UN_SAFE_CODE_LIST);
        return report == null;
    }

    @Override
    public boolean editAppointTime(int workOrderId, String appointTime) {
        Date date = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss").parseDateTime(appointTime).toDate();
        return adminWorkOrderReportDao.updateAppointTime(workOrderId, date) >= 1;
    }

    @Override
    public List<AdminWorkOrderReport> findByCaseIdAndDealResult(int caseId, List<Integer> dealResults) {
        return adminWorkOrderReportDao.findByCaseIdAndDealResult(caseId, dealResults);
    }

    @Override
    public AdminWorkOrderReport findByCaseIdAndReportId(int caseId, int reportId) {
        return adminWorkOrderReportDao.findByCaseIdAndReportId(caseId, reportId);
    }

    @Override
    public Pair<Long, List<AdminWorkOrderReportVo>> getSelectAdminWorkOrderReportEs(String title, Integer caseId, Long realIdLong, String name, int current, int pageSize) {

        CfWorkOrderIndexSearchParam searchParam = new CfWorkOrderIndexSearchParam();
        searchParam.setAwoOrderTypes(Lists.newArrayList(AdminWorkOrderConst.Type.CASE_REPORT.getCode()));
        searchParam.setAwoOrderTasks(Lists.newArrayList(AdminWorkOrderConst.Task.CASE_REPORT_DEAL.getCode()));

        if(StringUtils.isNotBlank(title)){
            searchParam.setCfTitle(title);
        }

        if (caseId != null && caseId != 0) {
            Long esCaseId = caseId.longValue();
            searchParam.setCfCaseIds(Lists.newArrayList(esCaseId));
        }

        if (realIdLong != null && realIdLong != 0) {
            searchParam.setCfUserIds(Lists.newArrayList(realIdLong));
        }

        if (StringUtils.isNotBlank(name)) {
            searchParam.setCaName(name);
        }

        searchParam.setFrom((current - 1) * pageSize);
        searchParam.setSize(pageSize);
        searchParam.setSortEnums(Lists.newArrayList(CfWorkOrderIndexSortEnum.AWOR_CASE_RISK_DESC, CfWorkOrderIndexSortEnum.AWOR_ID_ASC));

        Pair<Long, List<AdminWorkOrder>> pair = cfSearch.cfWorkOrderIndexSearch(searchParam);
        return Pair.of(pair.getLeft(), getAdminWorkOrderReportVos(pair.getRight()));
    }
}
