package com.shuidihuzhu.cf.biz.crowdfunding.report.impl;

import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.report.AdminCfInfoLostContactService;
import com.shuidihuzhu.cf.biz.crowdfunding.CfReportFollowCommentBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfInfoLostContactDAO;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.domain.cf.CfInfoLostContactDO;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.FinanceApproveService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2018-07-26  17:24
 */
@Service
public class AdminCfInfoLostContactServiceImpl implements AdminCfInfoLostContactService {

    private static final int MAX_REASON_LENGTH = 2048;
    private static final String LOST_CONTACT_REPORT_FOLLOW_COMMENT_TAG = "案例失联";
    private static final String BACK_LOST_CONTACT_REPORT_FOLLOW_COMMENT_TAG = "恢复联系";

    @Resource
    private AdminCfInfoLostContactDAO adminCfInfoLostContactDAO;

    @Resource
    private CfReportFollowCommentBiz cfReportFollowCommentBiz;

    @Resource
    private SeaAccountClientV1 seaAccountClientV1;

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Autowired
    private FinanceApproveService financeApproveService;

    @Override
    public CfInfoLostContactDO getLastByInfoUuid(String infoUuid) {
        return adminCfInfoLostContactDAO.getValidByInfoUuid(infoUuid);
    }

    @Override
    public boolean insert(CfInfoLostContactDO cfInfoLostContactDO) {
        return adminCfInfoLostContactDAO.insert(cfInfoLostContactDO) > 0;
    }

    @Override
    public boolean hasLost(String infoUuid) {
        CfInfoLostContactDO v = getLastByInfoUuid(infoUuid);
        if (v == null) {
            return false;
        }
        return val2HasLost(v.getLost());
    }

    @Override
    public OpResult update(int userId, String infoUuid, boolean hasLost, String reason) {

        // 失联必填原因
        if (hasLost && StringUtils.isEmpty(reason)) {
            OpResult.createFailResult(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        // 原因过长
        if (StringUtils.length(reason) > MAX_REASON_LENGTH) {
            OpResult.createFailResult(CfErrorCode.SYSTEM_PARAM_ERROR_TOO_LONG);
        }

        // 设置之前的记录为失效状态
        adminCfInfoLostContactDAO.setOldRecordInvalid(infoUuid);

        int lost = hasLost2Val(hasLost);
        CfInfoLostContactDO v = new CfInfoLostContactDO();
        v.setLost(lost);
        v.setReason(reason);
        v.setInfoUuid(infoUuid);
        v.setValid(1);
        boolean insertSuc = insert(v);

        // 获取运营名字
        AdminUserAccountModel userAccount = this.seaAccountClientV1.getValidUserAccountById(userId).getResult();
        String name = userAccount.getName();
        // 获取筹款id
        CrowdfundingInfo fundingInfo = crowdfundingDelegate.getCrowdfundingInfoByInfoId(infoUuid);
        int infoId = fundingInfo.getId();

        String tag = hasLost ? LOST_CONTACT_REPORT_FOLLOW_COMMENT_TAG : BACK_LOST_CONTACT_REPORT_FOLLOW_COMMENT_TAG;
        cfReportFollowCommentBiz.saveWithTag(userId, tag, reason, infoId, name);

        //1:失联，没有定义枚举？
        String operation = lost == 1 ? "标记失联" : "取消标记失联";
        String comment = lost == 1 ? "将案例标记为失联,原因:" + reason : "案例被取消标记失联";
        financeApproveService.addApprove(fundingInfo, operation, comment, userId);

        return OpResult.createResult(insertSuc, CfErrorCode.SYSTEM_ERROR, insertSuc);
    }

    @Override
    public boolean val2HasLost (int lost){
        return lost == 1;
    }

    /**
     * 是否已失联 1为失联 2为未失联
     * @param hasLost
     * @return
     */
    @Override
    public int hasLost2Val (Boolean hasLost){
        if (hasLost == null) {
            return 0;
        }
        return hasLost ? 1 : 2;
    }

}
