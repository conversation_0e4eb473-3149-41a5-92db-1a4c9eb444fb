package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.client.adminpure.model.initial.CfVolunteerRiskRecord;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.delegate.saas.AdminOrganization;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.OrganizationClientV1;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.delegate.saas.SeaAuthClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfCaseAuditClient;
import com.shuidihuzhu.cf.client.material.feign.CfMaterialReadClient;
import com.shuidihuzhu.cf.client.material.model.caseAudit.CfCaseAuditStageDetail;
import com.shuidihuzhu.cf.client.material.feign.CfRaiseMaterialClient;
import com.shuidihuzhu.cf.client.material.model.CfMaterialUserOpTime;
import com.shuidihuzhu.cf.client.material.model.CfRaiseFundUseModel;
import com.shuidihuzhu.cf.client.material.model.caseAudit.CfCaseStageAuditInfoView;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.dao.crowdfunding.CfMaterialVerityHistoryDAO;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingUserDelegate;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceReadFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.vo.PACheckResultVo;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.CrowdfundingInitialAuditInfo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.model.crowdfunding.material.AuditSuggestModifyDetail;
import com.shuidihuzhu.cf.model.crowdfunding.material.credit.CreditSupplementModel;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminCrowdfundingInfoView;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoVo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.CfHospitalNormalService;
import com.shuidihuzhu.cf.service.crowdfunding.CfCaseSensitiveWordService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditOperateService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationItem;
import com.shuidihuzhu.cf.vo.approve.TreatmentVO;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.cf.vo.approve.RiverHandleParamVO;
import com.shuidihuzhu.client.cf.growthtool.model.VolunteerLevelEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.shuidihuzhu.cf.model.crowdfunding.CfMaterialVerityHistory.MaterialInfoExtEnum.MATERIAL_AUDIT_SUGGEST_MODIFY_ID;
import static com.shuidihuzhu.cf.model.crowdfunding.CfMaterialVerityHistory.MaterialInfoExtEnum.MATERIAL_AUDIT_SUGGEST_USER_MODIFY;

@Slf4j
@Service
@RefreshScope
public class CfMaterialVerityHistoryImpl implements CfMaterialVerityHistoryBiz {

    @Autowired
    private CfMaterialVerityHistoryDAO verityHistoryDAO;

    @Autowired
    private AdminCrowdfundingAuthorBiz crowdfundingAuthorBiz;

    @Autowired
    private CfRefuseReasonTagBiz reasonTagBiz;

    @Autowired
    private CfRefuseReasonEntityBiz reasonEntityBiz;

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private ICrowdfundingUserDelegate crowdfundingUserDelegate;

    @Autowired
    private AdminCfRepeatInfoBiz repeatInfoBizService;

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private CfFinanceReadFeignClient readFeignClient;

    @Autowired
    private CfCaseSensitiveWordService cfCaseSensitiveWordService;

    @Autowired
    private OrganizationClientV1 orgClientV1;

    @Autowired
    private SeaAuthClientV1 seaAuthClientV1;

    @Autowired
    private CfMaterialReadClient materialReadClient;

    @Autowired
    private SeaAccountClientV1 accountClientV1;

    @Autowired
    private AdminCrowdfundingInfoStatusBiz infoStatusBiz;

    @Autowired
    private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;

    @Autowired
    private CfCaseAuditClient caseAuditClient;

    @Autowired
    private CfRaiseMaterialClient cfRaiseMaterialClient;

    @Autowired
    private CfHospitalNormalService cfHospitalNormalService;

    @Autowired
    private AdminCfInfoExtBiz adminCfInfoExtBiz;

    @Autowired
    private CommonOperationRecordClient commonOperateClient;
    @Autowired
    private CfVolunteerRiskRecordBiz cfVolunteerRiskRecordBiz;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Autowired
    private MaskUtil maskUtil;

    @Value("${apollo.begin.effect.submit.time:**********}")
    private long beginEffectSubmitTime;

    private static final Set<Integer> MATERIAL_IDS = Sets.newHashSet(
            CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT.getCode(),
            CrowdfundingInfoDataStatusTypeEnum.PATIENT_INFO_SUBMIT.getCode(),
            CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode(),
            CrowdfundingInfoDataStatusTypeEnum.TREATMENT_INFO_SUBMIT.getCode(),
            CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT.getCode(),
            CrowdfundingInfoDataStatusTypeEnum.ID_VERIFY.getCode(),
            CrowdfundingInfoDataStatusTypeEnum.FUND_USE_SUBMIT.getCode()
    );

    // 材料通过和拒绝的记录
    @Override
    @Async("materialVerityHistoryExecutor")
    public void recordVerityHistory(CfMaterialVerityHistory.CfMaterialVerityHistoryRecord historyRecord, long workOrderId, long approveControlId) {

        try {
            List<CfMaterialVerityHistory> verityHistories = Lists.newArrayList();

            recordPassHistory(historyRecord, verityHistories);
            recordRejectHistory(historyRecord, verityHistories);

            if (CollectionUtils.isNotEmpty(verityHistories)) {

                Map<Integer, CfMaterialVerityHistory.MaterialOpTimeObj> opTimeObjMap =
                        getMaterialOpTime(verityHistories.get(0).getCaseId());
                String operateDetail = queryOperateDetail(historyRecord.getUserId());

                for (CfMaterialVerityHistory verityHistory : verityHistories) {
                    verityHistory.setWorkOrderId(workOrderId);
                    verityHistory.setApproveControlId(approveControlId);
                    CfMaterialVerityHistory.MaterialOpTimeObj optTimeObj = opTimeObjMap.get(verityHistory.getMaterialId());

                    verityHistory.setMaterialOpTime(optTimeObj == null ? "" : optTimeObj.getMaterialOpTime());
                    verityHistory.setMaterialOpTimeType(optTimeObj == null ? 0 : optTimeObj.getMaterialOpType());
                    verityHistory.setOperatorDetail(operateDetail);
                }
                handleSnapshotSave(verityHistories);
                this.insertList(verityHistories);
                log.info("运营材料审核历史保存 record:{}", verityHistories);
            }

            totalRecordCaseApprove(historyRecord);
        } catch (Throwable e) {
            log.error("运营材料审核历史保存异常 record:{}, 异常", historyRecord, e);
        }
    }

    private void recordPassHistory(CfMaterialVerityHistory.CfMaterialVerityHistoryRecord historyRecord,
                                   List<CfMaterialVerityHistory> verityHistories) {
        if (CollectionUtils.isEmpty(historyRecord.getPassIds())) {
            return;
        }

        for (Integer passId : historyRecord.getPassIds()) {

            CfMaterialVerityHistory passVerityHistory = new CfMaterialVerityHistory();
            buildCommonField(historyRecord, passVerityHistory);
            passVerityHistory.setHandleType(CfMaterialVerityHistory.PASS_TYPE);
            passVerityHistory.setMaterialId(passId);
            passVerityHistory.setRefuseIds("");
            fillMaterialInfoById(historyRecord.getCaseId(), historyRecord.getInfoId(), passId, passVerityHistory);

            verityHistories.add(passVerityHistory);
        }
    }

    private void recordRejectHistory(CfMaterialVerityHistory.CfMaterialVerityHistoryRecord historyRecord,
                                     List<CfMaterialVerityHistory> verityHistories) {
        if (CollectionUtils.isEmpty(historyRecord.getRejectIds())) {
            return;
        }

        List<CfRefuseReasonEntity> reasonEntitys = reasonEntityBiz.selectByIds(historyRecord.getRejectIds());
        List<Integer> tagIds = reasonEntitys.stream().map(CfRefuseReasonEntity::getTagId).collect(Collectors.toList());
        List<CfRefuseReasonTag> allCfRefuseTags = reasonTagBiz.selectByTagIds(Sets.newHashSet(tagIds));
        Map<Integer, List<Integer>> dateType2Reason = Maps.newHashMap();
        for (CfRefuseReasonEntity reasonEntity : reasonEntitys) {
            for (CfRefuseReasonTag reasonTag : allCfRefuseTags) {
                if (reasonEntity.getTagId() == reasonTag.getId()) {
                    List<Integer> reasonIds = dateType2Reason.get(reasonTag.getDataType());
                    if (reasonIds == null) {
                        reasonIds = Lists.newArrayList();
                        dateType2Reason.put(reasonTag.getDataType(), reasonIds);
                    }
                    reasonIds.add(reasonEntity.getId());
                    break;
                }
            }
        }

        for (Map.Entry<Integer, List<Integer>> entry : dateType2Reason.entrySet()) {
            // fix 兼容前端bug，前端会将一些用户没有勾选过的id 都会传到后台。
            if (!MATERIAL_IDS.contains(entry.getKey())) {
                log.warn("审核的材料类型不是材料审核的材料, id:{}", entry.getKey());
                continue;
            }
            CfMaterialVerityHistory rejectVerityHistory = new CfMaterialVerityHistory();
            buildCommonField(historyRecord, rejectVerityHistory);
            rejectVerityHistory.setHandleType(CfMaterialVerityHistory.REJECT_TYPE);
            rejectVerityHistory.setMaterialId(entry.getKey());
            rejectVerityHistory.setRefuseIds(Joiner.on(CfMaterialVerityHistory.MATERIAL_VERITY_SEPARATOR).join(entry.getValue()));
            fillMaterialInfoById(historyRecord.getCaseId(), historyRecord.getInfoId(), entry.getKey(), rejectVerityHistory);

            fillRejectAuditSuggest(rejectVerityHistory, historyRecord.getSuggestModifyDetails());
            verityHistories.add(rejectVerityHistory);
        }
    }

    // 修改建议的历史记录
    private void fillRejectAuditSuggest(CfMaterialVerityHistory verityHistory,
                                  List<AuditSuggestModifyDetail> suggestDetails) {

        if (CollectionUtils.isEmpty(suggestDetails)) {
            return;
        }

        for (AuditSuggestModifyDetail suggest : suggestDetails) {
            if (suggest.getMaterialId() == verityHistory.getMaterialId()) {

                Map<String, String> extInfo = null;
                if (StringUtils.isNotBlank(verityHistory.getMaterialInfoExt())) {
                    extInfo = JSON.parseObject(verityHistory.getMaterialInfoExt(),
                            new TypeReference<Map<String, String>>() {});
                }
                if (extInfo == null) {
                    extInfo = Maps.newHashMap();
                }

                if (StringUtils.isNotBlank(suggest.getSuggestUserModify())) {
                    extInfo.put(MATERIAL_AUDIT_SUGGEST_USER_MODIFY.getWord(), suggest.getSuggestUserModify());
                }
                Set<Integer> modifyIds = suggest.getAllModifyIds();
                if (CollectionUtils.isNotEmpty(modifyIds)) {
                    extInfo.put(MATERIAL_AUDIT_SUGGEST_MODIFY_ID.getWord(), Joiner.on(",").join(modifyIds));
                }

                verityHistory.setMaterialInfoExt(JSON.toJSONString(extInfo));

                break;
            }
        }
    }

    private void buildCommonField(CfMaterialVerityHistory.CfMaterialVerityHistoryRecord historyRecord,
                                  CfMaterialVerityHistory passVerityHistory) {

        passVerityHistory.setCaseId(historyRecord.getCaseId());
        passVerityHistory.setInfoId(historyRecord.getInfoId());
        passVerityHistory.setMaterialPicInfo(queryCfPic(historyRecord.getCaseId()));
        passVerityHistory.setOperatorId(historyRecord.getUserId());
        passVerityHistory.setComment(StringUtils.trimToEmpty(historyRecord.getComment()));
        passVerityHistory.setOperatorType(queryUserOperatorType(historyRecord.getUserId()));
    }

    // 材料的保存或提交时间
    public Map<Integer, CfMaterialVerityHistory.MaterialOpTimeObj> getMaterialOpTime(int caseId) {
        RpcResult<CfMaterialUserOpTime> userOpTimeResult = materialReadClient.selectUserOpMaterialTime(caseId);

        Map<Integer, CfMaterialVerityHistory.MaterialOpTimeObj> result = Maps.newHashMap();
        if (userOpTimeResult != null && userOpTimeResult.getData() != null
            && (MapUtils.isNotEmpty(userOpTimeResult.getData().getSubmitReviewTimeDetail())
                || MapUtils.isNotEmpty(userOpTimeResult.getData().getSaveMaterialTimeDetail()))) {

            Map<CrowdfundingInfoDataStatusTypeEnum, Date> reviewMaterialMap = userOpTimeResult.getData().getSubmitReviewTimeDetail();
            Map<CrowdfundingInfoDataStatusTypeEnum, Date> saveMaterialMap = userOpTimeResult.getData().getSaveMaterialTimeDetail();
            for (CrowdfundingInfoDataStatusTypeEnum currType : CrowdfundingInfoDataStatusTypeEnum.values()) {
                Date submitTime = reviewMaterialMap != null ? reviewMaterialMap.get(currType) : null;
                Date saveTime =  saveMaterialMap != null ? saveMaterialMap.get(currType) : null;
                if ((saveTime == null && submitTime == null) || (saveTime == null && submitTime != null) ) {
                    continue;
                }
                if (submitTime == null || saveTime.after(submitTime)) {
                    result.put(currType.getCode(), new CfMaterialVerityHistory.MaterialOpTimeObj(DateUtil.formatDateTime(saveTime),
                            CfMaterialVerityHistory.MATERIAL_SAVE));
                } else {
                    result.put(currType.getCode(), new CfMaterialVerityHistory.MaterialOpTimeObj(DateUtil.formatDateTime(submitTime),
                            CfMaterialVerityHistory.MATERIAL_SUBMIT));
                }
            }
        }
        return result;
    }

    // 查询用户的组织-name
    @Override
    public String queryOperateDetail(int userId) {


        AdminUserAccountModel model = accountClientV1.getValidUserAccountById(userId).getResult();
        String name = Objects.nonNull(model) ? model.getName() : "";

        AuthRpcResponse<Map<Integer, List<AdminOrganization>>> userRoleModel = orgClientV1.getUserOrgs(Arrays.asList(userId));
        if (userRoleModel == null || userRoleModel.getResult() == null || userRoleModel.getResult().get(userId) == null) {
            return name;
        }

        List<String> orgName = Lists.newArrayList();
        for (AdminOrganization org : userRoleModel.getResult().get(userId)) {
            orgName.add(org.getName());
        }
        if (StringUtils.isNotBlank(name)) {
            orgName.add(name);
        }

        return Joiner.on("-").join(orgName);
    }

    @Override
    public int getCount(int caseId, int materialId, int handleType) {
        return verityHistoryDAO.getCount(caseId, materialId, handleType);
    }

    public int queryUserOperatorType(int userId) {
        AuthRpcResponse<Map<Integer, List<AdminOrganization>>> userRoleModel = orgClientV1.getUserOrgs(Arrays.asList(userId));
        if (userRoleModel == null || userRoleModel.getResult() == null || userRoleModel.getResult().get(userId) == null) {
            return CfMaterialVerityHistory.OTHER_OPERATOR_TYPE;
        }

        List<AdminOrganization> roleModel = userRoleModel.getResult().get(userId);

        String primaryOrg = "";
        for (AdminOrganization adminOrganization : roleModel) {
            String name = adminOrganization.getName();
            if (CfMaterialVerityHistory.containsPrimaryOrg(name)) {
                primaryOrg = name;
            }
            CfMaterialVerityHistory.LastOrgName lastOrgName = CfMaterialVerityHistory.findByDesc(primaryOrg, adminOrganization.getName());
            if (lastOrgName!= null) {
                return lastOrgName.getCode();
            }
        }
        return CfMaterialVerityHistory.OTHER_OPERATOR_TYPE;
    }

    @Override
    public PageInfo<CfMaterialVerityHistory.CfMaterialVerityHistoryVo> queryMaterialVerityHistory(String infoId,
                                                                                              int materialId,
                                                                                              int handleType,
                                                                                              int current,
                                                                                              int size) {

        PageInfo<CfMaterialVerityHistory.CfMaterialVerityHistoryVo> result = new PageInfo<>();

        result.setTotal(verityHistoryDAO.countByMaterialType(infoId, materialId, handleType));
        List<CfMaterialVerityHistory> verityHistories =  verityHistoryDAO.selectByMaterialType(infoId, materialId,
                handleType, (current - 1) * size, size);

        List<CfMaterialVerityHistory.CfMaterialVerityHistoryVo> historyVoList = Lists.newArrayList();

        for (CfMaterialVerityHistory verityHistory : verityHistories) {
            setDecrypt(verityHistory);
            historyVoList.add(buildHistoryVO(verityHistory));
        }
        result.setList(historyVoList);
        return result;
    }

    public void handleSnapshotSave(List<CfMaterialVerityHistory> verityHistories) {

        if (CollectionUtils.isEmpty(verityHistories) || verityHistories.get(0).getWorkOrderId() == 0) {
            return ;
        }

        String currentTime = DateUtil.formatDateTime(new Date());
        List<CfMaterialVerityHistory.CfMaterialVerityHistoryVo> historyVos = convertOperateViews(verityHistories, currentTime);
        Map<Integer, CrowdfundingInfoStatus> allMaterialStatus = infoStatusBiz.getByInfoUuid(verityHistories.get(0).getInfoId()).
                stream().collect(Collectors.toMap(CrowdfundingInfoStatus::getType, item->item, (item1, item2) -> item1));

        List<Integer> notHandleMaterialIds = getCurNotHandleMaterialIds(verityHistories);

        for (Integer materialId : notHandleMaterialIds) {

            CfMaterialVerityHistory.CfMaterialVerityHistoryVo lastHistoryVo = queryLastOperate(verityHistories.get(0),
                    materialId, allMaterialStatus, currentTime);

            if (lastHistoryVo != null) {
                historyVos.add(lastHistoryVo);
            }
        }

        log.info("保存案例的审核情况.msg:{}", JSON.toJSONString(historyVos));
        commonOperateClient.create()
                .buildBasicPlatform(verityHistories.get(0).getWorkOrderId(),
                        Math.toIntExact(verityHistories.get(0).getOperatorId()),
                        OperationActionTypeEnum.MATERIAL_INITIAL_OPERATE_DETAIL)
                .buildCaseId(verityHistories.get(0).getCaseId())
                .buildRemark(JSON.toJSONString(historyVos))
                .save();
    }

    private List<Integer> getCurNotHandleMaterialIds(List<CfMaterialVerityHistory> verityHistories) {

        List<Integer> notHandleMaterialIds = Lists.newArrayList();

        CfInfoExt infoExt = adminCfInfoExtBiz.getByCaseId(verityHistories.get(0).getCaseId());
        if (infoExt == null) {
            log.info("不能通过案例id找到infoext caseId:{}", verityHistories.get(0).getCaseId());
            return notHandleMaterialIds;
        }
        List<Integer> requireMaterialIds = CfVersion.getRequiredCaseList(infoExt.getNeedCaseList())
                .stream().map(CrowdfundingInfoDataStatusTypeEnum::getCode).collect(Collectors.toList());
        List<Integer> hasOperateIds = verityHistories.stream().map(CfMaterialVerityHistory::getMaterialId)
                .collect(Collectors.toList());

        for (Integer materialId : requireMaterialIds) {
            if (!hasOperateIds.contains(materialId)) {
                notHandleMaterialIds.add(materialId);
            }
        }

        return notHandleMaterialIds;
    }

    private CfMaterialVerityHistory.CfMaterialVerityHistoryVo queryLastOperate(CfMaterialVerityHistory verityHistory,
                                                                               int materialId,
                                                                               Map<Integer, CrowdfundingInfoStatus> allMaterialStatus,
                                                                               String currentTime) {
        CrowdfundingInfoStatus infoStatus = allMaterialStatus.get(materialId);
        if (infoStatus == null) {
            return null;
        }

        CfMaterialVerityHistory.CfMaterialVerityHistoryVo historyVo = new
                CfMaterialVerityHistory.CfMaterialVerityHistoryVo();

        if (infoStatus.getStatus() == CrowdfundingInfoStatusEnum.PASSED.getCode()) {
            CfMaterialVerityHistory history = this
                    .selectLatestMaterial(verityHistory.getCaseId(), materialId, CfMaterialVerityHistory.PASS_TYPE);

            historyVo =  history != null ? convertOperateView(history, DateUtil.formatDateTime(history.getCreateTime()))
                    : null;
        } else {
            historyVo.setMaterialId(materialId);
            historyVo.setHandleType(3);
            historyVo.setOperatorTime(currentTime);
            historyVo.setOperatorDetail(verityHistory.getOperatorDetail());

        }

        return historyVo;
    }

    private List<CfMaterialVerityHistory.CfMaterialVerityHistoryVo> convertOperateViews(
            List<CfMaterialVerityHistory> verityHistories, String handleTime) {

        List<CfMaterialVerityHistory.CfMaterialVerityHistoryVo> result = Lists.newArrayList();
        for (CfMaterialVerityHistory verityHistory : verityHistories) {
            result.add(convertOperateView(verityHistory, handleTime));
        }

        return result;
    }


    private CfMaterialVerityHistory.CfMaterialVerityHistoryVo convertOperateView(CfMaterialVerityHistory verityHistory,
                                                                                 String handleTime) {
        CfMaterialVerityHistory.CfMaterialVerityHistoryVo historyVo = new
                CfMaterialVerityHistory.CfMaterialVerityHistoryVo();

        historyVo.setMaterialId(verityHistory.getMaterialId());
        historyVo.setHandleType(verityHistory.getHandleType());
        historyVo.setOperatorTime(handleTime);
        historyVo.setOperatorDetail(verityHistory.getOperatorDetail());
        if (verityHistory.getHandleType() == CfMaterialVerityHistory.REJECT_TYPE) {
            historyVo.setRejectReason(getRefuseReason(verityHistory));
        }
        return historyVo;
    }

    @NotNull
    private CfMaterialVerityHistory.CfMaterialVerityHistoryVo buildHistoryVO(CfMaterialVerityHistory verityHistory) {
        if (verityHistory == null) {
            return new CfMaterialVerityHistory.CfMaterialVerityHistoryVo();
        }
        Map<String, Object> materialInfoMap = JSON.parseObject(verityHistory.getMaterialInfo(), new TypeReference<>(){});
        if(materialInfoMap.get("idCard") != null){
            String idType = (String) materialInfoMap.get("idType");
            if (StringUtils.equals(UserIdentityType.identity.name(), idType)) {
                String idCard = (String) materialInfoMap.get("idCard");
                materialInfoMap.put("idCardMask", maskUtil.buildByDecryptStrAndType(idCard, DesensitizeEnum.IDCARD));
                materialInfoMap.put("idCard", StringUtils.EMPTY);
            }
        }
        if(materialInfoMap.get("payeeBankCard" ) != null){
            String payeeBankCard = (String) materialInfoMap.get("payeeBankCard");
            materialInfoMap.put("payeeBankCardMask", maskUtil.buildByDecryptStrAndType(payeeBankCard, DesensitizeEnum.BANKNO));
            materialInfoMap.put("payeeBankCard", StringUtils.EMPTY);
        }
        if(materialInfoMap.get("payeeIdCard" ) != null){
            String idCard = (String) materialInfoMap.get("payeeIdCard");
            materialInfoMap.put("payeeIdCardMask", maskUtil.buildByDecryptStrAndType(idCard, DesensitizeEnum.IDCARD));
            materialInfoMap.put("payeeIdCard", StringUtils.EMPTY);
        }
        if(materialInfoMap.get("payeeMobile" ) != null){
            String mobile = (String) materialInfoMap.get("payeeMobile");
            materialInfoMap.put("payeeMobileMask", maskUtil.buildByDecryptPhone(mobile));
            materialInfoMap.put("payeeMobile", StringUtils.EMPTY);
        }
        if(materialInfoMap.get("hospitalBankCard" ) != null){
            String bankCard = (String) materialInfoMap.get("hospitalBankCard");
            materialInfoMap.put("hospitalBankCardMask", maskUtil.buildByDecryptStrAndType(bankCard, DesensitizeEnum.BANKNO));
            materialInfoMap.put("hospitalBankCard", StringUtils.EMPTY);
        }
        if(materialInfoMap.get("orgMobile" ) != null){
            String mobile = (String) materialInfoMap.get("orgMobile");
            materialInfoMap.put("orgMobileMask", maskUtil.buildByDecryptPhone(mobile));
            materialInfoMap.put("orgMobile", StringUtils.EMPTY);
        }
        if(materialInfoMap.get("orgBankCard" ) != null){
            String bankCard = (String) materialInfoMap.get("orgBankCard");
            materialInfoMap.put("orgBankCardMask", maskUtil.buildByDecryptStrAndType(bankCard, DesensitizeEnum.BANKNO));
            materialInfoMap.put("orgBankCard", StringUtils.EMPTY);
        }
        try {
            if (materialInfoMap.get("firstApproveCaseInfo") != null
                    && ((Map<?, ?>)materialInfoMap.get("firstApproveCaseInfo")).get("specialPrePoseDetail") != null) {
                Map<String, Object> specialPrePoseDetail = (Map<String, Object>)
                        ((Map<?, ?>) materialInfoMap.get("firstApproveCaseInfo")).get("specialPrePoseDetail");
                specialPrePoseDetail.put("mobile", null);
            }
        } catch (Exception e) {
            // pass
        }

        verityHistory.setMaterialInfo(JSONObject.toJSONString(materialInfoMap));
        CfMaterialVerityHistory.CfMaterialVerityHistoryVo historyVo = new CfMaterialVerityHistory.CfMaterialVerityHistoryVo();
        historyVo.setOperatorTime(DateUtil.formatDateTime(verityHistory.getCreateTime()));
        //诊断证明模块
        if (verityHistory.getMaterialId() == 4 && StringUtils.isNotBlank(verityHistory.getMaterialInfo())) {
            TreatmentVO treatmentVO = JSON.parseObject(verityHistory.getMaterialInfo(), TreatmentVO.class);
            treatmentVO.setHospitalAcceptToPublic(cfHospitalNormalService.validateHospitalAcceptToPublic(treatmentVO.getHospitalId(),treatmentVO.getHospitalCode()));
            treatmentVO.setDiagnoseHospitalAcceptToPublic(cfHospitalNormalService.validateHospitalAcceptToPublic(treatmentVO.getDiagnoseHospitalId(),null));
            historyVo.setMaterialInfo(treatmentVO);
        } else if (verityHistory.getMaterialId() == 3 && StringUtils.isNotBlank(verityHistory.getMaterialInfo())) {
            // 收款人信息
            historyVo.setMaterialInfo(materialInfoMap);
            String payeeIdCard = (String) materialInfoMap.getOrDefault("payeeIdCard", StringUtils.EMPTY);
            if (StringUtils.isNotBlank(payeeIdCard)) {
                String payeeIdCardEn = oldShuidiCipher.aesEncrypt(payeeIdCard);
                List<CfVolunteerRiskRecord> riskRecords = cfVolunteerRiskRecordBiz.getByCaseId(verityHistory.getCaseId());
                if (CollectionUtils.isNotEmpty(riskRecords) && riskRecords.stream().anyMatch(r -> r.getPatientIdCard().equals(payeeIdCardEn))) {
                    CfVolunteerRiskRecord item = riskRecords.stream().filter(r -> r.getPatientIdCard().equals(payeeIdCardEn)).findFirst().orElse(new CfVolunteerRiskRecord());
                    materialInfoMap.put("payeeRiskTypeStr", Optional.ofNullable(VolunteerLevelEnum.parseByLevel(item.getRiskType()))
                         .map(VolunteerLevelEnum::getDesc).orElse(StringUtils.EMPTY));
                }
            }
        }else {
            historyVo.setMaterialInfo(parse(verityHistory.getMaterialInfo()));
        }

        historyVo.setMaterialPicInfo(parse(verityHistory.getMaterialPicInfo()));
        historyVo.setMaterialInfoExt(parse(verityHistory.getMaterialInfoExt()));
        historyVo.setOperatorDetail(StringUtils.isNotBlank(verityHistory.getOperatorDetail()) ?
                verityHistory.getOperatorDetail() : queryOperateDetail(verityHistory.getOperatorId()));
        historyVo.setMaterialOpTime(verityHistory.getMaterialOpTime());
        historyVo.setMaterialOpTimeType(verityHistory.getMaterialOpTimeType());

        String reason = getRefuseReason(verityHistory);

        historyVo.setRejectReason(reason);
        historyVo.setWorkOrderId(verityHistory.getWorkOrderId());
        historyVo.setHandleType(verityHistory.getHandleType());
        historyVo.setMaterialId(verityHistory.getMaterialId());



        return historyVo;
    }

    @Override
    public Map<Integer, CfMaterialVerityHistory.HistoryOverviewVO> getHistoryOverview(String infoId) {
        return getHistoryOverview(infoId, Lists.newArrayList(
                InitialAuditOperateService.FIRST_APPROVE_TAG,
                CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT.getCode(),
                CrowdfundingInfoDataStatusTypeEnum.PATIENT_INFO_SUBMIT.getCode(),
                CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode(),
                CrowdfundingInfoDataStatusTypeEnum.TREATMENT_INFO_SUBMIT.getCode(),
                CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT.getCode(),
                CrowdfundingInfoDataStatusTypeEnum.ID_VERIFY.getCode(),
                CrowdfundingInfoDataStatusTypeEnum.CREDIT_INFO_NEW.getCode(),
                CrowdfundingInfoDataStatusTypeEnum.FUND_USE_SUBMIT.getCode()
        ), CfMaterialVerityHistory.REJECT_TYPE);
    }

    private Map<Integer, CfMaterialVerityHistory.HistoryOverviewVO> getHistoryOverview(String infoId, List<Integer> materialIds, int handleType) {
        HashMap<Integer, CfMaterialVerityHistory.HistoryOverviewVO> result = Maps.newHashMap();
        for (Integer materialId : materialIds) {
            PageInfo<CfMaterialVerityHistory.CfMaterialVerityHistoryVo> p = queryMaterialVerityHistory(infoId, materialId, handleType, 1, 1);
            CfMaterialVerityHistory.HistoryOverviewVO h = new CfMaterialVerityHistory.HistoryOverviewVO();
            h.setTotal(p.getTotal());
            List<CfMaterialVerityHistory.CfMaterialVerityHistoryVo> list = p.getList();
            if (CollectionUtils.isNotEmpty(list)) {
                h.setRecordInfo(p.getList().get(0));
            }
            result.put(materialId, h);
        }
        return result;
    }

    public void fillMaterialInfoById(int caseId, String infoId, int materialId,
                                     CfMaterialVerityHistory verityHistory) {

        String materialInfo = "";
        verityHistory.setMaterialInfoExt(JSON.toJSONString(Maps.newHashMap()));
        switch (CrowdfundingInfoDataStatusTypeEnum.parse(materialId)) {
            case BASE_INFO_SUBMIT:
                materialInfo = queryCfBaseInfo(infoId);
                break;
            case PATIENT_INFO_SUBMIT:
                materialInfo = queryCfAuthor(caseId);
                break;
            case PAYEE_INFO_SUBMIT:
                materialInfo = queryCfPayeeAndFillExtInfo(caseId, infoId, verityHistory);
                break;
            case TREATMENT_INFO_SUBMIT:
                materialInfo = queryCfTreatmentInfo(caseId);
                break;
            case CREDIT_SUPPLEMENT_INFO_SUBMIT:
                materialInfo = queryCfSupplyment(caseId);
                break;
            case ID_VERIFY:
                materialInfo = queryCfIdCase(caseId);
                break;
            case FUND_USE_SUBMIT:
                materialInfo = queryFundUse(caseId);
                break;
            default:
                break;
        }

        verityHistory.setMaterialInfo(materialInfo);
    }

    private String queryFundUse(int caseId) {
        RpcResult<CfRaiseFundUseModel> resp = cfRaiseMaterialClient.selectFundUse(caseId);
        if (resp.isFail()) {
            return "";
        }
        CfRaiseFundUseModel data = resp.getData();
        if (data == null) {
            return "";
        }
        return JSON.toJSONString(data);
    }

    // 患者基本信息
    private String queryCfAuthor(int caseId) {
        CrowdfundingAuthor crowdfundingAuthor = crowdfundingAuthorBiz.get(caseId);
        if (crowdfundingAuthor != null) {
            try {
                crowdfundingAuthor.setIdCard(shuidiCipher.decrypt(crowdfundingAuthor.getCryptoIdCard()));
            } catch (Exception e) {
                crowdfundingAuthor.setIdCard(crowdfundingAuthor.getCryptoIdCard());
            }
            try {
                crowdfundingAuthor.setPhone(shuidiCipher.decrypt(crowdfundingAuthor.getCryptoPhone()));
            } catch (Exception e) {
                crowdfundingAuthor.setPhone(crowdfundingAuthor.getCryptoPhone());
            }
            crowdfundingAuthor.setCryptoIdCard(null);
            crowdfundingAuthor.setCryptoPhone(null);
        }

        return crowdfundingAuthor == null ? "" : JSON.toJSONString(crowdfundingAuthor);
    }

    // 增信材料
    private String queryCfSupplyment(int caseId) {
        OpResult<CreditSupplementModel> creditSupplementModel = crowdfundingUserDelegate.getCreditSupplementByCaseId(caseId);

        return creditSupplementModel.isSuccess() ? JSON.toJSONString(creditSupplementModel.getData()) : "";
    }

    // 发起人身份证
    private String queryCfIdCase(int caseId) {
        CrowdfundingIdCase idCase = crowdfundingDelegate.getCrowdfundingIdCaseByInfoId(caseId);

        return idCase == null ? "" : JSON.toJSONString(idCase);
    }

    // 诊断证明
    private String queryCfTreatmentInfo(int caseId) {
        TreatmentVO treatment = crowdfundingUserDelegate.getCrowdfundingTreatmentVO(caseId);
        return treatment == null ? "" : JSON.toJSONString(treatment);
    }

    // 案例的图片信息
    private String queryCfPic(int caseId) {
        List<CrowdfundingAttachmentVo> cfAttachmentVoList = repeatInfoBizService.getFundingAttachmentWithRepeatInfo(caseId);
        return CollectionUtils.isEmpty(cfAttachmentVoList) ? "" : JSON.toJSONString(cfAttachmentVoList);
    }

    // 案例的基本信息
    private String queryCfBaseInfo(String infoUuid) {
        CrowdfundingInfoVo crowdfundingInfoVo = crowdfundingInfoBiz.getFundingInfoVoByInfoUuid(infoUuid);
        AdminCrowdfundingInfoView crowdfundingInfoView = new AdminCrowdfundingInfoView();
        BeanUtils.copyProperties(crowdfundingInfoVo, crowdfundingInfoView);

        Set<String> sensitiveWords = cfCaseSensitiveWordService.get(crowdfundingInfoView.getContent() + crowdfundingInfoView.getTitle());
        log.info("sensitiveWords size:{}", cfCaseSensitiveWordService.getWordSize());
        if (!org.apache.commons.collections4.CollectionUtils.isEmpty(sensitiveWords)) {
            crowdfundingInfoView.setHasSensitiveWord(true);
            crowdfundingInfoView.setSensitiveWords(sensitiveWords);
        }

        return crowdfundingInfoView == null ? "" : JSON.toJSONString(crowdfundingInfoView);
    }

    // 案例的收款人信息
    private String queryCfPayeeAndFillExtInfo(int caseId, String infoUuid, CfMaterialVerityHistory verityHistory) {

        CrowdfundingInfo cf = crowdfundingDelegate.getCrowdfundingInfoByInfoId(infoUuid);
        if (cf == null) {
            log.error("驳回记录历史查看，不能找到有效的case。 infoUuid:{}", infoUuid);
            return "";
        }

        Map<String, String> params = Maps.newHashMap();
        switch (cf.getRelationType()) {

            case LOCATION_HOSPITAL_ACCOUNT:

                CrowdfundingInfoHospitalPayee hospitalPayee = crowdfundingDelegate
                        .getCrowdfundingInfoHospitalPayeeByInfoUuid(infoUuid);
                if (hospitalPayee != null) {
                    FeignResponse<Response<PACheckResultVo>> result = readFeignClient.checkInAcctInfo(hospitalPayee.getHospitalBankCard(),
                            hospitalPayee.getHospitalBankBranchName(), hospitalPayee.getHospitalAccountName());
                    params.put(CfMaterialVerityHistory.MaterialInfoExtEnum.PAYEE_RELATION_TYPE.getWord(),
                            "" + CrowdfundingRelationType.getCode(CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT));
                    if (result != null && result.getData() != null && result.getData().getData() != null) {
                        params.put(CfMaterialVerityHistory.MaterialInfoExtEnum.PAYEE_IDCARD_VERITY_RESULT.getWord(),
                                JSON.toJSONString(result.getData().getData()));
                    }
                    verityHistory.setMaterialInfoExt(JSON.toJSONString(params));

                    return JSON.toJSONString(hospitalPayee);
                }
                break;

            case charitable_organization:
                //添加慈善组织
                CfCharityPayee cfCharityPayee =  crowdfundingDelegate.getCfCharityPayeeByUUid(infoUuid);
                if (cfCharityPayee != null){

                    cfCharityPayee.setOrgMobile(shuidiCipher.decrypt(cfCharityPayee.getOrgMobile()));
                    cfCharityPayee.setOrgBankCard(shuidiCipher.decrypt(cfCharityPayee.getOrgBankCard()));

                    List<CrowdfundingAttachmentVo> attachmentVos = crowdfundingDelegate.getAttachmentsByType(caseId, AttachmentTypeEnum.ATTACH_CHARITY);
                    StringBuilder sb = new StringBuilder();
                    attachmentVos.stream().forEach(r->{
                        sb.append(r.getUrl()).append(",");
                    });
                    cfCharityPayee.setOrgPic(sb.substring(0,sb.length()-1));


                    params.put(CfMaterialVerityHistory.MaterialInfoExtEnum.PAYEE_RELATION_TYPE.getWord(),
                            "" + CrowdfundingRelationType.getCode(CrowdfundingRelationType.charitable_organization));
                    verityHistory.setMaterialInfoExt(JSON.toJSONString(params));

                    return JSON.toJSONString(cfCharityPayee);
                }


            default:
                try {
                    cf.setPayeeIdCard(shuidiCipher.decrypt(cf.getPayeeIdCard()));
                } catch (Exception e) {
                    cf.setPayeeIdCard(cf.getPayeeIdCard());
                }

                try {
                    cf.setPayeeMobile(shuidiCipher.decrypt(cf.getPayeeMobile()));
                } catch (Exception e) {
                    cf.setPayeeMobile(cf.getPayeeMobile());
                }
                try {
                    cf.setPayeeBankCard(shuidiCipher.decrypt(cf.getPayeeBankCard()));
                } catch (Exception e) {
                    cf.setPayeeBankCard(cf.getPayeeBankCard());
                }

                params.put(CfMaterialVerityHistory.MaterialInfoExtEnum.PAYEE_RELATION_TYPE.getWord(),
                        "" + CrowdfundingRelationType.getCode(CrowdfundingRelationType.self));
                String imgUrls = getPayeeRelationVideoImgUrls(caseId);
                if (StringUtils.isNotBlank(imgUrls)) {
                    params.put(CfMaterialVerityHistory.MaterialInfoExtEnum.ATTACH_PAYEE_RELATION_VIDEO.getWord(), imgUrls);
                }
                if (isPayeeFaceAuth(cf)) {
                    params.put(CfMaterialVerityHistory.MaterialInfoExtEnum.PAYEE_FACE_RECOGNITION.getWord(), "1");
                }

                verityHistory.setMaterialInfoExt(JSON.toJSONString(params));

                return JSON.toJSONString(cf);
        }


        return "";
    }

    private boolean isPayeeFaceAuth(CrowdfundingInfo cf) {
        if (cf == null || cf.getRelationType() != CrowdfundingRelationType.other) {
            return false;
        }
        CrowdfundingInfoPayee payee = crowdfundingDelegate.getCrowdfundingInfoPayeeByInfoUuid(cf.getInfoId());
        return payee != null && payee.getFaceIdResult() == 20;
    }

    private String getPayeeRelationVideoImgUrls(int caseId) {
        List<CrowdfundingAttachmentVo> result = crowdfundingDelegate.getAttachmentsByType(caseId, AttachmentTypeEnum.ATTACH_PAYEE_RELATION_VIDEO);
        log.info("查询案例的收款人关系视频 caseId:{} result:{}", caseId, JSON.toJSONString(result));
        if ( CollectionUtils.isEmpty(result)) {
            return "";
        }

        List<String> imageUrls = Lists.newArrayList();
        for (CrowdfundingAttachmentVo vo : result) {
            imageUrls.add(vo.getUrl());
        }
        return Joiner.on(",").join(imageUrls);
    }


    private Object parse(String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        }
        return JSON.parse(jsonString);
    }

    @Override
    public boolean hasPermissionWithUser(int userId, String permission) {
        AuthRpcResponse<Boolean> auth = seaAuthClientV1.hasPermissionWithUser(userId, permission);
        log.info("用户判断权限 permission:{} userId:{} result:{}", permission, userId, auth);

        return auth == null || auth.getResult() == null || auth.getCode() == 10001
                || auth.getResult();
    }


    @Override
    public CfMaterialVerityHistory selectLatestMaterial(int caseId, int materialId, int handleType) {
        CfMaterialVerityHistory res = verityHistoryDAO.selectLatestMaterial(caseId, materialId, handleType);
        setDecrypt(res);
        return res;
    }

    private void setDecrypt(CfMaterialVerityHistory res) {
        Optional.ofNullable(res)
                .ifPresent(r -> {
                    r.setMaterialInfo(Optional.ofNullable(r.getMaterialInfoEncrypt())
                            .filter(StringUtils::isNotBlank)
                            .map(shuidiCipher::decrypt)
                            .orElse(StringUtils.EMPTY));
                    r.setMaterialInfoExt(Optional.ofNullable(r.getMaterialInfoExtEncrypt())
                            .filter(StringUtils::isNotBlank)
                            .map(shuidiCipher::decrypt)
                            .orElse(StringUtils.EMPTY));
                });
    }

    @Override
    public CfMaterialVerityHistory selectLatestMaterial(int caseId, int materialId) {
        CfMaterialVerityHistory res = verityHistoryDAO.selectLatestMaterialNoType(caseId, materialId);
        this.setDecrypt(res);
        return res;
    }

    @Override
    public CfMaterialVerityHistory.CfMaterialVerityHistoryVo getLastDetail(int caseId, int materialId, int handleType) {
        CfMaterialVerityHistory verityHistory = selectLatestMaterial(caseId, materialId, handleType);
        return buildHistoryVO(verityHistory);
    }

    @NotNull
    private String getRefuseReason(CfMaterialVerityHistory verityHistory) {
        String[] refuseIds = StringUtils.split(verityHistory.getRefuseIds(), ",");
        if (refuseIds.length == 0) {
            log.info("材料是驳回的 但是没有具体的驳回的理由。id：{}", verityHistory.getId());
            return "";
        }
        List<Integer> actualRefuseIds = Lists.newArrayList();
        for (String id : refuseIds) {
            actualRefuseIds.add(Integer.valueOf(id));
        }
        List<CfRefuseReasonEntity> refuseReasonEntities = reasonEntityBiz.selectByReasonIds(Sets.newHashSet(actualRefuseIds), null);
        StringBuilder reason = new StringBuilder();
        for (CfRefuseReasonEntity reasonEntity : refuseReasonEntities) {
            reason.append(reasonEntity.getContent()).append("\n");
        }
        return reason.toString();
    }

    @Override
    public int insertList(List<CfMaterialVerityHistory> verityHistories) {
        if (CollectionUtils.isEmpty(verityHistories)) {
            return 0;
        }
        verityHistories.forEach(r -> {
            Optional.ofNullable(r.getMaterialInfo())
                    .filter(StringUtils::isNotBlank)
                    .ifPresent(item -> r.setMaterialInfoEncrypt(oldShuidiCipher.aesEncrypt(item)));
            Optional.ofNullable(r.getMaterialInfoExt())
                    .filter(StringUtils::isNotBlank)
                    .ifPresent(item -> r.setMaterialInfoExtEncrypt(oldShuidiCipher.aesEncrypt(item)));
        });
        return  verityHistoryDAO.insertRecords(verityHistories);
    }

    @Override
    public Map<Integer, CfMaterialVerityHistory.MaterialOpDetail> queryMaterialOpDetail(String infoUuid) {

        List<CrowdfundingInfoStatus> allMaterialStatus = infoStatusBiz.getByInfoUuid(infoUuid);

        if (CollectionUtils.isEmpty(allMaterialStatus)) {
            return Maps.newHashMap();
        }

        int caseId = allMaterialStatus.get(0).getCaseId();

        // 注意增信的状态枚举和材料不同
        Integer creditStatus = Optional.ofNullable(crowdfundingOperationDelegate.selectCrowdfundingInitialAuditInfoByCaseId(caseId))
                .map(CrowdfundingInitialAuditInfo::getCreditInfo)
                .orElse(0);
        CrowdfundingInfoStatus creditInfoStatus = new CrowdfundingInfoStatus();
        creditInfoStatus.setStatus(creditStatus);
        creditInfoStatus.setType(InitialAuditItem.CREDIT_INFO);
        allMaterialStatus.add(creditInfoStatus);

        CrowdfundingInfo info = crowdfundingInfoBiz.getFundingInfoById(caseId);
        long createTime = info == null ? 0 : info.getCreateTime().getTime();

        Map<Integer, CfMaterialVerityHistory.MaterialOpTimeObj> opTimeMap = getMaterialOpTime(caseId);

        Map<Integer, CfMaterialVerityHistory.MaterialOpDetail> result = Maps.newHashMap();
        for (CrowdfundingInfoStatus curr : allMaterialStatus) {
            Integer status = curr.getStatus();
            Integer type = curr.getType();
            CfMaterialVerityHistory.MaterialOpDetail opDetail = new CfMaterialVerityHistory.MaterialOpDetail();
            opDetail.setMaterialStatus(status);

            CfMaterialVerityHistory lastOp = verityHistoryDAO.selectLatestMaterialByMaterial(caseId, type);
            this.setDecrypt(lastOp);
            // 最后一次操作的时间
            if (lastOp != null) {
                opDetail.setPassOrRejectTime(DateUtil.formatDateTime(lastOp.getCreateTime()));
                if (StringUtils.isBlank(lastOp.getOperatorDetail())) {
                    opDetail.setOperatorDetail(queryOperateDetail(lastOp.getOperatorId()));
                } else {
                    opDetail.setOperatorDetail(lastOp.getOperatorDetail());
                }
            }

            if (createTime >= beginEffectSubmitTime) {
                opDetail.setUserOpTimeDetail(opTimeMap.get(type));
            }

            result.put(type, opDetail);
        }

        return result;
    }

    @Override
    @Async("materialVerityHistoryExecutor")
    public void insertByType(InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam,
                                        String infoUuid, String materialInfo, int handleResult, int type) {

        List<Integer> passIds = handleCaseInfoParam.getPassIds();
        List<Integer> rejectIds = handleCaseInfoParam.getRejectIds();
        int caseId = handleCaseInfoParam.getCaseId();
        int userId = handleCaseInfoParam.getUserId();
        long workOrderId = handleCaseInfoParam.getWorkOrderId();
        String callComment = handleCaseInfoParam.getCallComment();

        if (CollectionUtils.isEmpty(passIds) &&
                CollectionUtils.isEmpty(rejectIds)) {
            return;
        }

        CfMaterialVerityHistory verityHistory = new CfMaterialVerityHistory();
        verityHistory.setCaseId(caseId);
        verityHistory.setInfoId(infoUuid);
        verityHistory.setHandleType(handleResult);
        verityHistory.setMaterialId(type);
        verityHistory.setRefuseIds(Joiner.on(",").join(rejectIds));
        verityHistory.setMaterialInfo(materialInfo);
        verityHistory.setMaterialPicInfo("");
        verityHistory.setMaterialInfoExt("");
        verityHistory.setOperatorId(userId);
        verityHistory.setOperatorType(0);
        verityHistory.setComment(callComment);
        verityHistory.setWorkOrderId(workOrderId);

        verityHistory.setOperatorDetail(queryOperateDetail(userId));
        verityHistory.setMaterialOpTimeType(0);
        verityHistory.setMaterialOpTime("");

        insertList(Lists.newArrayList(verityHistory));
    }


    // 案例审核
    private void totalRecordCaseApprove(CfMaterialVerityHistory.CfMaterialVerityHistoryRecord record) {

        if (record == null) {
            return;
        }

        CfCaseStageAuditInfoView infoView = new CfCaseStageAuditInfoView();

        infoView.setCaseId(record.getCaseId());
        infoView.setStage(CfCaseAuditStageDetail.AuditStage.CASE_MATERIAL_AUDIT);
        infoView.setPassMaterialIds(record.getPassIds());
        infoView.setRejectMaterialIds(getMaterialIdByRefuseIds(record.getRejectIds()));
        infoView.setRejectReasonIds(record.getRejectIds());
        infoView.setOperatorId(record.getUserId());
        infoView.setComment(StringUtils.trimToEmpty(record.getComment()));

        totalRecordOperate(infoView);
    }

    // 初审
    @Async("materialVerityHistoryExecutor")
    public void totalRecordInitialAudit(InitialAuditOperationItem.HandleCaseInfoParam param) {

        if (param == null) {
            return;
        }
        CfCaseStageAuditInfoView infoView = new CfCaseStageAuditInfoView();

        infoView.setCaseId(param.getCaseId());
        infoView.setStage(CfCaseAuditStageDetail.AuditStage.INITIAL_AUDIT);

        infoView.setPassMaterialIds(param.getPassIds());
        infoView.setRejectMaterialIds(getMaterialIdByRefuseIds(param.getRejectIds()));
        infoView.setRejectReasonIds(param.getRejectIds());

        infoView.setOperatorId(param.getUserId());
        infoView.setComment(StringUtils.trimToEmpty(param.getHandleComment()));

        totalRecordOperate(infoView);
    }

    // 增信审核
    @Async("materialVerityHistoryExecutor")
    public void totalRecordCreditAudit(RiverHandleParamVO param) {

        if (param == null) {
            return;
        }

        CfCaseStageAuditInfoView infoView = new CfCaseStageAuditInfoView();
        infoView.setCaseId(param.getCaseId());
        infoView.setStage(CfCaseAuditStageDetail.AuditStage.INSURANCE_AUDIT);

        infoView.setPassMaterialIds(param.getHandleType() == RiverHandleParamVO.HandleType.PASS ?
                Lists.newArrayList(CrowdfundingInfoDataStatusTypeEnum.CREDIT_INFO_NEW.getCode()) : Lists.newArrayList());
        infoView.setRejectMaterialIds(getMaterialIdByRefuseIds(param.getRejectIds()));
        infoView.setRejectReasonIds(param.getRejectIds());
        infoView.setOperatorId(param.getOperatorId());
        infoView.setComment(StringUtils.trimToEmpty(param.getHandleComment()));

        totalRecordOperate(infoView);
    }

    @Override
    public CfMaterialVerityHistory selectLatestByWorkOrder(int caseId, long workOrderId, int materialId) {
        CfMaterialVerityHistory res = verityHistoryDAO.selectLatestByWorkOrder(caseId, workOrderId, materialId);
        this.setDecrypt(res);
        return res;
    }

    // 通过驳回项id 找到 材料id
    private List<Integer> getMaterialIdByRefuseIds(List<Integer> rejectIds) {
        if (CollectionUtils.isEmpty(rejectIds)) {
            return Lists.newArrayList();
        }

        List<CfRefuseReasonEntity> rejectEntitys = reasonEntityBiz.selectByIds(rejectIds);
        List<Integer> tagIds = Lists.newArrayList();
        for (CfRefuseReasonEntity reasonEntity : rejectEntitys) {
            tagIds.add(reasonEntity.getTagId());
        }

        List<CfRefuseReasonTag> allTags = reasonTagBiz.selectByTagIds(Sets.newHashSet(tagIds));
        List<Integer> materialIds = Lists.newArrayList();
        for (CfRefuseReasonTag reasonTag : allTags) {
            materialIds.add(reasonTag.getDataType());
        }

        return materialIds;
    }

    private void totalRecordOperate(CfCaseStageAuditInfoView infoView) {
        RpcResult<Integer> result = caseAuditClient.addCaseAuditRecords(Lists.newArrayList(infoView));
        log.info("整体审核记录保存. infoView:{} result:{}", infoView, result);
    }


}
