package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminReportStatService;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminReportStatDao;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminWorkOrderReportConst;
import com.shuidihuzhu.cf.enums.crowdfunding.CaseReportDealStatus;
import com.shuidihuzhu.cf.model.report.GongDanDealNum;
import com.shuidihuzhu.cf.model.report.GongDanNum;
import com.shuidihuzhu.cf.model.report.ReportStatTotal;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2019/4/8
 */
@Service
public class AdminReportStatServiceImpl implements AdminReportStatService {

    @Autowired
    private AdminReportStatDao adminReportStatDao;

    @Resource
    private SeaAccountClientV1 seaAccountClientV1;


    @Override
    public List<ReportStatTotal> getReportStatUser() {

        List<ReportStatTotal> list = Lists.newArrayList();

        String now = LocalDate.now()+" "+LocalTime.now();

        String today = LocalDate.now()+" 00:00:00";

        List<Integer> users = adminReportStatDao.getHandlerUser(today,now);

        if (CollectionUtils.isEmpty(users)){
            return list;
        }

        AuthRpcResponse<List<AdminUserAccountModel>> rpcResponse = seaAccountClientV1.getUserAccountsByIds(users);

        final Map<Integer,String> map = Maps.newHashMap();
        if (rpcResponse != null && rpcResponse.isSuccess()){
            List<AdminUserAccountModel> accountModels = rpcResponse.getResult();
            map.putAll(accountModels.stream().collect(Collectors.toMap(AdminUserAccountModel::getId,AdminUserAccountModel::getName)));
        }

        list = users.stream().map(r->{
            ReportStatTotal rst = new ReportStatTotal();
            rst.setUserId(r);
            rst.setUserName(map.get(r));
            rst.setGongDanNum(getUserGongDanNum(today,now,r));
            rst.setGongDanDoneNum(getGongDanDealNum(today,now,AdminWorkOrderReportConst.DealResult.DEAL_COMPLETE.getCode(),r));
            rst.setGongDanDoingNum(getGongDanDoingNum(today,now,r));
            rst.setRateNum(getRate(rst));
            rst.setLostNum(adminReportStatDao.getUserLostNum(today,now,r));
            return rst;
        }).collect(Collectors.toList());

        return list;
    }


    private GongDanNum getUserGongDanNum(String today,String now,int userId){
        GongDanNum gdn = new GongDanNum();

        List<Long> lingqu = adminReportStatDao.getlingquNum(today,now,userId);
        gdn.setLingquNum(lingqu.size());
        if (CollectionUtils.isNotEmpty(lingqu)){
            gdn.setLingquEntryNum(adminReportStatDao.getEntryNum(lingqu));
        }
        List<Long> yiliu = adminReportStatDao.getyiliuNum(today,userId);
        gdn.setYiliuNum(yiliu.size());
        if (CollectionUtils.isNotEmpty(yiliu)){
            gdn.setYiliuEntryNum(adminReportStatDao.getEntryNum(yiliu));
        }

        return gdn;
    }


    @Override
    public ReportStatTotal getReportStatTotal() {

        ReportStatTotal rst = new ReportStatTotal();

        String now = LocalDate.now()+" "+LocalTime.now();

        String today = LocalDate.now()+" 00:00:00";

        int userNum = adminReportStatDao.getHandlerUser(today,now).size();

        rst.setUserNum(userNum);
        rst.setGongDanNum(getGongDanNum(today,now));

        rst.setGongDanDoingNum(getGongDanDoingNum(today,now,0));
        rst.setGongDanDoneNum(getGongDanDealNum(today,now,AdminWorkOrderReportConst.DealResult.DEAL_COMPLETE.getCode(),0));

        rst.setRateNum(getRate(rst));

        rst.setLostNum(adminReportStatDao.getLostNum(today,now));

        return rst;
    }


    private String getRate(ReportStatTotal rst ){
        int allEntry = rst.getGongDanNum().getAllEntryNum();
        if (allEntry != 0 ){
            int finishEntry = rst.getGongDanDoneNum().getAllEntryNum();
            DecimalFormat df=new DecimalFormat("0.00");
            return df.format((float)finishEntry/allEntry);
        }

        return "0";
    }



    private GongDanNum getGongDanNum(String today,String now){
        GongDanNum gdn = new GongDanNum();

        List<Long> lingqu = adminReportStatDao.getlingquNum(today,now,0);
        gdn.setLingquNum(lingqu.size());
        if (CollectionUtils.isNotEmpty(lingqu)){
            gdn.setLingquEntryNum(adminReportStatDao.getEntryNum(lingqu));
        }
        List<Long> yiliu = adminReportStatDao.getyiliuNum(today,0);
        gdn.setYiliuNum(yiliu.size());
        if (CollectionUtils.isNotEmpty(yiliu)){
            gdn.setYiliuEntryNum(adminReportStatDao.getEntryNum(yiliu));
        }
        List<Long> weichuli = adminReportStatDao.getweilingquNum();
        gdn.setWeilingquNum(weichuli.size());
        if (CollectionUtils.isNotEmpty(weichuli)){
            gdn.setWeilingquEntryNum(adminReportStatDao.getEntryNum(weichuli));
        }

        return gdn;
    }

    private GongDanDealNum getGongDanDoingNum(String today,String now,int userId){

        GongDanDealNum gddn = new GongDanDealNum();

        List<Long> jinriFinish = adminReportStatDao.getjinriFinishNum(today,now, AdminWorkOrderReportConst.DealResult.REPORT_FOLLOW.getCode() ,userId);
        gddn.setJinriNum(jinriFinish.size());
        List<Integer> list = Lists.newArrayList(AdminWorkOrderReportConst.DealResult.DEFAULT.getCode() ,
                              AdminWorkOrderReportConst.DealResult.REPORT_FOLLOW.getCode() );

        List<Long> yiliuFinish = adminReportStatDao.getyiliuDoingFinishNum(today,now, list,userId);
        gddn.setYiliuNum(yiliuFinish.size());

        List<Integer> dealResults = Lists.newArrayList(CaseReportDealStatus.SIMILAR_REPORT.getValue(),
                    CaseReportDealStatus.FINISH.getValue(),
                    CaseReportDealStatus.DISREGARD.getValue(),
                    CaseReportDealStatus.NO_HANDLE.getValue(),
                    CaseReportDealStatus.HANDLEING.getValue());

        List<Long> all = Lists.newArrayList(jinriFinish);
        all.addAll(yiliuFinish);
        if (CollectionUtils.isNotEmpty(all)){
            gddn.setEntryStatNums(adminReportStatDao.getEntryStatNum(all, dealResults));
        }

        return gddn;
    }


    private GongDanDealNum getGongDanDealNum(String today,String now,int status,int userId){

        GongDanDealNum gddn = new GongDanDealNum();

        List<Long> jinriFinish = adminReportStatDao.getjinriFinishNum(today,now, status,userId);
        gddn.setJinriNum(jinriFinish.size());
        List<Long> yiliuFinish = adminReportStatDao.getyiliuFinishNum(today,now, status,userId);
        gddn.setYiliuNum(yiliuFinish.size());


        List<Integer> dealResults = Lists.newArrayList(CaseReportDealStatus.SIMILAR_REPORT.getValue(),
                CaseReportDealStatus.FINISH.getValue(),CaseReportDealStatus.DISREGARD.getValue());

        List<Long> all = Lists.newArrayList(jinriFinish);
        all.addAll(yiliuFinish);
        if (CollectionUtils.isNotEmpty(all)){
            gddn.setEntryStatNums(adminReportStatDao.getEntryStatNum(all, dealResults));
        }

        return gddn;
    }
}
