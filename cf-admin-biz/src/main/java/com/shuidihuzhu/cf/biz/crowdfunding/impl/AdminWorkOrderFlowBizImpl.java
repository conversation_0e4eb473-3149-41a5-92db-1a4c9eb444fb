package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.base.Stopwatch;
import com.google.common.base.Strings;
import com.google.common.collect.*;
import com.qcloud.cos.model.PutObjectResult;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.admin.workorder.AdminWorkOrderMoneyBackExtBiz;
import com.shuidihuzhu.cf.dao.cfOperatingProfile.CfOperatingCaseLabelDao;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.admin.delegate.SeaUserAccountDelegate;
import com.shuidihuzhu.cf.biz.admin.AdminOrganizationBiz;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderBiz;
import com.shuidihuzhu.cf.biz.aiphoto.utils.ImageSimilarity;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingCityBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderClassifySettingsBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.workflow.StaffStatusBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.workflow.WorkFlowAutoAssignRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.workflow.WorkFlowTypePropertyBiz;
import com.shuidihuzhu.cf.biz.es.CfSearch;
import com.shuidihuzhu.cf.biz.mapper.AdminWorkOrderMapper;
import com.shuidihuzhu.cf.biz.workOrder.ExternalWorkOrderApiBiz;
import com.shuidihuzhu.cf.client.adminpure.model.transformorder.SyncOrderInfoParam;
import com.shuidihuzhu.cf.client.adminpure.model.transformorder.TransformOrderCreateParam;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.dao.admin.AdminWorkOrderFlowDao;
import com.shuidihuzhu.cf.dao.admin.AdminWorkOrderFlowRemindDao;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.diff.Diff;
import com.shuidihuzhu.cf.dto.WorkOrderCreateClewDTO;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminWorkOrderFlowConst;
import com.shuidihuzhu.cf.finance.client.feign.CfFinancePauseFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.enums.CfDrawCashPauseRecordEnum;
import com.shuidihuzhu.cf.model.admin.AdminOrganization;
import com.shuidihuzhu.cf.model.admin.AdminOrganizationUserMap;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrderMoneyBackExt;
import com.shuidihuzhu.cf.model.admin.vo.AdminWorkOrderMoneyBackExtVO;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingCaseLabel;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowDetailView;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowParam;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView;
import com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowStaffStatus;
import com.shuidihuzhu.cf.repository.AdminWorkOrderFlowRepository;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.service.FinanceApproveService;
import com.shuidihuzhu.cf.service.apollo.ApolloService;
import com.shuidihuzhu.cf.service.cfOperatingProfile.CfOperatingCaseLabelBiz;
import com.shuidihuzhu.cf.service.crowdfunding.CfCailiaoService;
import com.shuidihuzhu.cf.service.workorder.WorkFlowOrderCommonService;
import com.shuidihuzhu.cf.service.workorder.WorkOrderRemarkService;
import com.shuidihuzhu.cf.store.plugins.CosPlugins;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.auth.saas.configuration.AuthSaasProperties;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.client.cf.admin.model.NoticeBDFromFlow;
import com.shuidihuzhu.client.cf.admin.model.WorkCreatorChannelEnum;
import com.shuidihuzhu.client.cf.admin.model.WorkOrderFlowParam;
import com.shuidihuzhu.client.cf.clewtrack.enums.FlowWorkOrderModelV2Enum;
import com.shuidihuzhu.client.cf.clewtrack.model.FlowWorkOrderModelV2;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderIndexSearchParam;
import com.shuidihuzhu.client.cf.search.model.enums.CfWorkOrderIndexSortEnum;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.MobileUtil;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.cs.work.order.client.dto.CascadeRuleFields;
import com.shuidihuzhu.infra.starter.cos.configuration.CosClientWrapper;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst.Status.CREATED;
import static com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst.Status.HANDLING;

/**
 * Created by Ahrievil on 2017/12/21
 */
@Slf4j
@Service
@RefreshScope
public class AdminWorkOrderFlowBizImpl implements AdminWorkOrderFlowBiz {
    @Resource
    private SeaAccountClientV1 seaAccountClientV1;
    @Autowired
    private CfOperatingCaseLabelDao caseLabelDao;
    @Autowired
    private AdminWorkOrderFlowDao adminWorkOrderFlowDao;
    @Autowired
    private AdminWorkOrderFlowRepository adminWorkOrderFlowRepository;
    @Autowired
    private AdminWorkOrderBiz adminWorkOrderBiz;
    @Autowired
    private AdminWorkOrderFlowRecordBiz adminWorkOrderFlowRecordBiz;
    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Autowired
    private AdminWorkOrderClassifySettingsBiz classifySettingsBiz;
    @Autowired
    private AdminWorkOrderBiz workOrderBiz;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;
    @Autowired
    private AdminOrganizationBiz orgIdBiz;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired(required = false)
    private Producer producer;
    @Autowired
    private WorkFlowOrderCommonService flowOrderCommonService;
    @Autowired
    private CfSearch cfSearch;
    @Autowired
    private WorkOrderRemarkService workOrderRemarkService;
    @Autowired
    SeaUserAccountDelegate accountDelegate;
    @Autowired
    AdminWorkOrderFlowRemindDao remindDao;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private StaffStatusBiz staffStatusBiz;
    @Autowired
    private WorkFlowAutoAssignRecordBiz autoAssignRecordBiz;
    @Autowired
    private WorkFlowTypePropertyBiz typePropertyBiz;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Autowired
    private CfCailiaoService cfCailiaoService;

    @Autowired
    private FinanceApproveService financeApproveService;

    @Autowired
    private ExternalWorkOrderApiBiz externalWorkOrderApiBiz;

    @Autowired
    private CosPlugins cosPlugins;

    @Autowired
    private AdminCrowdfundingCityBiz adminCrowdfundingCityBiz;

    @Resource
    private AuthSaasProperties authSaasProperties;

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private CfOperatingCaseLabelBiz caseLabelBiz;

    @Autowired
    private MaskUtil maskUtil;
    @Autowired
    private ApplicationService applicationService;
    @Autowired
    private AdminWorkOrderMoneyBackExtBiz adminWorkOrderMoneyBackExtBiz;

    private static final String CLEW_FIRST_CLASSIFY_SETTINGS = "水滴筹-发起相关";
    private static final String CLEW_FIRST_CLASSIFY_CAILIAO = "水滴筹-材料审核";

    private static final String CLEW_SECOND_CLASSIFY_SETTINGS = "线索流转至1V1服务";
    private static final String CLEW_SECOND_CLASSIFY_SETTINGS_BD = "线索流转至线下";
    private static final String CLEW_SECOND_CLASSIFY_SETTINGS_FUWU = "需要材审服务";


    private static final String CLEW_ORG_FIRST_NAME = "水滴筹";
    private static final String CLEW_ORG_SENCOND_NAME = "运营";
    private static final String CLEW_ORG_THIRD_NAME = "微信服务组";

    private static final String CLEW_ORG_THIRD_KEFU = "客服组";

    private static final String THIRD_ORG_ONLINE = "在线客服";
    private static final String THIRD_ORG_CALL_IN = "呼入客服";
    private static final String THIRD_ORG_ERXIAN = "二线客服";

    private static final String SYSTEM_CREATE = "系统自动创建";

    @Value("${apollo.flow.judge.un-handle:false}")
    private boolean judgeUnHandle;

    @Value("${apollo.flow.judge.off.switch:false}")
    private boolean judgeOffSwitch;

    @Value("${apollo.cos.switch:false}")
    private boolean cosSwitch;

    @Resource(name = "cos-cf-images")
    private CosClientWrapper cosClientWrapper;

    @Autowired
    private CfFinancePauseFeignClient cfFinancePauseFeignClient;

    @Autowired
    private AlarmClient alarmClient;

    @Override
    public AdminWorkOrderFlow selectByWorkOrderId(long workOrderId) {
        AdminWorkOrderFlow flow = adminWorkOrderFlowDao.selectByWorkOrderId(workOrderId);
        if (flow != null && !Strings.isNullOrEmpty(flow.getEncryptMobile())) {
            flow.setMobile(shuidiCipher.decrypt(flow.getEncryptMobile()));
        }
        return flow;
    }

    private long insertOne(AdminWorkOrderFlowView flowView) {
        if (flowView == null) {
            return 0;
        }
        flowView.setProblemImg(StringUtils.defaultString(flowView.getProblemImg()));
        flowView.setProblemContent(StringUtils.defaultString(flowView.getProblemContent()));
        flowView.setMobile(StringUtils.defaultString(flowView.getMobile()));

        WorkCreatorChannelEnum channelEnum = WorkCreatorChannelEnum.findByDesc(flowView.getCreatorChannel());
        flowView.setAssignChannel(channelEnum == null ? 0 : channelEnum.getCode());

        if (Strings.isNullOrEmpty(flowView.getMobile())) {
            flowView.setEncryptMobile("");
        } else {
            flowView.setEncryptMobile(oldShuidiCipher.aesEncrypt(flowView.getMobile().trim()));
        }

        adminWorkOrderFlowDao.insertOne(flowView);

        AdminWorkOrderFlowRecord adminWorkOrderFlowRecord = new AdminWorkOrderFlowRecord(flowView, flowView.getCreatorId(),
                getFlowRecordComment(flowView), AdminWorkOrderFlowConst.OperateTypeEnum.CREATE_WORK_FLOW.getCode(),
                flowView.getLevel(), flowView.getSecondClassifyId());
        adminWorkOrderFlowRecord.setOrderOperatorId(flowView.getOperatorId());
        adminWorkOrderFlowRecordBiz.insertOne(adminWorkOrderFlowRecord);

        noticeClewApi(flowView);
        return flowView.getId();
    }

    private String getFlowRecordComment(AdminWorkOrderFlowView flowView) {

        String comment = "分配:";
        List<AdminOrganization> allOrganization = Lists.newArrayList();
        orgIdBiz.getOrgByLowestId(flowView.getProblemType(), allOrganization);
        if (CollectionUtils.isEmpty(allOrganization)) {
            log.warn("can not find any organization info problemType:{}", flowView.getProblemType());
        }
        
        comment += allOrganization.stream().sorted(Comparator.comparing(AdminOrganization::getId)).map(AdminOrganization::getName).collect(Collectors.joining("-"));

        //获取用户的姓名信息
        if (flowView.getOperatorId() != 0) {
            AuthRpcResponse<AdminUserAccountModel> validUserAccountById = seaAccountClientV1.getValidUserAccountById(flowView.getOperatorId());
            if (validUserAccountById != null && validUserAccountById.isSuccess() && validUserAccountById.getResult() != null) {
                comment += validUserAccountById.getResult().getName();
            } else {
                log.warn("operatorId:{} can not find name!", flowView.getOperatorId());
            }
        }

        return flowView.getTaskType() == AdminWorkOrderConst.TaskType.VISIT_OLD_RAISE_CASE_RECALL.getCode()
                ? "访问老接口发起案例，被拒绝用户" : comment;
    }

    private void noticeClewApi(AdminWorkOrderFlowView flowOrder) {
        log.info("是否需要通知外呼平台 flow：{}", flowOrder);
        int newFirstClassifyId = flowOrder.getNewFirstClassifyId();
        int newSecondClassifyId = flowOrder.getNewSecondClassifyId();
        int newThirdClassifyId = flowOrder.getNewThirdClassifyId();

        // 线上转线下线索流转重启(【发起问题/回传sea重新分配】)
        if (applicationService.isProduction()) {
            if (newFirstClassifyId == 3076 && newSecondClassifyId == 4254) {
                notice1v1(flowOrder);
            }
        } else {
            if (newFirstClassifyId == 3301 && newSecondClassifyId == 4152) {
                notice1v1(flowOrder);
            }
        }

        if (newFirstClassifyId == 3076 && newSecondClassifyId == 2709) {
            notice1v1(flowOrder);
        }

        if (newFirstClassifyId == 3076 && newSecondClassifyId == 2710) {
            noticeBD(flowOrder);
        }

        if (newFirstClassifyId == 3317 && newSecondClassifyId == 2774) {
            noticeFuwuOrder(flowOrder.getCaseId(), flowOrder.getWorkOrderId(), flowOrder.getMobile(), flowOrder.getCreatorId(), flowOrder.getProblemContent());
        }

        //材料审核问题-患者去世 线上：3104-3147-0 测试：3346-3370-0
        //材料审核问题-患者去世-患者去世想提现 线上：3104-3147-3148 测试：3346-3370-3371
        //材料审核问题-患者去世-患者去世要退款 线上：3104-3147-3149 测试：3346-3370-3372
        //材料审核问题-患者去世-非筹款方反馈患者去世 线上：3104-3147-3150 测试：3346-3370-4151
        if (applicationService.isProduction()) {
            //线上
            if ((newFirstClassifyId == 3104 && newSecondClassifyId == 3147 && newThirdClassifyId == 0)
                    || (newFirstClassifyId == 3104 && newSecondClassifyId == 3147 && newThirdClassifyId == 3148)
                    || (newFirstClassifyId == 3104 && newSecondClassifyId == 3147 && newThirdClassifyId == 3149)
                    || (newFirstClassifyId == 3104 && newSecondClassifyId == 3147 && newThirdClassifyId == 3150)) {
                pausePaymentsAndLabel(flowOrder);
            }
        } else {
            //测试
            if ((newFirstClassifyId == 3346 && newSecondClassifyId == 3370 && newThirdClassifyId == 0)
                    || (newFirstClassifyId == 3346 && newSecondClassifyId == 3370 && newThirdClassifyId == 3371)
                    || (newFirstClassifyId == 3346 && newSecondClassifyId == 3370 && newThirdClassifyId == 3372)
                    || (newFirstClassifyId == 3346 && newSecondClassifyId == 3370 && newThirdClassifyId == 4151)) {
                pausePaymentsAndLabel(flowOrder);
            }
        }
    }

    private int noticeFuwuOrder(int caseId, long flowOrderId,String moblie,int createId,String comment){
        String m = moblie+" 进线咨询 "+comment;
        int flag = cfCailiaoService.createFuwuWorkOrder(caseId,flowOrderId,m,0);
        log.info("createFuwuWorkOrder caseId={} flag={}",caseId,flag);
        //成功  增加操作备注
        if (flag == 0){
            // 加备注
            CrowdfundingInfo c = crowdfundingDelegate.getCaseInfoById(caseId);
            financeApproveService.addApprove(c,"操作备注内容", m, createId);
        }
        return flag;
    }

    private void notice1v1(AdminWorkOrderFlowView flowOrder){
        // 分配到的组织
        AdminOrganization thirdOrg = orgIdBiz.getAdminOrganizationById(flowOrder.getProblemType());
        if (thirdOrg == null) {
            return;
        }
        AdminOrganization secondOrg = orgIdBiz.getAdminOrganizationById(thirdOrg.getParentOrgId());
        if (secondOrg == null) {
            return;
        }

        AdminOrganization firstOrg = orgIdBiz.getAdminOrganizationById(secondOrg.getParentOrgId());
        if (firstOrg == null || !CLEW_ORG_FIRST_NAME.equals(StringUtils.trimToEmpty(firstOrg.getName()))
                || !CLEW_ORG_SENCOND_NAME.equals(StringUtils.trimToEmpty(secondOrg.getName()))
                || !CLEW_ORG_THIRD_NAME.equals(StringUtils.trimToEmpty(thirdOrg.getName()))) {
            return;
        }

        Message msg = new Message(MQTopicCons.CF, MQTagCons.ADMIN_FLOW_ORDER_NOTICE_CLEW_V2, String.valueOf(flowOrder.getId()),
                buildFlowWorkOrder(flowOrder));
        MessageResult result = producer.send(msg);
        log.info("信息传递工单创建、通知外呼。msg:{}, result:{}", msg, result);
    }

    private void noticeBD(AdminWorkOrderFlowParam.HandleParam param,AdminWorkOrderFlow oldFlowOrder){

        NoticeBDFromFlow nbf = new NoticeBDFromFlow();

        nbf.setCaseId(oldFlowOrder.getCaseId());
        nbf.setWorkOrderId(param.getWorkOrderId());
        nbf.setMobile(oldFlowOrder.getMobile());
        nbf.setProblemContent(param.getComment());
        nbf.setCityId(param.getCityId());
        nbf.setCityName(param.getCityName());
        nbf.setProvinceId(param.getProvinceId());
        nbf.setProvinceName(param.getProvinceName());
        nbf.setHospital(param.getHospital());

        noticeBD(nbf);
    }

    private void noticeBD(AdminWorkOrderFlowView flowOrder){

        NoticeBDFromFlow nbf = new NoticeBDFromFlow();

        nbf.setCaseId(flowOrder.getCaseId());
        nbf.setWorkOrderId(flowOrder.getWorkOrderId());
        nbf.setMobile(flowOrder.getMobile());
        nbf.setProblemContent(flowOrder.getProblemContent());
        nbf.setCityId(flowOrder.getCityId());
        nbf.setCityName(flowOrder.getCityName());
        nbf.setProvinceId(flowOrder.getProvinceId());
        nbf.setProvinceName(flowOrder.getProvinceName());
        nbf.setHospital(flowOrder.getHospital());

        noticeBD(nbf);
    }

    private void noticeBD(NoticeBDFromFlow nbf){

        Message msg = new Message(MQTopicCons.CF, CfClientMQTagCons.NOTICE_BD_FROM_FLOWORDER, String.valueOf(nbf.getWorkOrderId()), nbf);
        MessageResult result = producer.send(msg);
        log.info("信息传递工单创建、通知BD。msg:{}, result:{}", msg, result);

    }

    /**
     * 暂停打款&打“患者去世”标签
     */
    private void pausePaymentsAndLabel(AdminWorkOrderFlowView flowView) {
        int caseId = flowView.getCaseId();
        long workOrderId = flowView.getId();
        CfOperatingCaseLabel.QueryParam queryParam = new CfOperatingCaseLabel.QueryParam();
        queryParam.setCaseId(caseId);
        queryParam.setSource(1);

        int labelId = 9;
        if (applicationService.isDevelopment()) {
            labelId = 73;
        }
        List<CfOperatingCaseLabel> curLabels = Optional.ofNullable(caseLabelDao.selectOperatingCaseLabels(queryParam.getCaseId())).orElse(Lists.newArrayList());
        Set<Integer> curLabelIds = curLabels.stream().map(CfOperatingCaseLabel::getCaseLabelId).collect(Collectors.toSet());
        Set<Integer> newLabelIds = Sets.newHashSet(curLabelIds);
        newLabelIds.add(labelId);
        queryParam.setOldLabelIds(curLabelIds);
        queryParam.setLabelIds(newLabelIds);
        //打“患者去世”标签
        AdminErrorCode errorCode = caseLabelBiz.addCaseLabels(queryParam);
        if (errorCode != AdminErrorCode.SUCCESS) {
            sendByGroup("创建信息传递工单后自动打“患者去世”标签失败，请核实后手动操作", workOrderId, errorCode.getMsg());
        }

        //暂停打款
        FeignResponse feignResponse = cfFinancePauseFeignClient.addPauseV2(flowView.getOperatorId(), caseId, CfDrawCashPauseRecordEnum.PauseSourceTypeEnum.RISK_CONTROL.getCode(),
                Lists.newArrayList(CfDrawCashPauseRecordEnum.PauseReasonTypeEnum.PATIENT_HAS_DIED.getCode()),
                "患者去世", CfDrawCashPauseRecordEnum.RecordStatusEnum.PAUSE.getCode(), false, true);

        if (feignResponse == null) {
            sendByGroup("创建信息传递工单后自动“暂停打款”失败，请核实后手动操作", workOrderId, "系统异常");
        }

        if (feignResponse != null && feignResponse.notOk()) {
            sendByGroup("创建信息传递工单后自动“暂停打款”失败，请核实后手动操作", workOrderId, feignResponse.getMsg());
        }

    }

    private void sendByGroup(String warn, long workOrderId, String errorMsg) {

        String id = flowOrderCommonService.generateWorkFlowId(new Date(), workOrderId);

        String content = "\n提醒：" +
                warn +
                "\n工单号：" +
                id +
                "\n失败原因：" +
                errorMsg;
        AlarmBotService.sentText("8446148e-dc39-414e-9172-66621b1e4174", content, null, null);
    }

    @Override
    public void saveRecord(AdminWorkOrderFlow adminWorkOrderFlow, long userId, String comment, int operateType, int level, long secondClassifyId) {
        AdminWorkOrderFlowRecord adminWorkOrderFlowRecord = new AdminWorkOrderFlowRecord(adminWorkOrderFlow, Long.valueOf(userId).intValue(),
                comment, operateType, level, secondClassifyId);
        adminWorkOrderFlowRecord.setFlowId(adminWorkOrderFlow.getId());
        adminWorkOrderFlowRecordBiz.insertOne(adminWorkOrderFlowRecord);
    }

    @Override
    public void saveRecord(AdminWorkOrderFlow adminWorkOrderFlow, long userId, int orderOperatorId, int currentProblemType, String comment, int operateType, int level, long secondClassifyId) {
        AdminWorkOrderFlowRecord adminWorkOrderFlowRecord = new AdminWorkOrderFlowRecord(adminWorkOrderFlow, Long.valueOf(userId).intValue(),
                comment, operateType, level, secondClassifyId);
        adminWorkOrderFlowRecord.setFlowId(adminWorkOrderFlow.getId());
        adminWorkOrderFlowRecord.setOrderOperatorId(orderOperatorId);
        adminWorkOrderFlowRecord.setCurrentProblemType(currentProblemType);
        adminWorkOrderFlowRecordBiz.insertOne(adminWorkOrderFlowRecord);
    }

    /**
     * 是否存在 caseId(mobile) + 问题类型 相同还在处理中的工单,如果存在返回对应的错误信息
     * @param flowView
     * @return
     */
    @Override
    public Response<String> existRelateFlow(AdminWorkOrderFlow flowView) {
        if (flowView == null) {
            return NewResponseUtil.makeFail("flowView为空");
        }
        List<AdminWorkOrderFlow> relateWorkOrderFlowList = getRelateWorkOrderId(flowView.getMobile(), flowView.getCaseId(),flowView.getNewFirstClassifyId(),flowView.getNewSecondClassifyId(),flowView.getNewThirdClassifyId());
        if (CollectionUtils.isEmpty(relateWorkOrderFlowList)) {
            return NewResponseUtil.makeSuccess("");
        }
        List<Integer> needCheckStatus = Lists.newArrayList(CREATED.getCode(), HANDLING.getCode());
        Multimap<Long, AdminWorkOrderFlow> flowAndWorkId = Multimaps.index(relateWorkOrderFlowList, AdminWorkOrderFlow::getWorkOrderId);
        List<AdminWorkOrder> unHandleFlowList = adminWorkOrderBiz.selectByIdList(Lists.newArrayList(flowAndWorkId.keySet()))
                .stream()
                .filter(item -> needCheckStatus.contains(item.getOrderStatus()))
                .collect(Collectors.toList());
        ImmutableMap<Long, AdminWorkOrder> workAndId = Maps.uniqueIndex(unHandleFlowList, AdminWorkOrder::getId);
        List<String> flowIds = Lists.newArrayList();
        for (Long workOrderId : workAndId.keySet()) {
            for (AdminWorkOrderFlow flow : flowAndWorkId.get(workOrderId)) {
                flowIds.add(flowOrderCommonService.generateWorkFlowId(flow.getCreateTime(), flow.getId()));
            }
        }

        if (CollectionUtils.isNotEmpty(flowIds)) {
            String msg = "已存在相似工单" + Joiner.on(",").join(flowIds) + "，无法创建新的工单";
            return NewResponseUtil.makeFail(msg);
        }
        return NewResponseUtil.makeSuccess("");
    }

    @Override
    public int relateFlowNums(AdminWorkOrderFlow flowView) {
        if (flowView == null) {
            return 0;
        }
        List<AdminWorkOrderFlow> relateWorkOrderFlowList = getRelateWorkOrderId(flowView.getMobile(), flowView.getCaseId(),flowView.getNewFirstClassifyId(),flowView.getNewSecondClassifyId(),flowView.getNewThirdClassifyId());
        if (CollectionUtils.isEmpty(relateWorkOrderFlowList)) {
            return 0;
        }
        List<Integer> needCheckStatus = Lists.newArrayList(CREATED.getCode(), HANDLING.getCode());
        Multimap<Long, AdminWorkOrderFlow> flowAndWorkId = Multimaps.index(relateWorkOrderFlowList, AdminWorkOrderFlow::getWorkOrderId);
        List<AdminWorkOrder> unHandleFlowList = adminWorkOrderBiz.selectByIdList(Lists.newArrayList(flowAndWorkId.keySet()))
                .stream()
                .filter(item -> needCheckStatus.contains(item.getOrderStatus()))
                .collect(Collectors.toList());
        ImmutableMap<Long, AdminWorkOrder> workAndId = Maps.uniqueIndex(unHandleFlowList, AdminWorkOrder::getId);
        return workAndId.keySet().size();
    }


    private FlowWorkOrderModelV2 buildFlowWorkOrder(AdminWorkOrderFlowView orderView) {
        FlowWorkOrderModelV2 modelV2 = new FlowWorkOrderModelV2();

        modelV2.setWorkOrderId(orderView.getWorkOrderId());
        modelV2.setMobile(orderView.getMobile());
        modelV2.setProblemContent(orderView.getProblemContent());

        modelV2.setCreateId(orderView.getCreatorId());
        int creatorId = orderView.getCreatorId();
        AuthRpcResponse<String> misName = seaAccountClientV1.getMisByUserId(creatorId);
        modelV2.setCreateMis(misName == null ? "" : misName.getResult());
        modelV2.setCreateOrgNameComplexList(queryOrgNameList(orderView.getCreatorId()));

        Map<String, Object> extInfoParam = Maps.newHashMap();
        FlowWorkOrderModelV2Enum modelV2Enum = orderView.getTaskType() == WorkOrderFlowParam.VISIT_OLD_RAISE_CASE_RECALL
                ? FlowWorkOrderModelV2Enum.OLD_APP_INTERCEPTION_CHANNEL : FlowWorkOrderModelV2Enum.OTHER_CHANNEL;
        extInfoParam.put(modelV2Enum.getMapKey(), modelV2Enum.getMapValue());

        if (orderView.getTaskType() == AdminWorkOrderConst.TaskType.reject_workorder_test.getCode()) {
            extInfoParam.put(FlowWorkOrderModelV2Enum.CHUSHEN_REJECT_CHANNEL.getMapKey(), FlowWorkOrderModelV2Enum.CHUSHEN_REJECT_CHANNEL.getMapValue());
            extInfoParam.put(FlowWorkOrderModelV2Enum.CASE_ID.getMapKey(), orderView.getCaseId());
        }

        //补充信息工单创建
        String creatorChannel = StringUtils.isBlank(orderView.getCreatorChannel()) ? WorkCreatorChannelEnum.NORMAL.getDesc() : orderView.getCreatorChannel();
        extInfoParam.put(FlowWorkOrderModelV2Enum.CREATOR_CHANNEL.getMapKey(), creatorChannel);

        modelV2.setExtInfoParamMap(extInfoParam);

        // 如果是线上转线下线索增加标识
        long newFirstClassifyId = orderView.getNewFirstClassifyId();
        long newSecondClassifyId = orderView.getNewSecondClassifyId();
        if (applicationService.isProduction()) {
            if (newFirstClassifyId == 3076 && newSecondClassifyId == 4254) {
                modelV2.setTransformDBClew(true);
            }
        } else {
            if (newFirstClassifyId == 3301 && newSecondClassifyId == 4152) {
                modelV2.setTransformDBClew(true);
            }
        }

        return modelV2;
    }

    @Override
    public Response<Integer> AssigningTaskV2(int adminUserId) {
        String lockName = "admin-work-order-flow-assigning-task-lock-v2";
        String identifier = null;
        try {
            identifier = cfRedissonHandler.tryLock(lockName, 5 * 1000, 60 * 1000);
        } catch (InterruptedException e) {
            log.error("lock error", e);
        }
        if (StringUtils.isBlank(identifier)) {
            log.info("获取锁异常");
            return NewResponseUtil.makeSuccess(0);
        }
        try {
            List<Integer> userOrgIds = orgIdBiz.getLowestOrgByUserIds(Collections.singletonList(adminUserId)).stream().map(AdminOrganizationUserMap::getOrgId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userOrgIds)) {
                log.warn("该用户:{}不存在组织信息", adminUserId);
                return NewResponseUtil.makeError(AdminErrorCode.NO_ACCESS_TO_GET_WORK_ORDER);
            }
            // 有些组织不能手动领取工单
            if (!autoAssignRecordBiz.canManualAssignOrder(userOrgIds)) {
                log.info("当前用户不能手动领取信息传递工单.userId:{} userOrgIds:{}", adminUserId, userOrgIds);
                return NewResponseUtil.makeError(AdminErrorCode.WORK_FLOW_CAN_NOT_ASSIGN);
            }

            if (judgeUnHandle) {
                StopWatch stopWatch = new StopWatch();
                stopWatch.start();
                boolean flag = judgeUnHandleWorkId(adminUserId);
                stopWatch.stop();
                log.info("judgeUnHandleWorkIdDrawCashShutdown CountDownLatch await end...use {}Seconds", stopWatch.getTotalTimeSeconds());
                if (flag) {
                    log.info("存在仍未处理的工单,adminUserId:{}", adminUserId);
                    return NewResponseUtil.makeError(AdminErrorCode.EXIST_ORDER_NO_HANDLE);
                }
            } else {
                StopWatch stopWatch = new StopWatch();
                stopWatch.start();
                //查看当前用户是否还有未处理完成的工单
                AdminWorkOrderFlowParam.SearchParam searchParam = new AdminWorkOrderFlowParam.SearchParam();
                searchParam.setOperatorId(adminUserId);
                searchParam.setWorkOrderStatusList(Lists.newArrayList(CREATED.getCode()));
                List<Integer> unHandleWorkIds = adminWorkOrderFlowDao.selectWaitForHandleFlowIdsByParam(searchParam);
                stopWatch.stop();
                log.info("selectWaitForHandleFlowIdsByParamDrawCashShutdown CountDownLatch await end...use {}Seconds", stopWatch.getTotalTimeSeconds());
                if (CollectionUtils.isNotEmpty(unHandleWorkIds)) {
                    log.info("存在仍未处理的工单,unHandleWorkIds:{}", unHandleWorkIds);
                    return NewResponseUtil.makeError(AdminErrorCode.EXIST_ORDER_NO_HANDLE);
                }
            }

            List<AdminWorkOrderFlowView> allotWorkFlows = Lists.newArrayList();
            for (Integer userOrgId : userOrgIds) {
                List<AdminWorkOrderFlowView> adminWorkOrderFlows = listUnHandleWorkInTwoWeeks(0, Lists.newArrayList(userOrgId));
                if (CollectionUtils.isEmpty(adminWorkOrderFlows)) {
                    log.info("用户组织下无新工单");
                    continue;
                }
                int size = adminWorkOrderFlows.size();
                //查找这个组织下有多少在线用户
                List<Long> userIds = orgIdBiz.getOrgUserByOrgIds(Lists.newArrayList(userOrgId))
                        .stream()
                        .map(item -> (long) item.getUserId())
                        .collect(Collectors.toList());
                long memberIds = staffStatusBiz.listByUserIds(userIds)
                        .stream()
                        .filter(item -> WorkFlowStaffStatus.StaffStatusEnum.online.getCode() == item.getStaffStatus())
                        .count();
                if (memberIds > 0) {
                    long allotNum = size / memberIds;
                    if (allotNum == 0 && size > 0) {
                        allotNum = 1;
                    }
                    allotWorkFlows.addAll(adminWorkOrderFlows.subList(0, Math.toIntExact(allotNum)));
                    if (allotWorkFlows.size() > 0) {
                        break;
                    }
                }
            }
            List<Long> workOrderIds = allotWorkFlows.stream().map(AdminWorkOrderFlow::getWorkOrderId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(workOrderIds)) {
                return NewResponseUtil.makeSuccess(0,"当前无待领取工单");
            }
            adminWorkOrderBiz.updateWithOperatorIds(workOrderIds, adminUserId, CREATED.getCode());
            log.info("信息流转工单 userId:{} 领取了工单 {}", adminUserId, workOrderIds);
            List<AdminWorkOrderFlowRecord> recordList = allotWorkFlows.stream().map(val ->
                    new AdminWorkOrderFlowRecord(val, adminUserId, "", AdminWorkOrderFlowConst.OperateTypeEnum.ASSIGN_WORK_FLOW.getCode(), val.getLevel(), val.getSecondClassifyId()))
                    .collect(Collectors.toList());
            adminWorkOrderFlowRecordBiz.insertList(recordList);
            return NewResponseUtil.makeSuccess(allotWorkFlows.size());
        } finally {
            try {
                if (StringUtils.isNotBlank(identifier)) {
                    cfRedissonHandler.unLock(lockName, identifier);
                }
            } catch (Exception e) {
                log.info("释放锁异常", e);
            }
        }
    }

    private List<AdminWorkOrderFlowView> selectUnHandleTaskByUserIdAndSize(int userId, int maxTaskSize) {
        List<Integer> userOrgIds = orgIdBiz.getLowestOrgByUserIds(Arrays.asList(userId)).stream().map(AdminOrganizationUserMap::getOrgId)
                .collect(Collectors.toList());

        log.info("信息传递工单 userId:{}, orgIds:{}", userId, userOrgIds);
        if (CollectionUtils.isEmpty(userOrgIds)) {
            return Lists.newArrayList();
        }
        return listUnHandleWorkInTwoWeeks(maxTaskSize, userOrgIds);
    }

    @Nullable
    private List<AdminWorkOrderFlowView> listUnHandleWorkInTwoWeeks(int maxTaskSize, List<Integer> userOrgIds) {

        Date twoWeeksBefore = new DateTime().minusDays(14).withHourOfDay(0).withMinuteOfHour(0).toDate();

        List<AdminWorkOrderFlowView> flows = Lists.newArrayList();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        if (judgeOffSwitch) {
            List<AdminWorkOrder> workOrderList = adminWorkOrderBiz.selectUnHandleTask(twoWeeksBefore);
            if (CollectionUtils.isNotEmpty(workOrderList)) {
                workOrderList = workOrderList.stream().filter(v -> !Sets.newHashSet(4, 5).contains(v.getOrderStatus())).collect(Collectors.toList());
                List<Long> ids = workOrderList.stream().map(AdminWorkOrder::getId).collect(Collectors.toList());
                List<AdminWorkOrderFlow> adminWorkOrderFlowList = adminWorkOrderFlowDao.selectByWorkOrderIdList(ids);
                if (CollectionUtils.isNotEmpty(adminWorkOrderFlowList)) {
                    if (CollectionUtils.isNotEmpty(userOrgIds)) {
                        adminWorkOrderFlowList = adminWorkOrderFlowList.stream().filter(v -> userOrgIds.contains(v.getProblemType())).collect(Collectors.toList());
                    }
                    Map<Long, Integer> longIntegerMap = workOrderList.stream().collect(Collectors.toMap(AdminWorkOrder::getId, AdminWorkOrder::getLevel));
                    for (AdminWorkOrderFlow adminWorkOrderFlow : adminWorkOrderFlowList) {
                        AdminWorkOrderFlowView adminWorkOrderFlowView = new AdminWorkOrderFlowView();
                        BeanUtils.copyProperties(adminWorkOrderFlow, adminWorkOrderFlowView);
                        Integer level = longIntegerMap.get(adminWorkOrderFlow.getWorkOrderId());
                        adminWorkOrderFlowView.setLevel(Objects.isNull(level) ? 0 : level);
                        flows.add(adminWorkOrderFlowView);
                    }
                }
            }
        } else {
            // 查找用户角色下 未处理的的工单
            flows = adminWorkOrderFlowDao.selectUnHandleTask(userOrgIds, maxTaskSize, twoWeeksBefore);
        }
        stopWatch.stop();
        log.info("AdminWorkOrderFlowBizImplDrawCashShutdownJudgeOffSwitch CountDownLatch await end...use {}Seconds", stopWatch.getTotalTimeSeconds());
        if (CollectionUtils.isNotEmpty(flows)) {
            for (AdminWorkOrderFlow flow : flows) {
                if (flow != null && !Strings.isNullOrEmpty(flow.getEncryptMobile())) {
                    flow.setMobile(shuidiCipher.decrypt(flow.getEncryptMobile()));
                }
            }
        }
        return flows;
    }

    @Override
    public UnHandleFlowOrderSum countUnHandleOrder(int operatorId) {
        // 按理工单的数量不会太多～  0 表示没有限制
        List<AdminWorkOrderFlowView> orderFlows = selectUnHandleTaskByUserIdAndSize(operatorId, 0);
        UnHandleFlowOrderSum res = new UnHandleFlowOrderSum();

        res.total = orderFlows.size();
        for (AdminWorkOrderFlowView flowView : orderFlows) {
            switch (AdminWorkOrderConst.Level.getByCode(flowView.getLevel())) {
                case NO:
                    res.no++;
                    break;
                case LOW:
                    res.low++;
                    break;
                case MEDIUM:
                    res.medium++;
                    break;
                case HIGH:
                    res.high++;
                    break;
                default:
                    break;
            }
        }
        return res;
    }

    @Override
    public List<OrgUnHandleOrder> countUnHandleByOrgId(List<Integer> orgIds) {
        List<AdminWorkOrderFlowView> flowViews = listUnHandleWorkInTwoWeeks(0, orgIds);
        if (CollectionUtils.isEmpty(flowViews)) {
            return Lists.newArrayList();
        }
        Map<Integer, Long> orgAndUnHandlerNum = flowViews.stream().collect(Collectors.groupingBy(AdminWorkOrderFlowView::getProblemType, Collectors.counting()));
        List<OrgUnHandleOrder> unHandleOrderList = Lists.newArrayList();
        for (Integer orgId : orgAndUnHandlerNum.keySet()) {
            unHandleOrderList.add(new OrgUnHandleOrder(orgAndUnHandlerNum.get(orgId).intValue(), orgId));
        }
        return unHandleOrderList;
    }


    @Override
    public OperationResult<Long> createTransformOrder(TransformOrderCreateParam param){

        if(Objects.isNull(param)){
            OperationResult.failWithMsg("入参为空");
        }
        //显式置换appcode
        AuthSaasContext.setAuthAppCode(authSaasProperties.getAppCode());

        AdminWorkOrderFlowView adminWorkOrderFlowView = new AdminWorkOrderFlowView();
        BeanUtils.copyProperties(param, adminWorkOrderFlowView);
        log.info("信息---adminWorkOrderFlowView:{}",JSON.toJSONString(adminWorkOrderFlowView));
        int problemType = adminWorkOrderFlowView.getProblemType();
        String mobile = adminWorkOrderFlowView.getMobile();
        String creatorChannel = adminWorkOrderFlowView.getCreatorChannel();
        String problemContent = adminWorkOrderFlowView.getProblemContent();
        int creatorId = adminWorkOrderFlowView.getCreatorId();
        int caseId = adminWorkOrderFlowView.getCaseId();
        int operatorId = adminWorkOrderFlowView.getOperatorId();
        String problemImg = adminWorkOrderFlowView.getProblemImg();
        int cityId = adminWorkOrderFlowView.getCityId();
        int provinceId = adminWorkOrderFlowView.getProvinceId();
        int countyId = adminWorkOrderFlowView.getCountyId();
        int newFirstClassifyId = adminWorkOrderFlowView.getNewFirstClassifyId();
        int newSecondClassifyId = adminWorkOrderFlowView.getNewSecondClassifyId();
        int newThirdClassifyId = adminWorkOrderFlowView.getNewThirdClassifyId();
        adminWorkOrderFlowView.setHandleImg(StringUtils.EMPTY);

        List<CrowdfundingCity> crowdfundingCityList = adminCrowdfundingCityBiz.getListByCode(Lists.newArrayList(provinceId, cityId, countyId));
        adminWorkOrderFlowView.setProvinceId(0);
        adminWorkOrderFlowView.setProvinceName(StringUtils.EMPTY);
        adminWorkOrderFlowView.setCityId(0);
        adminWorkOrderFlowView.setCityName(StringUtils.EMPTY);
        adminWorkOrderFlowView.setCountyId(0);
        adminWorkOrderFlowView.setCountyName(StringUtils.EMPTY);
        for (CrowdfundingCity crowdfundingCity : crowdfundingCityList) {
            String code = crowdfundingCity.getCode();
            int id = crowdfundingCity.getId();
            String name = crowdfundingCity.getName();
            if (StringUtils.equals(code, String.valueOf(provinceId))) {
                adminWorkOrderFlowView.setProvinceId(id);
                adminWorkOrderFlowView.setProvinceName(name);
            } else if (StringUtils.equals(code, String.valueOf(cityId))) {
                adminWorkOrderFlowView.setCityId(id);
                adminWorkOrderFlowView.setCityName(name);
            } else if (StringUtils.equals(code, String.valueOf(countyId))) {
                adminWorkOrderFlowView.setCountyId(id);
                adminWorkOrderFlowView.setCountyName(name);
            }
        }

        //创建前判断是否能生成，同案例+同问题类型+未处理完
        Response<String> existRelateFlow = this.existRelateFlow(adminWorkOrderFlowView);
        if (existRelateFlow.notOk()) {
            return OperationResult.failWithMsg(existRelateFlow.getMsg());
        }

        // 问题至少要分配到组
        if (problemType == 0) {
            return OperationResult.failWithMsg("问题至少要分配到组");
        }

        // 外呼新加 如果是问题的类型是  线索流转至1V1服务   则需要判断则电话号码必填。
        if (MobileUtil.illegal(mobile)) {
            boolean flag = StringUtils.isEmpty(mobile);
            if (newSecondClassifyId == 2709 || newSecondClassifyId == 2710) {
                String title = flag ? "手机号码不允许为空" : "手机号码未通过合法校验，请确认手机号是否正确";
                AuthRpcResponse<String> response = seaAccountClientV1.getNameByUserId(creatorId);
                String userName = Optional.ofNullable(response).filter(AuthRpcResponse::isSuccess).map(AuthRpcResponse::getResult).orElse(StringUtils.EMPTY);
                String content = title + "【异常监控-无需处理】\n" +
                        "操作时间: " + DateUtil.getCurrentDateStr() + "\n" +
                        "操作人: " + userName + "\n" +
                        "问题分类: " + this.getNewClassifyDesc(newFirstClassifyId, newSecondClassifyId, newThirdClassifyId);
                //发送到企业微信
                AlarmBotService.sentText("07077bb0-383b-4dd4-b0ae-97905ac8108d", content, null, null);
                if (flag) {
                    return OperationResult.failWithMsg("手机号码不允许为空");
                } else {
                    return OperationResult.failWithMsg("手机号码未通过合法校验，请确认手机号是否正确");
                }
            }
        }

        //前端传了creatorChannel，但是不符合要求
        boolean creatorChannelIllegal = StringUtils.isNotEmpty(creatorChannel) &&
                (WorkCreatorChannelEnum.findByDesc(creatorChannel) == null);

        if (creatorChannelIllegal) {
            log.warn("渠道来源信息错误，vo:{}", adminWorkOrderFlowView);
            return OperationResult.failWithMsg("渠道来源信息错误");
        }

        createAdminWorkOrder(adminWorkOrderFlowView);
        log.info("adminWorkOrderFlow insert:{}", JSON.toJSONString(adminWorkOrderFlowView));
        long workOrderId = adminWorkOrderFlowView.getWorkOrderId();
        //保存图片
        if (StringUtils.isNotEmpty(problemImg)) {
            adminWorkOrderFlowView.setProblemImg(apolloService.getCosSwitch() ? this.saveImgV2(problemImg): this.saveImg(problemImg));
        }
        long workFlowId = insertOne(adminWorkOrderFlowView);
        adminWorkOrderFlowView.setWorkOrderFlowId(String.valueOf(workFlowId));
        String generateWorkFlowId = flowOrderCommonService.generateWorkFlowId(new Date(), workFlowId);
        syncCommentToCaseDetail(
                workOrderId,
                problemContent,
                "【工单问题创建】工单" + generateWorkFlowId,
                creatorId,
                caseId
        );

        // 若未指定人自动分配
        if (operatorId <= 0) {
            autoAssignRecordBiz.triggerAutoAssignReportOrderWithLock();
        } else {
            autoAssignRecordBiz.onWorkOrderAssign(workOrderId);
        }

        return OperationResult.success(workOrderId);
    }

    @Override
    public OperationResult<Long> updateTransformOrder(SyncOrderInfoParam param) {
        log.info("同步工单信息 param:{}",JSON.toJSONString(param));
        //未修改为null
        if (Objects.isNull(param) || Objects.isNull(param.getWorkOrderId()) || param.getWorkOrderId() <= 0L) {
            OperationResult.failWithMsg("入参为空");
        }
        long id = param.getWorkOrderId();
        Integer level = param.getLevel();
        Integer operatorId = param.getOperatorId();
        Integer orderStatus = param.getOrderStatus();
        Integer problemType = param.getProblemType();
        String replyContent = param.getReplyContent();

        //修改优先级、工单状态、处理人
        int adminWorkOrderRes = 0;
        if (Objects.nonNull(level) || Objects.nonNull(operatorId) || Objects.nonNull(orderStatus)) {
            adminWorkOrderRes = adminWorkOrderBiz.updateMsg(id, orderStatus, operatorId, level);
        }

        //修改组织架构
        int adminWorkOrderFlowRes = 0;
        if (Objects.nonNull(problemType)) {
            adminWorkOrderFlowRes = adminWorkOrderFlowDao.updateTaskType(id, problemType);
        }

        if (StringUtils.isNotBlank(replyContent)) {
            AdminWorkOrderFlow adminWorkOrderFlow = adminWorkOrderFlowDao.selectByWorkOrderId(id);
            //todo  图片二期在做
            if (Objects.nonNull(adminWorkOrderFlow)) {
                syncCommentToCaseDetail(
                        param.getWorkOrderId(),
                        replyContent,
                        "工单处理意见",
                        Objects.nonNull(operatorId) ? operatorId : 0,
                        adminWorkOrderFlow.getCaseId()
                );

                AdminWorkOrder adminWorkOrder = adminWorkOrderBiz.selectById(id);
                AdminWorkOrderFlowRecord adminWorkOrderFlowRecord = new AdminWorkOrderFlowRecord();
                adminWorkOrderFlowRecord.setWorkOrderId(id);
                adminWorkOrderFlowRecord.setFlowId(adminWorkOrderFlow.getId());
                adminWorkOrderFlowRecord.setProblemType(adminWorkOrderFlow.getProblemType());
                adminWorkOrderFlowRecord.setCurrentProblemType(adminWorkOrderFlow.getProblemType());
                adminWorkOrderFlowRecord.setComment(replyContent);
                adminWorkOrderFlowRecord.setCaseId(adminWorkOrderFlow.getCaseId());
                adminWorkOrderFlowRecord.setOperateType(AdminWorkOrderFlowConst.OperateTypeEnum.JING_XI_REPLY.getCode());
                adminWorkOrderFlowRecord.setLevel(Objects.nonNull(adminWorkOrder) ? adminWorkOrder.getLevel() : 0);
                adminWorkOrderFlowRecord.setOperatorId(Objects.nonNull(operatorId) ? operatorId : 0);
                adminWorkOrderFlowRecord.setProblemContent(StringUtils.EMPTY);
                adminWorkOrderFlowRecord.setProblemImg(StringUtils.EMPTY);
                adminWorkOrderFlowRecord.setOrderOperatorId(0);
                adminWorkOrderFlowRecord.setSecondClassifyId(0L);

                adminWorkOrderFlowRecordBiz.insertOne(adminWorkOrderFlowRecord);
            }
        }

        return OperationResult.success((long) adminWorkOrderRes & adminWorkOrderFlowRes);
    }

    @Override
    public Response createWorkOrderFlow(AdminWorkOrderFlowView vo) {

        // 问题至少要分配到组
        if (vo.getProblemType() == 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        // 外呼新加 如果是问题的类型是  线索流转至1V1服务   则需要判断则电话号码必填。
        if (MobileUtil.illegal(vo.getMobile())) {
            if (vo.getNewFirstClassifyId() == 2708 && vo.getNewSecondClassifyId() == 2709) {
                return NewResponseUtil.makeFail("问题类型是：水滴筹-发起相关 线索流转至1V1服务 电话号码必须是合法的");
            }
        }

        //前端传了creatorChannel，但是不符合要求
        boolean creatorChannelIllegal = StringUtils.isNotEmpty(vo.getCreatorChannel()) &&
                (WorkCreatorChannelEnum.findByDesc(vo.getCreatorChannel()) == null);

        if (creatorChannelIllegal) {
            log.warn("渠道来源信息错误，vo:{}", vo);
            return NewResponseUtil.makeFail("渠道来源信息错误");
        }

        if (workOrderCreateClewQuestionId(vo)) {
            if (StringUtils.isBlank(vo.getClewSendBDUniqueCode()) || StringUtils.isBlank(vo.getClewSendBDPhone())) {
                return NewResponseUtil.makeFail("线索流转至线下顾问，BD唯一标识和手机号不能为空");
            }
        }

        // 创建 父workorder
        createAdminWorkOrder(vo);
        log.info("adminWorkOrderFlow insert:{}", JSON.toJSONString(vo));
        long workFlowId = insertOne(vo);
        vo.setWorkOrderFlowId(String.valueOf(workFlowId));
        String generateWorkFlowId = flowOrderCommonService.generateWorkFlowId(new Date(), workFlowId);
        syncCommentToCaseDetail(
                vo.getWorkOrderId(),
                vo.getProblemContent(),
                "【工单问题创建】工单" + generateWorkFlowId,
                vo.getCreatorId(),
                vo.getCaseId()
        );

        // 若未指定人自动分配
        if (vo.getOperatorId() <= 0) {
            autoAssignRecordBiz.triggerAutoAssignReportOrderWithLock();
        } else {
            autoAssignRecordBiz.onWorkOrderAssign(vo.getWorkOrderId());
        }

        // 二线客服的工单可以自动分配
        autoAssignRecordBiz.assignWorkTOrg(vo.getProblemType(),0);
        // 自动暂停打款
        pauseDrawCash(vo);
        // 自动暂停退款
        pauseRefund(vo);
        //发起问题-线索流转至线下顾问
        if (workOrderCreateClewQuestionId(vo)) {
            workOrderCreateClewHandle(vo);
        }
        return NewResponseUtil.makeSuccess(generateWorkFlowId);
    }

    private void workOrderCreateClewHandle(AdminWorkOrderFlowView flowOrder) {
        try {
            WorkOrderCreateClewDTO workOrderCreateClewDTO = WorkOrderCreateClewDTO.builder()
                    .uniqueCode(flowOrder.getClewSendBDUniqueCode())
                    .phone(flowOrder.getClewSendBDPhone())
                    .build();
            Message<WorkOrderCreateClewDTO> msg = new Message<>(MQTopicCons.CF, MQTagCons.WORK_ORDER_CREATE_CLEW, String.valueOf(flowOrder.getId()),
                    workOrderCreateClewDTO);
            MessageResult result = producer.send(msg);
            log.info("信息传递工单发送线索消息到BD，result:{}", JSON.toJSONString(result));
            //工单处理
            Message<AdminWorkOrderFlowView> message = new Message<>(MQTopicCons.CF, MQTagCons.WORK_ORDER_CREATE_CLEW_ORDER_HANDLE, String.valueOf(flowOrder.getId()),
                    flowOrder);
            MessageResult messageResult = producer.send(message);
            log.info("信息传递工单工单处理，result:{}", JSON.toJSONString(messageResult));
        } catch (Exception e) {
            log.error("信息传递工单发送线索消息到BD失败，flowOrder:{}", JSON.toJSONString(flowOrder), e);
        }
    }

    //发起问题-线索流转至线下顾问
    private boolean workOrderCreateClewQuestionId(AdminWorkOrderFlowView vo) {
        long newFirstClassifyId = vo.getNewFirstClassifyId();
        long newSecondClassifyId = vo.getNewSecondClassifyId();
        if (applicationService.isProduction()) {
            //线上
            return newFirstClassifyId == 3076 && newSecondClassifyId == 4653;
        } else {
            //测试
            return newFirstClassifyId == 3301 && newSecondClassifyId == 4159;
        }
    }

    private void pauseRefund(AdminWorkOrderFlowView vo) {
        if (vo.getNewFirstClassifyId() != 3059 || vo.getNewSecondClassifyId() != 3068 || vo.getNewThirdClassifyId() != 3074) {
            return;
        }

        int operatorId = vo.getCreatorId();
        int caseId = vo.getCaseId();

        log.info("信息传递工单自动暂停退款 data: {}", vo);
        String comment = vo.getProblemContent();
        //暂停退款
        FeignResponse feignResponse = cfFinancePauseFeignClient.addPauseRefund(operatorId, caseId,
                CfDrawCashPauseRecordEnum.PauseSourceTypeEnum.ARTIFICIAL.getCode(),
                Lists.newArrayList(106),
                comment, CfDrawCashPauseRecordEnum.RecordStatusEnum.PAUSE.getCode(), true);
        if (feignResponse == null || feignResponse.notOk()) {
            log.error("信息传递工单自动暂停退款失败，data: {}", vo);
        }
    }
    /**
     * 暂停打款
     * @param vo
     */
    private void pauseDrawCash(AdminWorkOrderFlowView vo) {
        if (!Sets.newHashSet(3059, 3104, 3161).contains(vo.getNewFirstClassifyId())) {
            return;
        }

        if (!Sets.newHashSet(3060,3068,3141,3179).contains(vo.getNewSecondClassifyId())) {
            return;
        }

        if (!Sets.newHashSet(3061, 3069, 3143, 3182).contains(vo.getNewThirdClassifyId())) {
            return;
        }

        int operatorId = vo.getCreatorId();
        int caseId = vo.getCaseId();
        log.info("信息传递工单自动暂停打款 data: {}", vo);
        String comment = vo.getProblemContent();
        //暂停打款
        FeignResponse feignResponse = cfFinancePauseFeignClient.addPauseV2(operatorId, caseId,
                CfDrawCashPauseRecordEnum.PauseSourceTypeEnum.ARTIFICIAL.getCode(),
                Lists.newArrayList(106),
                comment, CfDrawCashPauseRecordEnum.RecordStatusEnum.PAUSE.getCode(),  false, true);
        if (feignResponse == null || feignResponse.notOk()) {
            log.error("信息传递工单自动暂停打款失败，data: {}", vo);
        }
    }

    private String saveImg(String problemImg) {
        try {
            Map<String, String> imagesMap = externalWorkOrderApiBiz.getAccessImages(Splitter.on(",").splitToList(problemImg));
            List<String> list = Lists.newArrayList();
            if (MapUtils.isNotEmpty(imagesMap)) {
                Collection<String> imageList = imagesMap.values();
                for (String image : imageList) {
                    InputStream inputStream = ImageSimilarity.getStream(image);
                    String imageUrl = cosPlugins.uploadFile(inputStream, "cf-images", "cf-images/image", UUID.randomUUID().toString() + "");
                    list.add(imageUrl);
                    log.info("上传图片的地址 imageUrl:{}", imageUrl);
                }
            }
            return Joiner.on(",").join(list);
        } catch (Exception e) {
            log.error("图片保存失败", e);
        }
        return StringUtils.EMPTY;
    }

    public String saveImgV2(String problemImg) {
        try {
            Map<String, String> imagesMap = externalWorkOrderApiBiz.getAccessImages(Splitter.on(",").splitToList(problemImg));
            List<String> list = Lists.newArrayList();
            if (MapUtils.isNotEmpty(imagesMap)) {
                Collection<String> imageList = imagesMap.values();
                for (String image : imageList) {
                    String key = "cf-images/image" + "/" + UUID.randomUUID().toString();
                    InputStream inputStream = ImageSimilarity.getStream(image);
                    PutObjectResult putObjectResult = cosClientWrapper.putObject(key, inputStream, null);
                    log.info("cos上传 putObjectResult:{}", JSON.toJSONString(putObjectResult));
                    String imageUrl = "https://image.shuidichou.com" + "/" + key;
                    list.add(imageUrl);
                    log.info("上传图片的地址 imageUrl:{}", imageUrl);
                }
            }
            return Joiner.on(",").join(list);
        } catch (Exception e) {
            log.error("图片保存失败", e);
        }
        return StringUtils.EMPTY;
    }

    private void createAdminWorkOrder(AdminWorkOrderFlowView vo) {

        AdminWorkOrder adminWorkOrder = AdminWorkOrder.create(
                AdminWorkOrderConst.Type.FLOW,
                AdminWorkOrderConst.Task.NO,
                AdminWorkOrderConst.Role.POST, vo.getCreatorId(), vo.getLevel(),
                "");
        //是否创建工单时，就将任务分配
        adminWorkOrder.setOperatorId(vo.getOperatorId());
        adminWorkOrder.setHandleResult(AdminWorkOrderConst.Result.INIT.getCode());
        adminWorkOrder.setOrderStatus(CREATED.getCode());
        adminWorkOrder.setTaskType(vo.getTaskType());

        workOrderBiz.insertOne(adminWorkOrder);

        vo.setWorkOrderId(adminWorkOrder.getId());
    }

    @Diff(diffMethod = "selectAdminWorkOrderByParamFromEs", diffCompare = "com.shuidihuzhu.cf.diff.SelectAdminWorkOrderByParamCompare")
    @Override
    public PageInfo<AdminWorkOrderFlowView> selectAdminWorkOrderByParam(AdminWorkOrderFlowParam.SearchParam searchParam) {
        // 前置参数的处理
        preHandleSearchParam(searchParam);
        PageHelper.startPage(searchParam.getPageNum(), searchParam.getPageSize());
        if(!Strings.isNullOrEmpty(searchParam.getMobile())){
            searchParam.setEncryptMobile(oldShuidiCipher.aesEncrypt(searchParam.getMobile()));
        }
        List<AdminWorkOrderFlowView> views = adminWorkOrderFlowDao.selectWorkFlowByParam(searchParam);
        if (views != null && views.size() > 0) {
            for (AdminWorkOrderFlowView flow : views) {
                if (flow != null && !Strings.isNullOrEmpty(flow.getEncryptMobile())) {
                    flow.setMobileMask(maskUtil.buildByEncryptPhone(flow.getEncryptMobile()));
                }
                flow.setFollowTagIds(AdminWorkOrderFlow.splitterFollowTagViews(flow.getFollowTags()));
                //新问题分类拼接
                int newFirstClassifyId = flow.getNewFirstClassifyId();
                int newSecondClassifyId = flow.getNewSecondClassifyId();
                int newThirdClassifyId = flow.getNewThirdClassifyId();
                flow.setNewClassifyDesc(this.getNewClassifyDesc(newFirstClassifyId,newSecondClassifyId,newThirdClassifyId));

                WorkCreatorChannelEnum channelEnum = WorkCreatorChannelEnum.findByCode(flow.getAssignChannel());
                flow.setCreatorChannel(channelEnum == null ? StringUtils.EMPTY : channelEnum.getDesc());
            }
        }
        PageInfo info = new PageInfo(views);

        postHandleResult(info);

        return info;
    }

    @Override
    public Pair<Long, List<AdminWorkOrderFlowView>> selectAdminWorkOrderByParamFromEs(AdminWorkOrderFlowParam.SearchParam searchParam) {
        // 前置参数的处理
        preHandleSearchParam(searchParam);

        CfWorkOrderIndexSearchParam indexSearchParam = new CfWorkOrderIndexSearchParam();
        indexSearchParam.setAwoOrderTypes(Lists.newArrayList(AdminWorkOrderConst.Type.FLOW.getCode()));

        if (searchParam.getFlowId() > 0) {
            indexSearchParam.setAwofIds(Lists.newArrayList((long) searchParam.getFlowId()));
        }

        if (CollectionUtils.isNotEmpty(searchParam.getWorkOrderIds())) {
            indexSearchParam.setAwoIds(Lists.newArrayList(searchParam.getWorkOrderIds()));
        }

        if (searchParam.getCaseId() != null && searchParam.getCaseId() > 0) {
            indexSearchParam.setCfCaseIds(Lists.newArrayList(searchParam.getCaseId()));
        }

        if (CollectionUtils.isNotEmpty(searchParam.getOrgIdList())) {
            indexSearchParam.setAwofProblemTypes(searchParam.getOrgIdList());
        }

        if (StringUtils.isNotBlank(searchParam.getMobile())) {
            indexSearchParam.setAwofMobiles(Lists.newArrayList(searchParam.getMobile()));
        }

        if (searchParam.getLevel() != null) {
            indexSearchParam.setAwoLevels(Lists.newArrayList(searchParam.getLevel().intValue()));
        }

        if (searchParam.getCreatorId() != null && searchParam.getCreatorId() != 0) {
            indexSearchParam.setAwoCreatorIds(Lists.newArrayList(searchParam.getCreatorId().longValue()));
        }

        if (searchParam.getOperatorId() != null && searchParam.getOperatorId() != 0) {
            indexSearchParam.setAwoOperatorIds(Lists.newArrayList(searchParam.getOperatorId().longValue()));
        }

        if (searchParam.getCreateStartTime() != null) {
            indexSearchParam.setAwoCreateTimeStart(searchParam.getCreateStartTime().getTime());
        }

        if (searchParam.getCreateEndTime() != null) {
            indexSearchParam.setAwoCreateTimeEnd(searchParam.getCreateEndTime().getTime());
        }

        if (searchParam.getUpdateStartTime() != null) {
            indexSearchParam.setAwoUpdateTimeStart(searchParam.getUpdateStartTime().getTime());
        }
        if (searchParam.getUpdateEndTime() != null) {
            indexSearchParam.setAwoUpdateTimeEnd(searchParam.getUpdateEndTime().getTime());
        }

        if (CollectionUtils.isNotEmpty(searchParam.getSecondClassifyIds())) {
            indexSearchParam.setAwofSecondClassifyIds(searchParam.getSecondClassifyIds());
        }

        if (searchParam.getWorkOrderStatus() != null) {
            indexSearchParam.setAwoOrderStatuses(Lists.newArrayList(searchParam.getWorkOrderStatus()));
        } else if (CollectionUtils.isNotEmpty(searchParam.getWorkOrderStatusList())) {
            indexSearchParam.setAwoOrderStatuses(searchParam.getWorkOrderStatusList());
        }

        if (searchParam.getFollowTag() != null) {
            indexSearchParam.setAwofFollowTags(Lists.newArrayList(searchParam.getFollowTag()));
        }

        if (searchParam.getNewFirstClassifyId() != null && searchParam.getNewFirstClassifyId() != 0) {
            indexSearchParam.setNewFirstClassifyId(Lists.newArrayList(searchParam.getNewFirstClassifyId()));
        }

        if (searchParam.getNewSecondClassifyId() != null && searchParam.getNewFirstClassifyId() != 0) {
            indexSearchParam.setNewSecondClassifyId(Lists.newArrayList(searchParam.getNewSecondClassifyId()));
        }

        if (searchParam.getNewThirdClassifyId() != null && searchParam.getNewThirdClassifyId() != 0) {
            indexSearchParam.setNewThirdClassifyId(Lists.newArrayList(searchParam.getNewThirdClassifyId()));
        }

        indexSearchParam.setFrom((searchParam.getPageNum() - 1) * searchParam.getPageSize());
        indexSearchParam.setSize(searchParam.getPageSize());
        indexSearchParam.setSortEnum(CfWorkOrderIndexSortEnum.AWO_UPDATE_TIME_DESC);

        Pair<Long, List<AdminWorkOrder>> pair = cfSearch.cfWorkOrderIndexSearch(indexSearchParam);
        List<AdminWorkOrderFlowView> flowViews = getAdminWorkOrderFlowViews(pair.getRight());
        postHandleResult(flowViews);
        return Pair.of(pair.getLeft(), flowViews);
    }

    private List<AdminWorkOrderFlowView> getAdminWorkOrderFlowViews(List<AdminWorkOrder> workOrders) {
        if (CollectionUtils.isEmpty(workOrders)) {
            return Lists.newArrayList();
        }
        List<Long> workOrderIds = workOrders.stream().map(x -> x.getId()).collect(Collectors.toList());
        List<AdminWorkOrderFlow> flows = adminWorkOrderFlowDao.selectByWorkOrderIdList(workOrderIds);
        if (flows != null && flows.size() > 0) {
            for (AdminWorkOrderFlow flow : flows) {
                if (flow != null && !Strings.isNullOrEmpty(flow.getEncryptMobile())) {
                    flow.setMobileMask(maskUtil.buildByEncryptPhone(flow.getEncryptMobile()));
                }
            }
        }

        Map<Long, AdminWorkOrderFlow> flowMap = Maps.uniqueIndex(flows, AdminWorkOrderFlow::getWorkOrderId);

        List<AdminWorkOrderFlowView> flowViews = Lists.newArrayList();
        for (AdminWorkOrder workOrder : workOrders) {

            AdminWorkOrderFlow flow = flowMap.get(workOrder.getId());
            if (flow == null) {
                continue;
            }
            AdminWorkOrderFlowView flowView = AdminWorkOrderMapper.INSTANCE.toAdminWorkOrderFlowView(workOrder, flow);
            if (flowView != null) {
                flowView.setFollowTagIds(AdminWorkOrderFlow.splitterFollowTagViews(flow.getFollowTags()));
                //新问题分类拼接
                int newFirstClassifyId = flow.getNewFirstClassifyId();
                int newSecondClassifyId = flow.getNewSecondClassifyId();
                int newThirdClassifyId = flow.getNewThirdClassifyId();
                flowView.setNewClassifyDesc(this.getNewClassifyDesc(newFirstClassifyId,newSecondClassifyId,newThirdClassifyId));
            }
            flowViews.add(flowView);
        }
        return flowViews;
    }

    // 填充必要的返回值
    private void postHandleResult(PageInfo<AdminWorkOrderFlowView> info) {
        postHandleResult(info.getList());
    }

    private void postHandleResult(List<AdminWorkOrderFlowView> views) {
        if (CollectionUtils.isEmpty(views)) {
            return;
        }

        List<Integer> caseIds = new ArrayList<>();
        List<Long> secondClassifyIds = new ArrayList<>();
        List<Integer> userIds = new ArrayList<>();
        List<Integer> flowIds = Lists.newArrayList();
        views.forEach((item) -> {
            caseIds.add(item.getCaseId());
            secondClassifyIds.add(item.getSecondClassifyId());
            userIds.add(item.getCreatorId());
            userIds.add(item.getOperatorId());
            flowIds.add((int) item.getId());
        });

        ImmutableMap<Integer, FlowAndRemindCount> flowId2RemindCount = Maps.uniqueIndex(getRemindCount(flowIds), FlowAndRemindCount::getFlowId);

        // 查找创建人 操作人的 组织
        Map<Integer, List<List<AdminOrganization>>> uId2OrgMap = orgIdBiz.getOrgIdsByUserIds(userIds);

        Map<Integer, String> userId2NameMap = Maps.newHashMap();
        AuthRpcResponse<List<AdminUserAccountModel>> authRpcResponse = seaAccountClientV1.getUserAccountsByIds(userIds);
        if (authRpcResponse != null && CollectionUtils.isNotEmpty(authRpcResponse.getResult())) {
            userId2NameMap = authRpcResponse.getResult().stream().collect(Collectors.toMap(AdminUserAccountModel::getId, AdminUserAccountModel::getName));
        }

        Map<Integer, CrowdfundingInfo> caseIdMap = crowdfundingDelegate.getMapByIds(caseIds);

        Map<Long, AdminWorkOrderClassifySettings> classifySettingMap = classifySettingsBiz.queryAllSettings().stream()
                .collect(Collectors.toMap(AdminWorkOrderClassifySettings::getId, Function.identity()));

        for (AdminWorkOrderFlowView flowView : views) {

            // 填充 问题的一级分类、问题的二级分类
            filterAndFillClassify(flowView, classifySettingMap);
            // 填充caseTitle
            CrowdfundingInfo cf = caseIdMap.get(flowView.getCaseId());
            if (cf != null) {
                flowView.setCaseTitle(cf.getTitle());
                flowView.setCaseInfoId(cf.getInfoId());
            }
            // 填充创建人，处理人 姓名、组织
            fillRoleName(flowView, uId2OrgMap, userId2NameMap);
            // 生成工单号
            flowView.setWorkOrderFlowId(flowOrderCommonService.generateWorkFlowId(flowView.getCreateTime(), flowView.getId()));
            //催单次数
            int remindCount = Optional.ofNullable(flowId2RemindCount.get((int) flowView.getId())).map(FlowAndRemindCount::getRemindTimes).orElse(0);
            flowView.setRemindNums(remindCount);
            flowView.setRemindHighlight(remindCount > 2);
        }
    }

    // 参数的前置处理
    private void preHandleSearchParam(AdminWorkOrderFlowParam.SearchParam searchParam) {
        // 对于分类设置过滤需要考虑
        if (searchParam.getSecondClassifyId() != null && searchParam.getSecondClassifyId() != 0) {
            searchParam.setSecondClassifyIds(Arrays.asList(searchParam.getSecondClassifyId()));
        } else if (searchParam.getFirstClassifyId() != null && searchParam.getFirstClassifyId() != 0) {
            searchParam.setSecondClassifyIds(classifySettingsBiz.selectChildClassifySettings(searchParam.getFirstClassifyId(),
                    AdminWorkOrderClassifySettingsBizImpl.NO_DELETE).stream()
                    .map(AdminWorkOrderClassifySettings::getId).collect(Collectors.toList()));
        }

        if (searchParam.getRoleId() != null && searchParam.getRoleId() != 0) {
            searchParam.setOrgIdList(orgIdBiz.getChildrenOrgIds(Arrays.asList(searchParam.getRoleId())));
        }

        if (StringUtils.isNotBlank(searchParam.getFlowIdStr())) {
            searchParam.setFlowId(flowOrderCommonService.decodeFromFlowIdString(searchParam.getFlowIdStr()));
        }
    }

    private void filterAndFillClassify(AdminWorkOrderFlowView view, Map<Long, AdminWorkOrderClassifySettings> classifySettingMap) {

        AdminWorkOrderClassifySettings secondSettings = classifySettingMap.get(view.getSecondClassifyId());
        if (secondSettings == null || classifySettingMap.get(secondSettings.getParentId()) == null) {
            log.info("classifySetting id : {}, can't find classifysetting", view.getSecondClassifyId());
            return;
        }

        view.setFirstClassifyDesc(classifySettingMap.get(secondSettings.getParentId()).getAllText());
        view.setFirstClassifyId(secondSettings.getParentId());
        view.setSecondClassifyDesc(secondSettings.getAllText());
        //旧问题分类拼接
        view.setOldClassifyDesc(view.getFirstClassifyDesc() + "/" + view.getSecondClassifyDesc());
    }

    // 填充 角色名
    private void fillRoleName(AdminWorkOrderFlowView view, Map<Integer, List<List<AdminOrganization>>> uId2OrgMap,
                              Map<Integer, String> userId2NameMap) {

        view.setCreatorRoleName(getRoleName(view.getCreatorId(), uId2OrgMap));
        String createName = userId2NameMap.get(view.getCreatorId());
        view.setCreatorName(StringUtils.isBlank(createName) ? SYSTEM_CREATE : createName);

        view.setOperatorRoleName(getRoleName(view.getOperatorId(), uId2OrgMap));
        if (view.getOperatorId() != 0) {
            view.setOperatorName(userId2NameMap.get(view.getOperatorId()));
        } else {
            view.setOperatorName(orgIdBiz.joinOrgDescByNextId(view.getProblemType()));
        }
    }

    private String getRoleName(int userId, Map<Integer, List<List<AdminOrganization>>> uId2OrgMap) {

        List<List<AdminOrganization>> userCurOrgs = uId2OrgMap.get(userId);
        if (CollectionUtils.isEmpty(userCurOrgs)) {
            return "";
        }

        List<String> roleNameList = Lists.newArrayList();
        for (List<AdminOrganization> orgs : userCurOrgs) {
            List<String> levelNames = Lists.newArrayList();
            orgs.forEach(item -> levelNames.add(item.getName()));
            roleNameList.add(Joiner.on("-").join(levelNames));
        }
        return Joiner.on(";").join(roleNameList);
    }

    // 操作工单
    @Override
    public Response handleWorkFlowOrder(AdminWorkOrderFlowParam.HandleParam param) {

        log.info("信息传递工单修改工单的状态. param：{}", param);

        Response res = validate(param);
        if (res.getCode() != 0) {
            return res;
        }

        AdminWorkOrder adminWorkOrder = this.adminWorkOrderBiz.selectById(param.getWorkOrderId());

        AdminWorkOrderFlow oldFlowOrder = adminWorkOrderFlowDao.selectByWorkOrderId(param.getWorkOrderId());
        if (oldFlowOrder != null && !Strings.isNullOrEmpty(oldFlowOrder.getEncryptMobile())) {
            oldFlowOrder.setMobile(shuidiCipher.decrypt(oldFlowOrder.getEncryptMobile()));
        }
        param.setComment(StringUtils.trimToEmpty(param.getComment()));
        param.setHandleImg(StringUtils.trimToEmpty(param.getHandleImg()));
        adminWorkOrder.setComment(param.getComment());
        AdminWorkOrderFlowConst.OperateTypeEnum operateType = null;
        switch (AdminWorkOrderFlowConst.handleType.getByCode(param.getHandleType())) {
            case NO_HANDLE:
                adminWorkOrder.setOrderStatus(AdminWorkOrderConst.Status.NO_NEED_HANDLE.getCode());
                adminWorkOrder.setHandleResult(AdminWorkOrderConst.Result.NO_NEED_HANDLE.getCode());
                operateType = AdminWorkOrderFlowConst.OperateTypeEnum.NO_HANDLE_WORK_FLOW;
                adminWorkOrder.setOperatorId(param.getUserId());
                break;
            case FINISH:
                adminWorkOrder.setOrderStatus(AdminWorkOrderConst.Status.HANDLE_SUCCESS.getCode());
                adminWorkOrder.setHandleResult(AdminWorkOrderConst.Result.HANDLE_SUCCESS.getCode());
                operateType = AdminWorkOrderFlowConst.OperateTypeEnum.HANDLE_SUCCESS_WORK_FLOW;
                adminWorkOrder.setOperatorId(param.getUserId());
                break;
            case ALLOT:
                //分配时增加校验，当前工单的状态：如果是“处理完成、不需要处理“，则提示“该工单已处理完成“
                //https://wiki.shuiditech.com/pages/viewpage.action?pageId=415961512
                List<Integer> finishStatus = Lists.newArrayList(AdminWorkOrderConst.Status.FINISHED.getCode(),
                        AdminWorkOrderConst.Status.NO_NEED_HANDLE.getCode());
                if (finishStatus.contains(adminWorkOrder.getOrderStatus())) {
                    return NewResponseUtil.makeError(AdminErrorCode.WORK_FLOW_FINISHED);
                }
                adminWorkOrder.setOrderStatus(CREATED.getCode());
                adminWorkOrder.setHandleResult(AdminWorkOrderConst.Result.HANDLING.getCode());
                operateType = AdminWorkOrderFlowConst.OperateTypeEnum.ALLOT_WORK_FLOW;
                this.updateTaskType(param.getWorkOrderId(), param.getRoleId());
                boolean auto = true;
                AdminWorkOrderClassifySettings secondSettings = classifySettingsBiz.selectClassifySettingsById(param.getSecondClassifyId());
                if (secondSettings != null){
                    if (CLEW_SECOND_CLASSIFY_SETTINGS_FUWU.equals(StringUtils.trimToNull(secondSettings.getAllText()))) {
                        int result = noticeFuwuOrder(oldFlowOrder.getCaseId(),oldFlowOrder.getWorkOrderId(),oldFlowOrder.getMobile(),param.getUserId(),param.getComment());
                        //创建服务工单不分配人
                        if (result == 0){
                            log.info("floworderId={},SecondClassifyId={} 不分配人员",param.getWorkOrderId(),param.getSecondClassifyId());
                            adminWorkOrder.setOperatorId(0);
                            auto = false;
                        }
                    }
                }
                if (auto){
                    fillAutoAssignUser(oldFlowOrder, param);
                }

                adminWorkOrder.setOperatorId(param.getOperatorId());
                // 对comment需要处理下， 要明确是分配给什么组织和什么人
                String orgName = Optional.ofNullable(orgIdBiz.joinOrgDescByNextId(param.getRoleId())).orElse("");
                if (param.getOperatorId() != 0) {
                    AuthRpcResponse<AdminUserAccountModel> response = seaAccountClientV1.getValidUserAccountById(param.getOperatorId());
                    if (response != null && response.getResult() != null) {
                        orgName = Joiner.on(ORG_NAME_SPLIT).join(Arrays.asList(orgName, Optional.ofNullable(response.getResult().getName()).orElse("")));
                    }
                }
                param.setComment("分配:" + orgName + "  " + param.getComment());
                if (secondSettings != null) {
                    //分配时判断是否需要通知BD
                    if (CLEW_SECOND_CLASSIFY_SETTINGS_BD.equals(StringUtils.trimToNull(secondSettings.getAllText()))) {
                        noticeBD(param,oldFlowOrder);
                        log.info("floworderId={},SecondClassifyId={} 不分配人员",param.getWorkOrderId(),param.getSecondClassifyId());
                        adminWorkOrderFlowDao.updateProvinceAndCity(param.getWorkOrderId(),param.getCityId(),param.getProvinceId(),param.getCityName(),param.getProvinceName(),param.getCountyId(),param.getCountyName(),param.getHospital(),param.getHospitalId());
                        adminWorkOrder.setOperatorId(0);
                    }
                }
                break;
            case HANDLE:
                adminWorkOrder.setOrderStatus(AdminWorkOrderConst.Status.HANDLING.getCode());
                adminWorkOrder.setHandleResult(AdminWorkOrderConst.Result.HANDLING.getCode());
                operateType = AdminWorkOrderFlowConst.OperateTypeEnum.HANDLE_WORK_FLOW;
                adminWorkOrder.setOperatorId(param.getUserId());
                typePropertyBiz.addFlowBeginHandleRecord(oldFlowOrder.getId(), param.getUserId());
                break;
            default:
                return NewResponseUtil.makeFail("");
        }

        postHandleFlowOrder(param, adminWorkOrder, oldFlowOrder, operateType);
        return NewResponseUtil.makeSuccess("");
    }

    // 如果是分配到二线组织且 没有分配到人，则需要自动分配
    private void fillAutoAssignUser(AdminWorkOrderFlow oldFlowOrder, AdminWorkOrderFlowParam.HandleParam param) {

        autoAssignRecordBiz.assignWorkTOrg(param.getRoleId(), param.getOperatorId());

//        autoAssignRecordBiz.triggerAutoAssignReportOrderWithLock();

    }

    private void autoAssignOfFinishWork(AdminWorkOrderFlowParam.HandleParam param) {

        AdminWorkOrderFlowConst.handleType type  = AdminWorkOrderFlowConst.handleType.getByCode(param.getHandleType());
        //处理中、无需处理、处理完成、分配
        if (type == AdminWorkOrderFlowConst.handleType.FINISH
                || type == AdminWorkOrderFlowConst.handleType.NO_HANDLE
                || type == AdminWorkOrderFlowConst.handleType.HANDLE
                || type == AdminWorkOrderFlowConst.handleType.ALLOT) {
            autoAssignRecordBiz.assignWorkTUser(param.getUserId());
        }
    }

    private void sendNoticeMsg2finance(AdminWorkOrder adminWorkOrder, AdminWorkOrderFlow oldFlowOrder) {
        log.info("单笔退款消息的发送 adminWorkOrder:{} AdminWorkOrderFlow:{}", adminWorkOrder, oldFlowOrder);
        if (adminWorkOrder.getTaskType() != AdminWorkOrderConst.TaskType.SINGLE_REFUND_DELAY_48H_NOTICE.getCode()
                || (adminWorkOrder.getOrderStatus() != AdminWorkOrderConst.Status.HANDLE_SUCCESS.getCode()
                && adminWorkOrder.getOrderStatus() != AdminWorkOrderConst.Status.NO_NEED_HANDLE.getCode())) {
            return;
        }

        flowOrderCommonService.sendRefundFlowOrderHandleSuccMsg(adminWorkOrder, oldFlowOrder);

    }

    private Response validate(AdminWorkOrderFlowParam.HandleParam param) {

        if (param == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        if (param.getHandleType() == AdminWorkOrderFlowConst.handleType.ALLOT.getValue() && param.getRoleId() == 0) {
            return NewResponseUtil.makeError(AdminErrorCode.ALLOT_WITHOUT_OPERATERID);
        }

        AdminWorkOrder adminWorkOrder = this.adminWorkOrderBiz.selectById(param.getWorkOrderId());
        if (adminWorkOrder == null) {
            log.error("信息流转工单 工单:workOrderId:{} 找不到可用的工单", param.getWorkOrderId());
            return NewResponseUtil.makeFail("不能找到工单");
        }

        if (adminWorkOrder.getOrderStatus() == AdminWorkOrderConst.Status.NO_NEED_HANDLE.getCode()
                || adminWorkOrder.getOrderStatus() == AdminWorkOrderConst.Status.HANDLE_SUCCESS.getCode()) {
            log.info("信息流转工单 工单:workOrderId{}已经是终态（处理完成或不需要处理）不能在操作工单", param.getWorkOrderId());
            return NewResponseUtil.makeFail("工单已经是终态（处理完成或不需要处理）不能在操作工单");
        }

        return NewResponseUtil.makeSuccess("");
    }

    private void postHandleFlowOrder(AdminWorkOrderFlowParam.HandleParam param, AdminWorkOrder adminWorkOrder,
                                     AdminWorkOrderFlow oldFlowOrder,
                                     AdminWorkOrderFlowConst.OperateTypeEnum operateType) {

//        adminWorkOrder.setComment(param.getComment());

        // 是否修改了工单的优先级
        String modifyLevelComment = "";
        int oldLevel = adminWorkOrder.getLevel();
        if (param.getLevel() != adminWorkOrder.getLevel()) {
            adminWorkOrder.setLevel(param.getLevel());
            modifyLevelComment = "修改紧急程度：" + flowOrderCommonService.queryPriorityLevelDesc(param.getLevel()) + "  <br>";
        }

        String modifySecondCIdComment = "";
        AdminWorkOrderFlow orderFlow = this.selectByWorkOrderId(param.getWorkOrderId());
        long oldSecondClassifyId = orderFlow.getSecondClassifyId();
        // 是否修改了工单的分类
        if (param.getNewFirstClassifyId() != 0) {
            modifySecondCIdComment = "修改问题类型: " + this.getNewClassifyDesc(param.getNewFirstClassifyId(),param.getNewSecondClassifyId(),param.getNewThirdClassifyId()) + "  <br>";
            adminWorkOrderFlowDao.updateClassifyId(param.getWorkOrderId(), param.getNewFirstClassifyId()
                    , param.getNewSecondClassifyId(), param.getNewThirdClassifyId());
        }

        // 更新 admin_work_order
        adminWorkOrderBiz.update(adminWorkOrder);

        // 更新处理图片
        adminWorkOrderFlowDao.updateHandleImg(param.getWorkOrderId(), param.getHandleImg());
        // 保存问题标签
        adminWorkOrderFlowDao.updateFlowFollowTags(param.getWorkOrderId(), AdminWorkOrderFlow.joinComma(param.getFollowTagIds()));

        // 保存操作记录到 flow_record 表
        if (StringUtils.isNotEmpty(modifyLevelComment)) {
            saveRecord(oldFlowOrder, param.getUserId(), modifyLevelComment,
                    AdminWorkOrderFlowConst.OperateTypeEnum.MODIFY_ORDER_LEVEL.getCode(), oldLevel, oldSecondClassifyId);
        }

        // 修改问题的分类
        if (StringUtils.isNotEmpty(modifySecondCIdComment)) {
            saveRecord(oldFlowOrder, param.getUserId(), modifySecondCIdComment,
                    AdminWorkOrderFlowConst.OperateTypeEnum.MODIFY_ORDER_SECOND_ID.getCode(), adminWorkOrder.getLevel(), oldSecondClassifyId);
        }

        saveRecord(oldFlowOrder, param.getUserId(), param.getOperatorId(), param.getRoleId(), param.getComment(), operateType.getCode(), adminWorkOrder.getLevel(),
                param.getSecondClassifyId() != 0 ? param.getSecondClassifyId() : oldSecondClassifyId);
        // 同步到案例详情页的评论
        flowOrderCommonService.generateWorkFlowId(orderFlow.getCreateTime(), orderFlow.getId());
        syncCommentToCaseDetail(
                param.getWorkOrderId(),
                modifyLevelComment + modifySecondCIdComment + param.getComment(),
                "工单处理意见",
                param.getUserId(),
                orderFlow.getCaseId()
        );

        sendNoticeMsg2finance(adminWorkOrder, oldFlowOrder);

        autoAssignOfFinishWork(param);
    }

    private void syncCommentToCaseDetail(long workOrderId, String comment, String prefixComment, int userId,
                                         int caseId) {
        CrowdfundingInfo cf = crowdfundingDelegate.getFundingInfoById(caseId);
        if (cf != null) {
            workOrderRemarkService.addFlowComment(caseId, userId, workOrderId, prefixComment, comment);
        } else {
            log.warn("评论同步案例详情，不能找到案例 workOrderId：{}, caseId:{}", workOrderId, caseId);
        }
    }

    // 查看详情
    @Override
    public AdminWorkOrderFlowDetailView queryWorkOrderFlowDetailView(long id) {

        AdminWorkOrderFlowView flowView = this.selectOrderFlowViewById(id);
        if (flowView == null) {
            return null;
        }
        AdminWorkOrderFlowDetailView detailView = new AdminWorkOrderFlowDetailView();
        BeanUtils.copyProperties(flowView, detailView);

        WorkCreatorChannelEnum channelEnum = WorkCreatorChannelEnum.findByCode(detailView.getAssignChannel());
        detailView.setCreatorChannel(channelEnum == null ? StringUtils.EMPTY : channelEnum.getDesc());

        detailView.setFlowLogs(queryOperateHistory(flowView.getWorkOrderId(),flowView.getNewFirstClassifyId()
                ,flowView.getNewSecondClassifyId(),flowView.getNewThirdClassifyId()));

        List<AdminWorkOrderFlow> relateWorkOrder = getRelateWorkOrderId(flowView.getMobile(), flowView.getCaseId(),flowView.getNewFirstClassifyId(),flowView.getNewSecondClassifyId(),flowView.getNewThirdClassifyId());

        //查找所有的相关记录信息
        detailView.setNewFlowLogs(getNewWorkOrderFlowLogs(relateWorkOrder));

        //查找催单信息
        List<AdminWorkOrderFlowRemindRecord> remindRecordList = remindDao.listByFlowId(flowView.getId());
        detailView.setRemindNums(remindRecordList.size());
        detailView.setRemindRecords(remindRecordList);

        // 人名
        List<AdminUserAccountModel> accountModels = seaAccountClientV1.getUserAccountsByIds(Arrays.asList(detailView.getCreatorId())).getResult();
        if (CollectionUtils.isNotEmpty(accountModels)) {
            detailView.setCreatorName(accountModels.get(0).getName());
        } else {
            detailView.setCreatorName(SYSTEM_CREATE);
        }
        // 问题类型
        Map<Long, AdminWorkOrderClassifySettings> classifySettingMap = classifySettingsBiz.queryAllSettings().stream()
                .collect(Collectors.toMap(AdminWorkOrderClassifySettings::getId, Function.identity()));
        filterAndFillClassify(detailView, classifySettingMap);

        // 案例标题
        CrowdfundingInfo cf = crowdfundingDelegate.getFundingInfoById(detailView.getCaseId());
        if (cf != null) {
            detailView.setCaseTitle(cf.getTitle());
        }

        detailView.setWorkOrderFlowId(flowOrderCommonService.generateWorkFlowId(detailView.getCreateTime(), detailView.getId()));
        detailView.setMobileMask(maskUtil.buildByDecryptPhone(detailView.getMobile()));
        detailView.setMobile(null);

        // 返还款类型特殊处理
        this.fillMoneyBackExt(detailView);

        return detailView;
    }

    private void fillMoneyBackExt(AdminWorkOrderFlowDetailView detailView) {
        int newSecondClassifyId = detailView.getNewSecondClassifyId();
        int moneyBackClassifyId = applicationService.isProduction() ? 3201 : 4156;
        // 如果是返还款类型，需要填充返还款相关信息
        if (newSecondClassifyId == moneyBackClassifyId) {
            List<AdminWorkOrderMoneyBackExt> moneyBackExtList = adminWorkOrderMoneyBackExtBiz.findByWorkOrderId(detailView.getWorkOrderId());
            List<AdminWorkOrderMoneyBackExtVO> moneyBackExtVOList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(moneyBackExtList)) {
                List<Integer> caseIds = moneyBackExtList.stream().map(AdminWorkOrderMoneyBackExt::getCaseId).distinct().collect(Collectors.toList());
                Map<Integer, CrowdfundingInfo> crowdfundingInfoMap = crowdfundingDelegate.getMapByIds(caseIds);
                for (AdminWorkOrderMoneyBackExt moneyBackExt : moneyBackExtList) {
                    AdminWorkOrderMoneyBackExtVO moneyBackExtVO = new AdminWorkOrderMoneyBackExtVO();
                    moneyBackExtVO.setWorkOrderId(detailView.getWorkOrderId());
                    moneyBackExtVO.setCaseId(moneyBackExt.getCaseId());
                    moneyBackExtVO.setTitle(crowdfundingInfoMap.get(moneyBackExt.getCaseId()).getTitle());
                    moneyBackExtVO.setAmountStr(Optional.ofNullable(moneyBackExt.getAmount()).map(MoneyUtil::buildBalance).map(String::valueOf).orElse(""));
                    moneyBackExtVO.setMobileMask(maskUtil.buildByEncryptPhone(moneyBackExt.getEncryptMobile()));
                    moneyBackExtVOList.add(moneyBackExtVO);
                }
            } else {
                // 兼容历史数据
                AdminWorkOrderMoneyBackExtVO moneyBackExtVO = new AdminWorkOrderMoneyBackExtVO();
                moneyBackExtVO.setWorkOrderId(detailView.getWorkOrderId());
                moneyBackExtVO.setCaseId(detailView.getCaseId());
                moneyBackExtVO.setTitle(detailView.getCaseTitle());
                moneyBackExtVO.setAmountStr("");
                moneyBackExtVO.setMobileMask(detailView.getMobileMask());
                moneyBackExtVOList.add(moneyBackExtVO);
            }
            detailView.setMoneyBackExtList(moneyBackExtVOList);
        }
    }

    @Override
    public List<AdminWorkOrderFlowDetailView.WorkOrderFlowLog> queryWorkOrderFlowLog(long id) {
        AdminWorkOrderFlowView flowView = this.selectOrderFlowViewById(id);
        if (flowView == null) {
            return null;
        }
        return queryOperateHistory(flowView.getWorkOrderId(),flowView.getNewFirstClassifyId()
                ,flowView.getNewSecondClassifyId(),flowView.getNewThirdClassifyId());
    }


    @NotNull
    private List<AdminWorkOrderFlowDetailView.NewWorkOrderFlowLog> getNewWorkOrderFlowLogs(List<AdminWorkOrderFlow> relateWorkOrderFlows) {
        List<AdminWorkOrderFlowDetailView.NewWorkOrderFlowLog> newFlowLogs = Lists.newArrayList();
        if (CollectionUtils.isEmpty(relateWorkOrderFlows)) {
            return newFlowLogs;
        }

        List<AdminWorkOrder> adminWorkOrders = adminWorkOrderBiz.selectByIdList(relateWorkOrderFlows.stream().map(AdminWorkOrderFlow::getWorkOrderId).collect(Collectors.toList()));
        Map<Long, Integer> workId2CreatorId = adminWorkOrders.stream().collect(Collectors.toMap(AdminWorkOrder::getId, AdminWorkOrder::getCreatorId, (before, after) -> before));
        // 查找创建人 操作人的 组织/名称
        Map<Integer, List<List<AdminOrganization>>> uId2OrgMap = orgIdBiz.getOrgIdsByUserIds(Lists.newArrayList(workId2CreatorId.values()));
        Map<Integer, String> userId2NameMap = Maps.newHashMap();
        AuthRpcResponse<List<AdminUserAccountModel>> authRpcResponse = seaAccountClientV1.getUserAccountsByIds(Lists.newArrayList(workId2CreatorId.values()));
        if (authRpcResponse != null && CollectionUtils.isNotEmpty(authRpcResponse.getResult())) {
            userId2NameMap = authRpcResponse.getResult().stream().collect(Collectors.toMap(AdminUserAccountModel::getId, AdminUserAccountModel::getName));
        }

        for (AdminWorkOrderFlow orderFlow : relateWorkOrderFlows) {
            AdminWorkOrderFlowDetailView.NewWorkOrderFlowLog newWorkOrderFlowLog = new AdminWorkOrderFlowDetailView.NewWorkOrderFlowLog();
            newWorkOrderFlowLog.setCreateTime(orderFlow.getCreateTime());
            newWorkOrderFlowLog.setFlowId(orderFlow.getId());
            newWorkOrderFlowLog.setWorkFlowId(flowOrderCommonService.generateWorkFlowId(orderFlow.getCreateTime(), orderFlow.getId()));
            newWorkOrderFlowLog.setLogs(queryOperateHistory(orderFlow.getWorkOrderId(),orderFlow.getNewFirstClassifyId()
                    ,orderFlow.getNewSecondClassifyId(),orderFlow.getNewThirdClassifyId()));
            int userId = workId2CreatorId.getOrDefault(orderFlow.getWorkOrderId(), 0);
            //创建人名称
            newWorkOrderFlowLog.setCreatorName(userId2NameMap.get(userId));
            //创建人组织关系
            newWorkOrderFlowLog.setCreatorOrg(getRoleName(userId, uId2OrgMap));
            newFlowLogs.add(newWorkOrderFlowLog);
        }
        return newFlowLogs;
    }


    private List<AdminWorkOrderFlow> getRelateWorkOrderId(String mobile, Integer caseId, int newFirstClassifyId, int newSecondClassifyId, int newThirdClassifyId) {
        List<AdminWorkOrderFlow> relateFlowViewList = Lists.newArrayList();
        if (StringUtils.isNotBlank(mobile)) {
            relateFlowViewList.addAll(adminWorkOrderFlowDao.selectByMobileAndClassifyId(oldShuidiCipher.aesEncrypt(mobile), newFirstClassifyId, newSecondClassifyId, newThirdClassifyId));
        }
        if (caseId != null && caseId > 0) {
            relateFlowViewList.addAll(adminWorkOrderFlowDao.selectByCaseIdAndClassifyId(caseId, newFirstClassifyId, newSecondClassifyId, newThirdClassifyId));
        }
        //去重
        List<AdminWorkOrderFlow> dstRelateFlowViewList = relateFlowViewList.stream()
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(AdminWorkOrderFlow::getId))), ArrayList::new));

        Ordering<AdminWorkOrderFlow> ordering = Ordering.natural().reverse().onResultOf(AdminWorkOrderFlow::getId);
        return dstRelateFlowViewList.stream()
                .sorted(ordering)
                .collect(Collectors.toList());
    }


    // 查找操作记录
    private List<AdminWorkOrderFlowDetailView.WorkOrderFlowLog> queryOperateHistory(long workOrderId,int newFirstClassifyId
            ,int newSecondClassifyId,int newThirdClassifyId) {
        String classifyDesc = this.getNewClassifyDesc(newFirstClassifyId,newSecondClassifyId,newThirdClassifyId);
        List<AdminWorkOrderFlowRecord> records = adminWorkOrderFlowRecordBiz.selectAllByWorkOrderId(workOrderId,
                AdminWorkOrderFlowConst.OperateTypeEnum.getOperateHisCode());

        List<AdminWorkOrderFlowDetailView.WorkOrderFlowLog> flowLogs = new ArrayList<>();
        if (CollectionUtils.isEmpty(records)) {
            return new ArrayList<>();
        }

        List<Integer> userIds = Lists.newArrayList();
        records.forEach(item -> {
            if (item.getOperatorId() != 0) {
                userIds.add(item.getOperatorId());
            }
        });
        Map<Integer, List<List<AdminOrganization>>> uId2OrgMap = orgIdBiz.getOrgIdsByUserIds(userIds);

        for (AdminWorkOrderFlowRecord record : records) {
            AdminWorkOrderFlowDetailView.WorkOrderFlowLog flowLog = new AdminWorkOrderFlowDetailView.WorkOrderFlowLog();
            // 备注
            flowLog.setComment(record.getComment());
            // 操作
            flowLog.setOperateDesc(AdminWorkOrderFlowConst.OperateTypeEnum.getEnumByCode(record.getOperateType()).getDesc());

            // 操作人姓名 和 组织名
            AdminUserAccountModel user = seaAccountClientV1.getValidUserAccountById(record.getOperatorId()).getResult();
            flowLog.setOperatorName(user == null ? SYSTEM_CREATE : user.getName());
            if (record.getOperatorId() != 0) {
                flowLog.setRoleName(getRoleName(record.getOperatorId(), uId2OrgMap));
            } else {
                flowLog.setRoleName(orgIdBiz.joinOrgDescByNextId(record.getProblemType()));
            }

            flowLog.setLevelDesc(flowOrderCommonService.queryPriorityLevelDesc(record.getLevel()));
            flowLog.setAllText(classifyDesc);
            // 处理时间
            flowLog.setOperateTime(DateUtil.getDate2LStr(record.getCreateTime()));
            flowLogs.add(flowLog);
        }

        return flowLogs.stream().sorted(Comparator.comparing(AdminWorkOrderFlowDetailView
                .WorkOrderFlowLog::getOperateTime).reversed()).collect(Collectors.toList());
    }


    @Override
    public PageInfo<AdminWorkOrderFlowView> selectHasHandleWorkOrderByParam(AdminWorkOrderFlowParam.SearchParam searchParam) {

        PageInfo result = new PageInfo();

        // 已办工单
        if (searchParam.getWorkOrderStatus() == null) {
            searchParam.setWorkOrderStatusList(Arrays.asList(AdminWorkOrderConst.Status.HANDLING.getCode(),
                    AdminWorkOrderConst.Status.SHUTDOWN.getCode(),
                    AdminWorkOrderConst.Status.NO_NEED_HANDLE.getCode(),
                    AdminWorkOrderConst.Status.HANDLE_SUCCESS.getCode()
            ));
        } else {
            searchParam.setWorkOrderStatusList(Arrays.asList(searchParam.getWorkOrderStatus()));
        }

        searchParam.setRecordOperatorTypes(AdminWorkOrderFlowConst.OperateTypeEnum.getHasHandledOperateCode());
        preHandleSearchParam(searchParam);
        if(!Strings.isNullOrEmpty(searchParam.getMobile())){
            searchParam.setEncryptMobile(oldShuidiCipher.aesEncrypt(searchParam.getMobile()));
        }
        Stopwatch stopwatch = Stopwatch.createStarted();
        int count = adminWorkOrderFlowRepository.countFinishFlowByParam(searchParam);
        stopwatch.stop();
        log.info("selectHasHandleWorkOrderByParam countFinishFlowByParam data:{}", count);
        log.info("selectHasHandleWorkOrderByParam countFinishFlowByParam 耗时:{}", stopwatch);

        if (count == 0) {
            return result;
        }

        searchParam.setLimitSize((searchParam.getPageNum() - 1) * searchParam.getPageSize());
        searchParam.setOffset(searchParam.getPageSize());
        Stopwatch stopwatch1 = Stopwatch.createStarted();
        List<AdminWorkOrderFlowView> flowViews = adminWorkOrderFlowRepository.selectFinishFlowByParam(searchParam);
        stopwatch1.stop();
        log.info("selectHasHandleWorkOrderByParam selectFinishFlowByParam data:{}", JSON.toJSONString(flowViews));
        log.info("selectHasHandleWorkOrderByParam selectFinishFlowByParam 耗时:{}", stopwatch1);
        if (flowViews != null && flowViews.size() > 0) {
            for (AdminWorkOrderFlowView flow : flowViews) {
                if (flow != null && !Strings.isNullOrEmpty(flow.getEncryptMobile())) {
                    flow.setMobile(shuidiCipher.decrypt(flow.getEncryptMobile()));
                }
                flow.setFollowTagIds(AdminWorkOrderFlow.splitterFollowTagViews(flow.getFollowTags()));
                //新问题分类拼接
                int newFirstClassifyId = flow.getNewFirstClassifyId();
                int newSecondClassifyId = flow.getNewSecondClassifyId();
                int newThirdClassifyId = flow.getNewThirdClassifyId();
                flow.setNewClassifyDesc(this.getNewClassifyDesc(newFirstClassifyId,newSecondClassifyId,newThirdClassifyId));
            }
        }

        result.setTotal(count);
        result.setPageNum(searchParam.getPageNum());
        result.setPageSize(searchParam.getPageSize());
        result.setList(flowViews);

        postHandleResult(result);

        return result;
    }

    // 我的工单
    @Override
    public PageInfo<AdminWorkOrderFlowView> selectCreateWorkOrderByParam(AdminWorkOrderFlowParam.SearchParam searchParam) {
        PageInfo result = new PageInfo();
        preHandleSearchParam(searchParam);
        if(!Strings.isNullOrEmpty(searchParam.getMobile())){
            searchParam.setEncryptMobile(oldShuidiCipher.aesEncrypt(searchParam.getMobile()));
        }
        int count = adminWorkOrderFlowDao.countCreateFlowByParam(searchParam);
        if (count == 0) {
            return result;
        }

        searchParam.setLimitSize((searchParam.getPageNum() - 1) * searchParam.getPageSize());
        searchParam.setOffset(searchParam.getPageSize());

        List<AdminWorkOrderFlowView> flowViews = adminWorkOrderFlowDao.selectCreateFlowByParam(searchParam);
        if (flowViews != null && flowViews.size() > 0) {
            for (AdminWorkOrderFlowView flow : flowViews) {
                if (Objects.isNull(flow)) {
                    continue;
                }
                if (!Strings.isNullOrEmpty(flow.getEncryptMobile())) {
                    flow.setMobileMask(maskUtil.buildByEncryptPhone(flow.getEncryptMobile()));
                }
                //新问题分类拼接
                int newFirstClassifyId = flow.getNewFirstClassifyId();
                int newSecondClassifyId = flow.getNewSecondClassifyId();
                int newThirdClassifyId = flow.getNewThirdClassifyId();
                flow.setNewClassifyDesc(this.getNewClassifyDesc(newFirstClassifyId, newSecondClassifyId, newThirdClassifyId));
            }
        }
        result.setTotal(count);
        result.setPageNum(searchParam.getPageNum());
        result.setPageSize(searchParam.getPageSize());
        result.setList(flowViews);

        postHandleResult(result);

        return result;
    }

    // 待办工单 Pair<待办工单信息，待办工单催单次数>
    @Override
    public Pair<PageInfo<AdminWorkOrderFlowView>, Integer> selectWaitForHandleFlowByParam(AdminWorkOrderFlowParam.SearchParam searchParam) {

        PageInfo result = new PageInfo();

        // 工单的状态是 创建 或 处理中
        if (searchParam.getWorkOrderStatus() == null) {
            searchParam.setWorkOrderStatusList(Arrays.asList(AdminWorkOrderConst.Status.HANDLING.getCode(),
                    AdminWorkOrderConst.Status.CREATED.getCode()));
        } else {
            searchParam.setWorkOrderStatusList(Collections.singletonList(searchParam.getWorkOrderStatus()));
        }
        preHandleSearchParam(searchParam);
        if(!Strings.isNullOrEmpty(searchParam.getMobile())){
            searchParam.setEncryptMobile(oldShuidiCipher.aesEncrypt(searchParam.getMobile()));
        }
        searchParam.setCreateTime(getDate());

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<Integer> finishFlowIds = adminWorkOrderFlowDao.selectWaitForHandleFlowIdsByParam(searchParam);
        stopWatch.stop();
        log.info("selectWaitForHandleFlowByParamDrawCashShutdown CountDownLatch await end...use {}Seconds", stopWatch.getTotalTimeSeconds());

        //排序
        List<Integer> sortedFlowIds = Lists.newArrayList();
        Multimap<Integer, AdminWorkOrderFlowRemindRecord> remindRecordGroupByFlowId = null;
        //用户未处理的催单总数
        DateTime startOfDay = new DateTime().withTimeAtStartOfDay();
        int remindTotalNums = remindDao.countByUserId(searchParam.getOperatorId(), startOfDay.toDate());
        //分批查询,避免慢查
        List<AdminWorkOrderFlowRemindRecord> remindRecordList = Lists.newArrayList();
        Consumer<List<Integer>> consumer = item -> remindRecordList.addAll(remindDao.listByFlowIds(item));
        Lists.partition(finishFlowIds, 200)
                .forEach(consumer);

        if (CollectionUtils.isEmpty(remindRecordList)) {
            sortedFlowIds = finishFlowIds;
        } else {
            remindRecordGroupByFlowId = Multimaps.index(remindRecordList, AdminWorkOrderFlowRemindRecord::getFlowId);
            sortedFlowIds = sortedUnHandleFlowId(finishFlowIds, remindRecordGroupByFlowId);
        }
        List<Integer> curPageIds = flowOrderCommonService.generateCurPageIds(sortedFlowIds, searchParam.getPageNum(), searchParam.getPageSize());

        if (CollectionUtils.isEmpty(curPageIds)) {
            return Pair.of(result, remindTotalNums);
        }

        List<AdminWorkOrderFlowView> flowViews = adminWorkOrderFlowDao.selectWorkFlowByFlowIds(curPageIds);

        Ordering<AdminWorkOrderFlowView> ordering = Ordering.explicit(curPageIds.stream().map(Long::valueOf).collect(Collectors.toList())).onResultOf(AdminWorkOrderFlowView::getId);
        List<AdminWorkOrderFlowView> resortFlowViews = flowViews.stream()
                .sorted(ordering)
                .collect(Collectors.toList());

        if (resortFlowViews.size() > 0) {
            for (AdminWorkOrderFlowView flow : resortFlowViews) {
                if (flow != null && !Strings.isNullOrEmpty(flow.getEncryptMobile())) {
                    flow.setMobile(shuidiCipher.decrypt(flow.getEncryptMobile()));
                }
                if (remindRecordGroupByFlowId != null && flow != null && remindRecordGroupByFlowId.get((int)flow.getId()) != null){
                    int remindSize = remindRecordGroupByFlowId.get((int)flow.getId()).size();
                    flow.setRemindNums(remindSize);
                    flow.setRemindHighlight(remindSize > 2);
                }
                flow.setFollowTagIds(AdminWorkOrderFlow.splitterFollowTagViews(flow.getFollowTags()));
                //新问题分类拼接
                int newFirstClassifyId = flow.getNewFirstClassifyId();
                int newSecondClassifyId = flow.getNewSecondClassifyId();
                int newThirdClassifyId = flow.getNewThirdClassifyId();
                flow.setNewClassifyDesc(this.getNewClassifyDesc(newFirstClassifyId,newSecondClassifyId,newThirdClassifyId));
            }
        }
        result.setTotal(finishFlowIds.size());
        result.setPageNum(searchParam.getPageNum());
        result.setPageSize(searchParam.getPageSize());
        resortFlowViews = this.sortFlowViews(resortFlowViews, searchParam.getWorkOrderStatus());
        result.setList(resortFlowViews);

        postHandleResult(result);

        return Pair.of(result, remindTotalNums);
    }


    @Data
    static class FlowAndRemindCount {
        int flowId;
        int remindTimes;
        FlowAndRemindCount(int flowId, int remindTimes) {
            this.flowId = flowId;
            this.remindTimes = remindTimes;
        }
    }

    @Override
    public String getNewClassifyDesc(int newFirstClassifyId,int newSecondClassifyId,int newThirdClassifyId){
        List<String> names = Lists.newArrayList();
        if (newFirstClassifyId > 0) {
            CascadeRuleFields cascadeRuleFields = externalWorkOrderApiBiz.fieldsCascade(newFirstClassifyId);
            if (Objects.nonNull(cascadeRuleFields) && Objects.nonNull(cascadeRuleFields.getName())) {
                names.add(cascadeRuleFields.getName());
            }
        }
        if (newSecondClassifyId > 0) {
            CascadeRuleFields cascadeRuleFields = externalWorkOrderApiBiz.fieldsCascade(newSecondClassifyId);
            if (Objects.nonNull(cascadeRuleFields) && Objects.nonNull(cascadeRuleFields.getName())) {
                names.add(cascadeRuleFields.getName());
            }
        }
        if (newThirdClassifyId > 0) {
            CascadeRuleFields cascadeRuleFields = externalWorkOrderApiBiz.fieldsCascade(newThirdClassifyId);
            if (Objects.nonNull(cascadeRuleFields) && Objects.nonNull(cascadeRuleFields.getName())) {
                names.add(cascadeRuleFields.getName());
            }
        }
        return CollectionUtils.isNotEmpty(names) ? StringUtils.join(names, "/") : StringUtils.EMPTY;
    }

    private List<Integer> sortedUnHandleFlowId(List<Integer> allUnHandleFlowIds, Multimap<Integer, AdminWorkOrderFlowRemindRecord> remindRecordGroupByFlowId) {
        List<FlowAndRemindCount> flowAndRemindCountList = Lists.newArrayList();
        for (Integer unHandleFlowId : allUnHandleFlowIds) {
            Collection<AdminWorkOrderFlowRemindRecord> remindRecords = remindRecordGroupByFlowId.get(unHandleFlowId);
            int remindCount = CollectionUtils.isEmpty(remindRecords) ? 0 : remindRecords.size();
            flowAndRemindCountList.add(new FlowAndRemindCount(unHandleFlowId, remindCount));
        }
        Ordering<FlowAndRemindCount> orderByRemindCount = Ordering.natural().reverse().onResultOf(FlowAndRemindCount::getRemindTimes);
        Ordering<FlowAndRemindCount> orderByFlowId = Ordering.natural().onResultOf(FlowAndRemindCount::getFlowId);
        return flowAndRemindCountList.stream()
                .sorted(Ordering.compound(Lists.newArrayList(orderByRemindCount, orderByFlowId)))
                .map(FlowAndRemindCount::getFlowId)
                .collect(Collectors.toList());
    }

    @Override
    public CaseTitleAndMobile getTitleAndMobileByCaseId(int caseId) {
        CrowdfundingInfo cf = crowdfundingDelegate.getFundingInfoById(caseId);

        if (cf == null) {
            return null;
        }
        return new CaseTitleAndMobile(cf.getTitle(), getOriginatorIMobile(cf));
    }

    private String getOriginatorIMobile(CrowdfundingInfo cfInfo) {
        UserInfoModel userInfoModel = this.userInfoServiceBiz.getUserInfoByUserId(cfInfo.getUserId());

        if (userInfoModel == null || StringUtils.isEmpty(userInfoModel.getCryptoMobile())) {
            log.info("caseId:{}, userId:{} 不能在accountService找到发起人手机号信息", cfInfo.getId(),
                    cfInfo.getUserId());
            return "";
        }
        return shuidiCipher.decrypt(userInfoModel.getCryptoMobile());
    }

    @Override
    public List<SimilarFlowOrderObj> querySimilarOrderByCaseIdAndSettingId(int caseId, int secondClassifyId) {

        List<SimilarFlowOrderObj> result = Lists.newArrayList();

        if (caseId != 0 && secondClassifyId != 0) {
            List<AdminWorkOrderFlowView> flowViews = adminWorkOrderFlowDao.selectByCaseIdAndSecondId(caseId, secondClassifyId);

            for (AdminWorkOrderFlowView view : flowViews) {
                SimilarFlowOrderObj obj = new SimilarFlowOrderObj(flowOrderCommonService.generateWorkFlowId(view.getCreateTime(), view.getId()), view.getId());
                result.add(obj);
            }
        }

        return result;
    }

    @Override
    public AdminWorkOrderFlowView buildFlowViewFromWorkOrder(AdminWorkOrderFlowBiz.WorkOrderFlow orderFlow) {

        log.info("创建信息传递工单， view:{}", orderFlow);
        if (orderFlow == null || StringUtils.isEmpty(orderFlow.getProblemTextDesc())) {
            return null;
        }

        AdminWorkOrderFlowView flowView = new AdminWorkOrderFlowView();

        if (CollectionUtils.isNotEmpty(orderFlow.getProblemClassifys())) {
            // 问题分类
            Long secondId = classifySettingsBiz.querySecondClassifyIdByText(orderFlow.getProblemClassifys());
            if (secondId == null) {
                log.error("创建信息传递工单，不能找到问题分类设置. msg：{}", orderFlow.getProblemClassifys());
                return null;
            }
            flowView.setSecondClassifyId(secondId);
        } else {
            flowView.setNewFirstClassifyId(orderFlow.getNewFirstClassifyId());
            flowView.setNewSecondClassifyId(orderFlow.getNewSecondClassifyId());
            flowView.setNewThirdClassifyId(orderFlow.getNewThirdClassifyId());
        }

        Integer lowestId = orgIdBiz.queryLowestIdByText(orderFlow.getHandleOrgs());
        if (lowestId == null) {
            log.error("创建信息传递工单，不能找到所分配的组织. msg：{}", orderFlow.getHandleOrgs());

            return null;
        }
        flowView.setProblemType(lowestId);

        // 案例标题
        flowView.setCaseId(orderFlow.getCaseId());
        CaseTitleAndMobile titleAndMobile = getTitleAndMobileByCaseId(orderFlow.getCaseId());
        if (titleAndMobile != null) {
            flowView.setCaseTitle(titleAndMobile.getTitle());
        }

        if (StringUtils.isEmpty(orderFlow.getMobile()) && titleAndMobile != null) {
            flowView.setMobile(titleAndMobile.getMobile());
        } else {
            flowView.setMobile(StringUtils.trimToEmpty(orderFlow.getMobile()));
        }

        flowView.setCreatorId(orderFlow.getCreateId());
        flowView.setProblemContent(StringUtils.trimToEmpty(orderFlow.getProblemTextDesc()));
        flowView.setProblemImg(StringUtils.trimToEmpty(orderFlow.getProblemPicDesc()));
        flowView.setHandleImg("");
        flowView.setUserIdentity(AdminWorkOrderFlowView.WorkOrderFlowUserIdentity.DEFAULT.getCode());
        flowView.setLevel(orderFlow.getPriorityLevel());

        flowView.setTaskType(orderFlow.getTaskType());

        // 组织和问题分类
        return flowView;
    }

    @Override
    public AdminWorkOrderFlowView selectOrderFlowViewById(long flowId) {
        AdminWorkOrderFlowView view = adminWorkOrderFlowDao.selectOrderFlowViewById(flowId);
        if (view != null ) {
            if (!Strings.isNullOrEmpty(view.getEncryptMobile())) {
                view.setMobile(shuidiCipher.decrypt(view.getEncryptMobile()));
            }
            view.setFollowTagIds(AdminWorkOrderFlow.splitterFollowTagViews(view.getFollowTags()));

        }

        return view;
    }


    private List<List<String>> queryOrgNameList(int userId) {
        Map<Integer, List<List<AdminOrganization>>> userOrgans = orgIdBiz.getOrgIdsByUserIds(Lists.newArrayList(userId));

        if (MapUtils.isEmpty(userOrgans) || CollectionUtils.isEmpty(userOrgans.get(userId))) {
            return null;
        }

        List<List<String>> orgNameComplexList = Lists.newArrayList();
        List<List<AdminOrganization>> orgComplexList = userOrgans.get(userId);
        for (List<AdminOrganization> orgList : orgComplexList) {
            List<String> orgNames = Lists.newArrayList();
            for (AdminOrganization org : orgList) {
                orgNames.add(org.getName());
            }
            orgNameComplexList.add(orgNames);
        }

        return orgNameComplexList;
    }

    @Override
    public AdminWorkOrderFlowView buildFlowViewFromWorkOrder(WorkOrderFlowParam orderFlow) {

        log.info("创建信息传递工单， view:{}", orderFlow);
        if (orderFlow == null || StringUtils.isEmpty(orderFlow.getProblemTextDesc())) {
            return null;
        }

        AdminWorkOrderFlowView flowView = new AdminWorkOrderFlowView();

        // 问题分类
        Long secondId = classifySettingsBiz.querySecondClassifyIdByText(orderFlow.getProblemClassifys());
        if (secondId == null) {
            log.error("创建信息传递工单，不能找到问题分类设置. msg：{}", orderFlow.getProblemClassifys());
            return null;
        }
        flowView.setSecondClassifyId(secondId);

        Integer lowestId = orgIdBiz.queryLowestIdByText(orderFlow.getHandleOrgs());
        if (lowestId == null) {
            log.error("创建信息传递工单，不能找到所分配的组织. msg：{}", orderFlow.getHandleOrgs());

            return null;
        }
        flowView.setProblemType(lowestId);

        // 案例标题
        flowView.setCaseId(orderFlow.getCaseId());
        CaseTitleAndMobile titleAndMobile = getTitleAndMobileByCaseId(orderFlow.getCaseId());
        if (titleAndMobile != null) {
            flowView.setCaseTitle(titleAndMobile.getTitle());
        }

        if (StringUtils.isEmpty(orderFlow.getMobile()) && titleAndMobile != null) {
            flowView.setMobile(titleAndMobile.getMobile());
        } else {
            flowView.setMobile(StringUtils.trimToEmpty(orderFlow.getMobile()));
        }

        flowView.setCreatorId(orderFlow.getCreateId());
        flowView.setProblemContent(StringUtils.trimToEmpty(orderFlow.getProblemTextDesc()));
        flowView.setProblemImg(StringUtils.trimToEmpty(orderFlow.getProblemPic()));
        flowView.setHandleImg("");
        flowView.setUserIdentity(orderFlow.getUserIdentity());
        flowView.setLevel(orderFlow.getPriorityLevel());
        flowView.setTaskType(orderFlow.getTaskType());

        // 组织和问题分类
        return flowView;
    }

    @Override
    public List<AdminWorkOrderFlowView> selectByMobileAndTaskType(String mobile, int taskType) {
        if(!Strings.isNullOrEmpty(mobile)){
            mobile=oldShuidiCipher.aesEncrypt(mobile);
        }
        List<AdminWorkOrderFlowView> views = adminWorkOrderFlowDao.selectByMobileAndTaskType(mobile, taskType);
        if (views != null && views.size() > 0) {
            for (AdminWorkOrderFlow flow : views) {
                if (flow != null && !Strings.isNullOrEmpty(flow.getEncryptMobile())) {
                    flow.setMobile(shuidiCipher.decrypt(flow.getEncryptMobile()));
                }
            }
        }
        return views;
    }

    @Override
    public AdminWorkOrderFlowView selectByCaseIdAndTaskType(int caseId, int taskType) {
        AdminWorkOrderFlowView flow = adminWorkOrderFlowDao.selectByCaseIdAndTaskType(caseId, taskType);
        if (flow != null && !Strings.isNullOrEmpty(flow.getEncryptMobile())) {
            flow.setMobile(shuidiCipher.decrypt(flow.getEncryptMobile()));
        }
        return flow;
    }

    @Override
    public List<String> listCreatorChannel(long userId, int problemId) {
        List<String> channelInfos = Lists.newArrayList(WorkCreatorChannelEnum.NORMAL.getDesc());
        //获取问题类型信息
        AdminWorkOrderClassifySettings classifySettings = classifySettingsBiz.selectClassifySettingsById(problemId);
        if (classifySettings == null) {
            log.warn("not find AdminWorkOrderClassifyInfo!userId:{}, classifyId:{}", userId, problemId);
            return channelInfos;
        }
        //起相关为线索流转至1v1服务
        boolean secondClassifySettings = CLEW_SECOND_CLASSIFY_SETTINGS.equals(StringUtils.trimToNull(classifySettings.getAllText()));

        //获取组织关系
        List<List<String>> complexOrgName = queryOrgNameList((int) userId);
        if (CollectionUtils.isEmpty(complexOrgName)) {
            return channelInfos;
        }

        if (complexOrgName.size() > 1) {
            log.warn("same userId:{} belong more than one org:{}!", userId, JSON.toJSONString(complexOrgName));
            return channelInfos;
        }

        List<String> singleOrgName = Optional.ofNullable(complexOrgName.get(0)).orElse(Lists.newArrayList());
        //判断是否是水滴筹-运营-客服 组织下
        boolean otherTeam = singleOrgName.contains(CLEW_ORG_FIRST_NAME)
                && singleOrgName.contains(CLEW_ORG_SENCOND_NAME)
                && singleOrgName.contains(CLEW_ORG_THIRD_KEFU);
        otherTeam = otherTeam && singleOrgName.contains(THIRD_ORG_CALL_IN)
                || singleOrgName.contains(THIRD_ORG_ERXIAN)
                || singleOrgName.contains(THIRD_ORG_ONLINE);

        if (otherTeam && singleOrgName.contains(THIRD_ORG_CALL_IN)) {
            channelInfos = Lists.newArrayList(WorkCreatorChannelEnum.CALL_IN.getDesc());
        } else if (otherTeam && singleOrgName.contains(THIRD_ORG_ONLINE)) {
            channelInfos = Lists.newArrayList(WorkCreatorChannelEnum.ONLINE.getDesc());
        } else if (otherTeam && singleOrgName.contains(THIRD_ORG_ERXIAN)) {
            if (secondClassifySettings) {
                channelInfos = Lists.newArrayList(WorkCreatorChannelEnum.SHUIDI_BAO.getDesc(), WorkCreatorChannelEnum.SHUIDI_HUZHU.getDesc());
            } else {
                channelInfos = Lists.newArrayList(WorkCreatorChannelEnum.NORMAL.getDesc());
            }
        } else {
            channelInfos = Lists.newArrayList(WorkCreatorChannelEnum.NORMAL.getDesc());
        }
        return channelInfos;
    }

    @Override
    public boolean addRemind(AdminWorkOrderFlowRemindRecord remindRecord, int adminUserId) {
        //补全remindRecord
        AuthRpcResponse<AdminUserAccountModel> account = seaAccountClientV1.getValidUserAccountById(adminUserId);
        Optional<AdminUserAccountModel> accountModel = Optional.ofNullable(account).map(AuthRpcResponse::getResult);
        String remindOperatorName = accountModel.map(AdminUserAccountModel::getName).orElse("");
        remindRecord.setRemindOperatorId(adminUserId);
        remindRecord.setRemindOperatorName(remindOperatorName);
        remindRecord.setRemindOperatorOrg(accountDelegate.getOrg(adminUserId));

        remindRecord.setComment(Optional.ofNullable(remindRecord.getComment()).orElse(""));
        if (StringUtils.isBlank(remindRecord.getCurrentOperatorName()) ||
                StringUtils.isBlank(remindRecord.getOperatorRoleName())) {
            AuthRpcResponse<AdminUserAccountModel> currentOperator = seaAccountClientV1.getValidUserAccountById(remindRecord.getCurrentOperatorId());
            remindRecord.setCurrentOperatorName(Optional.ofNullable(currentOperator).map(AuthRpcResponse::getResult).map(AdminUserAccountModel::getName).orElse(""));
            remindRecord.setOperatorRoleName(accountDelegate.getOrg(adminUserId));
        }
        boolean affect = remindDao.add(remindRecord) >= 1;
        if (affect) {
            //提升紧急程度
            AdminWorkOrderFlow flow = adminWorkOrderFlowDao.selectOrderFlowById(remindRecord.getFlowId());
            if (flow == null) {
                log.warn("查不到对应的信息工单,remindRecord:{}", JSON.toJSONString(remindRecord));
                return affect;
            }
            long workOrderId = flow.getWorkOrderId();
            AdminWorkOrder adminWorkOrder = adminWorkOrderBiz.selectById(workOrderId);
            if (adminWorkOrder == null) {
                log.warn("查不到对应的工单信息,workOrderId:{}", workOrderId);
                return affect;
            }
            int level = adminWorkOrder.getLevel();
            int calculateLevelWhenRemind = level < 3 ? level + 1 : level;
            adminWorkOrderBiz.updateLevel(workOrderId, calculateLevelWhenRemind);

            //查询信息传递工单
            syncCommentToCaseDetail(
                    workOrderId,
                    remindRecord.getComment(),
                    "【催单】工单编号" + remindRecord.getWorkOrderFlowId(),
                    adminUserId,
                    flow.getCaseId()
            );
        }
        return affect;
    }


    @Override
    public PageInfo<AdminWorkOrderFlowView> listUnHandleByCaseIdOrMobile(Integer caseId, String mobile, int currrent, int pageSize) {
        List<Integer> unHandlerStatus = Lists.newArrayList(CREATED.getCode(), HANDLING.getCode());
        PageHelper.startPage(currrent, pageSize);
        List<AdminWorkOrderFlowView> flowViews = adminWorkOrderFlowDao.selectWorkFlowByCaseIdOrMobile(oldShuidiCipher.aesEncrypt(mobile), caseId, unHandlerStatus);
        for (AdminWorkOrderFlowView flowView : flowViews) {
            flowView.setMobile(StringUtils.EMPTY);
        }
        PageInfo pageInfo = new PageInfo(flowViews);
        postHandleResult(pageInfo);
        return pageInfo;
    }


    @Override
    public void assignToGivenOrg(String generateWorkFlowId, String appendProblemDesc, List<String> orgNames) {
        log.info("工单流转到其它组织: {}-{}-{}", generateWorkFlowId, appendProblemDesc, orgNames);
        int flowId = flowOrderCommonService.decodeFromFlowIdString(generateWorkFlowId);
        AdminWorkOrderFlowView flowView = this.selectOrderFlowViewById(flowId);

        Integer lowestId = orgIdBiz.queryLowestIdByText(orgNames);
        if (canAssignOrg(generateWorkFlowId, flowView, lowestId, orgNames)) {
            this.updateTaskType(flowView.getWorkOrderId(), lowestId);
            adminWorkOrderBiz.updateOperatorId(0, flowView.getWorkOrderId());

            if (StringUtils.isNotBlank(appendProblemDesc)) {
                adminWorkOrderFlowDao.updateProblemContent(flowView.getWorkOrderId(),
                        flowView.getProblemContent() + AdminWorkOrderFlowBiz.ORG_NAME_SPLIT + appendProblemDesc);
            }
            AdminWorkOrderFlow flow = adminWorkOrderFlowDao.selectOrderFlowById(flowView.getId());
            if (flow != null && !Strings.isNullOrEmpty(flow.getEncryptMobile())) {
                flow.setMobile(shuidiCipher.decrypt(flow.getEncryptMobile()));
            }
            saveRecord(flow, 0, 0, lowestId,
                    "系统自动分配: " + Joiner.on(AdminWorkOrderFlowBiz.ORG_NAME_SPLIT).join(orgNames),
                    AdminWorkOrderFlowConst.OperateTypeEnum.ALLOT_WORK_FLOW.getCode(),
                    flowView.getLevel(), flowView.getSecondClassifyId());
        }

    }

    private boolean canAssignOrg(String generateWorkFlowId, AdminWorkOrderFlowView flowView, Integer lowestId, List<String> orgNames) {

        if (lowestId == null) {
            log.error("流转工单 orgName:{} 不能找到有效的组织id", orgNames);
            return false;
        }

        if (flowView == null) {
            log.error("流转工单generateWorkFlowId:{} 不能找到flow-work-order", generateWorkFlowId);
            return false;
        }

        if (flowView.getWorkOrderStatus() == AdminWorkOrderConst.Status.NO_NEED_HANDLE.getCode()
                || flowView.getWorkOrderStatus() == AdminWorkOrderConst.Status.HANDLE_SUCCESS.getCode()
                || flowView.getWorkOrderStatus() == AdminWorkOrderConst.Status.SHUTDOWN.getCode()) {
            log.info("流转工单generateWorkFlowId:{} 已处理完成orderStatus:{}", generateWorkFlowId, flowView.getWorkOrderStatus());
            return false;
        }

        if (flowView.getProblemType() == lowestId) {
            log.info("流转工单generateWorkFlowId:{} 当前处理已是需要流转的组织，不做流转.orgid:{}", generateWorkFlowId, lowestId);
            return false;
        }

        return true;
    }


    private List<FlowAndRemindCount> getRemindCount(List<Integer> flowIds){
        List<FlowAndRemindCount> remindCounts = Lists.newArrayList();
        List<AdminWorkOrderFlowRemindRecord> remindRecords = remindDao.listByFlowIds(flowIds);
        Multimap<Integer, AdminWorkOrderFlowRemindRecord> index = Multimaps.index(remindRecords, AdminWorkOrderFlowRemindRecord::getFlowId);
        for (Integer flowId : index.keySet()) {
            remindCounts.add(new FlowAndRemindCount(flowId, index.get(flowId).size()));
        }
        return remindCounts;
    }

    // 展示排序
    public  List<AdminWorkOrderFlowView> sortFlowViews(List<AdminWorkOrderFlowView>  workOrderFlowViews, Integer status) {
        if (CollectionUtils.isEmpty(workOrderFlowViews) || null ==status) {
            return  workOrderFlowViews;
        }

        // 工单的状态是 处理中
        if (status.equals(AdminWorkOrderConst.Status.HANDLING.getCode())) {
            workOrderFlowViews = workOrderFlowViews.stream()
                    .sorted(Comparator.comparing(AdminWorkOrderFlowView::getUpdateTime).reversed())
                    .collect(Collectors.toList());
            //  创建 待处理
        }else if (status.equals(AdminWorkOrderConst.Status.CREATED.getCode())){
            // 优先级 早创建的在后面
            workOrderFlowViews = workOrderFlowViews.stream()
                    .sorted(Comparator.comparing(AdminWorkOrderFlowView::getLevel)
                            .thenComparing(AdminWorkOrderFlowView::getCreateTime).reversed())
                    .collect(Collectors.toList());
        }

        return workOrderFlowViews;
    }

    public void updateTaskType(long workOrderId, int taskType) {
        int r = adminWorkOrderFlowDao.updateTaskType(workOrderId, taskType);
        if (r > 0) {
            externalWorkOrderApiBiz.syncWorkOrderInfoAdminWorkOrderFlow(workOrderId, taskType);
        }
    }

    private boolean judgeUnHandleWorkId(long adminUserId) {
        Date twoWeeksBefore = new DateTime().minusDays(14).withHourOfDay(0).withMinuteOfHour(0).toDate();
        AdminWorkOrder adminWorkOrder = adminWorkOrderBiz.getByOperatorIdAndStatus(twoWeeksBefore, adminUserId, CREATED.getCode());
        return Objects.nonNull(adminWorkOrder);
    }

    private Date getDate() {
        LocalDate localDate = LocalDate.now().minusDays(7);

        ZoneId zoneId = ZoneId.systemDefault();

        ZonedDateTime zdt = localDate.atStartOfDay(zoneId);

        return Date.from(zdt.toInstant());
    }
}












