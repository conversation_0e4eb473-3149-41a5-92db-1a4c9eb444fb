package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.github.pagehelper.PageHelper;
import com.shuidihuzhu.cf.dao.crowdfunding.*;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.OrganizationClientV1;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.admin.util.AdminListUtil;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoStatBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfReportAddTrustBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfReportMirrorRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingReportBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfAdminOperationRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfFakeShareRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingReportRecordBiz;
import com.shuidihuzhu.cf.delegate.commonservice.ICommonServiceDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.finance.impl.FinanceDelegate;
import com.shuidihuzhu.cf.enums.ReportWorkOrderOpEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceDrawCashFeignClient;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.report.AdminCfReportRiskCheck;
import com.shuidihuzhu.cf.model.crowdfunding.report.AdminCfReportRiskTagLabel;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportAddTrustVo;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.finance.model.vo.CfDrawCashApplyVo;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCfRefund;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCrowdfundingReportChild;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkReportMap;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportAddTrust;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportMirrorRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReportLabel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReportRecord;
import com.shuidihuzhu.cf.model.crowdfunding.ReportOperateRecordDO;
import com.shuidihuzhu.cf.vo.crowdfunding.CrowdfundingReportVo;
import com.shuidihuzhu.cf.vo.crowdfunding.ReportMailVo;
import com.shuidihuzhu.common.web.model.Response;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class AdminCrowdfundingReportBizImpl implements AdminCrowdfundingReportBiz {

	private static final Logger LOGGER = LoggerFactory.getLogger(AdminCrowdfundingReportBizImpl.class);

	private static final String REPORT_MSG = "水滴筹收到一条举报信息，筹款ID：【%s】。举报人电话：【%s】；是否上传图片：【%s】；举报内容：【%s】";

    private static final int MAX_ORGANIZATION = 128;

	@Autowired
	private AdminCrowdfundingReportDao adminCrowdfundingReportDao;
	@Autowired
	private AdminCrowdfundingInfoSlaveDao crowdfundingInfoSlaveDao;
	@Autowired
    private AdminCfInfoStatBiz adminCfInfoStatBiz;
	@Autowired
    private CfFakeShareRecordBiz cfFakeShareRecordBiz;
	@Autowired
	private AdminCfReportMirrorRecordBiz cfReportMirrorRecordBiz;
	@Autowired
	private AdminWorkOrderReportDao adminWorkOrderReportDao;
	@Autowired(required = false)
	private Producer producer;
	@Autowired
	private ICrowdfundingDelegate crowdfundingDelegate;
	@Autowired
	private AdminWorkOrderBiz adminWorkOrderBiz;
	@Autowired
	private CfAdminOperationRecordBiz cfAdminOperationRecordBiz;
	@Autowired
	private CrowdfundingReportRecordBiz crowdfundingReportRecordBiz;
	@Resource
	private AdminCfReportAddTrustBiz cfReportAddTrustBiz;
	@Autowired
	private ICommonServiceDelegate commonServiceDelegate;
	@Autowired
	private FinanceDelegate financeDelegate;
	@Autowired
	private SeaAccountClientV1 seaAccountClientV1;
	@Autowired
	private CfFinanceDrawCashFeignClient cfFinanceDrawCashFeignClient;
    @Autowired
    private ReportOperateRecordDAO reportOperateRecordDAO;
    @Resource
    private OrganizationClientV1 organizationClientV1;
    @Autowired
	private CfReportCommitmentInfoBiz cfReportCommitmentInfoBiz;
	@Autowired
	private CfWorkOrderClient cfWorkOrderClient;
	@Resource
	private AdminCfReportRiskCheckDao adminCfReportRiskCheckDao;
	@Resource
	private AdminCfReportRiskTagLabelDao adminCfReportRiskTagLabelDao;

    @Override
	public void add(CrowdfundingReport cr) {
		adminCrowdfundingReportDao.add(cr);
	}

	@Override
	public void addRealNameReport(CrowdfundingReport crowdfundingReport) {
		adminCrowdfundingReportDao.addRealNameReport(crowdfundingReport);
	}

	@Override
	public CrowdfundingReport query(int caseId, int reportId) {
		return adminCrowdfundingReportDao.query(caseId, reportId);
	}

	@Override
	public CrowdfundingReport getByInfoId(Integer activityId) {
		return adminCrowdfundingReportDao.getByInfoId(activityId);
	}

	@Override
	public List<CrowdfundingReport> getByInfoIds(List<Integer> activityIds) {
		if(CollectionUtils.isEmpty(activityIds)){
			return Collections.emptyList();
		}
		return adminCrowdfundingReportDao.getListByInfoIds(activityIds);
	}

	@Override
	public List<CrowdfundingReport> getByInfoIdsV2(List<Integer> activityId) {
		if(CollectionUtils.isEmpty(activityId)){
			return Collections.emptyList();
		}
		return adminCrowdfundingReportDao.getListByInfoIdsV2(activityId);
	}

	@Override
	public Map<Integer, CrowdfundingReport> getMapByInfoIds(List<Integer> activityIds) {
		List<CrowdfundingReport> crowdfundingReports = this.getByInfoIds(activityIds);
		if(CollectionUtils.isEmpty(crowdfundingReports)){
			return Collections.emptyMap();
		}
		return crowdfundingReports.stream().collect(Collectors.toMap(CrowdfundingReport :: getActivityId, Function.identity()));
	}

	@Override
	public List<CrowdfundingReport> getListByReportIds(List<Integer> reportIds) {
		if (CollectionUtils.isEmpty(reportIds)){
			return null;
		}

		List<CrowdfundingReport> totalReports = Lists.newArrayList();
		List<List<Integer>> totalPartition = Lists.partition(reportIds, 100);
		for (List<Integer> curPartition : totalPartition) {
			totalReports.addAll(adminCrowdfundingReportDao.getListByReportIds(curPartition));
		}
		return totalReports;
	}

    @Override
    public int newMailSender(int timer) {
		return 0;
    }

    @Override
    public int updateDealStatus(int dealStatus, int id, int operatorId) {
        return adminCrowdfundingReportDao.updateDealStatus(dealStatus, id, operatorId);
    }

	@Override
	public int updateHandleAndConnectStatus(int id, int handleStatus, int connectStatus, long newOperatorId, int dealstatus) {
		return adminCrowdfundingReportDao.updateHandleAndConnectStatus(id, handleStatus, connectStatus, newOperatorId, dealstatus);
	}

	@Override
	public List<CrowdfundingReport> getListByInfoId(Integer activityId) {
		return adminCrowdfundingReportDao.getListByInfoId(activityId);
	}

	@Override
	public List<CrowdfundingReport> getListByPage(List<Integer> activityId, Integer current, Integer pageSize,
												  Integer caseStatus, Integer reportDealStatus, String caseType) {
		PageHelper.startPage(current, pageSize);
		List<CrowdfundingReport> reportList;
		if(CollectionUtils.isEmpty(activityId))
		{
			if(caseStatus == AdminCfCaseStatus.No.getValue()) {
				reportList = adminCrowdfundingReportDao.getListByPage(reportDealStatus, null, caseType);
			}else{
				reportList=this.getCfRecordListCheakCaseStatus(reportDealStatus, caseStatus, null, caseType);
			}
		}else{
			//如果案例状态是空 不需要额外的条件
			if (caseStatus==AdminCfCaseStatus.No.getValue()){
				reportList=adminCrowdfundingReportDao.getListByPage(reportDealStatus, activityId, caseType);
			}else{
				reportList=this.getCfRecordListCheakCaseStatus(reportDealStatus, caseStatus, activityId, caseType);
			}

		}
		return reportList;
	}


	@Override
	public List<CrowdfundingReportVo> getCrowdfundingReportVo(List<CrowdfundingReport> crowdfundingReportList,
															  List<CrowdfundingInfo> crowdfundingInfoList,
															  int caseStatus) {
		List<CrowdfundingReportVo> crowdfundingReportVoList= Lists.newArrayList();
		//获取举报记录集合
		List<Integer> crowdfundingReportIds=Lists.newArrayList();
		for (CrowdfundingReport crowdfundingReport:crowdfundingReportList)
		{
			crowdfundingReportIds.add(crowdfundingReport.getId());
		}
		List<CrowdfundingReportRecord> reportRecordList=crowdfundingReportRecordBiz.getreportRecordListByReportIds(crowdfundingReportIds);
		//获取案例举报状态
		//List<CrowdfundingOperation> crowdfundingOperations=this.crowdfundingOperationBiz.getByInfoIds(crowdfundingInfoUuIdids);
		//根据reportid获取处理人姓名
		Set<Integer> userIds= crowdfundingReportList.stream().map(CrowdfundingReport::getOperatorId).collect(Collectors.toSet());
		List<AdminUserAccountModel> adminUserAccountModels =null;
		if (CollectionUtils.isNotEmpty(userIds)){
			adminUserAccountModels = this.seaAccountClientV1.getUserAccountsByIds(Lists.newArrayList(userIds)).getResult();
		}
		if (CollectionUtils.isEmpty(adminUserAccountModels)){
			LOGGER.warn("operatioName null warn :{}", adminUserAccountModels);
		}
		if (null == crowdfundingInfoList) {
		    crowdfundingInfoList = Lists.newArrayList();
        }

		//获取提现记录集合
        Response<Map<Integer, CfDrawCashApplyVo>> drawCashResponse = financeDelegate.getApplyInfoMap(
                crowdfundingInfoList.stream().map(CrowdfundingInfo::getId).collect(Collectors.toList()));
        Map<Integer, CfDrawCashApplyVo> drawCashApplyVoMap = drawCashResponse.getData();
        if (null == drawCashApplyVoMap) {
            drawCashApplyVoMap = Maps.newHashMap();
        }

		//获取退款记录集合
		Response<List<AdminCfRefund>> cfRefundsResponse = financeDelegate.getRefundByInfoUuids(
		        crowdfundingInfoList.stream().map(CrowdfundingInfo::getInfoId).collect(Collectors.toList()));

		//写入Vo start
		for (CrowdfundingReport crowdfundingReport:crowdfundingReportList){
			CrowdfundingReportVo crowdfundingReportVo=new CrowdfundingReportVo();
			//向Vo中写入Report信息
			BeanUtils.copyProperties(crowdfundingReport,crowdfundingReportVo);

			crowdfundingReportVo.setReportId(crowdfundingReport.getId());
			crowdfundingReportVo.setReportTime(crowdfundingReport.getCreateTime());
			crowdfundingReportVo.setReportDealStatus(crowdfundingReport.getDealStatus());
			crowdfundingReportVo=this.setReportRecord(crowdfundingReportVo,crowdfundingReport,reportRecordList);

            String operatorName = this.getoprNameByid(crowdfundingReport.getOperatorId(), adminUserAccountModels);
            crowdfundingReportVo.setOperatorName(operatorName);
            crowdfundingReportVo.setContact("***");
            crowdfundingReportVoList.add(crowdfundingReportVo);

			if (CollectionUtils.isEmpty(crowdfundingInfoList)) {
			    log.info("crowdfundingInfoList 为空");
			    continue;
            }

            //向vo写入案例状态信息
            //Vo写入info中的信息
			for (CrowdfundingInfo crowdfundingInfo : crowdfundingInfoList) {
			    if (crowdfundingReport.getActivityId() == crowdfundingInfo.getId()) {
			        crowdfundingReportVo.setTitle(crowdfundingInfo.getTitle());
			        crowdfundingReportVo.setInfoUuid(crowdfundingInfo.getInfoId());
			        //写入案例当前状态
                    if (caseStatus != AdminCfCaseStatus.No.getValue()) {
                        crowdfundingReportVo.setCrowdfundingStatus(caseStatus);
                    } else {
                        if (crowdfundingInfo.getStatus().value() != 2) {
                            crowdfundingReportVo.setCrowdfundingStatus(AdminCfCaseStatus.APPROVE_NO.getValue());
                        } else {
                            crowdfundingReportVo.setCrowdfundingStatus(AdminCfCaseStatus.APPROVE_FINISH.getValue());
                            crowdfundingReportVo = this.setCaseDrawStatus(crowdfundingReportVo,
                                    drawCashApplyVoMap.get(crowdfundingInfo.getId()));
                            crowdfundingReportVo = this.setCaseRefundStatus(crowdfundingReportVo, cfRefundsResponse.getData(),
                                    crowdfundingInfo.getInfoId());
                        }
                    }
			    }
			}
		}
		return crowdfundingReportVoList;
	}

	//写入举报记录
	private CrowdfundingReportVo setReportRecord(CrowdfundingReportVo crowdfundingReportVo,CrowdfundingReport crowdfundingReport
			,List<CrowdfundingReportRecord> reportRecordList)
	{
		LOGGER.info("CrowdfundingReportVo setREportRecord");
		//向Vo中写入举报记录的信息  这个if可以提前筛选一部分
		if (reportRecordList==null){
			if (crowdfundingReport.getDealStatus() == 0)
			{
				crowdfundingReportVo.setDealComment("暂无");
				crowdfundingReportVo.setDealTime("暂无");
			} else {
				crowdfundingReportVo.setDealComment("请到案例详情页面查看");
				crowdfundingReportVo.setDealTime("请到案例详情页面查看");
			}
			return crowdfundingReportVo;
		}
		if (crowdfundingReport.getDealStatus()!=0){
			List<CrowdfundingReportRecord> reportRecordoneReport=Lists.newArrayList();
			for(CrowdfundingReportRecord crowdfundingReportRecord:reportRecordList){
				if (crowdfundingReportRecord.getReportId()==crowdfundingReport.getId()){
					reportRecordoneReport.add(crowdfundingReportRecord);
				}
			}
			//为了保证与之前举报后台备注的页面进行兼容
			if (reportRecordoneReport.isEmpty())
			{
				crowdfundingReportVo.setDealComment("请到案例详情页面查看");
				crowdfundingReportVo.setDealTime("请到案例详情页面查看");
			}else{
				StringBuffer dealComment=new StringBuffer();
				StringBuffer dealTime=new StringBuffer();
				for(CrowdfundingReportRecord crowdfundingReportRecord:reportRecordoneReport){
					if (crowdfundingReportRecord.getDealStatus()==1)
					{
						dealTime.append("<p>处理中时间：").append(crowdfundingReportRecord.getCreateTime()).append("</p>");
						dealComment.append("<p>处理中备注：").append(crowdfundingReportRecord.getComment()).append("</p>");
					}else if (crowdfundingReportRecord.getDealStatus()==2){
						dealTime.append("<p>处理完成时间：").append(crowdfundingReportRecord.getCreateTime()).append("</p>");
						dealComment.append("<p>处理完成备注：").append(crowdfundingReportRecord.getComment()).append("</p>");
					}else if (crowdfundingReportRecord.getDealStatus()==3){
						dealTime.append("<p>不再处理时间：").append(crowdfundingReportRecord.getCreateTime()).append("</p>");
						dealComment.append("<p>不再处理备注：").append(crowdfundingReportRecord.getComment()).append("</p>");
					}
				}
				crowdfundingReportVo.setDealTime(dealTime.toString());
				crowdfundingReportVo.setDealComment(dealComment.toString());
			}
		}else{
			crowdfundingReportVo.setDealComment("暂无");
			crowdfundingReportVo.setDealTime("暂无");
		}
		return crowdfundingReportVo;
	}

	//查询运营名称
	private String getoprNameByid(int oprid,List<AdminUserAccountModel> adminUserAccountModels){
		if (CollectionUtils.isEmpty(adminUserAccountModels)){
			return "";
		}
		for(AdminUserAccountModel adminUserAccountModel : adminUserAccountModels){
			if (adminUserAccountModel.getId()==oprid) {
				return adminUserAccountModel.getName();
			}
		}
		return "";
	}

	//写入案例体现状态
	private CrowdfundingReportVo setCaseDrawStatus(CrowdfundingReportVo crowdfundingReportVo,
                                                   CfDrawCashApplyVo drawCashApplyVo) {
		//如果是空不改变
		if(null == drawCashApplyVo) {
			return crowdfundingReportVo;
		}

        if (drawCashApplyVo.getApplyStatus() == AdminCfDrawCashConstant.ApplyStatus.APPROVE_SUCCESS.getCode()) {
            if (drawCashApplyVo.getDrawStatus() == CfDrawCashConstant.DrawStatus.UNBUILD.getCode()) {
                //4.提现成功还未建立
                crowdfundingReportVo.setCrowdfundingStatus(AdminCfCaseStatus.DRAWCASH_APPROVE.getValue());
            } else if (drawCashApplyVo.getDrawStatus() == CfDrawCashConstant.DrawStatus.HANDLE_SUCCESS.getCode()
                    || drawCashApplyVo.getDrawStatus() == CfDrawCashConstant.DrawStatus.HANDLE_PARTIAL_SUCCESS.getCode()
                    || drawCashApplyVo.getDrawStatus() == CfDrawCashConstant.DrawStatus.HANDLING.getCode()
                    || drawCashApplyVo.getDrawStatus() == CfDrawCashConstant.DrawStatus.MANUAL_SUCCESS.getCode()
            ) {
                //6.已打款&部分打款成功&打款成功
                crowdfundingReportVo.setCrowdfundingStatus(AdminCfCaseStatus.DRAWCASH_SUCCESS.getValue());
            } else if (drawCashApplyVo.getDrawStatus() == CfDrawCashConstant.DrawStatus.UNHANDLE.getCode() ||
                    drawCashApplyVo.getDrawStatus() == CfDrawCashConstant.DrawStatus.HANDLE_FAILED.getCode()) {
                //5.已生成&打款失败
                crowdfundingReportVo.setCrowdfundingStatus(AdminCfCaseStatus.DRAWCASH_BUILD.getValue());
            }
        }
		return crowdfundingReportVo;
	}

	//写入案例退款状态
	private CrowdfundingReportVo setCaseRefundStatus(CrowdfundingReportVo crowdfundingReportVo,
													 List<AdminCfRefund> cfRefunds,String infoUuid) {
		//如果是空不改变
		if(CollectionUtils.isEmpty(cfRefunds))
		{
			return crowdfundingReportVo;
		}
		for (AdminCfRefund cfRefund:cfRefunds){
			if(cfRefund.getInfoUuid().equals(infoUuid)){
				if(cfRefund.getApplyStatus()> NewCfRefundConstant.ApplyStatus.UNSUBMIT.getCode()){
					//7.已申请退款
					crowdfundingReportVo.setCrowdfundingStatus(AdminCfCaseStatus.REFUND_SUBMIT.getValue());
				}
			}
		}
		return crowdfundingReportVo;
	}
    @Override
    public List<CrowdfundingReport> selectByActivityIds(List<Integer> list) {
        return adminCrowdfundingReportDao.selectByActivityIds(list);
    }

	//根据案例状态进行判断
	private List<CrowdfundingReport> getCfRecordListCheakCaseStatus(Integer reportDealStatus,Integer caseStatus,List<Integer> activityId, String caseType){
		//reportList=adminCrowdfundingReportDao.getListByPage(reportDealStatus,activityId);
		List<CrowdfundingReport> reportList=null;
		if(caseStatus==AdminCfCaseStatus.APPROVE_NO.getValue()){
			//1.审核未通过
			reportList=adminCrowdfundingReportDao.getListByWhereAndPage(reportDealStatus, activityId,
					null, null, caseType);
		}
		else if(caseStatus==AdminCfCaseStatus.APPROVE_FINISH.getValue()){
			//2.审核通过
			reportList=adminCrowdfundingReportDao.getListByWhereAndPage(reportDealStatus, activityId,
			                                                            CrowdfundingStatus.CROWDFUNDING_STATED.value(), null, caseType);
		}else if(caseStatus==AdminCfCaseStatus.DRAWCASH_SUBMIT.getValue()){
			//3.已申请提现
			reportList=adminCrowdfundingReportDao.getListByWhereDrawCashAndPage(reportDealStatus,activityId,
					CrowdfundingStatus.CROWDFUNDING_STATED.value(), AdminCfDrawCashConstant.ApplyStatus.UNSUBMIT.getCode(),
					null, caseType);
		}else if(caseStatus==AdminCfCaseStatus.DRAWCASH_APPROVE.getValue()){
			//4.体现审核通过还未生成
			reportList=adminCrowdfundingReportDao.getListByWhereDrawCashAndPage(reportDealStatus,activityId,
					CrowdfundingStatus.CROWDFUNDING_STATED.value(),AdminCfDrawCashConstant.ApplyStatus.APPROVE_SUCCESS.getCode(),
					CfDrawCashConstant.DrawStatus.UNBUILD.getCode(), caseType);
		}else if(caseStatus==AdminCfCaseStatus.DRAWCASH_BUILD.getValue()){
			//5.已生成&打款失败
			reportList=adminCrowdfundingReportDao.getListByWhereDrawCashAndPage(reportDealStatus,activityId,
					CrowdfundingStatus.CROWDFUNDING_STATED.value(),AdminCfDrawCashConstant.ApplyStatus.APPROVE_SUCCESS.getCode(),
					CfDrawCashConstant.DrawStatus.HANDLE_FAILED.getCode(), caseType);
		}else if(caseStatus==AdminCfCaseStatus.DRAWCASH_SUCCESS.getValue()){
			//6.已打款&部分打款成功
			reportList=adminCrowdfundingReportDao.getListByWhereDrawCashAndPage(reportDealStatus,activityId,
					CrowdfundingStatus.CROWDFUNDING_STATED.value(),AdminCfDrawCashConstant.ApplyStatus.APPROVE_SUCCESS.getCode(),
					CfDrawCashConstant.DrawStatus.HANDLE_SUCCESS.getCode(), caseType);
		}else if(caseStatus==AdminCfCaseStatus.REFUND_SUBMIT.getValue()){
			//7.已申请退款 >不提交
			reportList=adminCrowdfundingReportDao.getListByWhereAndPage(reportDealStatus, activityId,
			                                                            CrowdfundingStatus.CROWDFUNDING_STATED.value(), NewCfRefundConstant.ApplyStatus.UNSUBMIT.getCode(),
			                                                            caseType);
		}
		return reportList;
	}

	@Override
	public int updateReportStatusList(List<Integer> reportIds) {
		return adminCrowdfundingReportDao.updateReportStatusList(reportIds);
	}

	@Override
	public int updateReportIsNewStatus(List<Integer> reportIds) {
		return adminCrowdfundingReportDao.updateReportIsNewStatusList(reportIds);
	}

	@Override
	public List<CrowdfundingReport> getListByCreateTime(int sortType) {
		List<CrowdfundingReport> reportList=Lists.newArrayList();
		if(sortType == CfReportCaseSortTypeEnum.FIRST_REPORT_TIME.getValue()){
			reportList=adminCrowdfundingReportDao.getFirstListByCreateTimeAndInfoid(null);
		}else if(sortType == CfReportCaseSortTypeEnum.LAST_REPORT_TIME.getValue()){
			reportList=adminCrowdfundingReportDao.getLastListByCreateTimeAndInfoid(null);
		}
		if (CollectionUtils.isEmpty(reportList)){
			return null;
		}
		return reportList;
	}

	@Override
	public List<CrowdfundingReport> getListByCreateTimeAndInfoid(List<Integer> infoids, int sortType) {
		if (CollectionUtils.isEmpty(infoids)){
			return null;
		}
		List<CrowdfundingReport> reportList=Lists.newArrayList();
		if(sortType == CfReportCaseSortTypeEnum.FIRST_REPORT_TIME.getValue()){
			reportList = adminCrowdfundingReportDao.getFirstListByCreateTimeAndInfoid(infoids);
		}else if(sortType == CfReportCaseSortTypeEnum.LAST_REPORT_TIME.getValue()){
			reportList = adminCrowdfundingReportDao.getLastListByCreateTimeAndInfoid(infoids);
		}
		if (CollectionUtils.isEmpty(reportList)){
			return null;
		}
		return reportList;
	}


	@Override
	public List<CrowdfundingReport> getIsHaveNewReport(List<Integer> infoids) {
		if (CollectionUtils.isEmpty(infoids)){
			return null;
		}
		return adminCrowdfundingReportDao.getIsHaveNewReport(infoids);
	}

	@Override
	public List<CrowdfundingReport> getListByInfoIdAndPage(int pageSize, int current, int infoId) {
		PageHelper.startPage(current, pageSize);
		return adminCrowdfundingReportDao.getListByInfoIdAndPage(infoId);
	}

	@Override
	public int updateReportListDealStatus(List<Integer> reportIds, CaseReportDealStatus caseReportDealStatus, int operatorId) {
		if (CollectionUtils.isEmpty(reportIds)) {
			return 0;
		}
		return adminCrowdfundingReportDao.updateReportListDealStatus(reportIds, caseReportDealStatus.getValue(), operatorId);
	}

	@Override
	public int updateReportListOperator(List<Integer> reportIds, int targetUserId) {
		if(CollectionUtils.isEmpty(reportIds) || targetUserId <= 0){
			return 0;
		}
		return adminCrowdfundingReportDao.updateReportListOperator(reportIds, targetUserId);
	}

	@Override
	public int updateReportListFollowStatus(List<Integer> reportIds,  CaseReportFollowStatusEnum caseReportFollowStatusEnum) {
		if (CollectionUtils.isEmpty(reportIds)) {
			return 0;
		}
		return adminCrowdfundingReportDao.updateReportListFollowStatus(reportIds, caseReportFollowStatusEnum.getValue());
	}

	@Override
	public Map<String, Object> getAddTrustMirror(String infoUuid) {
		//获取增信信息
		CfReportAddTrust cfReportAddTrust = cfReportAddTrustBiz.getByInfoUuid(infoUuid);
		if (cfReportAddTrust == null) {
			return null;
		}
		CfReportAddTrustVo cfReportAddTrustVo = new CfReportAddTrustVo();
		BeanUtils.copyProperties(cfReportAddTrust,cfReportAddTrustVo);
		if (cfReportAddTrust.isIssuedCommitment()){
			CfReportCommitmentInfo cfReportCommitmentInfo = cfReportCommitmentInfoBiz.findByIncrTrustId(cfReportAddTrust.getId());
			if (Objects.nonNull(cfReportCommitmentInfo)){
				cfReportAddTrustVo.setCfReportCommitmentInfo(cfReportCommitmentInfo);
			}
		}
		Map<String, Object> result = Maps.newHashMap();
		result.put("cfReportAddTrust",cfReportAddTrustVo);
		List<String> imageList = Splitter.on(",").splitToList(cfReportAddTrust.getImageUrls());
		result.put("imageList", imageList);

		fillBeforeInfo(infoUuid, result, cfReportAddTrust);
		return result;
	}

	private void fillBeforeInfo(String infoUuid, Map<String, Object> result, CfReportAddTrust currentInfo) {

		// 是否是被驳回的
		boolean isRejected = currentInfo.getAuditStatus() == CrowdfundingInfoStatusEnum.REJECTED.getCode();

		// 若是被驳回的 上次提交用户信息 取currentInfo 上次提交运营备注取AFTER的备注
		CfInfoMirrorRecordEnum.Type type = isRejected ?
				CfInfoMirrorRecordEnum.Type.AFTER :
				CfInfoMirrorRecordEnum.Type.BEFORE;

		//获取增信镜像信息
		CfReportMirrorRecord userRecord = getMirrorRecordByRole(infoUuid,
				CfOperatingRecordEnum.Role.USER, type);
		if (userRecord == null) {
			return ;
		}
		CfReportMirrorRecord operatorRecord = getMirrorRecordByRole(infoUuid,
				CfOperatingRecordEnum.Role.OPERATOR, type);
		if (operatorRecord == null) {
			return ;
		}
		if (StringUtils.isBlank(userRecord.getAddTrustInfo())
				|| StringUtils.isBlank(operatorRecord.getAddTrustInfo())) {
			return;
		}

		// 运营操作记录中获取运营备注内容
		CfReportMirrorRecord.AddTrustInfo operatorInfoObject = operatorRecord.getAddTrustInfoObject();
		String operatorContent = operatorInfoObject.getOperatorContent();

		// 用户填充记录中 获取用户填充内容
		CfReportMirrorRecord.AddTrustInfo addTrustInfoObject = userRecord.getAddTrustInfoObject();
		String imageUrls = userRecord.getAddTrustInfoObject().getImageUrls();

		// 覆盖用户操作记录中保存的错误的运营备注内容
		addTrustInfoObject.setOperatorContent(operatorContent);

		// 如果是被驳回 取当前内容
		if (isRejected) {
			addTrustInfoObject.setContent(currentInfo.getContent());
			imageUrls = currentInfo.getImageUrls();
		}

		result.put("addTrustInfoBefore", addTrustInfoObject);
		List<String> imageList = Splitter.on(",").splitToList(imageUrls);
		result.put("imageListBefore", imageList);
	}

	private CfReportMirrorRecord getMirrorRecordByRole(String infoUuid, CfOperatingRecordEnum.Role role,
													   CfInfoMirrorRecordEnum.Type targetRecordType) {
		List<CfReportMirrorRecord> cfReportMirrorRecordList = cfReportMirrorRecordBiz.getByInfoUuid(infoUuid, role);
		LOGGER.info("role: {}, type: {}, cfInfoMirrorRecords:{}",
				role,
				targetRecordType,
				ToStringBuilder.reflectionToString(cfReportMirrorRecordList));
		if (CollectionUtils.isEmpty(cfReportMirrorRecordList) || cfReportMirrorRecordList.size() != 2) {
			return null;
		}
		CfReportMirrorRecord record = null;
		for (CfReportMirrorRecord cfReportMirrorRecord : cfReportMirrorRecordList) {
			int type = cfReportMirrorRecord.getType();
			if (targetRecordType.getCode() == type) {
				record = cfReportMirrorRecord;
				break;
			}
		}
		LOGGER.info("record: {}", record);
		return record;
	}

	@Override
	public List<AdminCrowdfundingReportChild> getCaseReportCount(List<Integer> caseIds) {
		return adminCrowdfundingReportDao.getCaseReportCount(caseIds);
	}

	@Override
	public List<CrowdfundingReport> getFirstCreateTimeByInfoIds(List<Integer> infoids) {
		return adminCrowdfundingReportDao.getFirstCreateTimeByInfoIds(infoids);
	}

	@Override
	public List<CrowdfundingReport> getLastCreateTimeByInfoIds(List<Integer> infoids) {
		return adminCrowdfundingReportDao.getLastCreateTimeByInfoIds(infoids);
	}


	@Override
	public void checkReportByTimes() {
//
// 		Date end = new Date();
//		Date start = DateUtils.addMinutes(end,-11);
//
//		List<CrowdfundingReport> reports = adminCrowdfundingReportDao.getReportByTimes(start,end);
//
//		if (CollectionUtils.isEmpty(reports)){
//			log.info("checkReportByTimes no reports");
//			return;
//		}
//
//		List<Integer> reportIds = reports.stream().map(CrowdfundingReport::getId).collect(Collectors.toList());
//
//		List<AdminWorkReportMap> reportMaps = adminWorkOrderReportDao.getReportMapByReportIds(reportIds);
//
//		Set<Long> set = reportMaps.stream().map(AdminWorkReportMap::getReportId).collect(Collectors.toSet());
//
//		List<CrowdfundingReport> needReports = Lists.newArrayList();
//
//		//比较查询出来的举报是否都生成工单
//		reports.stream().forEach(r -> {
//			if (!set.contains(Long.valueOf(r.getId()))){
//				//查新工单
//				WorkOrderExt workOrderExt = this.getNewReportWorkOrderExt(r);
//				if (Objects.isNull(workOrderExt)){
//					needReports.add(r);
//				}
//			}
//		});
//
//		//表示全生成工单了
//		if (CollectionUtils.isEmpty(needReports)){
//			log.info("checkReportByTimes reports ok ");
//			return;
//		}
//		log.info("checkReportByTimes new report needReports={}",JSON.toJSONString(needReports));
//
//		//存在漏单现象  补单  发送消息
//		needReports.stream().forEach( r -> {
//			CrowdfundingInfo info = crowdfundingDelegate.getFundingInfoById(r.getActivityId());
//			String infoUuid = info.getInfoId();
//			Map<String,Integer> map = Maps.newHashMap();
//			map.put(infoUuid,r.getId());
//			producer.send( new Message<>(MQTopicCons.CF, MQTagCons.CF_WORK_ORDER_REPORT,
//					MQTagCons.CF_WORK_ORDER_REPORT+"_"+infoUuid,map,DelayLevel.S5));
//		});
	}

	private WorkOrderExt getNewReportWorkOrderExt(CrowdfundingReport crowdfundingReport) {
		try {
			Thread.sleep(1000);
			Response<List<WorkOrderExt>> listResponse = cfWorkOrderClient.queryExtByCase(crowdfundingReport.getActivityId(),
					WorkOrderType.REPORT_TYPES, OrderExtName.reportId.getName(), String.valueOf(crowdfundingReport.getId()));
			if (listResponse.ok() && CollectionUtils.isNotEmpty(listResponse.getData())) {
				List<WorkOrderExt> workOrderExts = listResponse.getData();
				return workOrderExts.get(0);
			}
		} catch (Exception e) {
			log.error("AdminCrowdfundingReportBizImpl.getNewReportWorkOrderExt error", e);
		}
		return null;
	}


	@Override
	public List<CrowdfundingReportLabel> getReportLabels(List<Integer> reportIds) {
		if (CollectionUtils.isEmpty(reportIds)) {
			return Lists.newArrayList();
		}
		return adminCrowdfundingReportDao.getReportLabels(reportIds);
	}

	@Override
	public 	boolean canHandleReport(int reportId, int userId) {
		AdminWorkReportMap workReportMap = adminWorkOrderReportDao.getAdminWorkReportMapByReportId(reportId);
		if (workReportMap == null) {
			log.error("不能通过reportId:{} 找到AdminWorkReportMap", reportId);
			return false;
		}

		AdminWorkOrder workOrder = adminWorkOrderBiz.selectById(workReportMap.getWorkOrderId());
		if (workOrder == null) {
			log.error("不能通过workOrderId:{} 找到AdminWorkOrder", workReportMap.getWorkOrderId());

			return false;
		}

		return workOrder.getOperatorId() == 0 || workOrder.getOperatorId() == userId;
	}

	@Override
	public void updateReportLabel(int userId, List<CrowdfundingReportLabel> newLabelList) {
		log.info("举报工单修改举报的举报类型。userId:{}, newLabelList:{}", userId, newLabelList);
		if (CollectionUtils.isEmpty(newLabelList)) {
			return;
		}

		int reportId = (int)newLabelList.get(0).getReportId();
		// 从数据库中找出当前的有效的用户标签
		List<CrowdfundingReportLabel> oldReportLabels = adminCrowdfundingReportDao.getReportLabels(Arrays.asList(reportId));

		if (CollectionUtils.isNotEmpty(oldReportLabels)) {
			adminCrowdfundingReportDao.deleteReportLabels(reportId);
		}

		AdminWorkReportMap reportMap = adminWorkOrderReportDao.getAdminWorkReportMapByReportId(reportId);
		if(Objects.nonNull(reportMap)){
			long caseId = reportMap.getCaseId();
			long reportWorkOrderId = reportMap.getWorkOrderId();
			List<String> oldReportComments = Lists.newArrayList();
			List<String> newReportComments = Lists.newArrayList();
			for (CrowdfundingReportLabel reportLabel : oldReportLabels){
				oldReportComments.add(reportLabel.getReportComment());
			}

			for (CrowdfundingReportLabel reportLabel : newLabelList){
				newReportComments.add(reportLabel.getReportComment());
			}

			String oldReportComment = CollectionUtils.isEmpty(oldReportComments) ? "空" : String.join("/", oldReportComments);
			String newReportComment = CollectionUtils.isEmpty(newReportComments) ? "" : String.join("/", newReportComments);
			AuthRpcResponse<AdminUserAccountModel> userAccountRes = seaAccountClientV1.getValidUserAccountById(userId);
			String userName = Objects.nonNull(userAccountRes) && Objects.nonNull(userAccountRes.getResult()) ? userAccountRes.getResult().getName() : "";

			ReportOperateRecordDO recordDO = new ReportOperateRecordDO();
			recordDO.setCaseId(Integer.valueOf(String.valueOf(caseId)));
			recordDO.setWorkOrderId(Integer.valueOf(String.valueOf(reportWorkOrderId)));
			recordDO.setType(ReportWorkOrderOpEnum.CHANGE_REPORT_TYPE.getVaule());
			recordDO.setContent("类型:" + oldReportComment + ">" + newReportComment);
			recordDO.setComment("");
			recordDO.setOperatorId(userId);
			recordDO.setOperator(userName);
			recordDO.setDepartment(getOrganization(userId));
			reportOperateRecordDAO.insert(recordDO);
		}

		adminCrowdfundingReportDao.addReportLabelsForAdmin(newLabelList);

		List<AdminWorkReportMap> reportMaps = adminWorkOrderReportDao.getReportMapByReportIds(Arrays.asList((int)newLabelList.get(0).getReportId()));

		if (CollectionUtils.isEmpty(reportMaps)) {
			return;
		}

		CfInfoSimpleModel simpleModel = crowdfundingDelegate.getCfInfoSimpleModelById((int)reportMaps.get(0).getCaseId());

		cfAdminOperationRecordBiz.addOneOperationRecord(simpleModel == null ? "" : simpleModel.getInfoId(), userId, CfOperationRecordEnum.CHANGE_REPORT_LABELS.value(), "");
	}

    private String getOrganization(Integer operatorId) {
        String org = StringUtils.EMPTY;
        if (Objects.isNull(operatorId) || operatorId <= 0) {
            return org;
        }
        org = organizationClientV1.getUserRelationOrgName(operatorId).getResult();
        if (StringUtils.isEmpty(org)) {
            return "";
        }
        if (StringUtils.length(org) <= MAX_ORGANIZATION) {
            return org;
        }

        org = StringUtils.left(org, MAX_ORGANIZATION);
        return org;
    }

	@Override
	public List<CrowdfundingReport> selectReportByCaseIdAndCreateTime(int caseId, Date createTime, int dealStatus) {

		return adminCrowdfundingReportDao.selectReportByCaseIdAndCreateTime(caseId, createTime, dealStatus);
	}

	@Override
	public List<CrowdfundingReport> listByUserId(long reportUserId) {
		return adminCrowdfundingReportDao.listByUserId(reportUserId);
	}

	@Override
	public int countByUserId(long reportUserId) {
		return adminCrowdfundingReportDao.countByUserId(reportUserId);
	}

	@Override
	public int updateRiskLabel(String riskLabel, int reportId) {
    	if (StringUtils.isBlank(riskLabel)) {
    		return 0;
		}
		return adminCrowdfundingReportDao.updateRiskLabel(riskLabel, reportId);
	}

	@Override
	public List<CrowdfundingReport> getListByInfoIdAndName(int caseId, String userName) {
    	if (StringUtils.isBlank(userName) || caseId <= 0){
    		return Lists.newArrayList();
		}
    	return adminCrowdfundingReportDao.getListByInfoIdAndName(caseId, userName);
	}

	@Override
	public int addLabel(int reportId, String reportTypes) {
		if (reportId <= 0 || StringUtils.isEmpty(reportTypes)) {
			return 0;
		}

		List<CrowdfundingReportLabel> list = Splitter.on("|").splitToList(reportTypes).stream().map(l -> {

			List<String> types = Splitter.on(",").splitToList(l);
			int reportLabel = Integer.parseInt(types.get(0));
			if (types.size() > 1) {
				reportLabel = Integer.parseInt(types.get(1));
			}

			CrowdfundingReportLabel crowdfundingReportLabel = new CrowdfundingReportLabel();
			crowdfundingReportLabel.setReportId(reportId);
			crowdfundingReportLabel.setReportLabel(reportLabel);
			return crowdfundingReportLabel;
		}).collect(Collectors.toList());
		return adminCrowdfundingReportDao.addLabel(list);
	}

	@Override
	public int countByInfoId(int caseId) {
		if (caseId <= 0) {
			return 0;
		}
		return adminCrowdfundingReportDao.countByInfoId(caseId);
	}

	@Override
	public int addOrUpdateLabel(int reportId, String reportTypes, String details) {
		if (reportId <= 0 || StringUtils.isEmpty(reportTypes)) {
			return 0;
		}
		adminCrowdfundingReportDao.deleteReportLabelsByIdAndType(reportId);

		List<CrowdfundingReportLabel> list = Splitter.on("|").splitToList(reportTypes).stream().map(l -> {

			List<String> types = Splitter.on(",").splitToList(l);
			int reportLabel = Integer.parseInt(types.get(0));
			if (types.size() > 1) {
				reportLabel = Integer.parseInt(types.get(1));
			}


			CrowdfundingReportLabel crowdfundingReportLabel = new CrowdfundingReportLabel();
			crowdfundingReportLabel.setReportId(reportId);
			crowdfundingReportLabel.setReportLabel(reportLabel);
			if(reportLabel == CfReportTypeEnum.OTHER.getCode()){
				crowdfundingReportLabel.setReportComment(details);
			}else {
				crowdfundingReportLabel.setReportComment("");
			}
			return crowdfundingReportLabel;
		}).collect(Collectors.toList());
		return adminCrowdfundingReportDao.addModifyLabel(list);
    }

	@Override
	public List<CrowdfundingReportLabel> getReportLabelsModify(List<Integer> reportIds) {
		return adminCrowdfundingReportDao.getReportLabelsModify(reportIds);
	}

	@Override
	public int addReportRiskCheck(AdminCfReportRiskCheck adminCfReportRiskCheck) {
		return adminCfReportRiskCheckDao.insert(adminCfReportRiskCheck);
	}

	@Override
	public List<AdminCfReportRiskCheck> getReportRiskCheckByCaseId(int caseId) {
		return adminCfReportRiskCheckDao.getByCaseId(caseId);
	}

	@Override
	public List<AdminCfReportRiskTagLabel> getReportRiskTagLabel(int caseId) {
		return adminCfReportRiskTagLabelDao.getByCaseId(caseId);
	}

	@Override
	public int addCfReportRiskTagLabel(AdminCfReportRiskTagLabel adminCfReportRiskTagLabel) {
		return adminCfReportRiskTagLabelDao.insert(adminCfReportRiskTagLabel);
	}

	@Override
	public int updateEncryptMobileById(int id, String encryptMobile) {
		return adminCrowdfundingReportDao.updateEncryptMobileById(id, encryptMobile);
	}
}
