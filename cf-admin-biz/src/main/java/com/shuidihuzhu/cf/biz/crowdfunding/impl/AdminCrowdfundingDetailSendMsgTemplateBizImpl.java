package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.delegate.saas.AdminOrganization;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.OrganizationClientV1;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingDetailSendMsgTemplateBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfMaterialVerityHistoryBiz;
import com.shuidihuzhu.cf.dao.admin.AdminCrowdfundingSendMsgTemplateDao;
import com.shuidihuzhu.cf.model.crowdfunding.AdminSmsTemplateSettingsInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingMsgContent;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingMsgTemplateVo;
import com.shuidihuzhu.client.baseservice.msg.template.SmsTemplateClient;
import com.shuidihuzhu.msg.model.SmsTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by niejiangnan on 2017/7/19.
 */
@Primary
@Service
@Slf4j
public class AdminCrowdfundingDetailSendMsgTemplateBizImpl implements AdminCrowdfundingDetailSendMsgTemplateBiz {

    @Autowired
    private AdminCrowdfundingSendMsgTemplateDao crowdfundingSendMsgTemplateDao;

    @Autowired
    private CfMaterialVerityHistoryBiz verityHistoryBiz;

    @Autowired
    private OrganizationClientV1 orgClientV1;

    @Autowired
    private SeaAccountClientV1 accountClientV1;

    @Autowired
    private SmsTemplateClient smsTemplateClient;


    @Override
    public List<CrowdfundingMsgContent> getAllMsgTemplate() {
        return crowdfundingSendMsgTemplateDao.getAllMsgTemplate(1);
    }

    @Override
    public int updateMsgTemplate(CrowdfundingMsgContent crowdfundingMsgContent) {
        if(crowdfundingMsgContent==null)
        {
            return 0;
        }
        return crowdfundingSendMsgTemplateDao.updateMsgTemplate(crowdfundingMsgContent);
    }

    @Override
    public CrowdfundingMsgContent getByKey(String key) {
        if(StringUtils.isEmpty(key))
        {
          return null;
        }
        return crowdfundingSendMsgTemplateDao.getByKey(key);
    }


    //复制需要的属性
    @Override
    public List<CrowdfundingMsgTemplateVo> setVolist(List<CrowdfundingMsgTemplateVo> Volist, List<CrowdfundingMsgContent> MsgTemplatelist) {

        if(MsgTemplatelist==null||CollectionUtils.isEmpty(MsgTemplatelist) )
        {
            return Collections.emptyList();
        }
        for (CrowdfundingMsgContent c:MsgTemplatelist)
        {
            CrowdfundingMsgTemplateVo crowdfundingMsgTemplateVo=new CrowdfundingMsgTemplateVo();
            crowdfundingMsgTemplateVo.setTitle(c.getName());
            crowdfundingMsgTemplateVo.setKey(c.getKey());
            crowdfundingMsgTemplateVo.setContent(c.getContent());
            Volist.add(crowdfundingMsgTemplateVo);
        }
        return Volist;
    }

    @Override
    public String getMsgTitleByContent(String message) {
        if (StringUtils.isBlank(message)){
            return "";
        }
        return crowdfundingSendMsgTemplateDao.getMsgTitleByContent(message);
    }

    @Override
    public List<Integer> getAllSmsAuthGroup(int userId, int authType) {
        List<Integer> smsAuthGroupCode = Lists.newArrayList();

        for (AdminSmsTemplateSettingsInfo.SmsGroup group : AdminSmsTemplateSettingsInfo.SmsGroup.values()) {

            String authString = (authType == 0 ? group.getSendTemplateAuth() : group.getAddTemplateAuth());
            if (verityHistoryBiz.hasPermissionWithUser(userId, authString)) {
                smsAuthGroupCode.add(group.getCode());
            }
        }
        return smsAuthGroupCode;
    }

    @Override
    public int addSmsTemplate(int smsGroup, String modelNum, int userId) {
        log.info("添加短信模版. userId:{} smsGroup:{} modelNum:{}", userId, smsGroup, modelNum);

        // 一般没有这种情况的发送
        if (!verityHistoryBiz.hasPermissionWithUser(userId, AdminSmsTemplateSettingsInfo.SmsGroup.parseByCode(smsGroup).getAddTemplateAuth())) {
            log.info("用户没有添加短信模版的权限.userId:{}, smsGroup:{}, modelNum:{}" , userId, smsGroup, modelNum);
            return -1;
        }

        return doAddSmsTemplate(smsGroup, modelNum, userId);
    }

    public int doAddSmsTemplate(int smsGroup, String modelNum, int userId){
        List<AdminSmsTemplateSettingsInfo.SmsSettings> smsSettings = crowdfundingSendMsgTemplateDao.selectTemplateByParam(
                smsGroup, modelNum, null, null);

        AdminSmsTemplateSettingsInfo.SmsSettings addSettings = null;
        if (CollectionUtils.isNotEmpty(smsSettings)) {
            addSettings = smsSettings.get(0);
            addSettings.setIsDelete(AdminSmsTemplateSettingsInfo.SmsDataType.ENABLE.getCode());
            log.info("库中已有短信模版，只更新模版的操作人和启用状态. smsGroup:{} modelNum:{} userId:{} id:{}",
                    smsGroup, modelNum, userId, addSettings.getId());
        } else {
            addSettings = new AdminSmsTemplateSettingsInfo.SmsSettings();
            addSettings.setSmsGroup(smsGroup);
            addSettings.setModelNum(modelNum);
            addSettings.setPriority(queryCurrentPriorityBySmsGroup(smsGroup));
        }
        addSettings.setOperatorId(userId);

        int result = crowdfundingSendMsgTemplateDao.addSmsTemplate(addSettings);
        addSmsOperateLog(addSettings.getId(), userId, AdminSmsTemplateSettingsInfo.SmsOperateType.ADD.getCode());
        
        return result;
    }

    @Override
    public int queryCurrentPriorityBySmsGroup(int smsGroup) {

        List<AdminSmsTemplateSettingsInfo.SmsSettings> smsSettings = crowdfundingSendMsgTemplateDao.selectTemplateByParam(smsGroup,
                null,  null,null);

        int minPriority = 0;
        if (CollectionUtils.isNotEmpty(smsSettings)) {
            for (AdminSmsTemplateSettingsInfo.SmsSettings currentSettings : smsSettings) {
                minPriority = Math.max(minPriority, currentSettings.getPriority());
            }
        }

        return minPriority + 1;
    }

    @Override
    public int updateStatusById(int id, int dataStatus, int userId) {
        addSmsOperateLog(id, userId, AdminSmsTemplateSettingsInfo.SmsDataType.getOperateTypeByCode(dataStatus));
        int result = crowdfundingSendMsgTemplateDao.updateStatusById(id, dataStatus);
        log.info("操作短信模版. userId:{}, id:{}, dataStatus:{}", userId, id, dataStatus);
        return result;
    }

    @Override
    public int updateSmsTemplatePriority(int upId, int downId, int operateType, int userId) {
        log.info("修改短信模版的优先级. userId:{} upId:{} downId:{} operateType:{}", userId, upId, downId, operateType);

        // 交换upid 和 downId的优先级
        List<AdminSmsTemplateSettingsInfo.SmsSettings> smsSettings = crowdfundingSendMsgTemplateDao.selectTemplateByIds(Lists.newArrayList(upId, downId));
        AdminSmsTemplateSettingsInfo.SmsSettings upSettings = null;
        AdminSmsTemplateSettingsInfo.SmsSettings downSettings = null;
        if (smsSettings.get(0).getId() == upId) {
            upSettings = smsSettings.get(0);
            downSettings = smsSettings.get(1);
        } else {
            upSettings = smsSettings.get(1);
            downSettings = smsSettings.get(0);
        }

        crowdfundingSendMsgTemplateDao.updatePriorityById(upSettings.getId(), downSettings.getPriority());
        crowdfundingSendMsgTemplateDao.updatePriorityById(downSettings.getId(), upSettings.getPriority());

        boolean isUpId = (operateType == AdminSmsTemplateSettingsInfo.SmsOperateType.UP.getCode() ?
                true : false);
        addSmsOperateLog(isUpId ? upId : downId, userId, isUpId ? AdminSmsTemplateSettingsInfo.SmsOperateType.UP.getCode()
                : AdminSmsTemplateSettingsInfo.SmsOperateType.DOWN.getCode());
        return 0;
    }


    @Override
    public List<AdminSmsTemplateSettingsInfo.SmsSettingsRecordView> selectRecordByTemplateId(int smsTemplateSettingsId) {

        List<AdminSmsTemplateSettingsInfo.SmsSettingsRecord> recordList = crowdfundingSendMsgTemplateDao.selectRecordByTemplateId(smsTemplateSettingsId);
        return convertToRecordViews(recordList);
    }


    private List<AdminSmsTemplateSettingsInfo.SmsSettingsRecordView> convertToRecordViews(
            List<AdminSmsTemplateSettingsInfo.SmsSettingsRecord> recordList) {

        List<AdminSmsTemplateSettingsInfo.SmsSettingsRecordView> viewList = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(recordList)) {
            for (AdminSmsTemplateSettingsInfo.SmsSettingsRecord record : recordList) {
                AdminSmsTemplateSettingsInfo.SmsSettingsRecordView view = new
                        AdminSmsTemplateSettingsInfo.SmsSettingsRecordView();

                view.setOperateName(StringUtils.trimToEmpty(queryUserOrgName(
                        Lists.newArrayList(record.getOperatorId())).get(record.getOperatorId())));
                view.setOperateTypeName(AdminSmsTemplateSettingsInfo
                        .SmsOperateType.parseByCode(record.getOperateType()).getDesc());
                view.setUpdateTime(record.getUpdateTime());
                viewList.add(view);
            }
        }

        return viewList;
    }

    private Map<Integer, String> queryUserOrgName(List<Integer> userIds) {
        AuthRpcResponse<Map<Integer, List<AdminOrganization>>> userRoleModel = orgClientV1.getUserOrgs(userIds);
        AuthRpcResponse<List<AdminUserAccountModel>> userAccounts = accountClientV1.getUserAccountsByIds(userIds);

        Map<Integer, String> userIdToRoleNames = Maps.newHashMap();
        if (userAccounts == null || CollectionUtils.isEmpty(userAccounts.getResult())) {
            return userIdToRoleNames;
        }

        Map<Integer, List<AdminOrganization>> userId2OrgMap = (userRoleModel == null || userRoleModel.getResult() == null
                ? Maps.newHashMap() : userRoleModel.getResult());
        for (AdminUserAccountModel accountModel : userAccounts.getResult()) {
            List<AdminOrganization> orgList =  userId2OrgMap.get(accountModel.getId());
            List<String> roleNames = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(orgList)) {
                orgList.forEach(item->{roleNames.add(item.getName());});
            }

            roleNames.add(accountModel.getName());
            userIdToRoleNames.put(accountModel.getId(), Joiner.on("-").join(roleNames));
        }

        return userIdToRoleNames;
    }

    private void addSmsOperateLog(int smsTemplateId, int operatorId, int operateType) {

        AdminSmsTemplateSettingsInfo.SmsSettingsRecord record = new AdminSmsTemplateSettingsInfo.SmsSettingsRecord();
        record.setSmsTemplateSettingsId(smsTemplateId);
        record.setOperateType(operateType);
        record.setOperatorId(operatorId);
        crowdfundingSendMsgTemplateDao.addSmsTemplateRecord(record);
    }

    @Override
    public List<AdminSmsTemplateSettingsInfo.SmsContentResult> selectAuthSmsContent(int userId) {

        List<AdminSmsTemplateSettingsInfo.SmsContentResult> contentResults = Lists.newArrayList();
        List<Integer> smsGroups = getAllSmsAuthGroup(userId, 0);
        if (CollectionUtils.isEmpty(smsGroups)) {
            return contentResults;
        }

        obtainAuthSmsContent(smsGroups, contentResults);

        return contentResults;
    }

    public void obtainAuthSmsContent(List<Integer> smsGroups, List<AdminSmsTemplateSettingsInfo.SmsContentResult> contentResults){
        List<AdminSmsTemplateSettingsInfo.SmsSettings> settingLists = crowdfundingSendMsgTemplateDao.selectValidTemplateByGroups(smsGroups);
        settingLists = settingLists.stream().sorted(Comparator.comparingInt(AdminSmsTemplateSettingsInfo.SmsSettings::getPriority)).collect(Collectors.toList());

        Map<Integer, List<AdminSmsTemplateSettingsInfo.SmsContent>> groupToTemplateSettings = Maps.newTreeMap();
        for (AdminSmsTemplateSettingsInfo.SmsSettings settings : settingLists) {
            List<AdminSmsTemplateSettingsInfo.SmsContent> smsContentList = groupToTemplateSettings.get(settings.getSmsGroup());
            if (smsContentList == null) {
                smsContentList = Lists.newArrayList();
                groupToTemplateSettings.put(settings.getSmsGroup(), smsContentList);
            }
            List<SmsTemplate> smsTemplates = getTemplateByModelNum(settings.getModelNum());
            if (CollectionUtils.isEmpty(smsTemplates) || smsTemplates.size() > 1) {
                log.warn("短信模版为空或者大于两个 modelNum:{}", settings.getModelNum());
                continue;
            }
            smsContentList.add(new AdminSmsTemplateSettingsInfo.SmsContent(settings.getModelNum(), smsTemplates.get(0).getText(),
                    smsTemplates.get(0).getTitle()));
        }

        groupToTemplateSettings.entrySet().stream().forEach((item)->{contentResults
                .add(new AdminSmsTemplateSettingsInfo.SmsContentResult(item.getKey(), item.getValue()));});
    }

    @Override
    public AdminSmsTemplateSettingsInfo.SmsTemplateView selectTemplateByModelNum(String modelNum) {
        List<SmsTemplate> templateList = getTemplateByModelNum(modelNum);

        if (CollectionUtils.isEmpty(templateList) || templateList.size() > 1) {
            log.error("短信模版为空或者大于两个 modelNum:{}", modelNum);
            return null;
        }

        AdminSmsTemplateSettingsInfo.SmsTemplateView result = new AdminSmsTemplateSettingsInfo.SmsTemplateView();
        result.setModelNum(modelNum);
        result.setTemplateContent(templateList.get(0).getText());
        result.setTemplateTitle(templateList.get(0).getTitle());

        return result;
    }

    @Override
    public List<AdminSmsTemplateSettingsInfo.SmsTemplateView> selectTemplateByParam(int smsGroup, String templateTitle,
                                                                                    Integer operatorId,
                                                                             Integer dataStatus) {

        List<AdminSmsTemplateSettingsInfo.SmsTemplateView> result = Lists.newArrayList();
        List<Integer> dataStatusList = Lists.newArrayList();
        if (dataStatus != null) {
            dataStatusList.add(dataStatus);
        } else {
            dataStatusList.addAll(Lists.newArrayList(AdminSmsTemplateSettingsInfo.SmsDataType.ENABLE.getCode(),
                    AdminSmsTemplateSettingsInfo.SmsDataType.DISABLE.getCode()));
        }
        List<AdminSmsTemplateSettingsInfo.SmsSettings> smsSettings = crowdfundingSendMsgTemplateDao
                .selectTemplateByParam(smsGroup, null, operatorId, dataStatusList);

        if (CollectionUtils.isEmpty(smsSettings)) {
            return result;
        }

        smsSettings.sort((o1, o2)->{if (o1.getIsDelete() != o2.getIsDelete()) {return o1.getIsDelete() - o2.getIsDelete();}
         else {return o1.getPriority() - o2.getPriority();} });

        Map<Integer, String> userIdMap =  queryUserOrgName(smsSettings.stream().map(AdminSmsTemplateSettingsInfo.SmsSettings::getOperatorId).collect(Collectors.toList()));

        for (AdminSmsTemplateSettingsInfo.SmsSettings settings : smsSettings) {
            List<SmsTemplate> smsTemplates = getTemplateByModelNum(settings.getModelNum());
            if (CollectionUtils.isEmpty(smsTemplates) || smsTemplates.size() > 1) {
                log.error("短信模版为空或者大于两个 modelNum:{}", settings.getModelNum());
                continue;
            }

            if (StringUtils.isBlank(templateTitle) || smsTemplates.get(0).getTitle().contains(templateTitle)) {
                result.add(buildTemplateSettingView(settings, smsTemplates.get(0), userIdMap));
            }
        }

        return result;
    }

    private AdminSmsTemplateSettingsInfo.SmsTemplateView
    buildTemplateSettingView(AdminSmsTemplateSettingsInfo.SmsSettings settings, SmsTemplate smsTemplate,
                             Map<Integer, String> userIdMap) {
        AdminSmsTemplateSettingsInfo.SmsTemplateView view = new AdminSmsTemplateSettingsInfo.SmsTemplateView();
        view.setId(settings.getId());
        view.setModelNum(settings.getModelNum());
        view.setTemplateContent(smsTemplate.getText());
        view.setTemplateTitle(smsTemplate.getTitle());
        view.setIsDelete(settings.getIsDelete());
        view.setOperatorId(settings.getOperatorId());
        view.setUpdateTime(settings.getUpdateTime());
        view.setOperatorName(userIdMap.get(settings.getOperatorId()));

        return view;
    }

    @Override
    public List<SmsTemplate> getTemplateByModelNum(String modelNum) {
        long now = System.currentTimeMillis();
        List<SmsTemplate> smsTemplates = smsTemplateClient.getTemplateByModelNum(modelNum);
        log.info("调用消息系统查询短信模版 modelNum:{} result:{} cost:{}", modelNum, smsTemplates,
                System.currentTimeMillis() - now);

        return smsTemplates;
    }

    @Override
    public List<AdminSmsTemplateSettingsInfo.SmsContentResult> getGroupContentList(int smsGroupCode) {
        List<Integer> smsGroups =  Arrays.asList(smsGroupCode);
        List<AdminSmsTemplateSettingsInfo.SmsContentResult> contentResults = Lists.newArrayList();
        obtainAuthSmsContent(smsGroups, contentResults);
        return contentResults;
    }

}
