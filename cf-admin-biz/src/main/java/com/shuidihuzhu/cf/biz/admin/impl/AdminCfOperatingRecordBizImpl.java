package com.shuidihuzhu.cf.biz.admin.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.admin.AdminCfOperatingRecordBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfOperatingRecordDao;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.Duration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by ahrievil on 2017/6/29.
 */
@Service
public class AdminCfOperatingRecordBizImpl implements AdminCfOperatingRecordBiz {

    @Autowired
    private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;
    @Autowired
    private AdminCfOperatingRecordDao adminCfOperatingRecordDao;

    @Override
    public CfOperatingRecord getLastOneByType(String infoUuid, CfOperatingRecordEnum.Type type) {
        return this.crowdfundingOperationDelegate.getLastOneByType(infoUuid, type);
    }

    @Override
    public List<CfOperatingRecord> getLastOneByInfoUUidList(List<String> infoUuids, CfOperatingRecordEnum.Type type) {
        if (CollectionUtils.isEmpty(infoUuids)) {
            return Lists.newArrayList();
        }
        return this.adminCfOperatingRecordDao.getLastOneByInfoUUidList(infoUuids, type == null ? 0 : type.getCode());
    }

    @Override
    public List<CfOperatingRecord> getCapitalListByOperationIds(List<Long> operationIds) {
        if (CollectionUtils.isEmpty(operationIds)) {
            return Lists.newArrayList();
        }
        return adminCfOperatingRecordDao.getCapitalListByOperationIds(operationIds);
    }

    @Override
    public List<CfOperatingRecord> getListByInfoUuid(String infoUuid) {
        if (StringUtils.isBlank(infoUuid)) {
            return Lists.newArrayList();
        }
        return adminCfOperatingRecordDao.getListByInfoUuid(infoUuid);
    }

    @Override
    public List<CfOperatingRecord> getByCreateTime(Timestamp startTime, Timestamp endTime, List<CfOperatingRecordEnum.Type> types) {
        List<Integer> codes = null;
        if (CollectionUtils.isNotEmpty(types)) {
            codes = Lists.newArrayListWithCapacity(types.size());
            for (CfOperatingRecordEnum.Type type : types) {
                codes.add(type.getCode());
            }
        }
        return this.adminCfOperatingRecordDao.getByCreateTime(startTime, endTime, codes);
    }

    @Override
    public CfOperatingRecord getByInfoUuidAndType(String infoUuid, CfOperatingRecordEnum.Type typeEnum) {
        if (StringUtils.isEmpty(infoUuid) || null == typeEnum) {
            return  null;
        }
        return this.adminCfOperatingRecordDao.getByInfoUuidAndType(infoUuid, typeEnum.getCode());
    }

    @Override
    public List<CfOperatingRecord> getAllOperateByInfoUuidAndType(String infoUuid, int type) {
        if (StringUtils.isEmpty(infoUuid)) {
            return  null;
        }
        return this.adminCfOperatingRecordDao.getAllOperateByInfoUuidAndType(infoUuid, type);
    }

    @Override
    public List<CfOperatingRecord> getAllOperateByInfoUuidAndTypes(String infoUuid, List<CfOperatingRecordEnum.Type> types) {
        if (StringUtils.isEmpty(infoUuid) || CollectionUtils.isEmpty(types)) {
            return Lists.newArrayList();
        }
        List<Integer> integerList = types.stream().filter(item -> null != item)
                .map(CfOperatingRecordEnum.Type::getCode)
                .collect(Collectors.toList());
        return this.adminCfOperatingRecordDao.getAllOperateByInfoUuidAndTypes(infoUuid, integerList);
    }

    @Override
    public int deleteById(String infoUuid, long id) {
        if (StringUtils.isEmpty(infoUuid) || id <= 0) {
            return 0;
        }
        Integer count  = adminCfOperatingRecordDao.deleteById(infoUuid, id);
        return  null == count ? 0 : count;
    }

    @Override
    public List<String> selectCaseLastOperateByCreateTime(Long userSubmitBeginTime,
                                                          Long userSubmitEndTime,
                                                                      int type) {
        if (userSubmitBeginTime == null || userSubmitEndTime == null) {
            return Lists.newArrayList();
        }

        Set<String> currentInfoIds = Sets.newHashSet();
        long beginTime = userSubmitBeginTime;
        long endTime = Math.min(userSubmitEndTime + Duration.standardDays(1).getStandardSeconds(), userSubmitEndTime);
        while (beginTime <= userSubmitEndTime) {
            currentInfoIds.addAll(adminCfOperatingRecordDao.selectCaseLastOperateByCreateTime(beginTime, endTime, type));
            beginTime = endTime + 1;
            endTime = Math.min(beginTime + Duration.standardDays(1).getStandardSeconds(), userSubmitEndTime);
        }

        if (CollectionUtils.isEmpty(currentInfoIds)) {
            return Lists.newArrayList(currentInfoIds);
        }

        Set<String> hasOperator = Sets.newHashSet();

        hasOperator.addAll(adminCfOperatingRecordDao.selectOperateByCreateTimeAndInfoUuid(userSubmitEndTime, currentInfoIds, type));
        currentInfoIds.removeAll(hasOperator);
        return Lists.newArrayList(currentInfoIds);
    }
}
