package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.admin.util.StringCountUtils;
import com.shuidihuzhu.cf.biz.aiphoto.ImageWatermarkService;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.client.apipure.feign.CfFirstApproveMaterialFeignClient;
import com.shuidihuzhu.cf.client.apipure.model.crowdfunding.CfFirstApproveMaterialVO;
import com.shuidihuzhu.cf.client.base.page.v1.model.PaginationListVO;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.feign.CaseInfoApproveStageFeignClient;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCaseDetailsMsgDao;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfRepeatInfoDAO;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.finance.IFinanceDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.domain.caseinfo.CaseInfoApproveStageDO;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.enums.report.ReportPayMethodEnum;
import com.shuidihuzhu.cf.finance.model.vo.CfDrawCashApplyVo;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminCrowdfundingInfoView;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.service.AdminImageService;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.report.ReportScheduleService;
import com.shuidihuzhu.cf.store.enums.CfDomainEnum;
import com.shuidihuzhu.cf.store.model.AnalysisUrl;
import com.shuidihuzhu.cf.util.crowdfunding.CrowdfundingUtil;
import com.shuidihuzhu.cf.vo.approve.RecoverRepeatCaseVo;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.admin.model.AdminCaseDetailsMsg;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CfCaseSpecialPrePoseDetail;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class AdminCfRepeatInfoBizImpl implements AdminCfRepeatInfoBiz {
    @Resource
    private CfFirstApproveMaterialFeignClient cfFirstApproveMaterialFeignClient;

    @Autowired
    private AdminApproveService adminApproveService;

    @Autowired
    private ClewPreproseMaterialFeignClient clewPreproseMaterialFeignClient;

    @Autowired
    private AdminCaseDetailsMsgDao adminCaseDetailsMsgDao;

    @Autowired
    private AdminCfRepeatInfoDAO repeatInfoDAO;

    @Autowired
    private AdminCrowdfundingAuthorBiz cfAuthorBiz;

    @Autowired
    private IRiskDelegate riskDelegate;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Resource
    private SeaAccountClientV1 seaAccountClientV1;

    @Resource
    private ReportScheduleService reportScheduleService;

    @Autowired
    private AdminCrowdfundingInfoPayeeBiz cfPayeeBiz;

    @Autowired
    private AdminCrowdfundingInfoBiz cfInfoBiz;

    @Autowired
    private AdminCfInfoExtBiz cfInfoExtBiz;

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Autowired
    private AdminCfBaseInfoTemplateRecordBiz templateRecordBiz;

    @Autowired
    private MaskUtil maskUtil;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Autowired
    private AdminCrowdfundingAttachmentBiz cfAttachmentBiz;
    @Autowired
    private CaseInfoApproveStageFeignClient caseInfoApproveStageFeignClient;
    @Autowired
    private IFinanceDelegate financeDelegate;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private AdminCrowdfundingReportBiz adminCrowdfundingReportBiz;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Autowired
    private ImageWatermarkService watermarkService;
    @Resource
    private IRiskDelegate firstApproveBiz;
    @Autowired
    private ApplicationService applicationService;
    @Autowired
    private AdminImageService adminImageService;
    @Resource
    private CommonOperationRecordClient commonOperationClient;

    @Value("${apollo.repeat-case.not-allow-users:-1}")
    private String APOLLO_REPEAT_NO_ALLOW_USERS = "-1";

    private final static String LOG_PREFIX = "[重复或二次案例判断] ";
    private final static int BATCH_SIZE = 500;

    private final static Set<Integer> DIAGNOSIS_ATTACHMENT_TYPE = Sets.newHashSet(AttachmentTypeEnum.ATTACH_TREATMENT.value(),
            AttachmentTypeEnum.ATTACH_MEDICAL_RECORD_HOME.value(), AttachmentTypeEnum.ATTACH_PASS_HOSPITAL.value(),
            AttachmentTypeEnum.ATTACH_TREATMENT_NOTE.value(), AttachmentTypeEnum.ATTACH_INSPECTION_REPORT.value());

    @Override
    public List<AdminCfRepeatView> selectRepeatInfoViewByCaseId(Integer caseId) {

        List<AdminCfRepeatView> viewList = Lists.newArrayList();
        AdminCfRepeatInfo curRepeatInfo = findOrBuildRepeatInfo(caseId);
        if (curRepeatInfo ==  null) {
            return viewList;
        }

        Map<Integer, Set<Integer>> repeatCases = curRepeatInfo.getRepeatBaseInfoMap();
        if (MapUtils.isEmpty(repeatCases)) {
            log.info("{}, caseId:{} 没有重复的案例", LOG_PREFIX, caseId);
            return viewList;
        }

        Date curDate = new Date();

        // 添加第一个条目为当前案例
        CrowdfundingInfo currentInfo = cfInfoBiz.getFundingInfoById(caseId);
        AdminCfRepeatView currentView = buildRepeatView(currentInfo, AdminCfRepeatInfo.RepeatReason.DEFAULT.getCode(), curDate);
        // 前端判断-1 显示为 '--'
        currentView.setRepeatStatus(-1);
        currentView.setRepeatReason("--");
        viewList.add(currentView);

        // 所有有重复的情况
        for (Map.Entry<Integer, Set<Integer>> entry : repeatCases.entrySet()) {
            int viewStatus = entry.getKey();
            if (viewStatus == AdminCfRepeatInfo.RepeatReason.R7.getCode()
                    || viewStatus == AdminCfRepeatInfo.RepeatReason.R8.getCode()) {
                continue;
            }
            Collection<Integer> repeatCaseIds = entry.getValue();
            List<CrowdfundingInfo> repeatCfs = cfInfoBiz.getListByIds(new ArrayList<>(repeatCaseIds));
            for (CrowdfundingInfo cf : repeatCfs) {
                viewList.add(buildRepeatView(cf, viewStatus, curDate));
            }
        }
        return viewList;
    }

    @Override
    public PageInfo<AdminCfRepeatView> selectRepeatInfoPageByCaseId(Integer caseId, int current, int pageSize) {

        PageInfo<AdminCfRepeatView> currPageView = new PageInfo<>();
        AdminCfRepeatInfo curRepeatInfo = findOrBuildRepeatInfo(caseId);

        Set<Integer> totalCaseIds = Sets.newHashSet();
        Map<Integer, Set<Integer>> repeatCases = (curRepeatInfo == null || curRepeatInfo.getRepeatBaseInfoMap() == null)
                ? Maps.newHashMap() : curRepeatInfo.getRepeatBaseInfoMap();
        Map<Integer, Integer> caseId2RepeatInfo = Maps.newHashMap();
        for (Map.Entry<Integer, Set<Integer>> entry : repeatCases.entrySet()) {
            Integer repeatStatus = entry.getKey();
            if (repeatStatus == AdminCfRepeatInfo.RepeatReason.R7.getCode() ||
                    repeatStatus == AdminCfRepeatInfo.RepeatReason.R8.getCode()) {
                continue;
            }
            Collection<Integer> caseIds = entry.getValue();
            if (CollectionUtils.isNotEmpty(caseIds)) {
                totalCaseIds.addAll(Sets.newHashSet(caseIds));
                for (Integer i : caseIds) {
                    caseId2RepeatInfo.put(i, repeatStatus);
                }
            }
        }

        if (curRepeatInfo ==  null || CollectionUtils.isEmpty(totalCaseIds)) {
            log.info("{}, caseId:{} 没有重复的案例", LOG_PREFIX, caseId);
            return currPageView;
        }

        currPageView.setTotal(totalCaseIds.size());

        int startIndex = (current - 1) * pageSize;
        if (startIndex > totalCaseIds.size()) {
            return currPageView;
        }

        int endIndex = startIndex + pageSize > totalCaseIds.size() ? totalCaseIds.size() : startIndex + pageSize;
        List<Integer> currCaseIds = Lists.newArrayList(totalCaseIds);
        currCaseIds.sort(Comparator.reverseOrder());

        List<AdminCfRepeatView> viewList = Lists.newArrayList();

        Date curDate = new Date();

        // 第一页 添加第一个条目为当前案例
        if (current == 1) {
            CrowdfundingInfo currentInfo = cfInfoBiz.getFundingInfoById(caseId);
            AdminCfRepeatView currentView = buildRepeatView(currentInfo, AdminCfRepeatInfo.RepeatReason.DEFAULT.getCode(), curDate);
            // 前端判断-1 显示为 '--'
            currentView.setRepeatStatus(-1);
            currentView.setRepeatReason("--");
            currentView.setMobileMask(maskUtil.buildByDecryptPhone(currentView.getMobile()));
            currentView.setMobile(null);
            viewList.add(currentView);
        }

        List<CrowdfundingInfo> repeatCfs = cfInfoBiz.getListByIds(new ArrayList<>(currCaseIds.subList(startIndex, endIndex)));
        for (CrowdfundingInfo cf : repeatCfs) {
            viewList.add(buildRepeatView(cf, caseId2RepeatInfo.get(cf.getId()), curDate));
        }
        currPageView.setList(viewList);

        return currPageView;
    }

    // 给sea后台返回的案例重复的情况
    private AdminCfRepeatView buildRepeatView(CrowdfundingInfo cf, int viewStatus, Date curTime) {
        AdminCfRepeatView repeatview = new AdminCfRepeatView();
        repeatview.setCaseId(cf.getId());
        repeatview.setInfoId(cf.getInfoId());
        repeatview.setTitle(cf.getTitle());
        List<CfInfoExt> exts = cfInfoExtBiz.selectByInfoUuidList(Arrays.asList(cf.getInfoId()));
        if (CollectionUtils.isNotEmpty(exts)) {
            repeatview.setMobileMask(maskUtil.buildByDecryptPhone(findOriginatorIMobile(cf)));
        }
        List<CrowdfundingReport> crowdfundingReports = adminCrowdfundingReportBiz.getListByInfoId(cf.getId());

        boolean isEnd = curTime.after(cf.getEndTime());
        repeatview.setIfEnd(isEnd? "是" : "否");
        repeatview.setEnd(isEnd);
        repeatview.setPatientName(getPatientName(cf));
        repeatview.setTargetAmount(cf.getAmount()/100);
        repeatview.setAmount(cf.getAmount()/100);
        repeatview.setRealTargetAmount(cf.getTargetAmount()/100);
        repeatview.setAuditStatus(cf.getStatus() == null ? "": cf.getStatus().name());
        repeatview.setRaiseTime(cf.getCreateTime());
        repeatview.setContentCount(StringCountUtils.countHanZi(cf.getContent()));
        repeatview.setReportCount(crowdfundingReports.size());

        buildAdminCfRepeatView(repeatview, cf,crowdfundingReports);

        // 设置图片数量
        List<CrowdfundingAttachmentVo> attachmentsByType = crowdfundingDelegate.getAttachmentsByType(cf.getId(),
                AttachmentTypeEnum.ATTACH_CF);
        repeatview.setImageCount(CollectionUtils.size(attachmentsByType));

        Response<CfDrawCashApplyVo> applyVoResponse = financeDelegate.getApplyInfo(cf.getId());
        CfDrawCashApplyVo cashApplyVo = null;
        if (applyVoResponse.ok()) {
            cashApplyVo = applyVoResponse.getData();
        }
        if (cashApplyVo != null && cashApplyVo.getFinishTime() != null) {
            repeatview.setPayAccountTime(DateUtil.getDate2LStr(cashApplyVo.getFinishTime()));
        }
        AdminCfRepeatInfo.RepeatReason reason = AdminCfRepeatInfo.RepeatReason.valueOfCode(viewStatus);
        int repeatStatus = AdminCfRepeatView.RepeatReasonView.getReasonViewByRepeatReason(reason).getCode();
        repeatview.setRepeatStatus(repeatStatus);

        String desc = AdminCfRepeatInfo.RepeatReason.valueOfCode(viewStatus).getDesc();
        repeatview.setRepeatReason(desc);
        return repeatview;
    }

    private void buildAdminCfRepeatView(AdminCfRepeatView repeatview, CrowdfundingInfo cf,List<CrowdfundingReport> crowdfundingReports) {
        Response<WorkOrderVO> response = cfWorkOrderClient.getLastWorkOrderByTypes(cf.getId(), WorkOrderType.REPORT_TYPES);
        WorkOrderVO workOrderVO = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);
        if (Objects.nonNull(workOrderVO)) {
            AuthRpcResponse<String> authRpcResponse = seaAccountClientV1.getNameByLongUserId(workOrderVO.getOperatorId());
            String userName = Optional.ofNullable(authRpcResponse).map(AuthRpcResponse::getResult).orElse(StringUtils.EMPTY);
            repeatview.setReportOperateName(userName);
            repeatview.setReportHandleResult(workOrderVO.getHandleResult());
        }

        Response<CfCapitalAccount> capitalAccountResponse = financeDelegate.getCfCapitalAccountByInfoUuid(cf.getInfoId());
        CfCapitalAccount cfCapitalAccount = Optional.ofNullable(capitalAccountResponse).filter(Response::ok).map(Response::getData).orElse(null);
        if (Objects.nonNull(cfCapitalAccount)) {
            int drawCashAmount = (int) cfCapitalAccount.getDrawCashAmount();
            repeatview.setPaidAmount(drawCashAmount / 100);
        }

        ReportPayMethodEnum payMethodEnum = reportScheduleService.getPayMethodByCaseId(cf.getId());
        if (Objects.nonNull(payMethodEnum)) {
            repeatview.setReportMark(payMethodEnum.getCode());
        }

        if (repeatview.getReportCount() > 0) {
            List<Long> userIds = crowdfundingReports.stream().map(CrowdfundingReport::getUserId).collect(Collectors.toList());
            List<UserInfoModel> userInfoModels = userInfoServiceBiz.getUserInfoByUserIdBatch(userIds);
            log.info("获取用户信息 authRpcResponse:{}", JSON.toJSONString(userInfoModels));
            Map<Long, UserInfoModel> userInfoModelMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(userInfoModels)) {
                userInfoModelMap = userInfoModels.stream().collect(Collectors.toMap(UserInfoModel::getUserId, Function.identity(), (a, b) -> b));
            }
            List<AdminCfRepeatView.ReportInfo> reportInfoList = Lists.newArrayList();
            for (CrowdfundingReport crowdfundingReport : crowdfundingReports) {
                AdminCfRepeatView.ReportInfo reportInfo = new AdminCfRepeatView.ReportInfo();
                reportInfo.setContent(crowdfundingReport.getContent());

                UserInfoModel userInfoModel = userInfoModelMap.get(crowdfundingReport.getUserId());
                if (Objects.nonNull(userInfoModel)) {
                    reportInfo.setPhone(CrowdfundingUtil.getTelephoneMask(shuidiCipher.decrypt(userInfoModel.getCryptoMobile())));
                }
                reportInfoList.add(reportInfo);
            }
            repeatview.setReportInfoList(reportInfoList);
        }

    }

    private AdminCfRepeatInfo findOrBuildRepeatInfo(int caseId, CfCapitalAccount cfCapitalAccount) {
        AdminCfRepeatInfo cfRepeatInfo = repeatInfoDAO.selectByCaseId(caseId);

        if (cfRepeatInfo == null || cfRepeatInfo.getNeedUpdateBaseInfo() == AdminCfRepeatInfo.NEED_UPDATE) {
            handleCfMaterialRepeat(caseId, false, cfCapitalAccount);
            cfRepeatInfo = repeatInfoDAO.selectByCaseId(caseId);
            log.info("{}, caseId:{}计算重复二次案例repeatInfo:{}", LOG_PREFIX, caseId, cfRepeatInfo);
        } else if (needUpdateRepeatSummary(cfRepeatInfo)) {
            int summary = getRepeatSummaryByInfo(cfRepeatInfo.getRepeatBaseInfoMap());
            log.info("{}, caseId:{}计算重复二次案例repeatSummary:{}", LOG_PREFIX, caseId, summary);

            repeatInfoDAO.updateRepeatInfoByCaseId(caseId, cfRepeatInfo.getRepeatBaseInfo(), summary);
        }
        return cfRepeatInfo;
    }

    private AdminCfRepeatInfo findOrBuildRepeatInfo(int caseId) {
        return findOrBuildRepeatInfo(caseId, null);
    }

    @Override
    public AdminCfRepeatInfo buildRepeatInfo(int caseId) {
        return findOrBuildRepeatInfo(caseId, null);
    }

    private boolean needUpdateRepeatSummary(AdminCfRepeatInfo cfRepeatInfo) {
        if (cfRepeatInfo == null) {
            return false;
        }

        return StringUtils.isNotEmpty(cfRepeatInfo.getRepeatBaseInfo()) && !cfRepeatInfo.getRepeatBaseInfo().equals(
                JSON.toJSONString(Maps.newHashMap())) && cfRepeatInfo.getRepeatSummary() == 0;
    }

    @Override
    public Set<Integer> selectRepeatStatusByCaseId(int caseId) {
        return selectRepeatStatusByCaseId(caseId, null);
    }

    @Override
    public Set<Integer> selectRepeatStatusByCaseId(int caseId, CfCapitalAccount cfCapitalAccount) {
        AdminCfRepeatInfo cfRepeatInfo = findOrBuildRepeatInfo(caseId, cfCapitalAccount);

        Set<Integer> res = Sets.newHashSet();
        if (cfRepeatInfo == null) {
            return res;
        }

        Map<Integer, Set<Integer>> repeatCases = cfRepeatInfo.getRepeatBaseInfoMap();
        for (Integer reasonCode : repeatCases.keySet()) {
            res.add(AdminCfRepeatView.RepeatReasonView.getReasonViewByRepeatReason(
                    AdminCfRepeatInfo.RepeatReason.valueOfCode(reasonCode)).getCode());
        }
        return res.stream().collect(Collectors.toSet());
    }

    @Override
    public Set<String> selectRepeatDescByCaseId(int caseId, CfCapitalAccount cfCapitalAccount) {
        Set<Integer> repeatStatus = selectRepeatStatusByCaseId(caseId, cfCapitalAccount);
        if (CollectionUtils.isEmpty(repeatStatus)) {
            return Sets.newHashSet();
        }
        return repeatStatus.stream()
                .map(AdminCfRepeatView.RepeatReasonView::getReasonByCode)
                .map(AdminCfRepeatView.RepeatReasonView::getDesc)
                .collect(Collectors.toSet());
    }

    @Override
    public void handleCfMaterialRepeat(int caseId, boolean caseBaseInfoChange, CfCapitalAccount cfCapitalAccount) {
        try {
            log.info("{}, caseId:{} 开始计算重复二次", LOG_PREFIX, caseId);

            CrowdfundingInfo cfInfo = cfInfoBiz.getFundingInfoById(caseId);
            if (cfInfo == null) {
                log.info("{} caseId:{} 不能找到案例，不计算重复二次情况", LOG_PREFIX, caseId);
                return;
            }

            long userId = cfInfo.getUserId();
            if (APOLLO_REPEAT_NO_ALLOW_USERS.contains("#" + userId + "#")) {
                log.info("数据量太大测试用户不执行重复计算 caseId:{} userId:{}", caseId, userId);
                return;
            }

            StopWatch sp = new StopWatch("case重复情况判断caseId: " + caseId);
            sp.start("填充基本资料");

            AdminCfRepeatInfo.AdminCfMaterial cfMaterial = findCfMaterialByCaseId(cfInfo);
            sp.stop();

            sp.start("重复逻辑计算");
            Map<Integer, Set<Integer>> repeatRes = handleCaseRepeat(cfInfo, cfMaterial, cfCapitalAccount);
            sp.stop();

            AdminCfRepeatInfo curCaseRepeatInfo = repeatInfoDAO.selectByCaseId(caseId);
            if (curCaseRepeatInfo == null) {
                curCaseRepeatInfo = new AdminCfRepeatInfo();
                curCaseRepeatInfo.setCaseId(caseId);
                curCaseRepeatInfo.setRepeatDiagnosisInfo("");
                curCaseRepeatInfo.setRepeatBaseInfo("");
            }

            Set<Integer> relativeIds = getRelativeIds(curCaseRepeatInfo.getRepeatBaseInfoMap(), repeatRes);
            curCaseRepeatInfo.setRepeatBaseInfo(JSON.toJSONString(repeatRes));
            curCaseRepeatInfo.setNeedUpdateBaseInfo(AdminCfRepeatInfo.NO_NEED_UPDATE);
            curCaseRepeatInfo.setRepeatSummary(getRepeatSummaryByInfo(repeatRes));
            repeatInfoDAO.insertOrUpdate(curCaseRepeatInfo);

            sp.start("信息发生变更时，二次重复关系重新计算");
            if (caseBaseInfoChange && CollectionUtils.isNotEmpty(relativeIds)) {
                List<List<Integer>> batchCaseIds = Lists.partition(Lists.newArrayList(relativeIds), BATCH_SIZE);
                for (List<Integer> bCaseId : batchCaseIds) {
                    repeatInfoDAO.updateNeedUpdateByCaseIds(bCaseId, AdminCfRepeatInfo.NEED_UPDATE);
                }
            }
            sp.stop();
            log.info(sp.toString());

        } catch (Exception e) {
            log.error("AdminCfRepeatInfoBizImpl create report error caseId:{}", caseId, e);
        }
    }

    // 处理case 基本资料 变动的情形
    @Override
    public void handleCfMaterialRepeat(int caseId, boolean caseBaseInfoChange) {
        handleCfMaterialRepeat(caseId, caseBaseInfoChange, null);
    }

    private Set<Integer> getRelativeIds(Map<Integer, Set<Integer>> oldRes, Map<Integer, Set<Integer>> newRes) {
        Set<Integer> relativeIds = Sets.newHashSet();
        if (oldRes != null && oldRes.values() != null) {
            for (Collection t : oldRes.values()) {
                relativeIds.addAll(Sets.newHashSet(t));
            }
        }

        if (newRes != null && newRes.values() != null) {
            for (Collection t : newRes.values()) {
                relativeIds.addAll(Sets.newHashSet(t));
            }
        }
        return relativeIds;
    }

    // 查找案例的基本资料  发起人 患者 收款人
    @Override
    public AdminCfRepeatInfo.AdminCfMaterial findCfMaterialByCaseId(CrowdfundingInfo cfInfo) {

        AdminCfRepeatInfo.AdminCfMaterial cfMaterial = new AdminCfRepeatInfo.AdminCfMaterial();
        // 发起人 身份证 电话号码
        fillOriginatorId(cfInfo, cfMaterial);
        // 患者 身份证 电话号码
        fillPatientMaterial(cfInfo, cfMaterial);
        // 收款人 身份证
        fillPayeeIdCard(cfInfo, cfMaterial);
        log.info("{} caseId:{} 相关材料AdminCfMaterial:{}", LOG_PREFIX, cfInfo.getId(), cfMaterial);
        return cfMaterial;
    }

    @Override
    // 恢复筹款时获取重复案例
    public PaginationListVO<RecoverRepeatCaseVo> getRepeatCaseWhenRecovery(CrowdfundingInfo crowdfundingInfo, int current, int pageSize) {
        PaginationListVO<RecoverRepeatCaseVo> res = PaginationListVO.createEmpty();

        if (crowdfundingInfo == null || crowdfundingInfo.getId() <= 0) {
            return res;
        }
        int caseId = crowdfundingInfo.getId();
        AdminCfRepeatInfo.AdminCfMaterial cfMaterial = this.findCfMaterialByCaseId(crowdfundingInfo);
        if (cfMaterial.getPatientIdType() == 0) {
            cfMaterial.setPatientIdType(searchPatientIdType(caseId));
        }
        // 患者姓名重复
        Set<Integer> repeatNameCaseIdList = this.findRepeatPNameIds(cfMaterial);
        if (CollectionUtils.isEmpty(repeatNameCaseIdList)) {
            return res;
        }
        // 过滤掉已结束的案例
        List<CrowdfundingInfo> crowdfundingInfoList = cfInfoBiz.getListByIds(new ArrayList<>(repeatNameCaseIdList));
        repeatNameCaseIdList = crowdfundingInfoList.stream()
                .filter(info -> info.getEndTime() != null && info.getEndTime().compareTo(new Date()) > 0)
                .map(CrowdfundingInfo::getId)
                .collect(Collectors.toSet());
        // 患者身份证重复
        Set<Integer> repeatPIdCardIdList = this.findActualRepeatPIdCardIds(cfMaterial.getPatientIdCard());
        repeatPIdCardIdList = repeatPIdCardIdList.stream().filter(repeatNameCaseIdList::contains).collect(Collectors.toSet());
        // 患者出生证重复
        Set<Integer> repeatBornCardIdList = this.findRepeatBornCardIds(shuidiCipher.decrypt(cfMaterial.getPatientIdCard()));
        repeatBornCardIdList = repeatBornCardIdList.stream().filter(repeatNameCaseIdList::contains).collect(Collectors.toSet());
        // 发起人身份证重复
        Set<Integer> repeatOrIdCaseIdList = this.findRepeatOrIds(cfMaterial);
        repeatOrIdCaseIdList = repeatOrIdCaseIdList.stream().filter(repeatNameCaseIdList::contains).collect(Collectors.toSet());
        // 发起人手机号重复
        Set<Integer> repeatOrMobileCaseIdList = this.findRepeatOrMobileIds(cfMaterial);
        repeatOrMobileCaseIdList = repeatOrMobileCaseIdList.stream().filter(repeatNameCaseIdList::contains).collect(Collectors.toSet());

        // 代录入手机号重复
        Set<Integer> repeatPreposeMobileCaseIdList = new HashSet<>();
        Response<List<CfCaseSpecialPrePoseDetail>> response = clewPreproseMaterialFeignClient.getSpecialPrePoseDetail(Lists.newArrayList(repeatNameCaseIdList));
        List<CfCaseSpecialPrePoseDetail> prePoseDetailList = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());
        prePoseDetailList = prePoseDetailList.stream().filter(obj -> StringUtils.isNotEmpty(obj.getMobile())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(prePoseDetailList)) {
            Optional<CfCaseSpecialPrePoseDetail> optional = prePoseDetailList.stream()
                    .filter(r -> r.getCaseId() == caseId)
                    .findAny();

            if (optional.isPresent()) {
                CfCaseSpecialPrePoseDetail currentCasePrepose = optional.get();
                repeatPreposeMobileCaseIdList = prePoseDetailList.stream()
                        .filter(r -> r.getMobile().equals(currentCasePrepose.getMobile())
                                && r.getCaseId() != caseId)
                        .map(CfCaseSpecialPrePoseDetail::getCaseId)
                        .collect(Collectors.toSet());
            }
        }
        if (CollectionUtils.isEmpty(repeatOrIdCaseIdList)
                && CollectionUtils.isEmpty(repeatPIdCardIdList)
                && CollectionUtils.isEmpty(repeatOrMobileCaseIdList)
                && CollectionUtils.isEmpty(repeatPreposeMobileCaseIdList)
                && CollectionUtils.isEmpty(repeatBornCardIdList)) {
            return res;
        }

        Set<Integer> allCaseIds = Sets.newHashSet();
        allCaseIds.addAll(repeatPIdCardIdList);
        allCaseIds.addAll(repeatOrIdCaseIdList);
        allCaseIds.addAll(repeatPreposeMobileCaseIdList);
        allCaseIds.addAll(repeatBornCardIdList);
        allCaseIds.addAll(repeatOrMobileCaseIdList);
        allCaseIds.remove(caseId);
        List<Integer> allCaseIdList = new ArrayList<>(allCaseIds);

        crowdfundingInfoList = cfInfoBiz.getListByIds(allCaseIdList);
        int fromIndex = (current - 1) * pageSize;
        int endIndex = current * pageSize;
        // 过滤掉已结束的案例
        crowdfundingInfoList = crowdfundingInfoList.stream()
                .filter(info -> info.getEndTime() != null && info.getEndTime().compareTo(new Date()) > 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(crowdfundingInfoList)) {
            return res;
        }
        int total = crowdfundingInfoList.size();

        // 截取分页数量
        allCaseIdList = crowdfundingInfoList.stream().map(CrowdfundingInfo::getId).collect(Collectors.toList());
        allCaseIdList = allCaseIdList.subList(fromIndex, Math.min(allCaseIdList.size(), endIndex));
        List<Integer> finalAllCaseIdList = allCaseIdList;
        crowdfundingInfoList = crowdfundingInfoList.stream().filter(info -> finalAllCaseIdList.contains(info.getId())).collect(Collectors.toList());

        // 获取引导发起渠道
        Map<Integer, String> guideUserLaunchChannelMap = adminApproveService.getGuideUserLaunchChannelBatch(allCaseIdList);

        List<RecoverRepeatCaseVo> list = new ArrayList<>();
        for (CrowdfundingInfo cfInfo : crowdfundingInfoList) {
            int repeatCaseId = cfInfo.getId();

            RecoverRepeatCaseVo vo = new RecoverRepeatCaseVo();
            AdminCrowdfundingInfoView crowdfundingInfoView = new AdminCrowdfundingInfoView();
            crowdfundingInfoView.setInfoId(cfInfo.getInfoId());
            crowdfundingInfoView.setAmountInDouble(Math.max(0.0, Double.parseDouble(MoneyUtil.buildBalance(cfInfo.getAmount()))));
            crowdfundingInfoView.setCreateTime(cfInfo.getCreateTime());
            crowdfundingInfoView.setId(repeatCaseId);
            crowdfundingInfoView.setListGuideUserLaunchChannel(guideUserLaunchChannelMap.get(repeatCaseId));
            crowdfundingInfoView.setFinished(cfInfo.getEndTime() != null && cfInfo.getEndTime().compareTo(new Date()) <= 0);
            vo.setAdminCrowdfundingInfoView(crowdfundingInfoView);
            // 患者身份证重复
            if (repeatPIdCardIdList.contains(repeatCaseId)) {
                if (cfMaterial.getPatientIdType() == UserIdentityType.identity.getCode()) { // 当前案例是身份证
                    vo.setRepeatType(RecoverRepeatCaseVo.RepeatScenarioEnum.SCEN1.getCode());
                } else if (cfMaterial.getPatientIdType() == UserIdentityType.birth.getCode()) { // 当前案例是出生证
                    vo.setRepeatType(RecoverRepeatCaseVo.RepeatScenarioEnum.SCEN3.getCode());
                }
            }
            // 患者出生证重复
            else if (repeatBornCardIdList.contains(repeatCaseId)) {
                if (cfMaterial.getPatientIdType() == UserIdentityType.identity.getCode()) { // 当前案例是身份证
                    vo.setRepeatType(RecoverRepeatCaseVo.RepeatScenarioEnum.SCEN2.getCode());
                } else if (cfMaterial.getPatientIdType() == UserIdentityType.birth.getCode()) { // 当前案例是出生证
                    vo.setRepeatType(RecoverRepeatCaseVo.RepeatScenarioEnum.SCEN4.getCode());
                }
            }
            // 发起人身份证重复
            else if (repeatOrIdCaseIdList.contains(repeatCaseId)) {
                vo.setRepeatType(RecoverRepeatCaseVo.RepeatScenarioEnum.SCEN5.getCode());
            }
            // 发起人手机号重复
            else if (repeatOrMobileCaseIdList.contains(repeatCaseId)) {
                vo.setRepeatType(RecoverRepeatCaseVo.RepeatScenarioEnum.SCEN6.getCode());
            }
            // 代录入手机号重复
            else if (repeatPreposeMobileCaseIdList.contains(repeatCaseId)) {
                vo.setRepeatType(RecoverRepeatCaseVo.RepeatScenarioEnum.SCEN7.getCode());
            }

            list.add(vo);
        }
        res = PaginationListVO.create(list, current, pageSize, total);
        return res;
    }

    // 患者的名字和身份证
    @Override
    public void fillPatientMaterial(CrowdfundingInfo cfInfo, AdminCfRepeatInfo.AdminCfMaterial cfMaterial) {
        int caseId = cfInfo.getId();
        // 先找crowdfunding_author 表
        CrowdfundingAuthor patient = cfAuthorBiz.get(caseId);
        // 患者表里没有数据 就去首次筹款表里找记录
        if (patient == null) {
            CfFirsApproveMaterial param = new CfFirsApproveMaterial();
            param.setInfoId(caseId);

            List<CfFirsApproveMaterial> firstApproveMaterials = riskDelegate.getCfFirsApproveMaterialListByParam(param);
            // 首次筹款表里没有，去智能发起表里查找
            if (CollectionUtils.isEmpty(firstApproveMaterials)) {
                CfBaseInfoTemplateRecord recordParam = new CfBaseInfoTemplateRecord();
                recordParam.setInfoUuid(cfInfo.getInfoId());
                List<CfBaseInfoTemplateRecord> records = templateRecordBiz.selectByParam(recordParam);
                if (CollectionUtils.isNotEmpty(records)) {
                    cfMaterial.setPatientName(records.get(0).getAuthorName());
                } else {
                    log.info("{} caseId:{}, 不能找到患者信息", LOG_PREFIX, caseId);
                }
            } else {
                cfMaterial.setPatientName(firstApproveMaterials.get(0).getPatientRealName());
                cfMaterial.setPatientIdType(firstApproveMaterials.get(0).getPatientIdType());

                // 1身份证  2出生证 出生证没有加密
                if (firstApproveMaterials.get(0).getPatientIdType() == UserIdentityType.birth.getCode()) {
                    cfMaterial.setPatientIdCard(encryptWithOutExp(firstApproveMaterials.get(0).getPatientBornCard()));
                } else {
                    cfMaterial.setPatientIdCard(firstApproveMaterials.get(0).getPatientCryptoIdcard());
                }
            }
            return;
        }

        cfMaterial.setPatientName(patient.getName());
        cfMaterial.setPatientIdCard(patient.getCryptoIdCard());
    }

    private String getPatientName(CrowdfundingInfo cf) {

        CrowdfundingAuthor patient = cfAuthorBiz.get(cf.getId());
        if (patient != null) {
            return patient.getName();
        }

        CfFirsApproveMaterial param = new CfFirsApproveMaterial();
        param.setInfoId(cf.getId());
        List<CfFirsApproveMaterial> firstApproveMaterials = riskDelegate.getCfFirsApproveMaterialListByParam(param);
        if (CollectionUtils.isNotEmpty(firstApproveMaterials)) {
            return firstApproveMaterials.get(0).getPatientRealName();
        }

        CfBaseInfoTemplateRecord recordParam = new CfBaseInfoTemplateRecord();
        recordParam.setInfoUuid(cf.getInfoId());
        List<CfBaseInfoTemplateRecord> records = templateRecordBiz.selectByParam(recordParam);
        if (CollectionUtils.isNotEmpty(records)) {
            return records.get(0).getAuthorName();
        }

//        log.info("重复二次关系查询 caseId:{} 不能找到患者信息", cf.getId());
        return "";
    }

    // 收款人身份证号
    private void fillPayeeIdCard(CrowdfundingInfo cfInfo, AdminCfRepeatInfo.AdminCfMaterial cfMaterial) {

        // 不处理对公的账号
        if (cfInfo.getRelationType() == CrowdfundingRelationType.charitable_organization
                ||  cfInfo.getRelationType() == CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT) {
            log.info("案例的收款人类型不是个人类型,不处理. caseId:{}", cfInfo.getId());
            return;
        }

        List<CrowdfundingInfoPayee> payeeList = cfPayeeBiz.selectByInfoUuidList(Arrays.asList(cfInfo.getInfoId()));
        if (CollectionUtils.isNotEmpty(payeeList)) {
            cfMaterial.setPayeeIdCard(payeeList.get(0).getIdCard());
            return;
        }
        log.info("{} caseId:{}, 不能在crowdfunding_info_payee找到收款人信息 直接取cf中收款人信息:{}", LOG_PREFIX,
                cfInfo.getId(), cfInfo.getPayeeIdCard());
        cfMaterial.setPayeeIdCard(cfInfo.getPayeeIdCard());
    }

    // 发起人身份证号 手机号
    private void fillOriginatorId(CrowdfundingInfo cfInfo, AdminCfRepeatInfo.AdminCfMaterial cfMaterial) {

        // 发起人 身份证号    先找crowdfunding_id_case表  不能找到在去 首次审核的记录cf_first_approve_material表 里找
        CrowdfundingIdCase idCase = crowdfundingDelegate.getCrowdfundingIdCaseByInfoId(cfInfo.getId());
        if (idCase != null) {
            cfMaterial.setOriginatorIdCard(idCase.getCryptoIdCard());
        } else {
            CfFirsApproveMaterial param = new CfFirsApproveMaterial();
            param.setInfoId(cfInfo.getId());
            List<CfFirsApproveMaterial> cfFirsApproves = riskDelegate.getCfFirsApproveMaterialListByParam(param);
            if (CollectionUtils.isNotEmpty(cfFirsApproves)) {
                // 发起人 一定会有身份证
                if (StringUtils.isNotEmpty(cfFirsApproves.get(0).getSelfCryptoIdcard())) {
                    cfMaterial.setOriginatorIdCard(cfFirsApproves.get(0).getSelfCryptoIdcard());
                } else {
                    cfMaterial.setOriginatorIdCard(cfFirsApproves.get(0).getPatientCryptoIdcard());
                }
            } else {
                log.info("{} caseId:{}, infoId:{}  不能找到发起人身份证信息", LOG_PREFIX, cfInfo.getId(),
                        cfInfo.getInfoId());
            }
        }

        if (cfInfo.getUserId() == 0) {
            return;
        }
        cfMaterial.setUserId(cfInfo.getUserId());
        cfMaterial.setOriginatorIMobile(findOriginatorIMobile(cfInfo));
    }

    private String findOriginatorIMobile(CrowdfundingInfo cfInfo) {
        UserInfoModel userInfoModel = this.userInfoServiceBiz.getUserInfoByUserId(cfInfo.getUserId());

        if (userInfoModel == null || StringUtils.isEmpty(userInfoModel.getCryptoMobile())) {
            log.info("{} caseId:{}, userId:{} 不能在accountService找到发起人手机号信息", LOG_PREFIX, cfInfo.getId(),
                    cfInfo.getUserId());
            return "";
        }
        return shuidiCipher.decrypt(userInfoModel.getCryptoMobile());
    }

    private Map<Integer, Set<Integer>>  handleCaseRepeat(CrowdfundingInfo cfInfo, AdminCfRepeatInfo.AdminCfMaterial cfMaterial, CfCapitalAccount cfCapitalAccount) {

        Map<Integer, Set<Integer>> res = Maps.newHashMap();
        int caseId = cfInfo.getId();
//        if (!isNeedCalRepeat(cfInfo)) {
//            log.info("{} 案例结束且没有筹款，不计算重复二次的情况.caseId：{}", LOG_PREFIX, caseId);
//            return res;
//        }

        // 填充5种材料的重复情况
        List<Set<Integer>> repeatInfoList = Lists.newArrayList();
        repeatInfoList.add(findRepeatOrIds(cfMaterial));
        repeatInfoList.add(findRepeatOrMobileIds(cfMaterial));
        repeatInfoList.add(findRepeatPIdCardIds(cfMaterial));
        repeatInfoList.add(findRepeatPNameIds(cfMaterial));
        repeatInfoList.add(findRepeatPayeeIds(cfMaterial));

        for (int i = 0; i < repeatInfoList.size(); ++i) {
            repeatInfoList.get(i).remove(caseId);
        }

        Date now = new Date();
        for (int i = 0; i < repeatInfoList.size(); ++i) {
            Set<Integer> baseCaseIdSet = repeatInfoList.get(i);
            if (CollectionUtils.isEmpty(baseCaseIdSet)) {
                continue;
            }

            Map<Integer, CrowdfundingInfo> curCrowdfundingInfoMap = cfInfoBiz.getMapByIds(Lists.newLinkedList(baseCaseIdSet));
            // 判断 是否 其它几项材料也有重复的
            for (Integer curCaseId : baseCaseIdSet) {
                List<Integer> repeatIndexList = Lists.newArrayList(i + 1);
                for (int j = i + 1; j < repeatInfoList.size(); ++j) {
                    Set<Integer> curCaseIdSet = repeatInfoList.get(j);
                    if (curCaseIdSet.contains(curCaseId)) {
                        repeatIndexList.add(j+1);
                        curCaseIdSet.remove(curCaseId);
                    }
                }
                CrowdfundingInfo cfCase = curCrowdfundingInfoMap.get(curCaseId);

                // 得到具体是那种情况的重复
                String str = Joiner.on(",").join(repeatIndexList);
                // 如果当前caseId 与 主caseId 只有患者姓名相同 且两者都有身份证， 则不将重复关系入库
                // 4 表示只有患者姓名相等
                if (str.equals("4") && filterSamePatientName(cfInfo, cfCase)) {
                    continue;
                }
                // 查询结束时间 判断是重复还是二次
                if (cfCase == null || cfCase.getEndTime() == null) {
                    log.info("{} 计算具体的重复情况，caseId:{}不能找到筹款或endtime为null", LOG_PREFIX, curCaseId);
                    continue;
                }

//                if (!isNeedCalRepeat(cfCase, cfCapitalAccount)) {
//                    log.info("{} 案例结束且没有筹款，不计算重复二次的情况.caseId：{}", LOG_PREFIX, curCaseId);
//                    continue;
//                }

                int code = AdminCfRepeatInfo.RepeatReason.getCodeByRepeatContent(str, cfCase.getEndTime(), now);

                Set<Integer> repeatCaseIds = res.get(code);
                if (CollectionUtils.isEmpty(repeatCaseIds)) {
                    repeatCaseIds = Sets.newHashSet();
                }
                repeatCaseIds.add(curCaseId);
                res.put(code, repeatCaseIds);
            }
        }

        return res;
    }

    // 判断案例是否结束且没有筹到款
    private boolean isNeedCalRepeat(CrowdfundingInfo currentCf) {
        return isNeedCalRepeat(currentCf, null);
    }

    private boolean isNeedCalRepeat(CrowdfundingInfo currentCf, CfCapitalAccount cfAccount) {
        if (currentCf == null || currentCf.getEndTime() == null) {
            return false;
        }

        if (currentCf.getEndTime().before(new Date())) {

            if (cfAccount == null) {
                Response<CfCapitalAccount> cfCapitalAccountResponse =
                        financeDelegate.getCfCapitalAccountByInfoUuid(currentCf.getInfoId());
                if (cfCapitalAccountResponse.ok()) {
                    cfAccount = cfCapitalAccountResponse.getData();
                } else {
                    return true;
                }
            }

            if (currentCf.getAmount() == 0 || cfAccount == null ||
                    (cfAccount.getPayAmount() - cfAccount.getSingleRefundAmount() - cfAccount.getAllRefundAmount() == 0)) {
                return false;
            }
        }
        return true;
    }

    // 如果两个案例的都有患者身份证 且 只有患者姓名相等， 则不能算重复
    private boolean filterSamePatientName(CrowdfundingInfo cfBaseInfo, CrowdfundingInfo cfCompareInfo) {
        AdminCfRepeatInfo.AdminCfMaterial cfBaseMaterial = new AdminCfRepeatInfo.AdminCfMaterial();
        AdminCfRepeatInfo.AdminCfMaterial cfCompareMaterial = new AdminCfRepeatInfo.AdminCfMaterial();

        if (cfBaseInfo == null || cfCompareInfo == null) {
            log.info("{}, baseCaseId:{} 或 compareCaseId：{}不能找到筹款案例", LOG_PREFIX, cfBaseInfo, cfCompareInfo);
            return true;
        }

        fillPatientMaterial(cfBaseInfo, cfBaseMaterial);
        fillPatientMaterial(cfCompareInfo, cfCompareMaterial);

        boolean result=  StringUtils.isNotEmpty(cfBaseMaterial.getPatientIdCard())
                && StringUtils.isNotEmpty(cfCompareMaterial.getPatientIdCard());

        if (result) {
            log.info("{} 患者姓名相同，但是患者都有身份证 不计算重复二次关系。 baseCaseId：{} 身份证:{}," +
                            " compareCaseId:{} 身份证:{}", LOG_PREFIX, cfBaseInfo.getId(), cfBaseMaterial.getPatientIdCard(),
                    cfCompareInfo.getId(), cfCompareMaterial.getPatientIdCard());
        }

        return result;
    }

    // 发起人身份证重复
    @Override
    public Set<Integer> findRepeatOrIds(AdminCfRepeatInfo.AdminCfMaterial cfMaterial) {

        Set<Integer> repeatOIds = Sets.newHashSet();

        // 发起人身份证重复,需要处理身份证最后一位大小写的情况
        repeatOIds.addAll(findActualRepeatOrgIds(cfMaterial.getOriginatorIdCard()));
        repeatOIds.addAll(findActualRepeatOrgIds(getIdCardHandleUpper(cfMaterial.getOriginatorIdCard())));
        return repeatOIds;
    }

    // 发起人身份证重复 从发起人身份证表和首次审核表里 查找
    @Override
    public Set<Integer> findActualRepeatOrgIds(String OriginatorIdCard) {
        Set<Integer> repeatCaseIds = Sets.newHashSet();

        if (StringUtils.isEmpty(OriginatorIdCard)) {
            return repeatCaseIds;
        }

        repeatCaseIds.addAll(crowdfundingDelegate.getByCryptoIdCard(OriginatorIdCard)
                .stream().map(CrowdfundingIdCase::getCaseId).collect(Collectors.toList()));
        CfFirsApproveMaterial param = new CfFirsApproveMaterial();
        param.setSelfCryptoIdcard(OriginatorIdCard);
        List<CfFirsApproveMaterial> cfFirsApproves = riskDelegate.getCfFirsApproveMaterialListByParam(param);
        if (CollectionUtils.isNotEmpty(cfFirsApproves)) {
            List<Integer> cfACaseIds = cfFirsApproves.stream().map(CfFirsApproveMaterial::getInfoId).collect(Collectors.toList());
            for (Integer caseId : cfACaseIds) {
                if (crowdfundingDelegate.getCrowdfundingIdCaseByInfoId(caseId) == null) {
                    repeatCaseIds.add(caseId);
                }
            }
        }
        return repeatCaseIds;
    }

    // 发起人电话号码重复的
    @Override
    public Set<Integer> findRepeatOrMobileIds(AdminCfRepeatInfo.AdminCfMaterial cfMaterial) {

        Set<Integer> repeatOrNameIds = Sets.newHashSet();

        if (cfMaterial.getUserId() != 0) {
            repeatOrNameIds.addAll(cfInfoBiz.selectByUserIds(Sets.newHashSet(cfMaterial.getUserId())).stream()
                    .map(CrowdfundingInfo::getId).collect(Collectors.toList()));
        }
        return repeatOrNameIds;
    }

    // 查找患者身份证的重复
    @Override
    public Set<Integer> findRepeatPIdCardIds(AdminCfRepeatInfo.AdminCfMaterial cfMaterial) {

        Set<Integer> repeatPIds = Sets.newHashSet();
        // 患者身份证重复,需要处理身份证最后一位大小写的情况
        repeatPIds.addAll(findActualRepeatPIdCardIds(cfMaterial.getPatientIdCard()));
        repeatPIds.addAll(findActualRepeatPIdCardIds(getIdCardHandleUpper(cfMaterial.getPatientIdCard())));

        return repeatPIds;
    }

    // 患者身份证重复 从患者表和首次审核表里 查找
    @Override
    public Set<Integer> findActualRepeatPIdCardIds(String patientIdCard) {
        Set<Integer> repeatCaseIds = Sets.newHashSet();

        if (StringUtils.isEmpty(patientIdCard)) {
            return repeatCaseIds;
        }

        repeatCaseIds.addAll(cfAuthorBiz.selectByIdCardList(Arrays.asList(patientIdCard)).stream()
                .map(CrowdfundingAuthor::getCrowdfundingId).collect(Collectors.toSet()));

        //  去首次审核的表里找患者身份证
        CfFirsApproveMaterial param = new CfFirsApproveMaterial();
        param.setPatientCryptoIdcard(patientIdCard);
        List<Integer> cfFACaseIds = riskDelegate.getCfFirsApproveMaterialListByParam(param).stream()
                .map(CfFirsApproveMaterial::getInfoId).collect(Collectors.toList());
        // 如果 author表里已有 则已author表为准
        if (CollectionUtils.isNotEmpty(cfFACaseIds)) {
            Map<Integer, CrowdfundingAuthor> existMaterial = cfAuthorBiz.getByInfoIdList(cfFACaseIds);
            cfFACaseIds = cfFACaseIds.stream().filter(item -> !existMaterial.containsKey(item)).collect(Collectors.toList());
        }
        repeatCaseIds.addAll(cfFACaseIds);
        return repeatCaseIds;

    }

    // 患者出生证重复
    @Override
    public Set<Integer> findRepeatBornCardIds(String bornCard) {
        OperationResult<List<CfFirstApproveMaterialVO>> operationResult = cfFirstApproveMaterialFeignClient.getSingleByPatientBornCard(bornCard);

        List<CfFirstApproveMaterialVO> voList = Optional.ofNullable(operationResult)
                .filter(OperationResult::isSuccess)
                .map(OperationResult::getData)
                .orElse(Lists.newArrayList());

        return voList.stream().map(CfFirstApproveMaterialVO::getCaseId).collect(Collectors.toSet());
    }

    // 查找患者姓名的重复
    @Override
    public Set<Integer> findRepeatPNameIds(AdminCfRepeatInfo.AdminCfMaterial cfMaterial) {

        Set<Integer> repeatPNames = Sets.newHashSet();

        if (StringUtils.isNotEmpty(cfMaterial.getPatientName())) {

            repeatPNames.addAll(cfAuthorBiz.selectByNameList(Arrays.asList(cfMaterial.getPatientName())).stream()
                    .map(CrowdfundingAuthor::getCrowdfundingId).collect(Collectors.toSet()));

            // 去首次审核的表里 找患者姓名
            CfFirsApproveMaterial param = new CfFirsApproveMaterial();
            param.setPatientRealName(cfMaterial.getPatientName());
            List<Integer> cfFACaseIds = riskDelegate.getCfFirsApproveMaterialListByParam(param).stream()
                    .map(CfFirsApproveMaterial::getInfoId).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(cfFACaseIds)) {
                Map<Integer, CrowdfundingAuthor> existMaterial = cfAuthorBiz.getByInfoIdList(cfFACaseIds);
                cfFACaseIds = cfFACaseIds.stream().filter(item -> !existMaterial.containsKey(item)).collect(Collectors.toList());
                repeatPNames.addAll(cfFACaseIds);
            }

            // 智能发起表里找患者姓名
            CfBaseInfoTemplateRecord recordParam = new CfBaseInfoTemplateRecord();
            recordParam.setAuthorName(cfMaterial.getPatientName());
            List<String> infoUUids = templateRecordBiz.selectByParam(recordParam).stream()
                    .map(CfBaseInfoTemplateRecord::getInfoUuid).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(infoUUids)) {
                List<Integer> recordCaseIds = cfInfoBiz.getListByInfoUuIds(infoUUids).stream()
                        .map(CrowdfundingInfo::getId).collect(Collectors.toList());
                Map<Integer, CrowdfundingAuthor> existRMaterial = cfAuthorBiz.getByInfoIdList(recordCaseIds);
                Map<Integer, CfFirsApproveMaterial> existFa = riskDelegate.getMapByInfoIds(recordCaseIds);
                recordCaseIds = recordCaseIds.stream().filter(item -> !existRMaterial.containsKey(item) &&
                        !existFa.containsKey(item)).collect(Collectors.toList());
                repeatPNames.addAll(recordCaseIds);
            }
        }
        return repeatPNames;
    }

    // 收款人身份证的重复
    @Override
    public Set<Integer> findRepeatPayeeIds(AdminCfRepeatInfo.AdminCfMaterial cfMaterial) {

        Set<Integer> repeatPayeeIds = Sets.newHashSet();
        // 处理身份证尾号 大小写的情况。
        repeatPayeeIds.addAll(cfPayeeBiz.selectCaseIdsByPayeeIdCard(cfMaterial.getPayeeIdCard()));
        repeatPayeeIds.addAll(cfPayeeBiz.selectCaseIdsByPayeeIdCard(getIdCardHandleUpper(cfMaterial.getPayeeIdCard())));
        return repeatPayeeIds;
    }

    @Override
    public List<CrowdfundingAttachmentVo> getFundingAttachmentWithRepeatInfo(int caseId) {

        Response<CaseInfoApproveStageDO> stageInfoResp = caseInfoApproveStageFeignClient.getStageInfo(caseId);
        CaseInfoApproveStageDO stageInfo = stageInfoResp.getData();

        return buildAttachmentVo(caseId, stageInfo);
    }

    @Override
    public Map<Integer, List<CrowdfundingAttachmentVo>> getFundingAttachmentWithRepeatInfoBatch(List<Integer> caseIdList) {
        if (CollectionUtils.isEmpty(caseIdList)) {
            return new HashMap<>();
        }
        Map<Integer, List<CrowdfundingAttachmentVo>> res = new HashMap<>();
        Response<Map<Integer, CaseInfoApproveStageDO>> response = caseInfoApproveStageFeignClient.getStageInfoBatch(caseIdList);
        Map<Integer, CaseInfoApproveStageDO> data = new HashMap<>();
        if (response != null && response.getData() != null) {
            data = response.getData();
        }
        for (int caseId : caseIdList) {
            CaseInfoApproveStageDO caseInfoApproveStage = data.get(caseId);
            List<CrowdfundingAttachmentVo> attachmentVoList = buildAttachmentVo(caseId, caseInfoApproveStage);
            res.put(caseId, attachmentVoList);
        }
        return res;
    }

    @Override
    public List<CrowdfundingAttachmentVo> getFundingAttachmentWithRepeatInfoSupplement(int caseId) {
        CfFirsApproveMaterial material = firstApproveBiz.getCfFirsApproveMaterialByInfoId(caseId);
        if (Objects.isNull(material)) {
            return new ArrayList<>();
        }
        CrowdfundingAttachmentVo a = new CrowdfundingAttachmentVo();
        a.setUrl(material.getImageUrl());
        a.setType(AttachmentTypeEnum.ATTACH_FIRST_APPROVE_MEDICAL.value());
        Map<String, Integer> finalUrlTWatermark = cfAttachmentBiz.getImagesTWatermark(caseId, Lists.newArrayList(AttachmentTypeEnum.ATTACH_USER_STAGE_CF));
        a.setWatermark(watermarkService.getImageWatermark(finalUrlTWatermark, material.getImageUrl()));

        return Collections.singletonList(a);
    }

    private List<CrowdfundingAttachmentVo> buildAttachmentVo(int caseId, CaseInfoApproveStageDO stageInfo) {
        List<CrowdfundingAttachment> attachments = cfAttachmentBiz.getByParentId(caseId);

        // 将暂存图片替换发起图片
        Map<String, Integer> urlTWatermark = Maps.newHashMap();
        if (stageInfo != null) {
            // 有暂存图文
            attachments = attachments.stream()
                    .filter(v -> v.getType() != AttachmentTypeEnum.ATTACH_CF)
                    .collect(Collectors.toList());
            List<CrowdfundingAttachment> stageList = Arrays.stream(StringUtils.split(stageInfo.getImages(), ","))
                    .map(v -> {
                        CrowdfundingAttachment o = new CrowdfundingAttachment();
                        o.setUrl(v);
                        o.setType(AttachmentTypeEnum.ATTACH_CF);
                        return o;
                    }).collect(Collectors.toList());
            attachments.addAll(stageList);
            urlTWatermark = cfAttachmentBiz.getImagesTWatermark(caseId, Lists.newArrayList(AttachmentTypeEnum.ATTACH_USER_STAGE_CF));
        }

        if (CollectionUtils.isEmpty(attachments)) {
            return Lists.newArrayList();
        }

        Map<Integer, Integer> idTWatermark = cfAttachmentBiz.getImageWatermarkByAttachments(attachments);
        Map<String, Integer> finalUrlTWatermark = urlTWatermark;
        List<CrowdfundingAttachmentVo> attachmentVoList = attachments.stream().filter(item -> !cfAttachmentBiz.canNotShowInUgcManage(item.getType())).map(v -> {
            CrowdfundingAttachmentVo a = new CrowdfundingAttachmentVo();
            a.setUrl(adminImageService.convertSingleUrl(v.getUrl()));
            a.setType(v.getType().value());
            a.setId(v.getId());
            if (v.getId() != 0) {
                a.setWatermark(Objects.requireNonNullElse(idTWatermark.get(v.getId()), 0));
            } else {
                a.setWatermark(watermarkService.getImageWatermark(finalUrlTWatermark, v.getUrl()));
            }
            return a;
        }).collect(Collectors.toList());

        sortAttachments(attachmentVoList, caseId);
        return attachmentVoList;
    }

    // 对案例的图片排序
    private void sortAttachments(List<CrowdfundingAttachmentVo> attachments, int caseId) {
        sortAttachmentCf(attachments, caseId);
    }
    // 对图文信息的图片排序
    private void sortAttachmentCf(List<CrowdfundingAttachmentVo> attachments, int caseId) {
        moveHeadPictureAtFirst(attachments, caseId);
    }

    // 头图放到首位
    private void moveHeadPictureAtFirst(List<CrowdfundingAttachmentVo> attachments, int caseId) {
        AdminCaseDetailsMsg adminCaseDetailsMsg = adminCaseDetailsMsgDao.getByCaseId(caseId);
        if (adminCaseDetailsMsg == null) {
            return;
        }
        String headPictureUrl = adminCaseDetailsMsg.getHeadPictureUrl();
        if (StringUtils.isEmpty(headPictureUrl)) {
            return;
        }
        AnalysisUrl analysisUrl = AnalysisUrl.parse(headPictureUrl);
        analysisUrl.setHost(CfDomainEnum.COS_IMAGE.getDomain());
        String cosImageDomain = analysisUrl.toUrlString();
        analysisUrl.setHost(CfDomainEnum.COS_IMAGES.getDomain());
        String cosImagesDomain = analysisUrl.toUrlString();

        if (attachments.stream().noneMatch(obj ->
                obj.getType() == AttachmentTypeEnum.ATTACH_CF.value()
                        && (obj.getUrl().equals(cosImageDomain) || obj.getUrl().equals(cosImagesDomain))
        )) {
            return;
        }

        // 图文信息第一张图片
        CrowdfundingAttachmentVo firstAttachmentCf = attachments.stream()
                .filter(attachment -> attachment.getType() == AttachmentTypeEnum.ATTACH_CF.value()).findFirst()
                .get();
        int index = attachments.indexOf(firstAttachmentCf);
        if (index < 0) {
            return;
        }
        for (int i = 0;i < attachments.size(); i++) {
            CrowdfundingAttachmentVo attachment = attachments.get(i);
            if (StringUtils.equalsAny(attachment.getUrl(), cosImageDomain, cosImagesDomain)) {
                CrowdfundingAttachmentVo headPictureAttachment = attachments.remove(i);
                attachments.add(index, headPictureAttachment);
                return;
            }
        }
    }
    // 需要处理身份证最后一位的大小写, 大小写视为一致
    private String getIdCardHandleUpper(String encryptCard) {

        String idCard = shuidiCipher.decrypt(encryptCard);

        if (StringUtils.isEmpty(idCard)) {
            return "";
        }
        int lastIndex = idCard.length() - 1;
        char lastChar = idCard.charAt(lastIndex);

        if (Character.isDigit(lastChar)) {
            return "";
        }

        return  encryptWithOutExp(idCard.substring(0, lastIndex) + (Character.isUpperCase(lastChar) ? Character.toLowerCase(lastChar) :
                Character.toUpperCase(lastChar)));
    }

    public String encryptWithOutExp(String str) {
        try {
            return oldShuidiCipher.aesEncrypt(str);
        } catch (Exception e) {
            log.error("{}, 加密身份证:{}数据一样.", LOG_PREFIX, str, e);
        }
        return "";
    }

    private int getRepeatSummaryByInfo(Map<Integer, Set<Integer>> repeatRes) {

        int summary = 0;
        if (MapUtils.isEmpty(repeatRes)) {
            return summary;
        }

        Set<Integer> repeatSummarySet = Sets.newHashSet();
        for (Integer reasonCode : repeatRes.keySet()) {
            repeatSummarySet.add(AdminCfRepeatView.RepeatReasonView.getReasonViewByRepeatReason(
                    AdminCfRepeatInfo.RepeatReason.valueOfCode(reasonCode)).getSummaryCode());
        }

        for (Integer summaryCode : repeatSummarySet) {
            summary += summaryCode;
        }

        return summary;
    }

    @Override
    public AdminCfRepeatInfo selectByCaseId(int caseId) {
        return repeatInfoDAO.selectByCaseId(caseId);
    }

    // 查询身份证还是出生证
    private int searchPatientIdType(int caseId) {
        CfFirsApproveMaterial param = new CfFirsApproveMaterial();
        param.setInfoId(caseId);

        List<CfFirsApproveMaterial> firstApproveMaterials = riskDelegate.getCfFirsApproveMaterialListByParam(param);
        if (CollectionUtils.isEmpty(firstApproveMaterials)) {
            return 0;
        }

        return firstApproveMaterials.get(0).getPatientIdType();
    }

}
