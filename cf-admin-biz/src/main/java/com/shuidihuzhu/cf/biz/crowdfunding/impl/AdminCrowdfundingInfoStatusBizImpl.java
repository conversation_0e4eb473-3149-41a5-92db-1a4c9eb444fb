package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoStatusBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingInfoStatusDao;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.model.admin.AdminCreditSupplement;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by ahrievil on 2017/2/26.
 */
@Service
@RefreshScope
public class AdminCrowdfundingInfoStatusBizImpl implements AdminCrowdfundingInfoStatusBiz {
    @Autowired
    private AdminCrowdfundingInfoStatusDao crowdfundingInfoStatusDao;

    @Override
    public int add(CrowdfundingInfoStatus crowdfundingInfoStatus) {
        return this.crowdfundingInfoStatusDao.add(crowdfundingInfoStatus);
    }

    @Override
    public int updateByInfoId(String infoUuId, int type, CrowdfundingInfoStatusEnum status) {
        return this.crowdfundingInfoStatusDao.updateByType(infoUuId, type, status.getCode());
    }

    @Override
    public int updateByInfoId(String infoUuId, CrowdfundingInfoStatusEnum status) {
        return this.crowdfundingInfoStatusDao.update(infoUuId, status.getCode());
    }

    @Override
    public List<CrowdfundingInfoStatus> getByInfoUuid(String infoUuid) {
        if (StringUtils.isBlank(infoUuid)) {
            return Collections.emptyList();
        }
        return this.crowdfundingInfoStatusDao.getByInfoUuid(infoUuid);
    }

    @Override
    public List<CrowdfundingInfoStatus> getByCaseId(int caseId) {
        Preconditions.checkState(caseId != 0, "案例id不能为0");
        return crowdfundingInfoStatusDao.getByCaseId(caseId);
    }

    @Override
    public int changeTypeAndInfoUuid(CrowdfundingInfoStatus crowdfundingInfoStatus) {
        return crowdfundingInfoStatusDao.changeTypeAndInfoUuid(crowdfundingInfoStatus);
    }

    @Override
    public int updateByTypes(String infoUuid, CrowdfundingInfoStatusEnum crowdfundingInfoStatusEnum, Set<Integer> set) {
        if (CollectionUtils.isEmpty(set)) {
            return 0;
        }
        return crowdfundingInfoStatusDao.updateByTypes(infoUuid, crowdfundingInfoStatusEnum.getCode(), set);
    }

    @Override
    public CrowdfundingInfoStatus getByInfoUuidAndType(String infoUuid, int type) {
        return this.crowdfundingInfoStatusDao.getByInfoUuidAndType(infoUuid, type);
    }

    @Override
    public int updateByInfoUuidsAndType(int type, Set<String> infoUuidSet, int originalStatus, int targetStatus) {
        if (type < 0 || CollectionUtils.isEmpty(infoUuidSet) || originalStatus < 0 || targetStatus < 0) {
            return 0;
        }
        return this.crowdfundingInfoStatusDao.updateByInfoUuidsAndType(type, infoUuidSet, originalStatus, targetStatus);
    }

    @Override
    public List<CrowdfundingInfoStatus> getInfoUuidList(CrowdfundingInfoStatusEnum statusEnum,
                                                        CrowdfundingInfoDataStatusTypeEnum dataStatusTypeEnum, int offset, int size) {
        if (null == statusEnum || null == dataStatusTypeEnum || offset < 0 || size < 0) {
            return Lists.newArrayList();
        }
        return crowdfundingInfoStatusDao.getInfoUuidList(statusEnum.getCode(), dataStatusTypeEnum.getCode(), offset,
                size);
    }

    @Override
    public int countByTypeAndStatus(CrowdfundingInfoStatusEnum statusEnum, CrowdfundingInfoDataStatusTypeEnum dataStatusTypeEnum) {
        if (null == statusEnum || null == dataStatusTypeEnum) {
            return 0;
        }
        return crowdfundingInfoStatusDao.countByTypeAndStatus(statusEnum.getCode(), dataStatusTypeEnum.getCode());
    }

    @Override
    public List<String> selectByAttributes(AdminCreditSupplement houseCredit, AdminCreditSupplement carCredit,
                                           CrowdfundingInfoStatusEnum statusEnum,
                                           Date startTime, Date endTime, int offset, int size) {
        if (null == statusEnum || offset < 0 || size < 0) {
            return Lists.newArrayList();
        }
        return crowdfundingInfoStatusDao.selectByAttributes(houseCredit, carCredit, statusEnum.getCode(),
                startTime, endTime, offset, size);
    }


    @Override
    public int  getCountByAttributes(AdminCreditSupplement houseCredit, AdminCreditSupplement carCredit,
                                                              CrowdfundingInfoStatusEnum statusEnum,
                                                              Date startTime, Date endTime) {
        if ( null == statusEnum) {
            return 0;
        }
        return crowdfundingInfoStatusDao.getCountByAttributes(houseCredit, carCredit, statusEnum.getCode(),
                startTime, endTime);
    }

    @Override
    public int updateByInfoIdSet(Set<String> infoUuIdSet, CrowdfundingInfoDataStatusTypeEnum type, CrowdfundingInfoStatusEnum status) {
        if ( CollectionUtils.isEmpty(infoUuIdSet) || null == type || null == status){
            return 0;
        }
        return crowdfundingInfoStatusDao.updateByInfoIdSet(infoUuIdSet, type.getCode(), status.getCode());
    }

    @Override
    public List<CrowdfundingInfoStatus> getByInfoUuidsAndType(List<String> infoUuidList, CrowdfundingInfoDataStatusTypeEnum typeEnum) {
        if (CollectionUtils.isEmpty(infoUuidList) || null == typeEnum){
            return Lists.newArrayList();
        }
        return crowdfundingInfoStatusDao.getByInfoUuidsAndType(infoUuidList, typeEnum.getCode());
    }

    @Override
    public List<CrowdfundingInfoStatus> getByInfoUuids(List<String> infoUuidList) {
        if (CollectionUtils.isEmpty(infoUuidList)) {
            return Lists.newArrayList();
        }
        return crowdfundingInfoStatusDao.getByInfoUuids(infoUuidList);
    }

    @Override
    public List<CrowdfundingInfoStatus> getByInfoUuidsAndTypes(List<String> infoUuidList, List<CrowdfundingInfoDataStatusTypeEnum> typeEnums) {
        if (CollectionUtils.isEmpty(infoUuidList) || CollectionUtils.isEmpty(typeEnums)) {
            return Lists.newArrayList();
        }

        List<Integer> types = typeEnums.stream().map(CrowdfundingInfoDataStatusTypeEnum::getCode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(types)) {
            return Lists.newArrayList();
        }

        return crowdfundingInfoStatusDao.getByInfoUuidsAndTypes(infoUuidList, types);
    }
}
