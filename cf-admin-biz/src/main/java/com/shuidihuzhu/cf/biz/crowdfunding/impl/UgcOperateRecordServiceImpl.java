package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.service.crowdfunding.AdminUserOrgService;
import com.shuidihuzhu.cf.biz.crowdfunding.IUgcOperateRecordService;
import com.shuidihuzhu.cf.dao.crowdfunding.IUgcOperateRecordDAO;
import com.shuidihuzhu.cf.model.crowdfunding.AdminUgcOperateRecordDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/11/4 上午10:13
 * @desc
 */
@Service
public class UgcOperateRecordServiceImpl implements IUgcOperateRecordService {
    @Autowired
    private IUgcOperateRecordDAO ugcOperateRecordDAO;

    @Resource
    private SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    private AdminUserOrgService adminUserOrgService;

    @Override
    public int insert(AdminUgcOperateRecordDO ugcOperateRecordDO) {
        if(Objects.isNull(ugcOperateRecordDO)){
            return 0;
        }

        return ugcOperateRecordDAO.insert(ugcOperateRecordDO);
    }

    @Override
    public int insertUgcRecord(long caseId, int bizType, long bizId, int operateType, int adminUserId) {
        AuthRpcResponse<AdminUserAccountModel> userAccountRes = seaAccountClientV1.getValidUserAccountById(adminUserId);
        String adminUserName = 0 == userAccountRes.getCode() && Objects.nonNull(userAccountRes.getResult()) ? userAccountRes.getResult().getName() : "";

        AdminUgcOperateRecordDO ugcOperateRecordDO = new AdminUgcOperateRecordDO();
        ugcOperateRecordDO.setCaseId(Long.valueOf(caseId));
        ugcOperateRecordDO.setBizType(bizType);
        ugcOperateRecordDO.setBizId(bizId);
        ugcOperateRecordDO.setOperateType(operateType);
        ugcOperateRecordDO.setContent("");
        ugcOperateRecordDO.setOperatorId(adminUserId);
        ugcOperateRecordDO.setOperator(adminUserName);
        ugcOperateRecordDO.setDepartment(adminUserOrgService.getOrganization(adminUserId));

        return ugcOperateRecordDAO.insert(ugcOperateRecordDO);
    }

    @Override
    public List<AdminUgcOperateRecordDO> query(long caseId, int bizType, long bizId) {
        if(caseId <= 0 || bizType <= 0 || bizId <= 0){
            return Lists.newArrayList();
        }

        return ugcOperateRecordDAO.query(caseId, bizType, bizId);
    }
}
