package com.shuidihuzhu.cf.biz.admin;

import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;

import java.sql.Timestamp;
import java.util.List;
import java.util.Set;

/**
 * Created by ahrievil on 2017/6/29.
 */
public interface AdminCfOperatingRecordBiz {

    CfOperatingRecord getLastOneByType(String infoUuid, CfOperatingRecordEnum.Type type);

    List<CfOperatingRecord> getLastOneByInfoUUidList(List<String> infoUuids, CfOperatingRecordEnum.Type type);

    List<CfOperatingRecord> getCapitalListByOperationIds(List<Long> operationIds);

    List<CfOperatingRecord> getListByInfoUuid(String infoUuid);

    List<CfOperatingRecord> getByCreateTime(Timestamp startTime, Timestamp endTime, List<CfOperatingRecordEnum.Type> types);

    CfOperatingRecord getByInfoUuidAndType(String infoUuid, CfOperatingRecordEnum.Type typeEnum);

    List<CfOperatingRecord> getAllOperateByInfoUuidAndType(String infoUuid, int type);

    List<CfOperatingRecord> getAllOperateByInfoUuidAndTypes(String infoUuid, List<CfOperatingRecordEnum.Type> types);

    int deleteById (String infoUuid, long id);

    List<String> selectCaseLastOperateByCreateTime(Long userSubmitBeginTime,
                                                   Long userSubmitEndTime,
                                                         int type);
}
