package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.github.pagehelper.PageInfo;
import com.google.common.base.Strings;
import com.shuidi.weixin.common.util.StringUtils;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.OrganizationClientV1;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderBiz;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderRecordBiz;
import com.shuidihuzhu.cf.biz.admin.exception.ServiceRuntimeException;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminTaskUgcBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfFirstApproveOperatorBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.dao.crowdfunding.CfFirstApproveOperatorDao;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.admin.errors.WorkOrderErrorEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminUGCTask;
import com.shuidihuzhu.cf.enums.crowdfunding.EditMaterialType;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrderRecord;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.service.crowdfunding.SensitiveWordService;
import com.shuidihuzhu.cf.service.workorder.WorkOrderExtService;
import com.shuidihuzhu.cf.vo.crowdfunding.WorkOrderFirstApprove;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.joda.time.DateTime;
import org.joda.time.Hours;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Service
@Slf4j
public class CfFirstApproveOperatorBizImpl implements CfFirstApproveOperatorBiz {

    @Autowired
    private CfFirstApproveOperatorDao cfFirstApproveOperatorDao;
    @Autowired
    private AdminTaskUgcBiz taskUgcBiz;
    @Autowired
    private AdminWorkOrderBiz adminWorkOrderBiz;
    @Autowired
    private AdminWorkOrderRecordBiz workOrderRecordBiz;
    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @Resource
    private IRiskDelegate riskDelegate;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private MaskUtil maskUtil;


    @Resource
    private WorkOrderExtService workOrderExtService;

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Autowired
    private SensitiveWordService sensitiveWordService;

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private OrganizationClientV1 organizationClientV1;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Autowired(required = false)
    private Producer producer;

    @Override
    public CfFirstApproveOperator getFirstApproveOperatorById(int oporatorId) {
        CfFirstApproveOperator cfFirstApproveOperator = cfFirstApproveOperatorDao.getCfFirstApproveOperatorCountById(oporatorId);
        if (null == cfFirstApproveOperator) {
            cfFirstApproveOperatorDao.insertCfFirstApproveOperator(oporatorId, 10);
            return cfFirstApproveOperatorDao.getCfFirstApproveOperatorCountById(oporatorId);
        }
        return cfFirstApproveOperator;
    }

    @Override
    public void insertOperator(int operatorId, int count) {
        cfFirstApproveOperatorDao.insertCfFirstApproveOperator(operatorId, count);
    }

    @Override
    public void updateFirstApprovesCount(int operatorId, int count) {
        CfFirstApproveOperator operator = getFirstApproveOperatorById(operatorId);
        if (null == operator) {
            throw new ServiceRuntimeException(WorkOrderErrorEnum.FIRST_APPROVE_OPERATOR_NOT_EXIST);
        }
        cfFirstApproveOperatorDao.updateCfFirstApproveOperatorCount(operatorId, count);
    }


    @Override
    public void closeFirstUgc(int infoId, String failMsg, boolean pass, int userId) {

        log.info("closeUgc userId={} infoId={}, failMsg={}", userId, infoId, failMsg);
        AdminTaskUgc ugc = taskUgcBiz.selectFirstByCaseId(infoId);

        if (ugc == null){
            return;
        }
        if (StringUtils.isEmpty(failMsg)){
            failMsg = "审核通过";
        }

        // 工单结束的时候 存储工单附加信息 前置审核信息快照
        CfFirsApproveMaterial material = riskDelegate.getCfFirsApproveMaterialByInfoId(infoId);
        CrowdfundingInfo fundingInfo = crowdfundingDelegate.getFundingInfoById(infoId);
        int targetAmount = fundingInfo.getTargetAmount();
        workOrderExtService.saveFirstApproveWorkOrderExt(ugc.getWorkOrderId(), material, targetAmount);

        taskUgcBiz.updateResult(ugc.getId(), pass ? AdminUGCTask.Result.FIRST_APPROVE_PASS.getCode() : AdminUGCTask.Result.FIRST_APPROVE_REJECT.getCode());

        AdminWorkOrder workOrder = adminWorkOrderBiz.selectById(ugc.getWorkOrderId());
        workOrder.setOrderStatus(AdminWorkOrderConst.Status.SHUTDOWN.getCode());
        workOrder.setHandleResult(AdminWorkOrderConst.Result.HANDLE_SUCCESS.getCode());
        workOrder.setComment(failMsg);

        // 这里可能出现有特殊权限的人 处理了原本其它人已领取的工单
        if (workOrder.getOperatorId() != userId) {
            log.info("首次审核工单审核 初始userId:{} 审核userId：{}", workOrder.getOperatorId(), userId);
            workOrder.setOperatorId(userId);

            // 主要是为了处理特殊权限的人处理
            List<AdminWorkOrderRecord> assignRecords = workOrderRecordBiz.selectByWorkIdAndOperateTypes(Arrays.asList(ugc.getWorkOrderId()),
                    Arrays.asList(AdminWorkOrderConst.AdminWorkOrderOperateTypeEnum.FIRST_APPROVE_ORDER_ASSIGN.getCode()));
            boolean found = false;
            for (AdminWorkOrderRecord record : assignRecords) {
                if (record.getOperatorId() != userId || found) {
                    workOrderRecordBiz.deleteFirstUgcById(record.getId());
                } else {
                    found = true;
                }
            }

            if (!found) {
                // 插入一条领取的记录
                AdminWorkOrderRecord adminWorkOrderRecord = new AdminWorkOrderRecord(workOrder);
                adminWorkOrderRecord.setOrderStatus(AdminWorkOrderConst.Status.CREATED.getCode());
                adminWorkOrderRecord.setHandleResult(AdminWorkOrderConst.Result.INIT.getCode());
                adminWorkOrderRecord.setOperateType(AdminWorkOrderConst.AdminWorkOrderOperateTypeEnum.FIRST_APPROVE_ORDER_ASSIGN.getCode());
                log.info("首次审核工单审核 初始userId:{} 操作工单时，没有找到以前的领取记录，插入一条新领取记录", workOrder.getOperatorId());
                this.workOrderRecordBiz.insertOne(adminWorkOrderRecord);
            }
        }
        adminWorkOrderBiz.update(workOrder);

        AdminWorkOrderRecord adminWorkOrderRecord = new AdminWorkOrderRecord(workOrder);
        this.workOrderRecordBiz.insertOne(adminWorkOrderRecord);
    }

    // 明天8点， 后天8点
    @Override
    public void sendReCallMsgAfterReject(int caseId, long ugcTaskId, String tags) {

        try {
            DateTime now = new DateTime();
            long tomorrowEight = now.plusDays(1).withHourOfDay(8).withMinuteOfHour(0).getMillis();
            long afterTomorrowEight = now.plusDays(2).withHourOfDay(8).withMinuteOfHour(0).getMillis();
            if (!applicationService.isProduction()) {
                tomorrowEight = now.plusMinutes(2).getMillis();
                afterTomorrowEight = now.plusMinutes(3).getMillis();
            }

            Message message = Message.ofSchedule(MQTopicCons.CF, tags,
                    "" + System.nanoTime(), new RecallAfterRejectObject(caseId, ugcTaskId, 1),
                    tomorrowEight / 1000);
            MessageResult result = producer.send(message);
            log.info("前置审核驳回后第一天消息发送. msg：{}, result:{}", message, result);

            Message msg = Message.ofSchedule(MQTopicCons.CF, tags,
                    "next" + System.nanoTime(), new RecallAfterRejectObject(caseId, ugcTaskId, 2),
                    afterTomorrowEight / 1000);
            MessageResult messageResult = producer.send(msg);
            log.info("前置审核驳回后第二天消息发送. msg：{}, result:{}", msg, messageResult);
        } catch (Exception e) {
            log.error("前置审核驳回后召回三期消息发送异常.caseId:{}", caseId, e);
        }

    }

    public int getActualRejectCode(int code) {
        switch(code) {
            case 0:
                return EditMaterialType.DEFAULT.getCode();
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 10:
            case 11:
            case 12:
            case 13:
            case 18:
            case 19:
            case 20:
                return EditMaterialType.IMAGE_URL.getCode();

            case 7:
                return EditMaterialType.TARGET_AMOUNT_DESC.getCode();
            case 6:
            case 8:
            case 9:
                return EditMaterialType.ALL.getCode();
            case 14:
            case 15:
            case 16:
                return EditMaterialType.SUGGEST_END_CASE.getCode();
            case 17:
                return EditMaterialType.POVERTY.getCode();
        }
        return EditMaterialType.DEFAULT.getCode();
    }


    @Override
    public boolean addFirstApproveWhiteIdCard(String name, String idCard, String images, int reason, String otherReason, int operatorId) {

        log.info("添加前置审核身份证白名单:operatorId:{}, 添加name：{}, idCard:{}", operatorId, name, idCard);

        if (StringUtils.isBlank(name) || StringUtils.isBlank(idCard)) {
            return false;
        }

        String operator = getOrganization(operatorId) + getName(operatorId);
        IdcardVerifyWhiteList existIdCard = riskDelegate.getByNameAndIdcard(name, idCard);
        if (existIdCard == null) {
            return riskDelegate.addIdcardVerifyWhiteListV2(name, idCard, images, reason, otherReason, operator) == 1;
        }
        return true;
    }

    //获取名字
    private String getName(int userId){
        if (userId <= 0) {
            return "";
        }
        AuthRpcResponse<AdminUserAccountModel> accountResponse = seaAccountClientV1.getValidUserAccountById(userId);
        String name = "";
        if (accountResponse.isSuccess() && accountResponse.getResult() != null) {
            name = accountResponse.getResult().getName();
        }

        return name;
    }
    //获取组织
    private String getOrganization(int userId){
        if (userId <= 0) {
            return "";
        }
        AuthRpcResponse<String> rpcResponse = organizationClientV1.getUserRelationOrgName(userId);
        String organization = "";
        if (rpcResponse.isSuccess()) {
            organization = rpcResponse.getResult();
        }
        return org.apache.commons.lang3.StringUtils.isBlank(organization) ? organization : organization + "-";
    }

    @Override
    public List<IdcardVerifyWhiteListRecord> idCardWhiteListOperationRecord(int id) {
        return riskDelegate.whiteListOperationRecord(id);
    }

    @Override
    public PageInfo<IdcardVerifyWhiteList> queryAllWhiteIdCardList(int current, int pageSize, String name, String idCard) {
        if (StringUtils.isNotBlank(idCard)){
            idCard = oldShuidiCipher.aesEncrypt(idCard);
        }
        PageInfo<IdcardVerifyWhiteList> result = riskDelegate.selectAllWhiteIdCardList(current, pageSize, name, idCard);

        if (CollectionUtils.isNotEmpty(result.getList())) {
            int idCardPrefixLength = 6;
            for (IdcardVerifyWhiteList idcard : result.getList()) {
                try {
                    String decryptIdCard = shuidiCipher.decrypt(idcard.getCryptoIdCard());
                    if (StringUtils.isEmpty(decryptIdCard) || decryptIdCard.length() < 15) {
                        continue;
                    }
                    idcard.setIdCardMask(maskUtil.buildByDecryptStrAndType(decryptIdCard, DesensitizeEnum.IDCARD));
                } catch (Exception e) {
                    log.info("身份证解析异常", e);
                    idcard.setIdCard("");
                }
            }
        }
        return result;
    }

    @Override
    public int deleteFirstApproveWhiteIdById(int id) {
        return riskDelegate.deleteFirstApproveWhiteIdById(id);
    }

    @Override
    public String getGuideUserLaunchChannel(String serviceUserInfo) {

        if (StringUtils.isBlank(serviceUserInfo)) {
            return "";
        }

        int pos = serviceUserInfo.indexOf("-");
        return pos == -1 ? serviceUserInfo : serviceUserInfo.substring(0, pos);
    }

    @Override
    public void fillUgcBaseInfoDetail(WorkOrderFirstApprove firstApprove) {

        AdminTaskUgc adminTaskUgc = taskUgcBiz.selectLatelyTaskByCaseIdAndContent(firstApprove.getCaseId(),  AdminUGCTask.Content.BASE_INFO.getCode());
        if (adminTaskUgc == null) {
            log.error("前置审核初不能找到图文工单 caseId:{}", firstApprove.getCaseId());
            return;
        }

        firstApprove.setUgcBaseInfoOrderId(adminTaskUgc.getWorkOrderId());
        firstApprove.setUgcBaseInfoResult(adminTaskUgc.getResult());
    }

    @Override
    public void finishUgcBaseInfo(int userId, int actualCode, int caseId) {

        if (actualCode != EditMaterialType.SUGGEST_END_CASE.getCode()) {
            return ;
        }

        log.info("前置审核结束图文的工单 caseId:{}", caseId);
        AdminTaskUgc adminTaskUgc = taskUgcBiz.selectLatelyTaskByCaseIdAndContent(caseId,  AdminUGCTask.Content.BASE_INFO.getCode());
        if (adminTaskUgc == null) {
            log.info("前置审核-不能找到图文工单 caseId:{}", caseId);
            return;
        }

        sensitiveWordService.handleUgcWorkId(AdminUGCTask.Result.SUGGEST_STOP_CROWDFUNDING.getCode(),
                "" + adminTaskUgc.getWorkOrderId(), null, "停止筹款",
                0,
                null, null, null,userId);
    }

    @Override
    public boolean isChild(CfFirsApproveMaterial material) {
        if (material.getUserRelationType() == UserRelTypeEnum.SELF.getValue()) {
            return false;
        }
        //没有身份证就视为儿童
        return !material.isPatientHasIdCard();
    }

    @Override
    public String getIdcardWithWildchar(String cryptoIdcard) {
        if (org.apache.commons.lang.StringUtils.isEmpty(cryptoIdcard)) {
            return "";
        } else {
            String idcard = shuidiCipher.decrypt(cryptoIdcard);
            if (!org.apache.commons.lang.StringUtils.isEmpty(idcard) && idcard.length() >= 15) {
                String subStr = idcard.substring(0, idcard.length() - 4);
                return subStr + "****";
            } else {
                return "";
            }
        }
    }
}










