package com.shuidihuzhu.cf.delegate.saas;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.auth.saas.feign.PermissionFeignClient;
import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SeaAccountClientV1 {

    @Autowired
    private UserFeignClient userFeignClient;
    @Autowired
    private PermissionFeignClient permissionFeignClient;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Deprecated
    public AuthRpcResponse<AdminUserAccountModel> getValidUserAccountById(Integer userId) {
        Response<AuthUserDto> result = userFeignClient.getValidAuthUserById(Long.valueOf(userId));
        if (result == null || result.getData() == null) {
            return AuthRpcResponse.makeFailed(AdminErrorCode.SYSTEM_NOT_EXIST_RECORD_ERROR);
        }
        return AuthRpcResponse.makeSuccess(AdminUserAccountModel.convertFromAuthUser(result.getData(), shuidiCipher));
    }

    @Deprecated
    public AuthRpcResponse<List<AdminUserAccountModel>> getUserAccountsByIds(List<Integer> userIds) {
        Response<List<AuthUserDto>> result = userFeignClient.getAuthUserByIds(userIds.stream()
                .map(Long::valueOf).collect(Collectors.toList()));

        if (result == null || CollectionUtils.isEmpty(result.getData())) {
            return AuthRpcResponse.makeFailed(AdminErrorCode.SYSTEM_NOT_EXIST_RECORD_ERROR);
        }
        return AuthRpcResponse.makeSuccess(AdminUserAccountModel.convertFromAuthUserList(result.getData(), shuidiCipher));
    }

    @Deprecated
    public AuthRpcResponse<String> getNameByUserId(int userId) {
        Response<AuthUserDto> result = userFeignClient.getValidAuthUserById(Long.valueOf(userId));
        if (result == null || result.getData() == null) {
            return AuthRpcResponse.makeFailed(AdminErrorCode.SYSTEM_NOT_EXIST_RECORD_ERROR);
        }
        return AuthRpcResponse.makeSuccess(result.getData().getUserName());
    }

    public AuthRpcResponse<List<String>> getMisListBySeatNum(String seatNum) {

        Response<List<String>> res = userFeignClient.getUserByPhoneSeatNum(seatNum);
        if (res == null || CollectionUtils.isEmpty(res.getData())) {
            return AuthRpcResponse.makeFailed(AdminErrorCode.SYSTEM_NOT_EXIST_RECORD_ERROR);
        }
        return AuthRpcResponse.makeSuccess(res.getData());
    }

    //isCoverKey   mobile是否需要掩码处理 0否 1是
    public AuthRpcResponse<List<AdminUserAccountModel>> getUserAccountsByMisLike(String mis,Integer isCoverKey) {
        if (StringUtils.isBlank(mis)) {
            return AuthRpcResponse.makeFailed(AdminErrorCode.SYSTEM_PARAM_ERROR.getCode(),
                    AdminErrorCode.SYSTEM_PARAM_ERROR.getMsg());
        }

        if (isCoverKey == null) {
            return AuthRpcResponse.makeFailed(AdminErrorCode.SYSTEM_PARAM_ERROR.getCode(),
                    AdminErrorCode.SYSTEM_PARAM_ERROR.getMsg());
        }

        Response<List<AuthUserDto>> userDtoRes = userFeignClient.getByLoginNameLike(mis);
        if (userDtoRes == null || CollectionUtils.isEmpty(userDtoRes.getData())) {
            return AuthRpcResponse.makeFailed(AdminErrorCode.SYSTEM_NOT_EXIST_RECORD_ERROR);
        }
        List<AdminUserAccountModel> userAccountModels = AdminUserAccountModel.convertFromAuthUserList(userDtoRes.getData(),isCoverKey, shuidiCipher);
        if (isCoverKey == 0){
            for (AdminUserAccountModel model : userAccountModels){
                model.setMobile(shuidiCipher.decrypt(model.getMobile()));
            }
        }
        return AuthRpcResponse.makeSuccess(userAccountModels);
    }


    public AuthRpcResponse<List<AdminUserAccountModel>> listByUserNameLike(String userName) {
        if (StringUtils.isBlank(userName)) {
            return AuthRpcResponse.makeFailed(AdminErrorCode.SYSTEM_PARAM_ERROR.getCode(),
                    AdminErrorCode.SYSTEM_PARAM_ERROR.getMsg(), Lists.newArrayList());
        }
        Response<List<AuthUserDto>> response = userFeignClient.getValidAccountByUserNameLike(userName);
        log.debug("listByName param:{}, result:{}", userName, response);

        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getData())) {
            return AuthRpcResponse.makeFailed(AdminErrorCode.SYSTEM_NOT_EXIST_RECORD_ERROR);
        }

        return AuthRpcResponse.makeSuccess(AdminUserAccountModel.convertFromAuthUserList(response.getData(), shuidiCipher));
    }


    public AuthRpcResponse<List<AdminUserAccountModel>> getUserAccountsByNameLike(String name) {
        if (StringUtils.isBlank(name)) {
            return AuthRpcResponse.makeFailed(AdminErrorCode.SYSTEM_PARAM_ERROR.getCode(),
                    AdminErrorCode.SYSTEM_PARAM_ERROR.getMsg());
        }

        Response<List<AuthUserDto>> userDtoRes = userFeignClient.getValidAccountByNameLike(name);
        if (userDtoRes == null || CollectionUtils.isEmpty(userDtoRes.getData())) {
            return AuthRpcResponse.makeFailed(AdminErrorCode.SYSTEM_NOT_EXIST_RECORD_ERROR);
        }

        return AuthRpcResponse.makeSuccess(AdminUserAccountModel.convertFromAuthUserList(userDtoRes.getData(), shuidiCipher));
    }

    @Deprecated
    public AuthRpcResponse<String> getMisByUserId(Integer userId) {
        Response<AuthUserDto> result = userFeignClient.getValidAuthUserById(Long.valueOf(userId));
        if (result == null || result.getData() == null) {
            return AuthRpcResponse.makeFailed(AdminErrorCode.SYSTEM_NOT_EXIST_RECORD_ERROR);
        }

        return AuthRpcResponse.makeSuccess(result.getData().getLoginName());
    }

    public AuthRpcResponse<List<AdminUserAccountModel>> getUsersHavePermission(String permission) {

        Response<List<AuthUserDto>> res = permissionFeignClient.getUsersByPermission(permission);
        if (res == null || CollectionUtils.isEmpty(res.getData())) {
            return AuthRpcResponse.makeFailed(AdminErrorCode.SYSTEM_NOT_EXIST_RECORD_ERROR);
        }

        return  AuthRpcResponse.makeSuccess(AdminUserAccountModel.convertFromAuthUserList(res.getData(), shuidiCipher));
    }

    public AuthRpcResponse<AdminUserAccountModel> getValidUserAccountByLongId(long userId) {
        Response<AuthUserDto> response = userFeignClient.getValidAuthUserById(userId);
        AuthUserDto authUserDto = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);
        if (Objects.isNull(authUserDto)) {
            return AuthRpcResponse.makeFailed(AdminErrorCode.SYSTEM_NOT_EXIST_RECORD_ERROR);
        }
        return AuthRpcResponse.makeSuccess(AdminUserAccountModel.convertFromAuthUser(authUserDto, shuidiCipher));
    }

    public AuthRpcResponse<List<AdminUserAccountModel>> getValidUserAccountByLongIds(List<Long> userIds) {
        Response<List<AuthUserDto>> response = userFeignClient.getAuthUserByIds(userIds);
        List<AuthUserDto> authUserDtoList = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);
        if (Objects.isNull(authUserDtoList)) {
            return AuthRpcResponse.makeFailed(AdminErrorCode.SYSTEM_NOT_EXIST_RECORD_ERROR);
        }
        return AuthRpcResponse.makeSuccess(AdminUserAccountModel.convertFromAuthUserList(authUserDtoList, shuidiCipher));
    }


    public AuthRpcResponse<String> getNameByLongUserId(long userId) {
        Response<AuthUserDto> result = userFeignClient.getValidAuthUserById(userId);
        if (result == null || result.getData() == null) {
            return AuthRpcResponse.makeFailed(AdminErrorCode.SYSTEM_NOT_EXIST_RECORD_ERROR);
        }

        return AuthRpcResponse.makeSuccess(result.getData().getUserName());
    }

    public AuthRpcResponse<String> getMisByLongUserId(long userId) {
        Response<AuthUserDto> result = userFeignClient.getValidAuthUserById(userId);
        if (result == null || result.getData() == null) {
            return AuthRpcResponse.makeFailed(AdminErrorCode.SYSTEM_NOT_EXIST_RECORD_ERROR);
        }

        return AuthRpcResponse.makeSuccess(result.getData().getLoginName());
    }
}
