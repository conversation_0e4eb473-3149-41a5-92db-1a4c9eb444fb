package com.shuidihuzhu.cf.delegate.alarmbot;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.service.ApplicationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AlarmBotDelegateImpl implements AlarmBotDelegate {

    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    private ApplicationService applicationService;

    private static final String TAG = " alarm-bot ";

    @Override
    public void sendFlowReportAutoAssign(String workOrderId, int caseId, String classify, String problemContent, Integer userId) {
        log.info("sendFlowReportAutoAssign workOrderId={}, caseId={}, classify={}, problemContent={}, userId={}",
                workOrderId, caseId, classify, problemContent, userId);
        AuthRpcResponse<AdminUserAccountModel> r = seaAccountClientV1.getValidUserAccountById(userId);
        log.debug("sendFlowReportAutoAssign userInfo {}", r);
        if (!r.isSuccess() || r.getResult() == null) {
            log.info("error operatorId:{}, resp:{}", userId, JSON.toJSONString(r));
            return;
        }
        AdminUserAccountModel result = r.getResult();
        String mis = result.getMis();

        String temp = "【信息传递工单提醒】\n" +
                "工单编号：%s\n" +
                "案例id：%s\n" +
                "问题类型：%s\n" +
                "问题描述：%s";
        String text = String.format(temp, workOrderId, caseId > 0 ? caseId : "无", classify, problemContent);
        sentText(
                "02ce42ed-d431-4d4d-8eb4-95826ce146b9",
                text,
                new String[]{mis},
                null
        );
    }

    public void sentText(String key, String content, String[] mentioned_list, String[] mentioned_mobile_list) {
        log.info("{} sentText {} {} {} {}", TAG, key, content, mentioned_list, mentioned_mobile_list);
        if (applicationService.isDevelopment()) {
            content = "【测试环境】\n" + content;
        }
        AlarmBotService.sentText(key, content, mentioned_list, mentioned_mobile_list);
    }
}
