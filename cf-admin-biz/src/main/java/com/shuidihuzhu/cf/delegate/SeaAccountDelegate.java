package com.shuidihuzhu.cf.delegate;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.model.admin.AdminOrganization;
import com.shuidihuzhu.cf.model.admin.AdminOrganizationUserMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SeaAccountDelegate {

    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    private OrganizationDelegate organizationDelegate;

    public Map<Integer, String> getNameByUserIds(List<Integer> operatorIds) {
        AuthRpcResponse<List<AdminUserAccountModel>> authRpcResponse = seaAccountClientV1.getUserAccountsByIds(operatorIds);

        List<AdminUserAccountModel> list = Optional.ofNullable(authRpcResponse)
                .filter(AuthRpcResponse::isSuccess)
                .map(AuthRpcResponse::getResult)
                .orElse(Lists.newArrayList());

        return list.stream().collect(Collectors.toMap(AdminUserAccountModel::getId, AdminUserAccountModel::getName, (o1, o2) -> o2));
    }

    public String getNameByLongUserId(long operatorId) {
        return getNameByUserId((int) operatorId);
    }

    public String getNameByUserId(int operatorId) {
        if (operatorId <= 0) {
            return "";
        }
        AdminUserAccountModel userAccountModel = getAccountModel(operatorId);
        return Optional.ofNullable(userAccountModel)
                .map(AdminUserAccountModel::getName)
                .orElse("");
    }

    public String getMisByUserId(int operatorId) {
        AdminUserAccountModel userAccountModel = getAccountModel(operatorId);
        return Optional.ofNullable(userAccountModel)
                .map(AdminUserAccountModel::getMis)
                .orElse("");
    }

    public String getNameWithOrgByUserId(int operatorId) {
        AdminUserAccountModel userAccountModel = getAccountModel(operatorId);
        String name = Optional.ofNullable(userAccountModel).map(AdminUserAccountModel::getName).orElse("");

        String simpleOrganization = organizationDelegate.getSimpleOrganization(operatorId);

        return getNameWithOrg(name, simpleOrganization);
    }

    public String getNameWithOrgByLongUserId(long operatorId) {
        int userId = (int) operatorId;
        if (Long.parseLong(String.valueOf(userId)) != operatorId) {
            throw new RuntimeException("userId long 转 int后丢失精度");
        }
        return getNameWithOrgByUserId(userId);
    }

    public String getNameWithOrg(String name, String simpleOrganization) {
        List<String> e = Lists.newArrayList();
        if (StringUtils.isNotBlank(simpleOrganization)) {
            e.add(simpleOrganization);
        }

        if (StringUtils.isNotBlank(name)) {
            e.add(name);
        }
        return StringUtils.join(e, "-");
    }

    public String getOrg(int adminUserId) {
        return organizationDelegate.getSimpleOrganization(adminUserId);
    }

    public AdminUserAccountModel getAccountModel(int operatorId) {
        AuthRpcResponse<AdminUserAccountModel> r = seaAccountClientV1.getValidUserAccountById(operatorId);
        if (!r.isSuccess() || r.getResult() == null) {
            log.info("error operatorId:{}, resp:{}", operatorId, JSON.toJSONString(r));
            return null;
        }
        return r.getResult();
    }

    public String obtainOperatorNameAndOrg(String seatNumber) {
        //处理人的组织-名称
        String operatorInfo = "";
        if (StringUtils.isNotBlank(seatNumber)) {
            AuthRpcResponse<List<String>> authRpcResponse = seaAccountClientV1.getMisListBySeatNum(seatNumber);

            String misName = null;
            if (authRpcResponse != null && CollectionUtils.isNotEmpty(authRpcResponse.getResult())) {
                misName = authRpcResponse.getResult().stream().findFirst().orElse(null);
            }

            if (misName != null) {
                AuthRpcResponse<List<AdminUserAccountModel>> accountModel = seaAccountClientV1.getUserAccountsByMisLike(misName,1);
                AdminUserAccountModel userAccountModel = accountModel.getResult().stream().findFirst().orElse(null);
                if (userAccountModel != null) {
                    operatorInfo = userAccountModel.getName();
                    String orgName = organizationDelegate.getSimpleOrganization(userAccountModel.getId());
                    if (StringUtils.isNotBlank(orgName)) {
                        operatorInfo = orgName + "-" + operatorInfo;
                    }
                }
            } else {
                log.info("can not find mis, seatNumber:{}", seatNumber);
            }
        }
        return operatorInfo;
    }
}
