<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>cf-admin-model</artifactId>

	<parent>
		<groupId>com.shuidihuzhu.cf</groupId>
		<artifactId>cf-admin-parent</artifactId>
		<version>3.5.526-SNAPSHOT</version>
	</parent>

    <dependencies>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-enhancer-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.account</groupId>
            <artifactId>verify-client</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
        <dependency>
            <groupId>org.logback-extensions</groupId>
            <artifactId>logback-ext-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator-annotation-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml</groupId>
            <artifactId>classmate</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu</groupId>
            <artifactId>weixin-java-mp</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--<dependency>-->
            <!--<groupId>com.shuidihuzhu.cf</groupId>-->
            <!--<artifactId>cf-biz</artifactId>-->
            <!--<exclusions>-->
                <!--<exclusion>-->
                    <!--<groupId>com.shuidihuzhu.common</groupId>-->
                    <!--<artifactId>web-app</artifactId>-->
                <!--</exclusion>-->
                <!--<exclusion>-->
                    <!--<groupId>com.shuidihuzhu.common</groupId>-->
                    <!--<artifactId>web-model</artifactId>-->
                <!--</exclusion>-->
                <!--<exclusion>-->
                    <!--<groupId>com.shuidihuzhu.common</groupId>-->
                    <!--<artifactId>web-util</artifactId>-->
                <!--</exclusion>-->
            <!--</exclusions>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-model</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
        </dependency>

        <dependency>
            <groupId>com.madgag.spongycastle</groupId>
            <artifactId>core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.madgag.spongycastle</groupId>
            <artifactId>prov</artifactId>
        </dependency>
        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.account</groupId>
            <artifactId>account-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.common</groupId>
            <artifactId>shuidi-common-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.common</groupId>
            <artifactId>shuidi-common-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.wx</groupId>
            <artifactId>wx-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.common</groupId>
            <artifactId>shuidi-wx-provider</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.thetransactioncompany</groupId>
            <artifactId>cors-filter</artifactId>
            <version>2.5</version>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.shuidihuzhu.common</groupId>
                    <artifactId>web-model</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.pay</groupId>
            <artifactId>pay-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.common</groupId>
            <artifactId>web-core-v2</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.shuidihuzhu.msg</groupId>
                    <artifactId>msg-rpc-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.pf</groupId>
            <artifactId>pf-common-v2</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-material-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-ugc-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.data</groupId>
            <artifactId>servicelog-sdk-java</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-admin-api-pure-client</artifactId>
        </dependency>
    </dependencies>

</project>
