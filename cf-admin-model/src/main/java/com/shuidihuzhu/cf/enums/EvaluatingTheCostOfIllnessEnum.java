package com.shuidihuzhu.cf.enums;

/**
 * Description: 评估疾病花费枚举
 *
 * <AUTHOR>
 * @Create 2025/7/3 15:38
 * @Version 1.0
 */
public enum EvaluatingTheCostOfIllnessEnum {
    NONE(0, "不生成目标金额审核工单"),
    AMOUNT_AFTER_DEDUCTION_LESS_THAN_5W(1, "扣除可承担部分后目标金额低——疾病花费-应扣减金额<5万"),
    DEDUCTION_RATIO_GREATER_THAN_40(2, "扣除可承担部分后目标金额低——应扣减金额/疾病花费>0.4");

    private int code;
    private String description;

    EvaluatingTheCostOfIllnessEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String getDescription(int code) {
        for (EvaluatingTheCostOfIllnessEnum value : EvaluatingTheCostOfIllnessEnum.values()) {
            if (value.getCode() == code) {
                return value.getDescription();
            }
        }
        return  "";
    }
}
