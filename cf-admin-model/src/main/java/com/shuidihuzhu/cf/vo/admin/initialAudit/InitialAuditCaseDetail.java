package com.shuidihuzhu.cf.vo.admin.initialAudit;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.client.material.model.CfBasicLivingGuardModel;
import com.shuidihuzhu.cf.client.material.model.annotation.PreModifyFieldMapping;
import com.shuidihuzhu.cf.client.material.model.materialField.CfMaterialPreModifyField;
import com.shuidihuzhu.cf.enums.EvaluatingTheCostOfIllnessEnum;
import com.shuidihuzhu.cf.model.clew.CfUserInvitedLaunchCaseRecordVO;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.vo.approve.CreditInfoVO;
import com.shuidihuzhu.cf.vo.approve.InitialAuditOCRResultVO;
import com.shuidihuzhu.cf.vo.approve.RiverHandleParamVO;
import com.shuidihuzhu.client.cf.growthtool.model.CfCaseSpecialPrePoseDetail;
import com.shuidihuzhu.client.cf.risk.model.result.UserTagHistoryUnit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.List;

@Data
@ApiModel
public class InitialAuditCaseDetail {

    private CaseBaseInfo caseBaseInfo;
    private FirstApproveCaseInfo firstApproveCaseInfo;
    private CreditInfo creditInfo;

    private TagInfo tagInfo;

    @ApiModelProperty("前置报备信息")
    private CreditInfoVO prepost;

    private List<AdminCfInitialAuditCheckInfoVO> checkInfo;

    @ApiModelProperty("发起人标签维度风险详情")
    private List<UserTagHistoryUnit> userTagHistoryUnits = Lists.newArrayList();

    @ApiModelProperty("贫困与低保信息")
    private CfBasicLivingGuardView additionInfo;

    @ApiModelProperty("贫困处理记录")
    private RiverHandleParamVO pinKunHandleParam;

    @ApiModelProperty("低保处理记录")
    private RiverHandleParamVO diBaoHandleParam;

    @ApiModelProperty("判断姓名以及昵称一致性")
    private InitialJudgeConsistency judgeConsistency;

    @ApiModelProperty("是否使用过待录入")
    private boolean materialTypeInStatus;

    @ApiModelProperty("待录入手机号")
    private String prePostMobile;
    private NumberMaskVo prePostMobileMask;

    @ApiModelProperty("待录入患者身份证号")
    private String prePatientIdCard;

    @ApiModelProperty("待录入发起人身份证号")
    private String preSelfIdCard;

    @ApiModelProperty("风险提示文案列表")
    private List<String> riskTips;

    @ApiModelProperty("是否使用新版高风险字段")
    private boolean useHighRiskV2;

    @ApiModelProperty("工单的状态")
    private int orderResult;

    /**
     * @see EvaluatingTheCostOfIllnessEnum
     */
    @ApiModelProperty("评估疾病花费枚举Code")
    private List<Integer> evaluatingTheCostOfIllnessCode;

//    @Deprecated
//    private String mobile;
//    @Deprecated
//    private String infoUUid;


    @Data
    public static class CaseBaseInfo {

        @PreModifyFieldMapping(fieldNameForSea = CfMaterialPreModifyField.baseInfoTitle,
                fieldNameForC = CfMaterialPreModifyField.baseInfoTitle,
                rejectPositions = InitialAuditItem.EditMaterialType.BASE_INFO_TITLE)
        private String title;

        @PreModifyFieldMapping(fieldNameForSea = CfMaterialPreModifyField.baseInfoContent,
                fieldNameForC = CfMaterialPreModifyField.baseInfoContent,
                rejectPositions = InitialAuditItem.EditMaterialType.BASE_INFO_CONTENT)
        private String content;

        @PreModifyFieldMapping(fieldNameForSea = CfMaterialPreModifyField.baseInfoAttachments,
                fieldNameForC = CfMaterialPreModifyField.baseInfoAttachments,
                rejectPositions = InitialAuditItem.EditMaterialType.BASE_INFO_IMAGE)
        private List<CrowdfundingAttachmentVo> attachments;
        // 呼通的状态
        private int callStatus;

        private int caseId;
        private String infoUUid;
        private String mobile;
        private NumberMaskVo mobileMask;
        private String channel;
        private CfUserInvitedLaunchCaseRecordVO recordVO;

        private int pass;
        private List<Integer> rejectIds;
        //案例头图
        private String headPictureUrl;
        @PreModifyFieldMapping(fieldNameForSea = CfMaterialPreModifyField.targetAmount,
                fieldNameForC = CfMaterialPreModifyField.targetAmount,
                rejectPositions = InitialAuditItem.EditMaterialType.TARGET_AMOUNT)
        @ApiModelProperty("筹款目标金额")
        private int targetAmount;

        private String targetAmountBiggerReason;
        private String privacyImage;
    }


    @Data
    public static class FirstApproveCaseInfo {

        @PreModifyFieldMapping(fieldNameForSea = CfMaterialPreModifyField.firstSelfRealName,
                fieldNameForC = CfMaterialPreModifyField.firstSelfRealName,
                rejectPositions = {InitialAuditItem.EditMaterialType.PATIENT_NAME_IDCARD,InitialAuditItem.EditMaterialType.SELF_NAME_ID_CARD})
        @ApiModelProperty("发起人姓名")
        private String selfRealName;

        @PreModifyFieldMapping(fieldNameForSea = CfMaterialPreModifyField.firstSelfIdCard,
                fieldNameForC = CfMaterialPreModifyField.firstSelfIdCard,
                rejectPositions = {InitialAuditItem.EditMaterialType.PATIENT_NAME_IDCARD,InitialAuditItem.EditMaterialType.SELF_NAME_ID_CARD})
        @ApiModelProperty("发起人身份证")
        private String selfIdCard;
        @ApiModelProperty("发起人身份证(掩码)")
        private NumberMaskVo selfIdCardMask;

        @ApiModelProperty("发起人关系  1是本人")
        @PreModifyFieldMapping(fieldNameForSea = "userRelationType",
                fieldNameForC = CfMaterialPreModifyField.userRelationTypeForC,
                rejectPositions = {InitialAuditItem.EditMaterialType.PATIENT_NAME_IDCARD,InitialAuditItem.EditMaterialType.SELF_NAME_ID_CARD})
        private int userRelationType;

        // 新增关系
        @PreModifyFieldMapping(fieldNameForSea = CfMaterialPreModifyField.userRelationTypeForC,
                fieldNameForC = CfMaterialPreModifyField.userRelationTypeForC,
                rejectPositions = {InitialAuditItem.EditMaterialType.PATIENT_NAME_IDCARD,InitialAuditItem.EditMaterialType.SELF_NAME_ID_CARD})
        @ApiModelProperty("发起人关系")
        private int userRelationTypeForC;

        @ApiModelProperty("是否儿童")
        private int child;

        @PreModifyFieldMapping(fieldNameForSea = CfMaterialPreModifyField.patientRealName,
                fieldNameForC = CfMaterialPreModifyField.patientRealName,
                rejectPositions = {InitialAuditItem.EditMaterialType.PATIENT_NAME_IDCARD,InitialAuditItem.EditMaterialType.SELF_NAME_ID_CARD})
        @ApiModelProperty("患者姓名")
        private String patientRealName;
        @PreModifyFieldMapping(fieldNameForSea = CfMaterialPreModifyField.patientIdCard,
                fieldNameForC = CfMaterialPreModifyField.patientIdCard,
                rejectPositions = {InitialAuditItem.EditMaterialType.PATIENT_NAME_IDCARD,InitialAuditItem.EditMaterialType.SELF_NAME_ID_CARD})
        @ApiModelProperty("患者身份证")
        private String patientIdCard;
        @ApiModelProperty("患者身份证(掩码)")
        private NumberMaskVo patientIdCardMask;

        @PreModifyFieldMapping(fieldNameForSea = CfMaterialPreModifyField.patientBornCard,
                fieldNameForC = CfMaterialPreModifyField.patientBornCard,
                rejectPositions = {InitialAuditItem.EditMaterialType.PATIENT_NAME_IDCARD,InitialAuditItem.EditMaterialType.SELF_NAME_ID_CARD})
        @ApiModelProperty("患者出生证")
        private String patientBornCard;
        @ApiModelProperty("患者出生证(掩码)")
        private NumberMaskVo patientBornCardMask;
        @PreModifyFieldMapping(fieldNameForSea = CfMaterialPreModifyField.patientIdType,
                fieldNameForC = CfMaterialPreModifyField.patientIdType,
                rejectPositions = {InitialAuditItem.EditMaterialType.PATIENT_NAME_IDCARD,InitialAuditItem.EditMaterialType.SELF_NAME_ID_CARD})
        @ApiModelProperty("患者证件类型 " +
                "identity, 身份证 1" +
                "birth, //   出生证2" +
                "passport, // 护照3" +
                "others; // 其他0")
        private int patientIdType;

        @PreModifyFieldMapping(fieldNameForSea = "imageUrl",
                fieldNameForC = CfMaterialPreModifyField.preAuditImageUrl,
                rejectPositions = InitialAuditItem.EditMaterialType.IMAGE_URL)
        @ApiModelProperty("医疗材料")
        private String imageUrl = "";
        private int imageUrlType;
        @ApiModelProperty("0=未知，1=有水印，2=没有水印")
        private int imageUrlWatermark;

        @ApiModelProperty("贫困  0 默认  1  是  2 否")
        private Integer poverty;
        @ApiModelProperty("贫困证明材料")
        private String povertyImageUrl;

        @ApiModelProperty("筹款目标金额")
        private int targetAmount;
        @ApiModelProperty("筹款用途描述")
        private String targetAmountDesc;

        @ApiModelProperty("身份证验证状态 0：不匹配 2：在白名单里 3：系统错误 4:匹配 5:库无")
        private int firstApproveIdcardVerifyStatus;

        @ApiModelProperty("OCR识别结果")
        private InitialAuditOCRResultVO initialAuditOCRResultVO;

        private int pass;
        private List<Integer> rejectIds;

        @ApiModelProperty("特殊报备情况")
        private CfCaseSpecialPrePoseDetail specialPrePoseDetail;

        @ApiModelProperty("是否标记特殊报备")
        private Boolean specialReport;

        @ApiModelProperty("是否展示补充信息标签")
        private int showTag;

        @ApiModelProperty("快照保存时间")
        private Date createTime;
    }

    @Data
    public static class CreditInfo{

        private CreditInfoVO info;

        private int pass;
        private List<Integer> rejectIds;
        private String operateTime;

        public CreditInfo() {
        }

        public CreditInfo(CreditInfoVO info) {
            this.info = info;
        }

        public CreditInfo(CreditInfoVO info, int pass, List<Integer> rejectIds) {
            this.info = info;
            this.pass = pass;
            this.rejectIds = rejectIds;
        }
    }


    @Data
    public static class TagInfo {
        //是否需要显示打电话标签 0:展示 1:不展示
        private int callable;
    }

    @Data
    public static class CfBasicLivingGuardView extends CfBasicLivingGuardModel {
        @ApiModelProperty("0=未知，1=有水印，2=没有水印")
        private int allowanceImgWatermark;
        @ApiModelProperty("0=未知，1=有水印，2=没有水印")
        private int povertyImgWatermark;

        public CfBasicLivingGuardView() {

        }

        public CfBasicLivingGuardView(CfBasicLivingGuardModel model) {
            if (model != null) {
                BeanUtils.copyProperties(model, this);
            }
        }

    }
}
