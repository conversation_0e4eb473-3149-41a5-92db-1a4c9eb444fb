<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>spring-cloud-shuidi-parent</artifactId>
        <groupId>com.shuidihuzhu.infra</groupId>
        <version>3.1.42</version>
    </parent>

    <groupId>com.shuidihuzhu.cf</groupId>
    <artifactId>cf-admin-parent</artifactId>
    <packaging>pom</packaging>
    <version>3.5.526-SNAPSHOT</version>
    <properties>
        <spring-cloud-shuidi-dependencies.version>2.40.15-istio</spring-cloud-shuidi-dependencies.version>
        <boot-image.tag>2</boot-image.tag>
        <java.version>11</java.version>

        <maven.source.skip>false</maven.source.skip>
        <shuidi-wx-provider.version>2.0.88</shuidi-wx-provider.version>
        <weixin-java-mp.version>1.3.45</weixin-java-mp.version>
        <baseservice-client.version>2.1.57</baseservice-client.version>
        <servicelog-meta-cf.version>1.0.60</servicelog-meta-cf.version>
        <wx-grpc-client.version>1.0.100</wx-grpc-client.version>
        <baidu-aip.version>4.4.0</baidu-aip.version>
        <org-json.version>********</org-json.version>
        <me-xdrop.version>1.1.10</me-xdrop.version>
        <account-grpc-client.version>2.2.148</account-grpc-client.version>
        <kratos-client.version>2.0.14</kratos-client.version>
        <!--<sea-auth-client.version>1.8.8</sea-auth-client.version>-->

        <!--        cf外部业务client依赖-->

        <huzhu.client.version>3.0.0</huzhu.client.version>
        <msg-rpc-client.version>2.1.35</msg-rpc-client.version>
        <cos_api.version>5.6.28</cos_api.version>
        <dataservice-client.version>2.1.30</dataservice-client.version>
        <org.mapstruct.version>1.5.2.Final</org.mapstruct.version>
        <pf-common-v2.version>1.0.36</pf-common-v2.version>
        <ai-service-client.version>0.0.5</ai-service-client.version>
        <shuidi-wx-model.version>2.0.63</shuidi-wx-model.version>
        <sdb-order-client.version>1.1.14</sdb-order-client.version>
        <cos-spring-boot-starter.version>0.0.6</cos-spring-boot-starter.version>
        <shuidi-auth-saas-client.version>0.0.49</shuidi-auth-saas-client.version>
        <ai-ocean-client.version>0.0.9</ai-ocean-client.version>
        <ai-venus-client.version>1.0.1</ai-venus-client.version>


        <!--        cf内部业务client依赖-->
        <cf-client-collection.version>9.0.109</cf-client-collection.version>

        <cf-event-center-client.version>9.0.93</cf-event-center-client.version>
        <cf-finance-client.version>3.0.54</cf-finance-client.version>
        <cf-client.version>1.2.465</cf-client.version>
        <frame-client.version>3.0.0</frame-client.version>
        <cf-api.version>3.6.640</cf-api.version>
        <cf-pay-common.version>1.0.0</cf-pay-common.version>
        <cf-stat-client-version>1.0.2</cf-stat-client-version>
        <cf-risk-rpc-client.version>1.0.133</cf-risk-rpc-client.version>
        <charity-rpc-client.version>0.0.101</charity-rpc-client.version>
        <cf-finance-feign-client.version>3.0.21</cf-finance-feign-client.version>
        <cf-store.version>1.0.12</cf-store.version>
        <cf-patient-recruit-client.version>0.0.1</cf-patient-recruit-client.version>
        <cf-finance-common-model.version>1.1.64</cf-finance-common-model.version>
        <org.projectlombok.version>1.18.24</org.projectlombok.version>
        <org.projectlombok.lombok-mapstruct-binding.version>0.2.0</org.projectlombok.lombok-mapstruct-binding.version>
        <cf-lion-client.version>0.0.15</cf-lion-client.version>
        <feign-reactor.version>3.2.6</feign-reactor.version>
        <!--        其他依赖-->
        <cf-enhancer-starter.version>1.0.95</cf-enhancer-starter.version>

    </properties>


    <scm>
        <connection>scm:git:http://git.shuiditech.com/cf/cf-admin-api</connection>
        <developerConnection>scm:git:**********************:cf/cf-admin-api.git</developerConnection>
        <url>http://git.shuiditech.com/cf/cf-admin-api</url>
        <tag>3.5.309</tag>
    </scm>
    <modules>
        <module>cf-admin-model</module>
        <module>cf-admin-common</module>
        <module>cf-admin-dal</module>
        <module>cf-admin-biz</module>
        <module>cf-admin-app</module>
    </modules>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${org.projectlombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${org.projectlombok.lombok-mapstruct-binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
<!--                    <showWarnings>true</showWarnings>-->
<!--                    <compilerArgs>-->
<!--                        <arg>-MapStruct.suppressGeneratorTimestamp=true</arg>-->
<!--                        <arg>-MapStruct.suppressGeneratorVersionInfoComment=true</arg>-->
<!--                        <arg>-MapStruct.verbose=true</arg>-->
<!--                    </compilerArgs>-->
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <dependencies>
                    <dependency>
                        <groupId>javax.activation</groupId>
                        <artifactId>activation</artifactId>
                        <version>1.1.1</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>

    </build>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.playtika.reactivefeign</groupId>
                <artifactId>feign-reactor-bom</artifactId>
                <version>${feign-reactor.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.playtika.reactivefeign</groupId>
                <artifactId>feign-reactor-cloud</artifactId>
                <version>${feign-reactor.version}</version>
            </dependency>

            <dependency>
                <groupId>com.playtika.reactivefeign</groupId>
                <artifactId>feign-reactor-spring-configuration</artifactId>
                <version>${feign-reactor.version}</version>
            </dependency>

            <dependency>
                <groupId>com.playtika.reactivefeign</groupId>
                <artifactId>feign-reactor-webclient</artifactId>
                <version>${feign-reactor.version}</version>
            </dependency>


            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-bom</artifactId>
                <version>4.1.69.Final</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-lion-client</artifactId>
                <version>${cf-lion-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-enhancer-starter</artifactId>
                <version>${cf-enhancer-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.account</groupId>
                <artifactId>account-relation-client</artifactId>
                <version>0.1.1</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.ai-alps</groupId>
                <artifactId>ai-venus-client</artifactId>
                <version>${ai-venus-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.ai-alps</groupId>
                <artifactId>ai-ocean-client</artifactId>
                <version>${ai-ocean-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.op</groupId>
                <artifactId>shuidi-auth-saas-client</artifactId>
                <version>${shuidi-auth-saas-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-finance-common-model</artifactId>
                <version>${cf-finance-common-model.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>easyexcel</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>


            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-patient-recruit-client</artifactId>
                <version>${cf-patient-recruit-client.version}</version>
            </dependency>


            <dependency>
                <groupId>com.shuidihuzhu.cs</groupId>
                <artifactId>cs-work-order-client</artifactId>
                <version>1.0.3</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.pay</groupId>
                <artifactId>pay-common</artifactId>
                <version>${cf-pay-common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-admin-common</artifactId>
                <version>3.5.526-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-admin-model</artifactId>
                <version>3.5.526-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.account</groupId>
                <artifactId>verify-client</artifactId>
                <version>1.0.14</version>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-admin-dal</artifactId>
                <version>3.5.526-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-admin-biz</artifactId>
                <version>3.5.526-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.wx</groupId>
                <artifactId>wx-grpc-client</artifactId>
                <version>${wx-grpc-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu</groupId>
                <artifactId>weixin-java-mp</artifactId>
                <version>${weixin-java-mp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.common</groupId>
                <artifactId>shuidi-wx-model</artifactId>
                <version>${shuidi-wx-model.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.common</groupId>
                <artifactId>shuidi-wx-provider</artifactId>
                <version>${shuidi-wx-provider.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-common</artifactId>
                <version>${cf-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-model</artifactId>
                <version>${cf-api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.shuidihuzhu.common</groupId>
                        <artifactId>web-core-v2</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.shuidihuzhu.cf</groupId>
                        <artifactId>cf-finance-common-model</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-api-client</artifactId>
                <version>${cf-api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.shuidihuzhu.cf</groupId>
                        <artifactId>cf-model</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>


            <dependency>
                <groupId>com.shuidihuzhu.data</groupId>
                <artifactId>servicelog-meta-cf</artifactId>
                <version>${servicelog-meta-cf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.frame</groupId>
                <artifactId>frame-client</artifactId>
                <version>${frame-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.shuidihuzhu.msg</groupId>
                        <artifactId>msg-rpc-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.shuidihuzhu.common</groupId>
                        <artifactId>web-model</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>msg-send-common</artifactId>
                        <groupId>com.shuidihuzhu.msg</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.account</groupId>
                <artifactId>account-grpc-client</artifactId>
                <version>${account-grpc-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baidu.aip</groupId>
                <artifactId>java-sdk</artifactId>
                <version>${baidu-aip.version}</version>
            </dependency>
            <dependency>
                <groupId>org.json</groupId>
                <artifactId>json</artifactId>
                <version>${org-json.version}</version>
            </dependency>
            <dependency>
                <groupId>me.xdrop</groupId>
                <artifactId>fuzzywuzzy</artifactId>
                <version>${me-xdrop.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-client</artifactId>
                <version>${cf-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.msg</groupId>
                <artifactId>msg-rpc-client</artifactId>
                <version>${msg-rpc-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos_api</artifactId>
                <version>${cos_api.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.dataservice</groupId>
                <artifactId>dataservice-client</artifactId>
                <version>${dataservice-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.shuidihuzhu.msg</groupId>
                        <artifactId>msg-rpc-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.shuidihuzhu.pay</groupId>
                        <artifactId>pay-rpc-model</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-finance-client</artifactId>
                <version>${cf-finance-client.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>cf-model</artifactId>
                        <groupId>com.shuidihuzhu.cf</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.shuidihuzhu.cf</groupId>
                        <artifactId>cf-admin-model</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.shuidihuzhu.cf</groupId>
                        <artifactId>cf-finance-common-model</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-finance-feign-client</artifactId>
                <version>${cf-finance-feign-client.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>cf-model</artifactId>
                        <groupId>com.shuidihuzhu.cf</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.shuidihuzhu.cf</groupId>
                        <artifactId>cf-admin-model</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.xml.bind</groupId>
                        <artifactId>jaxb-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.activation</groupId>
                        <artifactId>activation</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-event-center-client</artifactId>
                <version>${cf-event-center-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.shuidihuzhu.common</groupId>
                        <artifactId>web-model</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${org.projectlombok.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>${org.projectlombok.lombok-mapstruct-binding.version}</version>
            </dependency>

            <dependency>
                <groupId>org.skyscreamer</groupId>
                <artifactId>jsonassert</artifactId>
                <version>1.5.0</version>
            </dependency>


            <dependency>
                <groupId>com.shuidihuzhu.eb</groupId>
                <artifactId>schedule-mq-client</artifactId>
                <version>2.1.7</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.hz</groupId>
                <artifactId>huzhu-client</artifactId>
                <version>${huzhu.client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.shuidihuzhu.common</groupId>
                        <artifactId>web-app</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.shuidihuzhu.common</groupId>
                        <artifactId>web-util</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.ctrip.framework.apollo</groupId>
                <artifactId>apollo-client</artifactId>
                <version>1.1.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>aopalliance</groupId>
                        <artifactId>aopalliance</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.pf</groupId>
                <artifactId>pf-common-v2</artifactId>
                <version>${pf-common-v2.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-material-client</artifactId>
                <version>${cf-client-collection.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.shuidihuzhu.common</groupId>
                        <artifactId>web-model</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.shuidihuzhu.common</groupId>
                        <artifactId>web-util</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-ugc-client</artifactId>
                <version>${cf-client-collection.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.shuidihuzhu.common</groupId>
                        <artifactId>web-model</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.shuidihuzhu.common</groupId>
                        <artifactId>web-util</artifactId>
                    </exclusion>
                </exclusions>

            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-client-base</artifactId>
                <version>${cf-client-collection.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>2.1.4</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-activity-client</artifactId>
                <version>${cf-client-collection.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-admin-api-pure-client</artifactId>
                <version>${cf-client-collection.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.cf-risk</groupId>
                <artifactId>cf-risk-rpc-client</artifactId>
                <version>${cf-risk-rpc-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-stat-client</artifactId>
                <version>${cf-stat-client-version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.algo</groupId>
                <artifactId>medicare-client</artifactId>
                <version>0.0.7</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.shuidihuzhu.common</groupId>
                        <artifactId>web-model</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.shuidihuzhu.common</groupId>
                        <artifactId>web-util</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.pf</groupId>
                <artifactId>pf-rpc-client</artifactId>
                <version>2.0.15</version>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.frame</groupId>
                <artifactId>charity-rpc-client</artifactId>
                <version>${charity-rpc-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.ai-algo</groupId>
                <artifactId>ai-service-client</artifactId>
                <version>${ai-service-client.version}</version>
            </dependency>

            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz</artifactId>
                <version>2.3.0</version>
                <exclusions>
                    <exclusion>
                        <artifactId>HikariCP-java6</artifactId>
                        <groupId>com.zaxxer</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>c3p0</artifactId>
                        <groupId>com.mchange</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>markdown2image-plugin</artifactId>
                <version>1.4</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-store</artifactId>
                <version>${cf-store.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-api-pure-client</artifactId>
                <version>${cf-client-collection.version}</version>
            </dependency>
            <!-- 通用权限拦截器 -->
            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>shuidi-security-plugin</artifactId>
                <version>0.0.6</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.shuidihuzhu.op</groupId>
                        <artifactId>shuidi-auth-saas-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 水滴保查询接口 -->
            <dependency>
                <groupId>com.shuidihuzhu.sdb</groupId>
                <artifactId>sdb-order-client</artifactId>
                <version>${sdb-order-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.shuidihuzhu.common</groupId>
                        <artifactId>web-app</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.shuidihuzhu.common</groupId>
                        <artifactId>web-util</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.shuidihuzhu.common</groupId>
                        <artifactId>web-model</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.devops</groupId>
                <artifactId>cloud-app-activity-client</artifactId>
                <version>0.0.5</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.ai-alps</groupId>
                <artifactId>ai-client</artifactId>
                <version>0.0.5</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-data-platform-client</artifactId>
                <version>9.0.99</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
